(footprint "CP_32_7" (version 20230517) (generator pcbnew)
  (layer "F.Cu")
  (fp_text reference "REF**" (at 0 0) (layer "F.SilkS") (tstamp 560166af-5d13-4315-b926-bb9d94195389)
    (effects (font (size 1.27 1.27) (thickness 0.15)))
  )
  (fp_text value "CP_32_7" (at 0 0) (layer "F.SilkS") (tstamp 7c57ec84-94e3-4f7a-8f58-353588e9c9d8)
    (effects (font (size 1.27 1.27) (thickness 0.15)))
  )
  (fp_poly
    (pts
      (xy -0.25 -0.25)
      (xy -1.25 -0.25)
      (xy -1.25 -1.25)
      (xy -0.25 -1.25)
    )
    (stroke (width 0.01) (type solid)) (fill solid) (layer "F.Paste") (tstamp c4743a40-3e13-46b7-a97b-c3451f64f5c4))
  (fp_poly
    (pts
      (xy -0.25 1.25)
      (xy -1.25 1.25)
      (xy -1.25 0.25)
      (xy -0.25 0.25)
    )
    (stroke (width 0.01) (type solid)) (fill solid) (layer "F.Paste") (tstamp a4532941-056b-4e1d-bc13-930c5554526a))
  (fp_poly
    (pts
      (xy 1.25 -0.25)
      (xy 0.25 -0.25)
      (xy 0.25 -1.25)
      (xy 1.25 -1.25)
    )
    (stroke (width 0.01) (type solid)) (fill solid) (layer "F.Paste") (tstamp 029a6bf2-e360-4d25-885a-a2d203e90be1))
  (fp_poly
    (pts
      (xy 1.25 1.25)
      (xy 0.25 1.25)
      (xy 0.25 0.25)
      (xy 1.25 0.25)
    )
    (stroke (width 0.01) (type solid)) (fill solid) (layer "F.Paste") (tstamp 4dc072a8-d0ef-487a-a80e-5575a8151db0))
  (fp_line (start -2.5019 -2.5019) (end -2.5019 -2.23514)
    (stroke (width 0.1524) (type solid)) (layer "F.SilkS") (tstamp f7145a9d-d444-44b5-9b3a-a25ed7c20726))
  (fp_line (start -2.5019 2.23514) (end -2.5019 2.5019)
    (stroke (width 0.1524) (type solid)) (layer "F.SilkS") (tstamp 5f2dc06f-c843-459b-8a3e-e47ac8fd130c))
  (fp_line (start -2.5019 2.5019) (end -2.23514 2.5019)
    (stroke (width 0.1524) (type solid)) (layer "F.SilkS") (tstamp 57666fb5-b9ce-4a25-a366-ebce603b845d))
  (fp_line (start -2.23514 -2.5019) (end -2.5019 -2.5019)
    (stroke (width 0.1524) (type solid)) (layer "F.SilkS") (tstamp 74e4dfab-f2d4-44c9-a070-353e15f3fe9a))
  (fp_line (start 2.23514 2.5019) (end 2.5019 2.5019)
    (stroke (width 0.1524) (type solid)) (layer "F.SilkS") (tstamp 968da8c0-d596-4093-8624-7bb2fe63d987))
  (fp_line (start 2.5019 -2.5019) (end 2.23514 -2.5019)
    (stroke (width 0.1524) (type solid)) (layer "F.SilkS") (tstamp 6cc7f468-6f6b-4562-b831-6446de211593))
  (fp_line (start 2.5019 -2.23514) (end 2.5019 -2.5019)
    (stroke (width 0.1524) (type solid)) (layer "F.SilkS") (tstamp c1d1a17e-5e3b-4960-8f5d-a24cd8ec4567))
  (fp_line (start 2.5019 2.5019) (end 2.5019 2.23514)
    (stroke (width 0.1524) (type solid)) (layer "F.SilkS") (tstamp bd8c9af4-353e-49df-bd14-4c88916b59f9))
  (fp_poly
    (pts
			(xy -2.465384 -2.787688) (xy -2.416428 -2.779518) (xy -2.369485 -2.763403) (xy -2.325834 -2.73978)
			(xy -2.286667 -2.709295) (xy -2.253052 -2.67278) (xy -2.225906 -2.631229) (xy -2.205969 -2.585777)
			(xy -2.193785 -2.537663) (xy -2.189686 -2.4882) (xy -2.193785 -2.438737) (xy -2.205969 -2.390623)
			(xy -2.225906 -2.345171) (xy -2.253052 -2.30362) (xy -2.286667 -2.267105) (xy -2.325834 -2.23662)
			(xy -2.369485 -2.212997) (xy -2.416428 -2.196882) (xy -2.465384 -2.188712) (xy -2.4902 -2.1882) (xy -2.515016 -2.188712)
			(xy -2.563972 -2.196882) (xy -2.610915 -2.212997) (xy -2.654566 -2.23662) (xy -2.693733 -2.267105)
			(xy -2.727348 -2.30362) (xy -2.754494 -2.345171) (xy -2.774431 -2.390623) (xy -2.786615 -2.438737)
			(xy -2.790714 -2.4882) (xy -2.786615 -2.537663) (xy -2.774431 -2.585777) (xy -2.754494 -2.631229)
			(xy -2.727348 -2.67278) (xy -2.693733 -2.709295) (xy -2.654566 -2.73978) (xy -2.610915 -2.763403)
			(xy -2.563972 -2.779518) (xy -2.515016 -2.787688) (xy -2.4902 -2.7882)
    )
    (stroke (width 0.2032) (type solid)) (fill solid) (layer "F.SilkS") (tstamp 47562d03-fbda-4d62-a7eb-2cc8b2dc4138))
  (fp_poly
    (pts
      (xy 1.625 1.625)
      (xy -1.625 1.625)
      (xy -1.625 -1.625)
      (xy 1.625 -1.625)
    )
    (stroke (width 0.01) (type solid)) (fill solid) (layer "F.Mask") (tstamp 87ab8447-6479-4e4d-9434-3eb5396ccdf8))
  (fp_line (start -2.5 -2.5) (end -2.5 -1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp dc0f11ec-f499-421b-81d8-b2042059bb88))
  (fp_line (start -2.5 -2.5) (end -2.5 2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 6030f791-ab7e-4174-bc65-799d0482f69d))
  (fp_line (start -2.5 -1.75) (end -1.75 -1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp cb55a6b7-0554-4c87-a4c3-c363e245940a))
  (fp_line (start -2.5 1.75) (end -2.5 2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 6bbd47be-5c2b-4152-9368-f6ca1ac6efb0))
  (fp_line (start -2.5 2.5) (end -1.75 2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 50a5e31d-90da-4ca0-9acf-b4e9cf095e9f))
  (fp_line (start -2.5 2.5) (end 2.5 2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 342cd87e-b8b7-417d-a5e1-1ce6e5c525ec))
  (fp_line (start -1.75 -2.5) (end -2.5 -2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp b4d03b32-c822-4a41-91ad-f5e06548876a))
  (fp_line (start -1.75 -1.75) (end -1.75 -2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 6ee63a4e-616b-4888-91f6-e39303467fab))
  (fp_line (start -1.75 -1.75) (end -1.75 1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 608164ac-85ef-4dcb-b384-fa12288f0ba6))
  (fp_line (start -1.75 1.75) (end -2.5 1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 8ef35e6a-ee0a-4c20-a67c-5fc773a095bb))
  (fp_line (start -1.75 1.75) (end 1.75 1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 046d2c13-f228-432e-8ab0-d83911e39041))
  (fp_line (start -1.75 2.5) (end -1.75 1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp ebdd919b-606e-45d2-ba55-298fd124c718))
  (fp_line (start -1.5 -1.5) (end -1.5 1.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp c1b3f3d8-1e8f-4279-af17-989dab51a854))
  (fp_line (start -1.5 1.5) (end 1.5 1.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 187242c3-845a-4b2b-98e2-f2d6ca6235ae))
  (fp_line (start 1.5 -1.5) (end -1.5 -1.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 3f3bca60-51ab-46a9-9f62-9232d6559467))
  (fp_line (start 1.5 1.5) (end 1.5 -1.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 7f0ba0a6-365f-405a-9d02-380b851f9cbb))
  (fp_line (start 1.75 -2.5) (end 1.75 -1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 9771213c-b42d-4e16-9865-cd3fcb9ec70f))
  (fp_line (start 1.75 -1.75) (end -1.75 -1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp b7b0709e-75b8-4d22-8738-3d5a0287e799))
  (fp_line (start 1.75 -1.75) (end 2.5 -1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 4c3a6c2e-928e-4f73-a108-110ba09a9288))
  (fp_line (start 1.75 1.75) (end 1.75 -1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 7b22b513-21df-4be5-8871-76be66f05a15))
  (fp_line (start 1.75 1.75) (end 1.75 2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp d9862dfc-26fd-4fbc-86fc-48fb26894f36))
  (fp_line (start 1.75 2.5) (end 2.5 2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 04c45a9a-e1fe-46d0-9007-15ec52812fc4))
  (fp_line (start 2.5 -2.5) (end -2.5 -2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 41eaed31-e342-4f43-926c-a66fbe808ecf))
  (fp_line (start 2.5 -2.5) (end 1.75 -2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 03fdc2ba-0a8e-4abd-9068-2f684fbeb53c))
  (fp_line (start 2.5 -1.75) (end 2.5 -2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 68481484-3ea1-40fc-8278-073fdd0fec2f))
  (fp_line (start 2.5 1.75) (end 1.75 1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp f2d8feed-1a40-492f-b1cf-ed3a9c089b42))
  (fp_line (start 2.5 2.5) (end 2.5 -2.5)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp d93d3e43-3fed-481a-82cc-033a6958c94a))
  (fp_line (start 2.5 2.5) (end 2.5 1.75)
    (stroke (width 0.01) (type solid)) (layer "Eco1.User") (tstamp 9aea01ff-272c-4553-83ef-50762872aca1))
  (fp_line (start -2.85 -2.85) (end -2.85 2.85)
    (stroke (width 0.01) (type solid)) (layer "F.CrtYd") (tstamp cd1bd6d0-cb19-4e7f-9490-c45e6cb2f2b2))
  (fp_line (start -2.85 2.85) (end 2.85 2.85)
    (stroke (width 0.01) (type solid)) (layer "F.CrtYd") (tstamp 4c02122f-8aae-4ef7-852c-2699c92888b6))
  (fp_line (start 2.85 -2.85) (end -2.85 -2.85)
    (stroke (width 0.01) (type solid)) (layer "F.CrtYd") (tstamp ceb76797-0442-47d7-b690-760a823d50f0))
  (fp_line (start 2.85 2.85) (end 2.85 -2.85)
    (stroke (width 0.01) (type solid)) (layer "F.CrtYd") (tstamp c454246d-c8aa-49c1-b3ce-6624f1f93338))
  (fp_line (start -2.5019 -1.9024) (end -2.5019 -1.5976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp b28ea4fe-7ca7-4f43-aef4-7302648cd4bd))
  (fp_line (start -2.5019 -1.5976) (end -2.5019 -1.9024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 729b1b55-b766-4c9d-9c02-045a63451843))
  (fp_line (start -2.5019 -1.4024) (end -2.5019 -1.0976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 2d3fbf84-ce53-40be-8592-f0fca141c012))
  (fp_line (start -2.5019 -1.2319) (end -1.2319 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 66280522-8cc2-4459-a270-66c018c3bd97))
  (fp_line (start -2.5019 -1.0976) (end -2.5019 -1.4024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 60f01d07-bf50-47c7-a61e-730ffebd70e5))
  (fp_line (start -2.5019 -0.9024) (end -2.5019 -0.5976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp d1a70d42-3a04-4144-94bd-a2e39a02d37a))
  (fp_line (start -2.5019 -0.5976) (end -2.5019 -0.9024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 485e5881-0533-44fc-986e-6a6040a2f05c))
  (fp_line (start -2.5019 -0.4024) (end -2.5019 -0.0976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 8aeecff0-a0ef-4b8d-96dd-b6f5bebab1df))
  (fp_line (start -2.5019 -0.0976) (end -2.5019 -0.4024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 5ef6e5de-834e-4a77-8d34-dbb666750e28))
  (fp_line (start -2.5019 0.0976) (end -2.5019 0.4024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 4dabf7cc-8e09-4f65-8e1e-a3a3b6f38400))
  (fp_line (start -2.5019 0.4024) (end -2.5019 0.0976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 8fa68438-11b7-446f-9c90-34deb39d34ac))
  (fp_line (start -2.5019 0.5976) (end -2.5019 0.9024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 74551c47-6957-495d-b44c-6bf1efa84eec))
  (fp_line (start -2.5019 0.9024) (end -2.5019 0.5976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 9a5698b1-8450-4e60-b598-46c6a2a389d5))
  (fp_line (start -2.5019 1.0976) (end -2.5019 1.4024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 25eecaf5-f2ce-40b4-a36a-2b0c8e624afe))
  (fp_line (start -2.5019 1.4024) (end -2.5019 1.0976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 4e16a7b3-b834-40fc-b344-4eeaeb2e8ce4))
  (fp_line (start -2.5019 1.5976) (end -2.5019 1.9024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp c5a8fb95-fcc3-409b-8e74-3523ff8db92e))
  (fp_line (start -2.5019 1.9024) (end -2.5019 1.5976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 4bb2a8fb-26d8-4b8d-a6f2-246c9e4792a2))
  (fp_line (start -2.5 -2.5) (end -2.5 2.5)
    (stroke (width 0.01) (type solid)) (layer "F.Fab") (tstamp 90a2909b-f365-4be6-9861-f5bb7f83f6ee))
  (fp_line (start -2.5 2.5) (end 2.5 2.5)
    (stroke (width 0.01) (type solid)) (layer "F.Fab") (tstamp 03a85d72-fca3-45ce-8da6-bf54e543b8f6))
  (fp_line (start -1.9024 -2.5019) (end -1.5976 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 4069d8a3-31ed-4f59-9726-6e302cb7b715))
  (fp_line (start -1.5976 -2.5019) (end -1.9024 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 485e8f20-6c7d-49c8-8463-0549e783839d))
  (fp_line (start -1.5976 2.5019) (end -1.9024 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp aa5bdf55-f3f9-48a1-9ec9-3e5504e3b5d7))
  (fp_line (start -1.4024 -2.5019) (end -1.0976 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp df158a87-5ff2-4137-b301-8a549085df50))
  (fp_line (start -1.4024 2.5019) (end -1.0976 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 7ec23205-7d9a-40b3-a60e-e0c42a7dcff3))
  (fp_line (start -1.0976 -2.5019) (end -1.4024 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 25088cd6-1ab8-46e3-afd6-1a572cfc522b))
  (fp_line (start -1.0976 2.5019) (end -1.4024 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 15b43596-9fb5-4f0f-9a95-072b5d9f974d))
  (fp_line (start -0.9024 -2.5019) (end -0.5976 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 0505d9e7-8d14-47b7-8da1-f0607eba35e4))
  (fp_line (start -0.9024 2.5019) (end -0.5976 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 150c6b06-45aa-479b-9fb2-0b1ec0d37a43))
  (fp_line (start -0.5976 -2.5019) (end -0.9024 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp e985d951-f4ef-44f1-97f9-4ecb991e267c))
  (fp_line (start -0.5976 2.5019) (end -0.9024 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 6e14a478-7d38-44cb-9c55-24534bbd3ce1))
  (fp_line (start -0.4024 -2.5019) (end -0.0976 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp baa5c256-2556-4d28-b591-4a7e8dfc5925))
  (fp_line (start -0.4024 2.5019) (end -0.0976 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 1fada355-e8a0-4bc3-bfa4-ff5f9d5af50d))
  (fp_line (start -0.0976 -2.5019) (end -0.4024 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 1811d296-ce47-4ae4-abea-b6a486c51eef))
  (fp_line (start -0.0976 2.5019) (end -0.4024 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 4ad32e36-b542-4e3f-8e05-ed6e5729e0bf))
  (fp_line (start 0.0976 -2.5019) (end 0.4024 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp b53ccdb7-2d14-4851-9f4f-d31940bfb980))
  (fp_line (start 0.0976 2.5019) (end 0.4024 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 6c1904a5-01dd-4f04-9c2e-5d976ab24626))
  (fp_line (start 0.4024 -2.5019) (end 0.0976 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp a68b78d1-9b9f-4301-9e8c-b49e81ab6819))
  (fp_line (start 0.4024 2.5019) (end 0.0976 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 8593aec1-c4b6-412b-a897-e79dc47679dd))
  (fp_line (start 0.5976 -2.5019) (end 0.9024 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 7b41bf98-eacb-4b74-9ad1-e68ee9d81505))
  (fp_line (start 0.5976 2.5019) (end 0.9024 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 72e5b6dd-148c-433f-b75d-96911f6be51a))
  (fp_line (start 0.9024 -2.5019) (end 0.5976 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 27c7d0b0-9e44-4286-a703-6b4a2e41f393))
  (fp_line (start 0.9024 2.5019) (end 0.5976 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 3fa98199-5f93-4be9-a510-45b0fb364806))
  (fp_line (start 1.0976 -2.5019) (end 1.4024 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 25eb81a8-1828-4580-bd90-4776fca0bd2d))
  (fp_line (start 1.0976 2.5019) (end 1.4024 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 92a85c20-6c72-4139-b874-6f6e31b6d25d))
  (fp_line (start 1.4024 -2.5019) (end 1.0976 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 2ca2511b-06b1-48ca-8906-b5663fe545ee))
  (fp_line (start 1.4024 2.5019) (end 1.0976 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 0b1a0f94-e8b6-41f7-add5-23de9be00829))
  (fp_line (start 1.5976 -2.5019) (end 1.9024 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp ec1c7b55-bbc0-4d1a-a6d3-7311569e9d91))
  (fp_line (start 1.5976 2.5019) (end 1.9024 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp a8b51572-a71e-4b36-ad09-9c1439827c00))
  (fp_line (start 1.9024 -2.5019) (end 1.5976 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 56eb7535-e15f-49f1-8a0e-bce1c8cceffb))
  (fp_line (start 1.9024 2.5019) (end 1.5976 2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 978e2cd8-1920-4963-9ef8-ddbfbede7d18))
  (fp_line (start 2.5 -2.5) (end -2.5 -2.5)
    (stroke (width 0.01) (type solid)) (layer "F.Fab") (tstamp ce82d3db-702f-4dbf-a242-5d41213bcdfb))
  (fp_line (start 2.5 2.5) (end 2.5 -2.5)
    (stroke (width 0.01) (type solid)) (layer "F.Fab") (tstamp 698857a4-331d-4240-8d16-7a563d00b2b8))
  (fp_line (start 2.5019 -2.5019) (end -2.5019 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 902777b4-fddc-442b-adcd-da5dc91f2f1e))
  (fp_line (start 2.5019 -1.9024) (end 2.5019 -1.5976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp c2d21324-04ba-433d-8cca-f2a8e1ce947b))
  (fp_line (start 2.5019 -1.5976) (end 2.5019 -1.9024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp caaad63f-6380-4789-96fc-9cfe782b37af))
  (fp_line (start 2.5019 -1.4024) (end 2.5019 -1.0976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 8e17cb8c-8adb-4b6b-a676-6a37b53c3c80))
  (fp_line (start 2.5019 -1.0976) (end 2.5019 -1.4024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp e902c399-aa98-4d40-91b1-62e4198230a4))
  (fp_line (start 2.5019 -0.9024) (end 2.5019 -0.5976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 586ef6a8-e5b8-473e-b853-0f86025f2040))
  (fp_line (start 2.5019 -0.5976) (end 2.5019 -0.9024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 54f64678-daf5-4a0f-9196-bd4ae9fcba73))
  (fp_line (start 2.5019 -0.4024) (end 2.5019 -0.0976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp cd74d86e-1a1d-4d11-81cf-6b62c6fb435c))
  (fp_line (start 2.5019 -0.0976) (end 2.5019 -0.4024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp b59c0dd6-9400-4e73-a3ff-284cb31298d7))
  (fp_line (start 2.5019 0.0976) (end 2.5019 0.4024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 9563fba3-b1f2-4373-9d00-7e2a284abf3d))
  (fp_line (start 2.5019 0.4024) (end 2.5019 0.0976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 893a3134-06f1-4f05-9bca-91f5ab1acf95))
  (fp_line (start 2.5019 0.5976) (end 2.5019 0.9024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 7e5bf19e-4397-4de8-b73a-bbf8c321d1d5))
  (fp_line (start 2.5019 0.9024) (end 2.5019 0.5976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp fe401756-80d0-4ba7-a332-31e8a8ca1d93))
  (fp_line (start 2.5019 1.0976) (end 2.5019 1.4024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp fac2708d-4bd0-4cd0-a797-9fa509d66d2a))
  (fp_line (start 2.5019 1.4024) (end 2.5019 1.0976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 8311ebff-d06b-45b8-b7cb-c888e09f5335))
  (fp_line (start 2.5019 1.5976) (end 2.5019 1.9024)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 33c984f2-6da1-492b-8ea2-d08dadce75a8))
  (fp_line (start 2.5019 1.9024) (end 2.5019 1.5976)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 6a142f82-96b9-4518-aee6-78ad64769ac8))
  (fp_line (start 2.5019 2.5019) (end 2.5019 -2.5019)
    (stroke (width 0.0254) (type solid)) (layer "F.Fab") (tstamp 0d641784-b3f3-471d-a8a2-f17bb6cdd15d))
  (pad "1" smd rect (at -2.4003 -1.75) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp e68f7211-2a51-4e12-b294-031f7dc2130c))
  (pad "2" smd rect (at -2.4003 -1.25) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp c962482e-1c33-43cc-aefa-f2f57a845028))
  (pad "3" smd rect (at -2.4003 -0.75) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 5115fd75-42df-48da-8786-c8ca798f6375))
  (pad "4" smd rect (at -2.4003 -0.25) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 1c2edfaf-e99c-42aa-9590-6d7d0c0bb90d))
  (pad "5" smd rect (at -2.4003 0.25) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 912b1077-827a-43f0-9fe7-9b7420f1088a))
  (pad "6" smd rect (at -2.4003 0.75) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 57fcc5a0-7a12-43f1-80b7-c161f831ffaa))
  (pad "7" smd rect (at -2.4003 1.25) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp e82effd2-7e59-4ecc-8f18-65233e0d72bb))
  (pad "8" smd rect (at -2.4003 1.75) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 412337c2-736f-48ab-aaa3-d3a9de63ba5d))
  (pad "9" smd rect (at -1.75 2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp f3e7a204-24e0-409f-9a7a-1a02bbe012d2))
  (pad "10" smd rect (at -1.25 2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 3b4bea7a-9302-4c5c-b6be-f19d4a6c3482))
  (pad "11" smd rect (at -0.75 2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 64c8206f-2949-4329-8820-827c5adb7f08))
  (pad "12" smd rect (at -0.25 2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp e5fc8ace-35d6-47b4-808d-d4503869f0b7))
  (pad "13" smd rect (at 0.25 2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 71a2f6c1-a31a-4fb8-94bb-85b29356da6a))
  (pad "14" smd rect (at 0.75 2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 4e46d619-f7ed-44a8-98cb-2f4e491f2fc0))
  (pad "15" smd rect (at 1.25 2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp d8dfc3a3-9769-47d5-8dba-40674fa079da))
  (pad "16" smd rect (at 1.75 2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 587cfcd8-9ac5-4a3d-b53a-e987297aa61c))
  (pad "17" smd rect (at 2.4003 1.75) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 5e3693fb-fea1-46f5-acb8-29ae8c2392d1))
  (pad "18" smd rect (at 2.4003 1.25) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp fff6e556-f720-43a1-82c7-40633b2ed33a))
  (pad "19" smd rect (at 2.4003 0.75) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 9629097e-d49f-43e0-adc2-d27398435d53))
  (pad "20" smd rect (at 2.4003 0.25) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp b28039a7-b00c-4a2d-8198-bf449f96a9b6))
  (pad "21" smd rect (at 2.4003 -0.25) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp d77e62fd-5902-4eee-a0d6-498bded5fdbd))
  (pad "22" smd rect (at 2.4003 -0.75) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp ba9498cb-dd1a-419f-ae4a-272d66bf66e1))
  (pad "23" smd rect (at 2.4003 -1.25) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 04d9f9f8-04d6-4a99-8b82-83f802475aa4))
  (pad "24" smd rect (at 2.4003 -1.75) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 7c40046b-7f0b-44c4-9401-72c7a4fb7827))
  (pad "25" smd rect (at 1.75 -2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp a396e70e-a033-4cc2-a1f4-feb31c7a0996))
  (pad "26" smd rect (at 1.25 -2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp c97c014a-8b63-4aec-bd3e-56fc7db9e3c7))
  (pad "27" smd rect (at 0.75 -2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 80c0d66d-2b5f-4c8f-9fb4-2b431c72fbad))
  (pad "28" smd rect (at 0.25 -2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 1730799c-52e3-4fbc-a0ea-d11bb3ece684))
  (pad "29" smd rect (at -0.25 -2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 7ec5a5c6-9698-4d61-b6c3-4745e287f623))
  (pad "30" smd rect (at -0.75 -2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp df181f7c-e264-4347-9d33-8186d10394f2))
  (pad "31" smd rect (at -1.25 -2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 82267ab0-f866-4748-87c3-bf397f3a4ab0))
  (pad "32" smd rect (at -1.75 -2.4003 270) (size 0.8 0.3) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.0659) (thermal_bridge_angle 45) (tstamp 14ec3448-e923-4395-a76d-a121d194efc7))
  (pad "33" thru_hole circle (at -0.8 -0.8) (size 0.3 0.3) (drill 0.2) (layers "*.Cu") (tstamp fdda2038-465e-4151-a5ae-8860c08125cf))
  (pad "33" smd custom (at -0.8 -0.8) (size 0.3 0.3) (layers "F.Cu")
    (thermal_bridge_angle 45)
    (options (clearance outline) (anchor circle))
    (primitives
      (gr_poly
        (pts
          (xy 2.30898 -0.70898)
          (xy 2.3127 -0.7)
          (xy 2.3127 2.3)
          (xy 2.30898 2.30898)
          (xy 2.3 2.3127)
          (xy -0.7 2.3127)
          (xy -0.70898 2.30898)
          (xy -0.7127 2.3)
          (xy -0.7127 -0.7)
          (xy -0.70898 -0.70898)
          (xy -0.7 -0.7127)
          (xy 2.3 -0.7127)
        )
        (width 0) (fill yes))
    ) (tstamp 5c9904d1-5930-4716-8701-acb9ae3de775))
  (pad "33" thru_hole circle (at -0.8 0) (size 0.3 0.3) (drill 0.2) (layers "*.Cu") (tstamp 14e6b7f0-a9dd-4014-b6a4-d0dcddc992d9))
  (pad "33" thru_hole circle (at -0.8 0.8) (size 0.3 0.3) (drill 0.2) (layers "*.Cu") (tstamp dd86521d-61d7-4e2a-b4fd-75e17a9044c2))
  (pad "33" thru_hole circle (at 0 -0.8) (size 0.3 0.3) (drill 0.2) (layers "*.Cu") (tstamp 84145658-d4b5-4d81-a915-7fc6ef93e8f9))
  (pad "33" thru_hole circle (at 0 0) (size 0.3 0.3) (drill 0.2) (layers "*.Cu") (tstamp b4c1a8da-7416-4c76-a6b4-55c1b937b0d3))
  (pad "33" thru_hole circle (at 0 0.8) (size 0.3 0.3) (drill 0.2) (layers "*.Cu") (tstamp b3686c13-0b66-44cb-b048-decaf6b3f728))
  (pad "33" thru_hole circle (at 0.8 -0.8) (size 0.3 0.3) (drill 0.2) (layers "*.Cu") (tstamp 39b9a055-c4fe-4864-88df-e6fa1e208d3f))
  (pad "33" thru_hole circle (at 0.8 0) (size 0.3 0.3) (drill 0.2) (layers "*.Cu") (tstamp 3bf2c3d7-4766-40d1-b698-d214cea20c5f))
  (pad "33" thru_hole circle (at 0.8 0.8) (size 0.3 0.3) (drill 0.2) (layers "*.Cu") (tstamp 931e6623-1572-440b-94c4-599176e2f580))
)
