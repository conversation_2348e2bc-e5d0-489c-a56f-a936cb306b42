{"board": {"3dviewports": [], "design_settings": {"defaults": {"board_outline_line_width": 0.09999999999999999, "copper_line_width": 0.19999999999999998, "copper_text_italic": false, "copper_text_size_h": 1.5, "copper_text_size_v": 1.5, "copper_text_thickness": 0.3, "copper_text_upright": false, "courtyard_line_width": 0.049999999999999996, "dimension_precision": 4, "dimension_units": 3, "dimensions": {"arrow_length": 1270000, "extension_offset": 500000, "keep_text_aligned": true, "suppress_zeroes": false, "text_position": 0, "units_format": 1}, "fab_line_width": 0.09999999999999999, "fab_text_italic": false, "fab_text_size_h": 1.0, "fab_text_size_v": 1.0, "fab_text_thickness": 0.15, "fab_text_upright": false, "other_line_width": 0.15, "other_text_italic": false, "other_text_size_h": 1.0, "other_text_size_v": 1.0, "other_text_thickness": 0.15, "other_text_upright": false, "pads": {"drill": 0.0, "height": 0.8, "width": 0.8}, "silk_line_width": 0.15, "silk_text_italic": false, "silk_text_size_h": 1.0, "silk_text_size_v": 1.0, "silk_text_thickness": 0.15, "silk_text_upright": false, "zones": {"45_degree_only": false, "min_clearance": 0.0}}, "diff_pair_dimensions": [{"gap": 0.0, "via_gap": 0.0, "width": 0.0}, {"gap": 0.25, "via_gap": 0.25, "width": 0.13}], "drc_exclusions": [], "meta": {"version": 2}, "rule_severities": {"annular_width": "error", "clearance": "error", "connection_width": "ignore", "copper_edge_clearance": "error", "copper_sliver": "warning", "courtyards_overlap": "error", "diff_pair_gap_out_of_range": "error", "diff_pair_uncoupled_length_too_long": "error", "drill_out_of_range": "error", "duplicate_footprints": "warning", "extra_footprint": "warning", "footprint": "error", "footprint_type_mismatch": "error", "hole_clearance": "error", "hole_near_hole": "error", "invalid_outline": "error", "isolated_copper": "warning", "item_on_disabled_layer": "error", "items_not_allowed": "error", "length_out_of_range": "error", "lib_footprint_issues": "error", "lib_footprint_mismatch": "error", "malformed_courtyard": "error", "microvia_drill_out_of_range": "error", "missing_courtyard": "ignore", "missing_footprint": "warning", "net_conflict": "warning", "npth_inside_courtyard": "ignore", "padstack": "error", "pth_inside_courtyard": "ignore", "shorting_items": "error", "silk_edge_clearance": "warning", "silk_over_copper": "warning", "silk_overlap": "ignore", "skew_out_of_range": "error", "solder_mask_bridge": "error", "starved_thermal": "error", "text_height": "warning", "text_thickness": "warning", "through_hole_pad_without_hole": "error", "too_many_vias": "error", "track_dangling": "warning", "track_width": "error", "tracks_crossing": "error", "unconnected_items": "error", "unresolved_variable": "error", "via_dangling": "warning", "zones_intersect": "error"}, "rules": {"allow_blind_buried_vias": true, "allow_microvias": false, "max_error": 0.005, "min_clearance": 0.09999999999999999, "min_connection": 0.0, "min_copper_edge_clearance": 0.5, "min_hole_clearance": 0.01, "min_hole_to_hole": 0.01, "min_microvia_diameter": 0.19999999999999998, "min_microvia_drill": 0.125, "min_resolved_spokes": 1, "min_silk_clearance": 0.0, "min_text_height": 0.7999999999999999, "min_text_thickness": 0.12, "min_through_hole_diameter": 0.09999999999999999, "min_track_width": 0.09999999999999999, "min_via_annular_width": 0.09999999999999999, "min_via_diameter": 0.39999999999999997, "solder_mask_clearance": 0.0, "solder_mask_min_width": 0.0, "solder_mask_to_copper_clearance": 0.0, "use_height_for_length_calcs": true}, "teardrop_options": [{"td_allow_use_two_tracks": true, "td_curve_segcount": 5, "td_on_pad_in_zone": false, "td_onpadsmd": true, "td_onroundshapesonly": false, "td_ontrackend": false, "td_onviapad": true}], "teardrop_parameters": [{"td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.2, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_target_name": "td_round_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_target_name": "td_rect_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_target_name": "td_track_end", "td_width_to_size_filter_ratio": 0.9}], "track_widths": [0.0, 0.07, 0.1, 0.12, 0.15, 0.2, 0.3, 0.4, 0.5], "via_dimensions": [{"diameter": 0.0, "drill": 0.0}, {"diameter": 0.4, "drill": 0.15}, {"diameter": 0.5, "drill": 0.3}, {"diameter": 0.8, "drill": 0.5}], "zones_allow_external_fillets": false, "zones_use_no_outline": true}, "layer_presets": [], "viewports": []}, "boards": [], "cvpcb": {"equivalence_files": []}, "erc": {"erc_exclusions": [], "meta": {"version": 0}, "pin_map": [[0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 0, 1, 0, 0, 1, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 2], [0, 1, 0, 0, 0, 0, 1, 1, 2, 1, 1, 2], [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 2], [0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 1, 2, 0, 0, 1, 0, 2, 2, 2, 2], [0, 2, 0, 1, 0, 0, 1, 0, 2, 0, 0, 2], [0, 2, 1, 1, 0, 0, 1, 0, 2, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "rule_severities": {"bus_definition_conflict": "error", "bus_entry_needed": "error", "bus_label_syntax": "error", "bus_to_bus_conflict": "error", "bus_to_net_conflict": "error", "different_unit_footprint": "error", "different_unit_net": "error", "duplicate_reference": "error", "duplicate_sheet_names": "error", "extra_units": "error", "global_label_dangling": "warning", "hier_label_mismatch": "error", "label_dangling": "error", "lib_symbol_issues": "warning", "multiple_net_names": "warning", "net_not_bus_member": "warning", "no_connect_connected": "warning", "no_connect_dangling": "warning", "pin_not_connected": "warning", "pin_not_driven": "warning", "pin_to_pin": "error", "power_pin_not_driven": "warning", "similar_labels": "warning", "unannotated": "error", "unit_value_mismatch": "error", "unresolved_variable": "error", "wire_dangling": "error"}}, "libraries": {"pinned_footprint_libs": ["My_Devices"], "pinned_symbol_libs": []}, "meta": {"filename": "pns.kicad_pro", "version": 1}, "net_settings": {"classes": [{"bus_width": 12, "clearance": 0.12, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.0, "microvia_drill": 0.0, "name": "<PERSON><PERSON><PERSON>", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.12, "via_diameter": 0.5, "via_drill": 0.3, "wire_width": 6}, {"bus_width": 12, "clearance": 0.25, "diff_pair_gap": 0.16, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.1, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "100Ohm_diff", "pcb_color": "rgb(0, 194, 194)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.25, "via_diameter": 0.5, "via_drill": 0.3, "wire_width": 6}, {"bus_width": 12, "clearance": 0.2, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "100Ohm_diff_inter", "pcb_color": "rgb(0, 132, 132)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.25, "via_diameter": 0.5, "via_drill": 0.3, "wire_width": 6}, {"bus_width": 12, "clearance": 0.4, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "50Ohm_single", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.25, "via_diameter": 0.5, "via_drill": 0.3, "wire_width": 6}], "meta": {"version": 3}, "net_colors": null, "netclass_assignments": null, "netclass_patterns": [{"netclass": "100Ohm_diff", "pattern": "/camera signal/IN.0P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/IN.1P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/IN.1N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/IN.2P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/IN.2N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/IN.3P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/IN.3N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/IN.CP"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/IN.CN"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/IN.0N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/OUT.1P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/OUT.3P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/OUT.3N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/OUT.2P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/OUT.2N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/OUT.1N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/OUT.0P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/OUT.0N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/OUT.CP"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/OUT.CN"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/LVDS_D.CP"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/LVDS_D.CN"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/LVDS_D.2P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/LVDS_D.2N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/LVDS_D.0P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/LVDS_D.0N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/LVDS_D.1P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/LVDS_D.1N"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/LVDS_D.3P"}, {"netclass": "100Ohm_diff", "pattern": "/camera signal/LVDS_D.3N"}]}, "pcbnew": {"last_paths": {"gencad": "", "idf": "", "netlist": "", "specctra_dsn": "", "step": "", "vrml": ""}, "page_layout_descr_file": ""}, "schematic": {"annotate_start_num": 0, "drawing": {"dashed_lines_dash_length_ratio": 12.0, "dashed_lines_gap_length_ratio": 3.0, "default_line_thickness": 6.0, "default_text_size": 50.0, "field_names": [], "intersheets_ref_own_page": false, "intersheets_ref_prefix": "", "intersheets_ref_short": false, "intersheets_ref_show": true, "intersheets_ref_suffix": "", "junction_size_choice": 3, "label_size_ratio": 0.375, "pin_symbol_size": 25.0, "text_offset_ratio": 0.15}, "legacy_lib_dir": "", "legacy_lib_list": [], "meta": {"version": 1}, "net_format_name": "OrcadPCB2", "ngspice": {"fix_include_paths": true, "fix_passive_vals": false, "meta": {"version": 0}, "model_mode": 0, "workbook_filename": ""}, "page_layout_descr_file": "", "plot_directory": "", "spice_adjust_passive_values": false, "spice_external_command": "spice \"%I\"", "spice_save_all_currents": false, "spice_save_all_voltages": false, "subpart_first_id": 65, "subpart_id_separator": 0}, "sheets": [["e63e39d7-6ac0-4ffd-8aa3-1841a4541b55", ""]], "text_variables": {}}