(kicad_sch (version 20220622) (generator eeschema)

  (uuid 3a449e21-a736-4714-8865-e7318f0055f6)

  (paper "A4")

  (lib_symbols
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:IDC" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "IDC" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "dc(1)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, DC" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "IDC_0_0"
        (polyline
          (pts
            (xy -1.27 0.254)
            (xy 1.27 0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.254)
            (xy -1.27 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.254 -0.254)
            (xy -0.254 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.254)
            (xy 0.762 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "IDC_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "IDC_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:IEXP" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "IEXP" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "exp(0 1 2n 30n 60n 40n)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, exponential" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "IEXP_0_0"
        (polyline
          (pts
            (xy -1.27 -0.762)
            (xy -1.778 -0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.762)
            (xy 1.778 -0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0.762) (mid -0.851 0.1794) (end -1.27 -0.762)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0.762) (mid 0.3591 -0.2299) (end 1.27 -0.762)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "IEXP_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "IEXP_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:IPULSE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "IPULSE" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "pulse(0 1 2n 2n 2n 50n 100n)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, pulse" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "IPULSE_0_0"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy -1.397 -0.762)
            (xy -1.143 0.762)
            (xy -0.127 0.762)
            (xy 0.127 -0.762)
            (xy 1.143 -0.762)
            (xy 1.397 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "IPULSE_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "IPULSE_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:IPWL" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "IPWL" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "pwl(0 -1 50n -1 51n 0 97n 1 171n -1 200n -1)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, piece-wise linear" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "IPWL_0_0"
        (polyline
          (pts
            (xy -1.778 -1.016)
            (xy -0.762 1.016)
            (xy -0.254 0)
            (xy 0.762 0)
            (xy 1.27 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "IPWL_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "IPWL_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:ISIN" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "ISIN" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "sin(0 1 1k)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, sinusoidal" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "ISIN_0_0"
        (arc (start 0 0) (mid -0.635 0.635) (end -1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0) (mid 0.635 -0.635) (end 1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "ISIN_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "ISIN_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:ITRNOISE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "ITRNOISE" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "trnoise(20n 0.5n 0 0)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, transient noise" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "ITRNOISE_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy -1.651 1.143)
            (xy -1.651 -0.254)
            (xy -1.524 0.508)
            (xy -1.397 -1.143)
            (xy -0.889 1.016)
            (xy -0.762 -0.762)
            (xy -0.508 0.254)
            (xy -0.381 -0.508)
            (xy -0.254 0.381)
            (xy -0.127 -0.889)
            (xy 0.381 1.397)
            (xy 0.508 -1.397)
            (xy 0.635 0.762)
            (xy 1.016 -0.381)
            (xy 1.27 1.397)
            (xy 1.524 -0.508)
            (xy 1.778 0.381)
            (xy 2.032 -0.889)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "ITRNOISE_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:ITRRANDOM" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "ITRRANDOM" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "trrandom(2 10m 0 1)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, random noise" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "ITRRANDOM_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy -1.651 1.143)
            (xy -1.651 -0.254)
            (xy -1.524 0.508)
            (xy -1.397 -1.143)
            (xy -0.889 1.016)
            (xy -0.762 -0.762)
            (xy -0.508 0.254)
            (xy -0.381 -0.508)
            (xy -0.254 0.381)
            (xy -0.127 -0.889)
            (xy 0.381 1.397)
            (xy 0.508 -1.397)
            (xy 0.635 0.762)
            (xy 1.016 -0.381)
            (xy 1.27 1.397)
            (xy 1.524 -0.508)
            (xy 1.778 0.381)
            (xy 2.032 -0.889)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "ITRRANDOM_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VDC" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VDC" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "dc(1)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, DC" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VDC_0_0"
        (polyline
          (pts
            (xy -1.27 0.254)
            (xy 1.27 0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.254)
            (xy -1.27 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.254 -0.254)
            (xy -0.254 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.254)
            (xy 0.762 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VDC_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VDC_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VEXP" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VEXP" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "exp(0 1 2n 30n 60n 40n)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, exponential" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VEXP_0_0"
        (polyline
          (pts
            (xy -1.27 -0.762)
            (xy -1.778 -0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.762)
            (xy 1.778 -0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0.762) (mid -0.851 0.1794) (end -1.27 -0.762)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0.762) (mid 0.3591 -0.2299) (end 1.27 -0.762)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VEXP_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VEXP_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VPULSE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VPULSE" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "pulse(0 1 2n 2n 2n 50n 100n)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, pulse" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VPULSE_0_0"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy -1.397 -0.762)
            (xy -1.143 0.762)
            (xy -0.127 0.762)
            (xy 0.127 -0.762)
            (xy 1.143 -0.762)
            (xy 1.397 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VPULSE_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VPULSE_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VPWL" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VPWL" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "pwl(0 -1 50n -1 51n 0 97n 1 171n -1 200n -1)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, piece-wise linear" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VPWL_0_0"
        (polyline
          (pts
            (xy -1.778 -1.016)
            (xy -0.762 1.016)
            (xy -0.254 0)
            (xy 0.762 0)
            (xy 1.27 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VPWL_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VPWL_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VSIN" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VSIN" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "sin(0 1 1k)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, sinusoidal" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VSIN_0_0"
        (arc (start 0 0) (mid -0.635 0.635) (end -1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0) (mid 0.635 -0.635) (end 1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VSIN_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VSIN_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VTRNOISE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VTRNOISE" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "trnoise(20n 0.5n 0 0)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, transient noise" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VTRNOISE_0_0"
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VTRNOISE_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy -1.651 1.143)
            (xy -1.651 -0.254)
            (xy -1.524 0.508)
            (xy -1.397 -1.143)
            (xy -0.889 1.016)
            (xy -0.762 -0.762)
            (xy -0.508 0.254)
            (xy -0.381 -0.508)
            (xy -0.254 0.381)
            (xy -0.127 -0.889)
            (xy 0.381 1.397)
            (xy 0.508 -1.397)
            (xy 0.635 0.762)
            (xy 1.016 -0.381)
            (xy 1.27 1.397)
            (xy 1.524 -0.508)
            (xy 1.778 0.381)
            (xy 2.032 -0.889)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VTRNOISE_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VTRRANDOM" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VTRRANDOM" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "trrandom(2 10m 0 1)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, random noise" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VTRRANDOM_0_0"
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VTRRANDOM_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy -1.651 1.143)
            (xy -1.651 -0.254)
            (xy -1.524 0.508)
            (xy -1.397 -1.143)
            (xy -0.889 1.016)
            (xy -0.762 -0.762)
            (xy -0.508 0.254)
            (xy -0.381 -0.508)
            (xy -0.254 0.381)
            (xy -0.127 -0.889)
            (xy 0.381 1.397)
            (xy 0.508 -1.397)
            (xy 0.635 0.762)
            (xy 1.016 -0.381)
            (xy 1.27 1.397)
            (xy 1.524 -0.508)
            (xy 1.778 0.381)
            (xy 2.032 -0.889)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VTRRANDOM_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (id 0) (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (id 1) (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )


  (wire (pts (xy 215.9 25.4) (xy 266.7 25.4))
    (stroke (width 0) (type default))
    (uuid 00742899-3f52-4ff9-85d8-b90d03c212ce)
  )
  (wire (pts (xy 76.2 127) (xy 76.2 129.54))
    (stroke (width 0) (type default))
    (uuid 028c6938-0993-4248-8a82-5e0fd5ffdea0)
  )
  (wire (pts (xy 266.7 60.96) (xy 266.7 62.23))
    (stroke (width 0) (type default))
    (uuid 037fbb25-85e1-463e-b1c5-82a13bf145e8)
  )
  (wire (pts (xy 266.7 50.8) (xy 266.7 53.34))
    (stroke (width 0) (type default))
    (uuid 03d2212f-751f-4f27-aed4-55cabd38f3f1)
  )
  (wire (pts (xy 203.2 25.4) (xy 203.2 27.94))
    (stroke (width 0) (type default))
    (uuid 0475c231-8111-456b-a102-ca93825f7bc6)
  )
  (wire (pts (xy 203.2 86.36) (xy 203.2 87.63))
    (stroke (width 0) (type default))
    (uuid 0afc8607-00db-40c2-a924-679e8c438a91)
  )
  (wire (pts (xy 139.7 60.96) (xy 139.7 62.23))
    (stroke (width 0) (type default))
    (uuid 0c0fe282-fb9a-4ebe-902c-3436ae15ae7a)
  )
  (wire (pts (xy 76.2 111.76) (xy 76.2 113.03))
    (stroke (width 0) (type default))
    (uuid 109f7d05-3943-47cd-9dab-adebcd695dc0)
  )
  (wire (pts (xy 88.9 25.4) (xy 88.9 26.67))
    (stroke (width 0) (type default))
    (uuid 11105a07-f174-4045-a151-8cc05cf2cd25)
  )
  (wire (pts (xy 203.2 137.16) (xy 203.2 138.43))
    (stroke (width 0) (type default))
    (uuid 12f267be-4eab-430d-af51-0793178088dc)
  )
  (wire (pts (xy 76.2 76.2) (xy 76.2 78.74))
    (stroke (width 0) (type default))
    (uuid 137dac01-7f5c-48e9-ab94-25be00d66629)
  )
  (wire (pts (xy 139.7 162.56) (xy 139.7 163.83))
    (stroke (width 0) (type default))
    (uuid 17213057-7735-42f7-a337-5a8bfaf90c38)
  )
  (wire (pts (xy 139.7 101.6) (xy 139.7 104.14))
    (stroke (width 0) (type default))
    (uuid 18cc687e-56a5-4eac-9d6e-e1295ba57b86)
  )
  (wire (pts (xy 76.2 86.36) (xy 76.2 87.63))
    (stroke (width 0) (type default))
    (uuid 1f7438a6-e13f-4b21-84a4-35652544ae07)
  )
  (wire (pts (xy 215.9 76.2) (xy 266.7 76.2))
    (stroke (width 0) (type default))
    (uuid 27e8d0dc-5c01-4213-91e8-150e5cfc0f2f)
  )
  (wire (pts (xy 88.9 50.8) (xy 139.7 50.8))
    (stroke (width 0) (type default))
    (uuid 2b5a7b59-7f96-4ad6-8e58-530601a023cc)
  )
  (wire (pts (xy 25.4 152.4) (xy 76.2 152.4))
    (stroke (width 0) (type default))
    (uuid 2c381579-29b4-41a5-93a5-9a21f3dd5e51)
  )
  (wire (pts (xy 76.2 162.56) (xy 76.2 163.83))
    (stroke (width 0) (type default))
    (uuid 2cb30b85-c584-40d6-862d-3b318b2be359)
  )
  (wire (pts (xy 215.9 25.4) (xy 215.9 26.67))
    (stroke (width 0) (type default))
    (uuid 2dcdebb5-ac3e-4dd9-a358-65b3628206ef)
  )
  (wire (pts (xy 76.2 101.6) (xy 76.2 104.14))
    (stroke (width 0) (type default))
    (uuid 32ac05f5-b31a-455b-a8e7-989fe772a077)
  )
  (wire (pts (xy 25.4 101.6) (xy 25.4 102.87))
    (stroke (width 0) (type default))
    (uuid 358f4bfd-0e03-453a-a96e-ff2c9f4727ce)
  )
  (wire (pts (xy 76.2 177.8) (xy 76.2 180.34))
    (stroke (width 0) (type default))
    (uuid 3bd13dbc-f042-4d6f-b864-c3d3a422c981)
  )
  (wire (pts (xy 215.9 101.6) (xy 266.7 101.6))
    (stroke (width 0) (type default))
    (uuid 3c05de10-1a63-4275-bd9e-173feeca57b7)
  )
  (wire (pts (xy 139.7 25.4) (xy 139.7 27.94))
    (stroke (width 0) (type default))
    (uuid 3d0252e4-bdc5-45a1-8f15-7e16c11107b4)
  )
  (wire (pts (xy 88.9 177.8) (xy 88.9 179.07))
    (stroke (width 0) (type default))
    (uuid 3ef56260-b956-4b79-8e02-5819bd6db19c)
  )
  (wire (pts (xy 139.7 35.56) (xy 139.7 36.83))
    (stroke (width 0) (type default))
    (uuid 416b5fea-8c8e-495e-98e0-4f3a2f782b5b)
  )
  (wire (pts (xy 25.4 177.8) (xy 76.2 177.8))
    (stroke (width 0) (type default))
    (uuid 4236f994-fc70-42d8-be91-ac6323899636)
  )
  (wire (pts (xy 76.2 50.8) (xy 76.2 53.34))
    (stroke (width 0) (type default))
    (uuid 4a865f4e-cc4b-435d-9c80-1154a0b5d2e0)
  )
  (wire (pts (xy 76.2 137.16) (xy 76.2 138.43))
    (stroke (width 0) (type default))
    (uuid 53066e60-c6ea-4853-9387-d6e1e3835812)
  )
  (wire (pts (xy 88.9 152.4) (xy 88.9 153.67))
    (stroke (width 0) (type default))
    (uuid *************-42e0-bb9c-1f166b3a5282)
  )
  (wire (pts (xy 152.4 25.4) (xy 203.2 25.4))
    (stroke (width 0) (type default))
    (uuid 574650f7-f5ea-439a-b520-9d6291a8ad53)
  )
  (wire (pts (xy 152.4 25.4) (xy 152.4 26.67))
    (stroke (width 0) (type default))
    (uuid 58b27e8d-ffdf-4ed9-9964-b648303e7fcf)
  )
  (wire (pts (xy 152.4 76.2) (xy 203.2 76.2))
    (stroke (width 0) (type default))
    (uuid 5a036304-69fa-4908-8f8a-e0033686ae01)
  )
  (wire (pts (xy 215.9 101.6) (xy 215.9 102.87))
    (stroke (width 0) (type default))
    (uuid 5a9be8cf-d26a-420d-b8af-f4e84cfbd63c)
  )
  (wire (pts (xy 25.4 76.2) (xy 76.2 76.2))
    (stroke (width 0) (type default))
    (uuid 5bb1bc40-0505-4589-8bec-32f6f0a57a34)
  )
  (wire (pts (xy 203.2 50.8) (xy 203.2 53.34))
    (stroke (width 0) (type default))
    (uuid 5d5db234-af5d-4a1b-9aa0-a32fc60a440d)
  )
  (wire (pts (xy 25.4 25.4) (xy 25.4 26.67))
    (stroke (width 0) (type default))
    (uuid 5dd78378-313c-49df-9ffe-101aed3939ba)
  )
  (wire (pts (xy 88.9 76.2) (xy 88.9 77.47))
    (stroke (width 0) (type default))
    (uuid 5dfbde72-a864-4faf-b4b2-c025c488827f)
  )
  (wire (pts (xy 266.7 111.76) (xy 266.7 113.03))
    (stroke (width 0) (type default))
    (uuid 5f1569d6-fbf3-499f-a0d2-70b1617bcbab)
  )
  (wire (pts (xy 152.4 101.6) (xy 203.2 101.6))
    (stroke (width 0) (type default))
    (uuid 623cb906-51c3-4bec-af27-ddc5fb437057)
  )
  (wire (pts (xy 139.7 111.76) (xy 139.7 113.03))
    (stroke (width 0) (type default))
    (uuid *************-4fe9-a181-9198633b73ad)
  )
  (wire (pts (xy 203.2 111.76) (xy 203.2 113.03))
    (stroke (width 0) (type default))
    (uuid 674d7f58-c5e5-4812-acbe-c74c7d2a2545)
  )
  (wire (pts (xy 215.9 127) (xy 266.7 127))
    (stroke (width 0) (type default))
    (uuid 67c12ea7-e373-42db-abf8-5b2b074e8107)
  )
  (wire (pts (xy 88.9 50.8) (xy 88.9 52.07))
    (stroke (width 0) (type default))
    (uuid 6946bab2-5cf1-45ae-95d4-3a3f79f7f45d)
  )
  (wire (pts (xy 76.2 152.4) (xy 76.2 154.94))
    (stroke (width 0) (type default))
    (uuid 6b55b2fb-e01f-4efa-8875-ae9ad2314629)
  )
  (wire (pts (xy 266.7 86.36) (xy 266.7 87.63))
    (stroke (width 0) (type default))
    (uuid 7540f463-c2eb-43da-b207-8cc3cb3d3958)
  )
  (wire (pts (xy 203.2 35.56) (xy 203.2 36.83))
    (stroke (width 0) (type default))
    (uuid 772bc58c-784e-4889-ab98-8c0138c8c2f1)
  )
  (wire (pts (xy 152.4 101.6) (xy 152.4 102.87))
    (stroke (width 0) (type default))
    (uuid 77301e09-47ae-4183-ae38-c2572cf7aefe)
  )
  (wire (pts (xy 139.7 127) (xy 139.7 129.54))
    (stroke (width 0) (type default))
    (uuid 78523f20-0199-4c86-a041-b3839c4b73b1)
  )
  (wire (pts (xy 139.7 177.8) (xy 139.7 180.34))
    (stroke (width 0) (type default))
    (uuid 79e1f3c2-7d07-40e1-9d9e-b9681b6cb90a)
  )
  (wire (pts (xy 139.7 137.16) (xy 139.7 138.43))
    (stroke (width 0) (type default))
    (uuid 7f8891cc-7b04-4d26-abf8-261debea0461)
  )
  (wire (pts (xy 152.4 127) (xy 203.2 127))
    (stroke (width 0) (type default))
    (uuid 8ab623a5-6fc1-4a3a-9099-dfecc94d0a84)
  )
  (wire (pts (xy 88.9 177.8) (xy 139.7 177.8))
    (stroke (width 0) (type default))
    (uuid 8b0e9674-f4a5-4c51-b514-f619f60479f5)
  )
  (wire (pts (xy 25.4 127) (xy 25.4 128.27))
    (stroke (width 0) (type default))
    (uuid 8c53f180-b466-463f-857d-432d7c7ac137)
  )
  (wire (pts (xy 139.7 187.96) (xy 139.7 189.23))
    (stroke (width 0) (type default))
    (uuid 8cfde3f4-9de3-4878-ad44-cc37e6ca0bf1)
  )
  (wire (pts (xy 266.7 35.56) (xy 266.7 36.83))
    (stroke (width 0) (type default))
    (uuid 91dd36fc-f27a-4f9d-aea9-2bf7fa8ffcef)
  )
  (wire (pts (xy 266.7 127) (xy 266.7 129.54))
    (stroke (width 0) (type default))
    (uuid 98a75ef7-ea57-4cb1-a966-b2d88e5aed30)
  )
  (wire (pts (xy 152.4 50.8) (xy 203.2 50.8))
    (stroke (width 0) (type default))
    (uuid 9d31ad7e-317b-4291-b3a4-66bc5fbf751e)
  )
  (wire (pts (xy 139.7 86.36) (xy 139.7 87.63))
    (stroke (width 0) (type default))
    (uuid 9e232d1a-9f1a-4194-a902-5ed9a2d30780)
  )
  (wire (pts (xy 76.2 25.4) (xy 76.2 27.94))
    (stroke (width 0) (type default))
    (uuid 9e8ff586-132f-45a7-9bc6-ad6a124a1656)
  )
  (wire (pts (xy 215.9 50.8) (xy 215.9 52.07))
    (stroke (width 0) (type default))
    (uuid a3d0d180-431d-4740-ae99-be65a792b51e)
  )
  (wire (pts (xy 203.2 76.2) (xy 203.2 78.74))
    (stroke (width 0) (type default))
    (uuid a53eabbf-31c5-4c86-977c-db025162fc8a)
  )
  (wire (pts (xy 152.4 50.8) (xy 152.4 52.07))
    (stroke (width 0) (type default))
    (uuid a59a674d-781b-450e-a8fe-a74db2950277)
  )
  (wire (pts (xy 88.9 25.4) (xy 139.7 25.4))
    (stroke (width 0) (type default))
    (uuid aa9cad90-fd91-46a1-b9e4-6f387c090a56)
  )
  (wire (pts (xy 215.9 76.2) (xy 215.9 77.47))
    (stroke (width 0) (type default))
    (uuid abadeb9c-319c-48b3-91f2-a4c06eb42284)
  )
  (wire (pts (xy 88.9 127) (xy 139.7 127))
    (stroke (width 0) (type default))
    (uuid b4066e56-9b13-4116-8f56-56c8e5d60ef0)
  )
  (wire (pts (xy 25.4 177.8) (xy 25.4 179.07))
    (stroke (width 0) (type default))
    (uuid b591322f-6720-4bdc-9717-44fe5690d761)
  )
  (wire (pts (xy 139.7 50.8) (xy 139.7 53.34))
    (stroke (width 0) (type default))
    (uuid b5ed141e-4a6e-480b-9274-7051b072927f)
  )
  (wire (pts (xy 88.9 101.6) (xy 139.7 101.6))
    (stroke (width 0) (type default))
    (uuid b63b5678-51c3-4350-acf6-fff0c1c1e0cb)
  )
  (wire (pts (xy 266.7 137.16) (xy 266.7 138.43))
    (stroke (width 0) (type default))
    (uuid bb07c7af-3ac7-4ff0-b698-9a27f90d1ef9)
  )
  (wire (pts (xy 152.4 127) (xy 152.4 128.27))
    (stroke (width 0) (type default))
    (uuid bbda5802-755b-444d-bb62-cced0a30c11d)
  )
  (wire (pts (xy 25.4 101.6) (xy 76.2 101.6))
    (stroke (width 0) (type default))
    (uuid bbec9f00-b0b6-4e61-8b02-b295dc39960e)
  )
  (wire (pts (xy 25.4 50.8) (xy 25.4 52.07))
    (stroke (width 0) (type default))
    (uuid bde1f230-21c5-4c07-a4cd-6ab13675d880)
  )
  (wire (pts (xy 76.2 60.96) (xy 76.2 62.23))
    (stroke (width 0) (type default))
    (uuid c182574e-a626-4718-bc2d-731bdc7fa75d)
  )
  (wire (pts (xy 139.7 152.4) (xy 139.7 154.94))
    (stroke (width 0) (type default))
    (uuid c6a9ff27-0772-4b61-ab9b-74385a83a617)
  )
  (wire (pts (xy 152.4 76.2) (xy 152.4 77.47))
    (stroke (width 0) (type default))
    (uuid c8bd7d63-ca3e-4694-9a03-353fa38c26e2)
  )
  (wire (pts (xy 215.9 50.8) (xy 266.7 50.8))
    (stroke (width 0) (type default))
    (uuid c9e11afb-5bba-4369-8bcd-8777a921d83f)
  )
  (wire (pts (xy 266.7 25.4) (xy 266.7 27.94))
    (stroke (width 0) (type default))
    (uuid cb018f11-ff48-4307-b84d-5623cdb562d4)
  )
  (wire (pts (xy 88.9 152.4) (xy 139.7 152.4))
    (stroke (width 0) (type default))
    (uuid cbc6a5c3-c1fa-481f-ac29-3a22c334b116)
  )
  (wire (pts (xy 76.2 187.96) (xy 76.2 189.23))
    (stroke (width 0) (type default))
    (uuid cdefa687-ae5c-47bd-aefe-716a5c3e5127)
  )
  (wire (pts (xy 76.2 35.56) (xy 76.2 36.83))
    (stroke (width 0) (type default))
    (uuid cebfa657-110a-4ffc-9cd3-3c17258ad0fb)
  )
  (wire (pts (xy 25.4 25.4) (xy 76.2 25.4))
    (stroke (width 0) (type default))
    (uuid d247f77f-93a8-4d45-9721-62da982b1c6b)
  )
  (wire (pts (xy 203.2 60.96) (xy 203.2 62.23))
    (stroke (width 0) (type default))
    (uuid d24e4c76-ddb3-4ad3-ba00-************)
  )
  (wire (pts (xy 88.9 101.6) (xy 88.9 102.87))
    (stroke (width 0) (type default))
    (uuid d5654d44-46f0-473a-87dd-1074c856de36)
  )
  (wire (pts (xy 25.4 127) (xy 76.2 127))
    (stroke (width 0) (type default))
    (uuid d77ff81f-6059-4570-91e0-b812e168b3dc)
  )
  (wire (pts (xy 203.2 127) (xy 203.2 129.54))
    (stroke (width 0) (type default))
    (uuid d87d57ea-014d-4d4f-af20-5481351da245)
  )
  (wire (pts (xy 266.7 101.6) (xy 266.7 104.14))
    (stroke (width 0) (type default))
    (uuid d9d05e57-2b43-49c6-be87-a65111701cc4)
  )
  (wire (pts (xy 266.7 76.2) (xy 266.7 78.74))
    (stroke (width 0) (type default))
    (uuid de6511a8-1c88-41b5-af40-92b81c2fdc6d)
  )
  (wire (pts (xy 25.4 76.2) (xy 25.4 77.47))
    (stroke (width 0) (type default))
    (uuid e01910ef-df16-4108-ab06-25db9e6c027e)
  )
  (wire (pts (xy 88.9 76.2) (xy 139.7 76.2))
    (stroke (width 0) (type default))
    (uuid e48cd23a-b439-42ce-94ec-2dc2e52aa101)
  )
  (wire (pts (xy 25.4 152.4) (xy 25.4 153.67))
    (stroke (width 0) (type default))
    (uuid e4fa4def-8d40-42ff-801c-214650342e0c)
  )
  (wire (pts (xy 203.2 101.6) (xy 203.2 104.14))
    (stroke (width 0) (type default))
    (uuid e5036da8-64ef-4059-a874-3a960eae6487)
  )
  (wire (pts (xy 139.7 76.2) (xy 139.7 78.74))
    (stroke (width 0) (type default))
    (uuid e52ee726-121e-421a-9c76-b1d8e06ff5a2)
  )
  (wire (pts (xy 88.9 127) (xy 88.9 128.27))
    (stroke (width 0) (type default))
    (uuid e99f2ba5-ff6a-438e-8ce0-4bcba07539e0)
  )
  (wire (pts (xy 25.4 50.8) (xy 76.2 50.8))
    (stroke (width 0) (type default))
    (uuid ea8bcbf5-5c2c-44e7-98cd-0fced2e4e345)
  )
  (wire (pts (xy 215.9 127) (xy 215.9 128.27))
    (stroke (width 0) (type default))
    (uuid ef4d1a41-09c1-4969-99b0-6a7f9f87132e)
  )

  (text ".tran 1u 10m" (at 25.4 22.86 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 99c65ee3-46e2-4631-a54a-fdafd7741ca3)
  )

  (label "vranduniform" (at 203.2 50.8 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 22cefc16-9948-4da2-9674-98d195884b37)
  )
  (label "irandnormal" (at 266.7 76.2 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 23c686c4-1427-432c-8373-d3780ecf0639)
  )
  (label "irandexp" (at 266.7 101.6 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 25c8c38d-88c1-43f5-9cbe-6c3a7bd79db2)
  )
  (label "iranduniform" (at 266.7 50.8 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 27690235-b428-42bc-be9b-096c8cb9033f)
  )
  (label "vpinknoise" (at 76.2 177.8 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 3a1ec529-92fe-44bd-8fc2-b47ac82fed9d)
  )
  (label "vbehavioral" (at 203.2 127 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 3b481130-5ad5-4dce-a65c-ec1904478a5c)
  )
  (label "vpulse" (at 76.2 76.2 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 4b3da236-9b86-48b4-9e32-3905fa28393c)
  )
  (label "iburstnoise" (at 266.7 25.4 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 664b3144-3531-4c54-bf79-8c9a5b953d74)
  )
  (label "vpwl" (at 76.2 127 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 6e67f042-5391-4f93-a0ac-c9a67bae1ee4)
  )
  (label "vwhitenoise" (at 76.2 152.4 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 70d02665-adeb-44e5-b41d-415cf1dcaf3a)
  )
  (label "ibehavioral" (at 266.7 127 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid *************-4430-94b6-f827b5501645)
  )
  (label "ipwl" (at 139.7 127 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 84a14c1b-55a3-4941-add4-29fbc5f426d1)
  )
  (label "idc" (at 139.7 25.4 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 898647bd-73ef-4ce8-9eab-09a6c965b547)
  )
  (label "vsin" (at 76.2 50.8 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 92137c72-7f80-44b0-8d19-43cb696dd8fa)
  )
  (label "vdc" (at 76.2 25.4 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 98ecaf90-fc45-41d5-b36b-d82aaf09bcd3)
  )
  (label "isin" (at 139.7 50.8 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid aedaa769-92d3-4274-a981-44f75ffa8a37)
  )
  (label "vburstnoise" (at 203.2 25.4 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid b15d54be-465d-463a-91bf-02e59755ccd2)
  )
  (label "iwhitenoise" (at 139.7 152.4 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid baf80d00-1778-4cf3-bf51-fe5cd5335e38)
  )
  (label "vrandexp" (at 203.2 101.6 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid bb2ba1c2-f14b-41dc-922b-e8f2100f259b)
  )
  (label "vexp" (at 76.2 101.6 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid c2774982-e14c-4681-bf81-4ba6356b0ba2)
  )
  (label "ipulse" (at 139.7 76.2 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid c9df2ee9-4819-4dad-9425-11720bbaed0d)
  )
  (label "ipinknoise" (at 139.7 177.8 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid e1d048f7-9e7a-450b-aeb8-8cd90c1efc01)
  )
  (label "iexp" (at 139.7 101.6 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid eab562b0-70d1-49fd-9aad-27327030ee89)
  )
  (label "vrandnormal" (at 203.2 76.2 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid fa32a2f8-421d-4489-ad13-001d7b588861)
  )

  (symbol (lib_id "Device:R") (at 76.2 184.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 05a0c313-785e-4dbd-a2f1-9f25d0b512a6)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 183.515 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 186.055 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 184.15 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 184.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 01cf95fe-4a95-4e30-b4d0-cf1105efef4c))
    (pin "2" (uuid 76471887-272b-4a56-9dad-47348dfc9205))
  )

  (symbol (lib_id "power:GND") (at 76.2 189.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 0bc516f1-8d59-49e1-94af-1387cb0f6d44)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 195.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 194.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 189.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 189.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 615ca0eb-e702-41f9-8ffe-dc8bd038b231))
  )

  (symbol (lib_id "Simulation_SPICE:VTRNOISE") (at 25.4 158.75 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 1035e4d2-6278-482b-8c00-dddd61b84994)
    (default_instance (reference "V") (unit 1) (value "VTRNOISE") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 157.48 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VTRNOISE" (id 1) (at 29.21 160.02 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 158.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 158.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 905167e5-c9b0-41b8-807d-b2b52ca574e0))
    (pin "2" (uuid 66e7ddcf-c67d-4663-9c21-0e2b018960ed))
  )

  (symbol (lib_id "power:GND") (at 76.2 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 127c6869-e53c-4a8f-bb84-075e336124e9)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 865dbd5b-2462-4259-87fa-07e8d57338bb))
  )

  (symbol (lib_id "Simulation_SPICE:VSIN") (at 25.4 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 162dd4fd-6b0c-4c88-9d11-dfd2cf5af61f)
    (default_instance (reference "V") (unit 1) (value "VSIN") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 55.245 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VSIN" (id 1) (at 29.21 57.785 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b0709f45-82fa-4eb5-81f8-914f50317636))
    (pin "2" (uuid c3453967-d074-4cc2-9a43-eec70062d515))
  )

  (symbol (lib_id "Device:R") (at 76.2 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 183b6ba4-3657-4b16-8996-abd81eff6890)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 56.515 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 59.055 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 57.15 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d635c9ad-9314-4ae4-98e0-c89730125f77))
    (pin "2" (uuid 3b78e370-b246-4148-9537-daea197778e0))
  )

  (symbol (lib_id "Device:R") (at 266.7 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 1e865d52-3e9d-40e2-94ba-ad8d2b33aca4)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 269.24 81.915 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 269.24 84.455 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 264.922 82.55 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 266.7 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8ba3f244-a18b-46ec-a841-542e225aa78e))
    (pin "2" (uuid b985107e-c08f-44a0-aef8-af7b5efbb0e8))
  )

  (symbol (lib_id "power:GND") (at 139.7 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 2b8ae715-294b-4d1b-8930-56ebe519569d)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b1b8ab09-0ad4-4779-a7d9-538e04564c83))
  )

  (symbol (lib_id "Device:R") (at 139.7 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 2cb952de-7eb1-4747-ae80-3e8dcefdf7b8)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 56.515 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 59.055 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 57.15 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 307b8e9b-66c1-4fbc-a363-807fee4c3c11))
    (pin "2" (uuid 1f637481-6f82-411d-8891-2624843f64d4))
  )

  (symbol (lib_id "power:GND") (at 139.7 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 2d43b4ca-185e-47a6-a2b2-d3e67a2155a7)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3375af0c-ff20-484e-bf63-159d22e8dae1))
  )

  (symbol (lib_id "Device:R") (at 76.2 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 2d99cf32-c26c-4a2f-8c01-427fabe7f976)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 107.95 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b98b4ded-3960-41d0-8d6d-8cf44e45a9d9))
    (pin "2" (uuid 36b661ef-1901-4914-a6e6-c917fa7fd27b))
  )

  (symbol (lib_id "power:GND") (at 266.7 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 2fcd38a4-3c32-447d-bfd2-a9b96c46f709)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 266.7 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 266.7 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 266.7 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 266.7 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 78a21540-bf8d-4e90-abd1-6a7abaf3617a))
  )

  (symbol (lib_id "power:GND") (at 25.4 163.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 348ed3a9-fff3-4488-b8ea-73412129f38e)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 170.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 168.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 163.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 163.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 0c05e1a0-2b4f-4276-b855-f0c8b82a600b))
  )

  (symbol (lib_id "power:GND") (at 215.9 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 3598a99a-ad64-4c4e-889d-00f61c21a9e4)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 215.9 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 215.9 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 215.9 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 215.9 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid a74d34da-5cdf-4241-a734-5d103dff47de))
  )

  (symbol (lib_id "Device:R") (at 139.7 184.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 3893752b-7abf-4822-8759-2794fab1e696)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 183.515 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 186.055 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 184.15 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 184.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 07a5f85b-afb7-49b6-8cda-190c9168a5e9))
    (pin "2" (uuid 9bb03e4f-4cee-4c37-894d-13d56a02b546))
  )

  (symbol (lib_id "Device:R") (at 139.7 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 3926526d-6bdf-4df1-b6ca-e1ed30b5305b)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 81.915 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 84.455 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 82.55 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 11572ec7-121b-4b0e-b3fb-a3d720d62082))
    (pin "2" (uuid 62e9cf51-dda3-431e-ade4-d0ba172eb6d0))
  )

  (symbol (lib_id "power:GND") (at 76.2 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 3b6aa53c-9a95-4c84-90db-32d8028b844d)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f26363ad-a99e-4f84-904b-e4a709ae8f71))
  )

  (symbol (lib_id "Simulation_SPICE:ITRNOISE") (at 88.9 184.15 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 3bacb257-993d-44eb-922e-40ef9a788e8f)
    (default_instance (reference "I") (unit 1) (value "ITRNOISE") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 182.88 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ITRNOISE" (id 1) (at 92.71 185.42 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 184.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 184.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8f2757a0-3260-4161-b18e-37986888d24c))
    (pin "2" (uuid a97fe31f-f247-42f1-b666-1ee6fd5ef054))
  )

  (symbol (lib_id "Device:R") (at 203.2 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 41274798-127c-45d0-983c-e20ccad3c45f)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 205.74 81.915 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 205.74 84.455 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 201.422 82.55 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 203.2 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 9cd98034-39e5-45f2-b171-9e6ff2152fd2))
    (pin "2" (uuid 97a1e5e7-cce1-400a-b01e-9512f2aab35a))
  )

  (symbol (lib_id "Simulation_SPICE:ITRNOISE") (at 215.9 31.75 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 44f3bfec-18ca-4a10-b9dd-7b481a410f0f)
    (default_instance (reference "I") (unit 1) (value "ITRNOISE") (footprint ""))
    (property "Reference" "I" (id 0) (at 219.71 30.48 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ITRNOISE" (id 1) (at 219.71 33.02 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 215.9 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 215.9 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3afb506c-ad4a-46a4-9594-8a5e87c50584))
    (pin "2" (uuid a60edc1a-15b9-44aa-9901-b923f081b4c6))
  )

  (symbol (lib_id "power:GND") (at 152.4 138.43 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 44fb8663-f6c5-473f-948a-f45b78532a12)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 152.4 144.78 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 152.4 143.51 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 152.4 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 152.4 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid cce70818-1d41-46c1-9d7d-cfd39075e7dc))
  )

  (symbol (lib_id "Simulation_SPICE:ISIN") (at 88.9 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 4a11c031-0cff-43e4-b00d-ffd7533c6b4c)
    (default_instance (reference "I") (unit 1) (value "ISIN") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 55.245 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ISIN" (id 1) (at 92.71 57.785 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 69ef791b-1979-4fde-b83c-471cb92caf57))
    (pin "2" (uuid fadc8087-627b-48e1-9cd9-************))
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 25.4 31.75 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 4a13630f-aae8-4aa8-b67e-b09bc4077213)
    (default_instance (reference "V") (unit 1) (value "VDC") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 29.845 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VDC" (id 1) (at 29.21 32.385 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 36b46520-4f81-40de-8d75-aad344d9d805))
    (pin "2" (uuid e70914e9-dede-41bf-a8a0-b9315978f18a))
  )

  (symbol (lib_id "power:GND") (at 139.7 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 4b3bd156-df27-4707-b782-364ea4daec71)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 121b9b69-6264-4e78-8876-42871e0b0fd5))
  )

  (symbol (lib_id "power:GND") (at 215.9 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 4b51dcec-ead2-4989-941a-aa26fef66dab)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 215.9 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 215.9 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 215.9 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 215.9 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 114b8e62-ef24-4372-b67a-9a34dfa557b4))
  )

  (symbol (lib_id "Device:R") (at 266.7 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 4cb970f3-fd99-4bf3-a224-e7637478343a)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 269.24 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 269.24 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 264.922 107.95 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 266.7 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 9ccb5e98-8916-400b-b5c3-bbd0fbd77ce4))
    (pin "2" (uuid 43478fc9-c35e-494d-943e-2fd87cdccec3))
  )

  (symbol (lib_id "power:GND") (at 266.7 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 4f019669-1c87-467d-a93e-9b3c43806963)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 266.7 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 266.7 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 266.7 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 266.7 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 40ba5498-1033-4bd5-9c39-ebfc16fc024c))
  )

  (symbol (lib_id "Simulation_SPICE:ITRRANDOM") (at 215.9 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 511ecaa8-fa3b-4ee5-b019-f024f7990a37)
    (default_instance (reference "I") (unit 1) (value "ITRRANDOM") (footprint ""))
    (property "Reference" "I" (id 0) (at 219.71 80.645 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ITRRANDOM" (id 1) (at 219.71 83.185 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 215.9 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 215.9 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 572cb35a-b840-4fe9-b48b-4d027396c51e))
    (pin "2" (uuid d91d6db4-f5da-46cf-8d0c-533e0ff6c291))
  )

  (symbol (lib_id "Simulation_SPICE:VTRNOISE") (at 152.4 31.75 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 529ade44-fd15-4861-9282-7bc8f7fda7d3)
    (default_instance (reference "V") (unit 1) (value "VTRNOISE") (footprint ""))
    (property "Reference" "V" (id 0) (at 156.21 30.48 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VTRNOISE" (id 1) (at 156.21 33.02 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 152.4 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 152.4 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b2ff7a26-83b0-455c-a6f3-9886d386a2eb))
    (pin "2" (uuid 7481821f-3791-4fb2-bb97-8fbfbbd9a17a))
  )

  (symbol (lib_id "Simulation_SPICE:IPULSE") (at 88.9 82.55 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 530cae72-e62d-4472-a544-cf6e7504a547)
    (default_instance (reference "I") (unit 1) (value "IPULSE") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 80.645 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IPULSE" (id 1) (at 92.71 83.185 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5f53eb4d-b42a-4ffd-be9d-b89c771d7a54))
    (pin "2" (uuid 90f78bc7-5d64-40c7-bde9-6ee2bed32215))
  )

  (symbol (lib_id "power:GND") (at 203.2 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 53936774-684c-428f-a1b2-bbeabec5a433)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 203.2 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 203.2 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 203.2 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 203.2 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid cb3fe52e-e6ac-43c4-aec5-3bcbdebb454e))
  )

  (symbol (lib_id "Simulation_SPICE:ITRNOISE") (at 88.9 158.75 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 557deca4-e3af-4631-9c7b-dce1d008f719)
    (default_instance (reference "I") (unit 1) (value "ITRNOISE") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 157.48 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ITRNOISE" (id 1) (at 92.71 160.02 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 158.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 158.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7addaf24-7897-4fde-8201-d045a408b3ef))
    (pin "2" (uuid 069114fe-6042-4c34-8bbb-44d4a45e24dc))
  )

  (symbol (lib_id "Device:R") (at 76.2 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 59c377ca-586c-4d47-8389-118b72e33ab9)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 81.915 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 84.455 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 82.55 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c39116b0-12c9-4d05-b5dd-6144ac4623b3))
    (pin "2" (uuid ba346ec9-ab94-493e-808c-90e051dc3412))
  )

  (symbol (lib_id "Simulation_SPICE:IEXP") (at 88.9 107.95 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 5b2fd2dc-d066-4c39-8bc9-ea8e365975ae)
    (default_instance (reference "I") (unit 1) (value "IEXP") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 106.68 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IEXP" (id 1) (at 92.71 109.22 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5355afb3-2ba4-4480-afbe-0ce0324c0e77))
    (pin "2" (uuid 7ff6cc4d-cdb3-4fb1-bd2d-a3ac7bcaab9e))
  )

  (symbol (lib_id "Device:R") (at 139.7 31.75 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 5bd5c24b-684f-4add-8e04-b731deb77ff7)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 31.115 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 33.655 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 31.75 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 4441eec1-1bdd-4f51-bbfa-ffc16e5eb05b))
    (pin "2" (uuid 55c6bd68-8d14-42b5-a0b9-a3cdda47a276))
  )

  (symbol (lib_id "power:GND") (at 203.2 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 5be794f9-d628-4d3a-87a9-bf3b0640c0ac)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 203.2 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 203.2 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 203.2 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 203.2 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f5e38de0-fc85-41cc-8eff-ec62d1cbd5fc))
  )

  (symbol (lib_id "Device:R") (at 203.2 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 5f543059-cd5d-4bd8-ad91-961bacc1314e)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 205.74 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 205.74 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 201.422 107.95 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 203.2 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 0d697f66-865b-47fb-98d0-031996d10395))
    (pin "2" (uuid 8060e32c-1b2b-49c6-9ec5-f4df63f60d7b))
  )

  (symbol (lib_id "power:GND") (at 215.9 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 6682ceb3-f8c3-4259-9339-b08b6dcdb6f4)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 215.9 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 215.9 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 215.9 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 215.9 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 9958ed3a-e764-41b6-a710-0e8f33d750ee))
  )

  (symbol (lib_id "power:GND") (at 152.4 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 68292dcf-c018-4d28-8f6e-620399df8b3d)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 152.4 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 152.4 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 152.4 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 152.4 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3a056721-6fbf-453a-a64d-ab455cf1402f))
  )

  (symbol (lib_id "Simulation_SPICE:VTRRANDOM") (at 152.4 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 6922b540-e03b-45e6-ab60-de3e42567059)
    (default_instance (reference "V") (unit 1) (value "VTRRANDOM") (footprint ""))
    (property "Reference" "V" (id 0) (at 156.21 80.645 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VTRRANDOM" (id 1) (at 156.21 83.185 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 152.4 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 152.4 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 492db0e7-8823-4c93-80f1-7eaebe04562f))
    (pin "2" (uuid 4469b09f-fff1-4d90-a02b-e9e178064826))
  )

  (symbol (lib_id "Simulation_SPICE:ITRRANDOM") (at 215.9 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 6ddd77dc-f1ba-4e98-b8c2-81b7e98b9786)
    (default_instance (reference "I") (unit 1) (value "ITRRANDOM") (footprint ""))
    (property "Reference" "I" (id 0) (at 219.71 56.515 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ITRRANDOM" (id 1) (at 219.71 59.055 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 215.9 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 215.9 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 687833f3-8e2b-440d-9527-04523867b8fd))
    (pin "2" (uuid f1bd7bfd-1ccd-4886-aeab-************))
  )

  (symbol (lib_id "power:GND") (at 266.7 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 708debee-879a-4cae-b7c9-3da17d548e5f)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 266.7 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 266.7 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 266.7 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 266.7 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 0a46fd56-7405-4e90-a9e0-8d42b4ea7c56))
  )

  (symbol (lib_id "Device:R") (at 203.2 31.75 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 71a83d3a-3786-43b1-8f38-bb2040b16368)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 205.74 31.115 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 205.74 33.655 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 201.422 31.75 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 203.2 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid ad6ba916-8d6b-478b-a9ef-9db84d784742))
    (pin "2" (uuid af268a8a-ea27-47a3-8d16-9d8c538f1855))
  )

  (symbol (lib_id "power:GND") (at 76.2 138.43 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 72571608-d69f-4ba1-82c8-************)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 144.78 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 143.51 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8986cf06-79cb-4335-8440-a6d98cb2cb3d))
  )

  (symbol (lib_id "power:GND") (at 88.9 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 74654e3a-e2bb-42f4-9b7b-9d294fd6386e)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 89be6608-7ebd-413e-84ce-e99c0f3f7036))
  )

  (symbol (lib_id "power:GND") (at 25.4 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 78e47da6-e06f-44cd-a293-3d24fc58787d)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid dcb87709-7689-4d8f-a1b2-7f1246aac0bd))
  )

  (symbol (lib_id "power:GND") (at 152.4 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 7c26ed1a-02c1-4dd4-b749-990844bf2c56)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 152.4 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 152.4 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 152.4 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 152.4 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1f14ff55-b63b-4c1a-a92b-b5dddfa0aa9c))
  )

  (symbol (lib_id "Device:R") (at 266.7 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 7d09a479-b0c2-46ea-b193-2f7efcdcf1f1)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 269.24 132.715 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 269.24 135.255 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 264.922 133.35 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 266.7 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f6936b85-57a2-4bd4-9547-6c43a4eacbb3))
    (pin "2" (uuid d28835ed-d792-424f-a49f-dabf952f6843))
  )

  (symbol (lib_id "power:GND") (at 152.4 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 7e8f760e-e465-4013-ab3f-f482db08eec4)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 152.4 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 152.4 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 152.4 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 152.4 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b48204c8-ad7f-4043-9009-c81ba4bc05f4))
  )

  (symbol (lib_id "power:GND") (at 203.2 138.43 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 836c6d73-036d-41fb-8d46-b78aaa7fecd0)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 203.2 144.78 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 203.2 143.51 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 203.2 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 203.2 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 93a74ccb-e79b-4e32-9695-ec701c1e8c75))
  )

  (symbol (lib_id "Device:R") (at 266.7 31.75 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 85462b10-caca-41b0-85e4-3e5c5b0fc436)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 269.24 31.115 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 269.24 33.655 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 264.922 31.75 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 266.7 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid dc8a711d-f645-4c0c-bcc9-8f19d50f01ba))
    (pin "2" (uuid f9586fe0-9658-430f-9c69-68647125e6a6))
  )

  (symbol (lib_id "Simulation_SPICE:ITRRANDOM") (at 215.9 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 8bb473bb-2d27-406f-b1e3-fd97fb279901)
    (default_instance (reference "I") (unit 1) (value "ITRRANDOM") (footprint ""))
    (property "Reference" "I" (id 0) (at 219.71 131.445 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ITRRANDOM" (id 1) (at 219.71 133.985 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 215.9 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 215.9 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid fd63a805-012c-4b57-8a67-20fd0952c762))
    (pin "2" (uuid 6398566e-924a-4ab1-a7cf-c7bbece3d855))
  )

  (symbol (lib_id "power:GND") (at 88.9 189.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 8ce6a30e-bf10-426e-98c2-957bc9e06bc0)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 195.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 194.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 189.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 189.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3f278695-48a3-4190-8861-5752ffbf1c66))
  )

  (symbol (lib_id "power:GND") (at 25.4 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 8ead6375-404c-4c81-ad7a-987ea2c4d03d)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 78bad74c-e5c3-41da-a618-c1f8a6af808f))
  )

  (symbol (lib_id "Simulation_SPICE:VTRRANDOM") (at 152.4 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 955c3193-72c9-433e-bd84-a687bb47b0b7)
    (default_instance (reference "V") (unit 1) (value "VTRRANDOM") (footprint ""))
    (property "Reference" "V" (id 0) (at 156.21 106.045 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VTRRANDOM" (id 1) (at 156.21 108.585 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 152.4 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 152.4 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 39628273-cd1b-4d16-a555-dc291950d13e))
    (pin "2" (uuid 39fd9125-9666-4976-abb0-cdd9e4418bc0))
  )

  (symbol (lib_id "power:GND") (at 88.9 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 96b63841-de75-42e3-b7ed-d987ad24c00f)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1d892116-42a5-459e-9aa7-8cde0a6d5915))
  )

  (symbol (lib_id "power:GND") (at 266.7 138.43 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 99492963-560a-4b83-875e-743583f7c046)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 266.7 144.78 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 266.7 143.51 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 266.7 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 266.7 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 135797eb-d997-49ea-ab5a-fbdf7a7e1362))
  )

  (symbol (lib_id "Device:R") (at 266.7 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 9a598ad2-f5a0-4e37-ad0c-1012374e5f4b)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 269.24 56.515 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 269.24 59.055 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 264.922 57.15 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 266.7 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 71e63378-aa3c-4ffd-8ffb-cfbf6267eb81))
    (pin "2" (uuid 35c10198-86dd-4370-ab23-437e0390363d))
  )

  (symbol (lib_id "Simulation_SPICE:VTRRANDOM") (at 152.4 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 9b2776eb-8216-4cef-b1f8-57196fbebbcc)
    (default_instance (reference "V") (unit 1) (value "VTRRANDOM") (footprint ""))
    (property "Reference" "V" (id 0) (at 156.21 131.445 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VTRRANDOM" (id 1) (at 156.21 133.985 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 152.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 152.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 448bafb0-8da7-412e-a2a1-47933b5c20cd))
    (pin "2" (uuid 4857f530-353c-4435-afe9-7fc2861bdd04))
  )

  (symbol (lib_id "power:GND") (at 76.2 163.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid a1044b70-5210-4a3d-9d01-c93ae164f98a)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 170.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 168.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 163.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 163.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f7b70360-c0a4-4554-b408-************))
  )

  (symbol (lib_id "power:GND") (at 266.7 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid a209f5ba-de46-4a99-9b8a-0b088fbf53b5)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 266.7 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 266.7 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 266.7 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 266.7 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 087b6ef2-b62c-44dd-a6a7-c965ece0db41))
  )

  (symbol (lib_id "power:GND") (at 76.2 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid a4b6db34-c852-47c5-8cdd-e8801ad1c485)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 9d260b42-ad17-4f2e-9889-d6868bcd889c))
  )

  (symbol (lib_id "power:GND") (at 76.2 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid a54fb244-ba0f-4464-aebd-33800b8b27be)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid bc28fe56-f534-4ed1-ae33-a2388a98648c))
  )

  (symbol (lib_id "power:GND") (at 88.9 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid a8a8d31d-dc05-4c9f-8748-a64d133cadb1)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7dd7c82a-e14a-41df-91f8-41fd31e10f32))
  )

  (symbol (lib_id "Device:R") (at 203.2 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid ab830d0e-1b87-4954-ae8f-a4e2dda7cde7)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 205.74 56.515 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 205.74 59.055 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 201.422 57.15 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 203.2 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 24aef753-0306-4869-9fe2-0e2f6fc55f21))
    (pin "2" (uuid baf5d66a-875c-4717-b8b8-862d989ba520))
  )

  (symbol (lib_id "Simulation_SPICE:IPWL") (at 88.9 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid aeb4fd99-50d3-4ee5-82f6-2e8e513f2af7)
    (default_instance (reference "I") (unit 1) (value "IPWL") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 132.715 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IPWL" (id 1) (at 92.71 135.255 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8331e90e-f5dd-43b1-b50a-a16ca7e1cb9e))
    (pin "2" (uuid 4a3c7b4a-dc29-42f9-9348-c7a447f0950f))
  )

  (symbol (lib_id "power:GND") (at 215.9 138.43 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid af2255d6-0d33-42f3-844f-679a8f629490)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 215.9 144.78 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 215.9 143.51 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 215.9 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 215.9 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5a833f6b-f308-45de-adb3-e1b2aefa5c3d))
  )

  (symbol (lib_id "Device:R") (at 139.7 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid afae866c-ee68-4958-bbec-3bf2e19ce1ef)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 132.715 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 135.255 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 133.35 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 70448ed3-970f-41cd-9282-76422bdb184d))
    (pin "2" (uuid 6a04489d-270a-461c-9393-7d91ca571132))
  )

  (symbol (lib_id "Simulation_SPICE:VTRRANDOM") (at 152.4 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid b1698d13-4248-4da9-8a8e-dbd2bfbf5ca4)
    (default_instance (reference "V") (unit 1) (value "VTRRANDOM") (footprint ""))
    (property "Reference" "V" (id 0) (at 156.21 55.245 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VTRRANDOM" (id 1) (at 156.21 57.785 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 152.4 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 152.4 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b1f16942-b177-4fec-962d-5225d6a145c4))
    (pin "2" (uuid ec088e0f-4b1a-4e3f-8dee-3fb776aa87c6))
  )

  (symbol (lib_id "Simulation_SPICE:VEXP") (at 25.4 107.95 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid b2411a13-3e8d-4353-8659-2d857db7af3d)
    (default_instance (reference "V") (unit 1) (value "VEXP") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 106.68 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VEXP" (id 1) (at 29.21 109.22 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid cfc0a1a4-06fe-4baa-bc18-76bc2ac42867))
    (pin "2" (uuid 55540642-5d12-42a0-985d-8b664e394ff4))
  )

  (symbol (lib_id "Simulation_SPICE:ITRRANDOM") (at 215.9 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid b28bf5f4-50a4-4b7b-a795-258142b8b285)
    (default_instance (reference "I") (unit 1) (value "ITRRANDOM") (footprint ""))
    (property "Reference" "I" (id 0) (at 219.71 106.045 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ITRRANDOM" (id 1) (at 219.71 108.585 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 215.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 215.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid db898c01-24b2-4658-8bd9-7375ca2ff4e6))
    (pin "2" (uuid 2db0cf38-221e-41ee-bc3b-aae0fa18cbee))
  )

  (symbol (lib_id "Device:R") (at 76.2 31.75 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid b6ec050d-c595-459d-a9f4-99286f86bde8)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 31.115 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 33.655 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 31.75 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c61c0cb9-3773-41b6-8d83-376aca3c1373))
    (pin "2" (uuid b2d65b45-cc42-44c5-8ac1-44ce92522175))
  )

  (symbol (lib_id "Simulation_SPICE:VPULSE") (at 25.4 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid b7c3917c-3f65-47ba-9428-cd2c6af6c0a0)
    (default_instance (reference "V") (unit 1) (value "VPULSE") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 80.645 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VPULSE" (id 1) (at 29.21 83.185 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6f03fd92-1628-4882-8b4e-2b3c90883e8e))
    (pin "2" (uuid b4436efa-ed17-45e3-bd96-b5f12763923e))
  )

  (symbol (lib_id "power:GND") (at 139.7 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid bc458ec0-a49c-4827-848d-cfd66e255f12)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8c07c4ee-e25f-478d-a915-4c03834851ef))
  )

  (symbol (lib_id "power:GND") (at 139.7 189.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid c25b194b-1c7f-4abe-8fea-abe3dd53b92f)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 195.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 194.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 189.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 189.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid ccf1f143-df8a-4351-a472-71105bf41a40))
  )

  (symbol (lib_id "Simulation_SPICE:IDC") (at 88.9 31.75 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid c56bb3f4-658b-426f-95a6-42baa2464677)
    (default_instance (reference "I") (unit 1) (value "IDC") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 29.845 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IDC" (id 1) (at 92.71 32.385 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid fa213b16-0b6d-468e-8962-b60168a90f61))
    (pin "2" (uuid 869d1d17-0c14-4d04-a1a4-6fe425bcf57d))
  )

  (symbol (lib_id "Simulation_SPICE:VTRNOISE") (at 25.4 184.15 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid cb12fd1f-7d81-46ec-a880-ef1712525887)
    (default_instance (reference "V") (unit 1) (value "VTRNOISE") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 182.88 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VTRNOISE" (id 1) (at 29.21 185.42 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 184.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 184.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f721933f-b430-41a7-ba29-c84d00a8153e))
    (pin "2" (uuid 3e7c178d-4286-4685-8fe4-07b6486004a5))
  )

  (symbol (lib_id "Device:R") (at 203.2 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid ce86c56e-bad2-4214-91e5-f1d02906f382)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 205.74 132.715 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 205.74 135.255 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 201.422 133.35 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 203.2 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d0d29887-0f22-4ef6-8df6-76d05a73bd92))
    (pin "2" (uuid a88af530-da9f-4538-a8cf-dc5328365fe3))
  )

  (symbol (lib_id "power:GND") (at 139.7 163.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid cee36049-9d94-45a1-84fb-a0862a0cd508)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 170.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 168.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 163.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 163.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 50dd46e0-9c8d-421a-9122-05e37cb5c137))
  )

  (symbol (lib_id "power:GND") (at 152.4 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid cfe42866-c7a9-4376-963a-9d687f5523eb)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 152.4 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 152.4 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 152.4 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 152.4 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 20e678ae-7ecc-4b83-b5d8-ad707cf075c3))
  )

  (symbol (lib_id "power:GND") (at 203.2 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid d06cdbc2-3e8f-40c4-8236-9eda5afb6782)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 203.2 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 203.2 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 203.2 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 203.2 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6b36081c-33b0-4bb9-97a5-4faa49c4ebf2))
  )

  (symbol (lib_id "Device:R") (at 139.7 158.75 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid d43c67b0-b6f5-4e82-85b8-e33b1f8ef473)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 158.115 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 160.655 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 158.75 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 158.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid cfd09ff6-8d23-458e-a1e2-a4fd6b8fab59))
    (pin "2" (uuid 5ebfba0f-dc9c-4861-82b3-a7dadea5c37f))
  )

  (symbol (lib_id "power:GND") (at 88.9 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid d6a3c3ac-aab0-4596-a5a7-df9f31d8935c)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b4177792-6657-4e2b-bbc4-70b996757b7f))
  )

  (symbol (lib_id "power:GND") (at 88.9 163.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid d8f3d98e-2622-4175-a524-56cdba0237ee)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 170.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 168.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 163.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 163.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6c1135fe-6594-421e-949a-e7bd824cd8a2))
  )

  (symbol (lib_id "power:GND") (at 25.4 189.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid db0b89e7-3104-4c39-9998-53d9f7e2860c)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 195.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 194.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 189.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 189.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 0a4f14bb-4f0a-4524-b127-fb759ed2f60a))
  )

  (symbol (lib_id "power:GND") (at 215.9 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid db7eb64c-81a0-48eb-b68d-7f5b23bae3db)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 215.9 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 215.9 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 215.9 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 215.9 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 4eae9403-13e6-41bc-b316-987e52bd28f9))
  )

  (symbol (lib_id "power:GND") (at 25.4 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid dddca9c8-1d32-4357-85ee-7242407d9f0e)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 51be085b-39ea-4262-9cb6-fac0c305b4b3))
  )

  (symbol (lib_id "power:GND") (at 88.9 138.43 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid e1e8ad98-5194-42e9-8d98-6286cfdd5a9d)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 144.78 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 143.51 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 10612421-d3ea-4180-8793-01b193b72472))
  )

  (symbol (lib_id "Simulation_SPICE:VPWL") (at 25.4 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid ea0404ea-1765-4d60-b23c-3011191dd829)
    (default_instance (reference "V") (unit 1) (value "VPWL") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 132.715 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VPWL" (id 1) (at 29.21 135.255 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e55ef022-234c-4bed-917e-3950666cf1b6))
    (pin "2" (uuid 6bb47679-dbc6-4adb-b4a5-48d4239955ed))
  )

  (symbol (lib_id "power:GND") (at 25.4 138.43 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid eb4ac000-6296-4f85-9652-7020045d4017)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 144.78 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 143.51 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7812269a-0e62-4536-9f6c-73bfe31d1ae0))
  )

  (symbol (lib_id "power:GND") (at 203.2 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid ec5cf671-b414-4901-bafd-6c623d303d9e)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 203.2 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 203.2 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 203.2 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 203.2 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7a3fb6e5-448e-4259-b011-811373ea3b98))
  )

  (symbol (lib_id "Device:R") (at 76.2 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid ed8d1d5a-1962-4286-8cce-b4277960ea0b)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 132.715 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 135.255 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 133.35 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8049ba31-d508-4844-b74a-0b36d5464e8a))
    (pin "2" (uuid 950f2c97-6058-46f5-92a5-79340f364351))
  )

  (symbol (lib_id "power:GND") (at 139.7 138.43 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid f058d83e-d2f4-4fc2-a0fa-f064b4601836)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 144.78 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 143.51 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 47cf2f3a-73da-40bb-8cb1-6a6ae3a74675))
  )

  (symbol (lib_id "power:GND") (at 25.4 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid f42fd0ec-cf74-4d08-90b5-a959b1ecbd18)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid ec00c1d1-919f-41d1-b7f5-7fecf24cf839))
  )

  (symbol (lib_id "Device:R") (at 76.2 158.75 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid f857ded5-ac3c-467c-8618-5b211d1cb690)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 158.115 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 160.655 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 158.75 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 158.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3cbe0c2d-e1e8-4783-b28f-b0aaf61b30c9))
    (pin "2" (uuid 14c54f60-228b-42c1-ab47-d5c5edd0cdc6))
  )

  (symbol (lib_id "Device:R") (at 139.7 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid fc0d6ea0-e468-4260-9fe0-aa14c6cfb400)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 107.95 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 2d776c9f-58ea-4353-9773-11cc3ca56a4c))
    (pin "2" (uuid f2443325-015b-4f8e-8a11-80366e3c4f5a))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/78e47da6-e06f-44cd-a293-3d24fc58787d"
      (reference "#PWR01") (unit 1) (value "GND") (footprint "")
    )
    (path "/f42fd0ec-cf74-4d08-90b5-a959b1ecbd18"
      (reference "#PWR02") (unit 1) (value "GND") (footprint "")
    )
    (path "/dddca9c8-1d32-4357-85ee-7242407d9f0e"
      (reference "#PWR03") (unit 1) (value "GND") (footprint "")
    )
    (path "/8ead6375-404c-4c81-ad7a-987ea2c4d03d"
      (reference "#PWR04") (unit 1) (value "GND") (footprint "")
    )
    (path "/eb4ac000-6296-4f85-9652-7020045d4017"
      (reference "#PWR05") (unit 1) (value "GND") (footprint "")
    )
    (path "/348ed3a9-fff3-4488-b8ea-73412129f38e"
      (reference "#PWR06") (unit 1) (value "GND") (footprint "")
    )
    (path "/db0b89e7-3104-4c39-9998-53d9f7e2860c"
      (reference "#PWR07") (unit 1) (value "GND") (footprint "")
    )
    (path "/127c6869-e53c-4a8f-bb84-075e336124e9"
      (reference "#PWR08") (unit 1) (value "GND") (footprint "")
    )
    (path "/3b6aa53c-9a95-4c84-90db-32d8028b844d"
      (reference "#PWR09") (unit 1) (value "GND") (footprint "")
    )
    (path "/a4b6db34-c852-47c5-8cdd-e8801ad1c485"
      (reference "#PWR010") (unit 1) (value "GND") (footprint "")
    )
    (path "/a54fb244-ba0f-4464-aebd-33800b8b27be"
      (reference "#PWR011") (unit 1) (value "GND") (footprint "")
    )
    (path "/72571608-d69f-4ba1-82c8-************"
      (reference "#PWR012") (unit 1) (value "GND") (footprint "")
    )
    (path "/a1044b70-5210-4a3d-9d01-c93ae164f98a"
      (reference "#PWR013") (unit 1) (value "GND") (footprint "")
    )
    (path "/0bc516f1-8d59-49e1-94af-1387cb0f6d44"
      (reference "#PWR014") (unit 1) (value "GND") (footprint "")
    )
    (path "/96b63841-de75-42e3-b7ed-d987ad24c00f"
      (reference "#PWR015") (unit 1) (value "GND") (footprint "")
    )
    (path "/d6a3c3ac-aab0-4596-a5a7-df9f31d8935c"
      (reference "#PWR016") (unit 1) (value "GND") (footprint "")
    )
    (path "/74654e3a-e2bb-42f4-9b7b-9d294fd6386e"
      (reference "#PWR017") (unit 1) (value "GND") (footprint "")
    )
    (path "/a8a8d31d-dc05-4c9f-8748-a64d133cadb1"
      (reference "#PWR018") (unit 1) (value "GND") (footprint "")
    )
    (path "/e1e8ad98-5194-42e9-8d98-6286cfdd5a9d"
      (reference "#PWR019") (unit 1) (value "GND") (footprint "")
    )
    (path "/d8f3d98e-2622-4175-a524-56cdba0237ee"
      (reference "#PWR020") (unit 1) (value "GND") (footprint "")
    )
    (path "/8ce6a30e-bf10-426e-98c2-957bc9e06bc0"
      (reference "#PWR021") (unit 1) (value "GND") (footprint "")
    )
    (path "/bc458ec0-a49c-4827-848d-cfd66e255f12"
      (reference "#PWR022") (unit 1) (value "GND") (footprint "")
    )
    (path "/2d43b4ca-185e-47a6-a2b2-d3e67a2155a7"
      (reference "#PWR023") (unit 1) (value "GND") (footprint "")
    )
    (path "/4b3bd156-df27-4707-b782-364ea4daec71"
      (reference "#PWR024") (unit 1) (value "GND") (footprint "")
    )
    (path "/2b8ae715-294b-4d1b-8930-56ebe519569d"
      (reference "#PWR025") (unit 1) (value "GND") (footprint "")
    )
    (path "/f058d83e-d2f4-4fc2-a0fa-f064b4601836"
      (reference "#PWR026") (unit 1) (value "GND") (footprint "")
    )
    (path "/cee36049-9d94-45a1-84fb-a0862a0cd508"
      (reference "#PWR027") (unit 1) (value "GND") (footprint "")
    )
    (path "/c25b194b-1c7f-4abe-8fea-abe3dd53b92f"
      (reference "#PWR028") (unit 1) (value "GND") (footprint "")
    )
    (path "/7e8f760e-e465-4013-ab3f-f482db08eec4"
      (reference "#PWR029") (unit 1) (value "GND") (footprint "")
    )
    (path "/68292dcf-c018-4d28-8f6e-620399df8b3d"
      (reference "#PWR030") (unit 1) (value "GND") (footprint "")
    )
    (path "/7c26ed1a-02c1-4dd4-b749-990844bf2c56"
      (reference "#PWR031") (unit 1) (value "GND") (footprint "")
    )
    (path "/cfe42866-c7a9-4376-963a-9d687f5523eb"
      (reference "#PWR032") (unit 1) (value "GND") (footprint "")
    )
    (path "/44fb8663-f6c5-473f-948a-f45b78532a12"
      (reference "#PWR033") (unit 1) (value "GND") (footprint "")
    )
    (path "/d06cdbc2-3e8f-40c4-8236-9eda5afb6782"
      (reference "#PWR034") (unit 1) (value "GND") (footprint "")
    )
    (path "/5be794f9-d628-4d3a-87a9-bf3b0640c0ac"
      (reference "#PWR035") (unit 1) (value "GND") (footprint "")
    )
    (path "/ec5cf671-b414-4901-bafd-6c623d303d9e"
      (reference "#PWR036") (unit 1) (value "GND") (footprint "")
    )
    (path "/53936774-684c-428f-a1b2-bbeabec5a433"
      (reference "#PWR037") (unit 1) (value "GND") (footprint "")
    )
    (path "/836c6d73-036d-41fb-8d46-b78aaa7fecd0"
      (reference "#PWR038") (unit 1) (value "GND") (footprint "")
    )
    (path "/db7eb64c-81a0-48eb-b68d-7f5b23bae3db"
      (reference "#PWR039") (unit 1) (value "GND") (footprint "")
    )
    (path "/4b51dcec-ead2-4989-941a-aa26fef66dab"
      (reference "#PWR040") (unit 1) (value "GND") (footprint "")
    )
    (path "/3598a99a-ad64-4c4e-889d-00f61c21a9e4"
      (reference "#PWR041") (unit 1) (value "GND") (footprint "")
    )
    (path "/6682ceb3-f8c3-4259-9339-b08b6dcdb6f4"
      (reference "#PWR042") (unit 1) (value "GND") (footprint "")
    )
    (path "/af2255d6-0d33-42f3-844f-679a8f629490"
      (reference "#PWR043") (unit 1) (value "GND") (footprint "")
    )
    (path "/4f019669-1c87-467d-a93e-9b3c43806963"
      (reference "#PWR044") (unit 1) (value "GND") (footprint "")
    )
    (path "/a209f5ba-de46-4a99-9b8a-0b088fbf53b5"
      (reference "#PWR045") (unit 1) (value "GND") (footprint "")
    )
    (path "/708debee-879a-4cae-b7c9-3da17d548e5f"
      (reference "#PWR046") (unit 1) (value "GND") (footprint "")
    )
    (path "/2fcd38a4-3c32-447d-bfd2-a9b96c46f709"
      (reference "#PWR047") (unit 1) (value "GND") (footprint "")
    )
    (path "/99492963-560a-4b83-875e-743583f7c046"
      (reference "#PWR048") (unit 1) (value "GND") (footprint "")
    )
    (path "/8bb473bb-2d27-406f-b1e3-fd97fb279901"
      (reference "I1") (unit 1) (value "=sin(2*pi*10000*time)") (footprint "")
    )
    (path "/44f3bfec-18ca-4a10-b9dd-7b481a410f0f"
      (reference "IBURSTNOISE1") (unit 1) (value "ampl=1 tcapt=1m temit=1m") (footprint "")
    )
    (path "/c56bb3f4-658b-426f-95a6-42baa2464677"
      (reference "IDC1") (unit 1) (value "dc=1") (footprint "")
    )
    (path "/5b2fd2dc-d066-4c39-8bc9-ea8e365975ae"
      (reference "IEXP1") (unit 1) (value "y2=1 td1=1m tau1=500u td2=2m tau2=500u") (footprint "")
    )
    (path "/3bacb257-993d-44eb-922e-40ef9a788e8f"
      (reference "IPINKNOISE1") (unit 1) (value "rms=1m slope=1 dt=1u") (footprint "")
    )
    (path "/530cae72-e62d-4472-a544-cf6e7504a547"
      (reference "IPULSE1") (unit 1) (value "y2=1 td=1m tr=1u tf=1u tw=1m per=2m") (footprint "")
    )
    (path "/aeb4fd99-50d3-4ee5-82f6-2e8e513f2af7"
      (reference "IPWL1") (unit 1) (value "pwl=\"0 0 1m 1 2m 0 3m 0.5 4m 0\"") (footprint "")
    )
    (path "/b28bf5f4-50a4-4b7b-a795-258142b8b285"
      (reference "IRANDEXP1") (unit 1) (value "offset=1 mean=1m dt=1u") (footprint "")
    )
    (path "/511ecaa8-fa3b-4ee5-b019-f024f7990a37"
      (reference "IRANDNORMAL1") (unit 1) (value "mean=1 stddev=1m dt=1u") (footprint "")
    )
    (path "/6ddd77dc-f1ba-4e98-b8c2-81b7e98b9786"
      (reference "IRANDUNIFORM1") (unit 1) (value "min=-1 max=1 dt=1u td=1m") (footprint "")
    )
    (path "/4a11c031-0cff-43e4-b00d-ffd7533c6b4c"
      (reference "ISIN1") (unit 1) (value "ampl=1 f=1k td=1m") (footprint "")
    )
    (path "/557deca4-e3af-4631-9c7b-dce1d008f719"
      (reference "IWHITENOISE1") (unit 1) (value "rms=1m dt=1u") (footprint "")
    )
    (path "/b6ec050d-c595-459d-a9f4-99286f86bde8"
      (reference "R1") (unit 1) (value "100") (footprint "")
    )
    (path "/183b6ba4-3657-4b16-8996-abd81eff6890"
      (reference "R2") (unit 1) (value "100") (footprint "")
    )
    (path "/59c377ca-586c-4d47-8389-118b72e33ab9"
      (reference "R3") (unit 1) (value "100") (footprint "")
    )
    (path "/2d99cf32-c26c-4a2f-8c01-427fabe7f976"
      (reference "R4") (unit 1) (value "100") (footprint "")
    )
    (path "/ed8d1d5a-1962-4286-8cce-b4277960ea0b"
      (reference "R5") (unit 1) (value "100") (footprint "")
    )
    (path "/f857ded5-ac3c-467c-8618-5b211d1cb690"
      (reference "R6") (unit 1) (value "100") (footprint "")
    )
    (path "/05a0c313-785e-4dbd-a2f1-9f25d0b512a6"
      (reference "R7") (unit 1) (value "100") (footprint "")
    )
    (path "/5bd5c24b-684f-4add-8e04-b731deb77ff7"
      (reference "R8") (unit 1) (value "100") (footprint "")
    )
    (path "/2cb952de-7eb1-4747-ae80-3e8dcefdf7b8"
      (reference "R9") (unit 1) (value "100") (footprint "")
    )
    (path "/3926526d-6bdf-4df1-b6ca-e1ed30b5305b"
      (reference "R10") (unit 1) (value "100") (footprint "")
    )
    (path "/fc0d6ea0-e468-4260-9fe0-aa14c6cfb400"
      (reference "R11") (unit 1) (value "100") (footprint "")
    )
    (path "/afae866c-ee68-4958-bbec-3bf2e19ce1ef"
      (reference "R12") (unit 1) (value "100") (footprint "")
    )
    (path "/d43c67b0-b6f5-4e82-85b8-e33b1f8ef473"
      (reference "R13") (unit 1) (value "100") (footprint "")
    )
    (path "/3893752b-7abf-4822-8759-2794fab1e696"
      (reference "R14") (unit 1) (value "100") (footprint "")
    )
    (path "/71a83d3a-3786-43b1-8f38-bb2040b16368"
      (reference "R15") (unit 1) (value "100") (footprint "")
    )
    (path "/ab830d0e-1b87-4954-ae8f-a4e2dda7cde7"
      (reference "R16") (unit 1) (value "100") (footprint "")
    )
    (path "/41274798-127c-45d0-983c-e20ccad3c45f"
      (reference "R17") (unit 1) (value "100") (footprint "")
    )
    (path "/5f543059-cd5d-4bd8-ad91-961bacc1314e"
      (reference "R18") (unit 1) (value "100") (footprint "")
    )
    (path "/ce86c56e-bad2-4214-91e5-f1d02906f382"
      (reference "R19") (unit 1) (value "100") (footprint "")
    )
    (path "/85462b10-caca-41b0-85e4-3e5c5b0fc436"
      (reference "R20") (unit 1) (value "100") (footprint "")
    )
    (path "/9a598ad2-f5a0-4e37-ad0c-1012374e5f4b"
      (reference "R21") (unit 1) (value "100") (footprint "")
    )
    (path "/1e865d52-3e9d-40e2-94ba-ad8d2b33aca4"
      (reference "R22") (unit 1) (value "100") (footprint "")
    )
    (path "/4cb970f3-fd99-4bf3-a224-e7637478343a"
      (reference "R23") (unit 1) (value "100") (footprint "")
    )
    (path "/7d09a479-b0c2-46ea-b193-2f7efcdcf1f1"
      (reference "R24") (unit 1) (value "100") (footprint "")
    )
    (path "/9b2776eb-8216-4cef-b1f8-57196fbebbcc"
      (reference "V1") (unit 1) (value "=sin(2*pi*10000*time)") (footprint "")
    )
    (path "/529ade44-fd15-4861-9282-7bc8f7fda7d3"
      (reference "VBURSTNOISE1") (unit 1) (value "ampl=1 tcapt=1m temit=1m") (footprint "")
    )
    (path "/4a13630f-aae8-4aa8-b67e-b09bc4077213"
      (reference "VDC1") (unit 1) (value "dc=1") (footprint "")
    )
    (path "/b2411a13-3e8d-4353-8659-2d857db7af3d"
      (reference "VEXP1") (unit 1) (value "y2=1 td1=1m tau1=500u td2=2m tau2=500u") (footprint "")
    )
    (path "/cb12fd1f-7d81-46ec-a880-ef1712525887"
      (reference "VPINKNOISE1") (unit 1) (value "rms=1m slope=1 dt=1u") (footprint "")
    )
    (path "/b7c3917c-3f65-47ba-9428-cd2c6af6c0a0"
      (reference "VPULSE1") (unit 1) (value "y2=1 td=1m tr=1u tf=1u tw=1m per=2m") (footprint "")
    )
    (path "/ea0404ea-1765-4d60-b23c-3011191dd829"
      (reference "VPWL1") (unit 1) (value "pwl=\"0 0 1m 1 2m 0 3m 0.5 4m 0\"") (footprint "")
    )
    (path "/955c3193-72c9-433e-bd84-a687bb47b0b7"
      (reference "VRANDEXP1") (unit 1) (value "offset=1 mean=1m dt=1u") (footprint "")
    )
    (path "/6922b540-e03b-45e6-ab60-de3e42567059"
      (reference "VRANDNORMAL1") (unit 1) (value "mean=1 stddev=1m dt=1u") (footprint "")
    )
    (path "/b1698d13-4248-4da9-8a8e-dbd2bfbf5ca4"
      (reference "VRANDUNIFORM1") (unit 1) (value "min=-1 max=1 dt=1u td=1m") (footprint "")
    )
    (path "/162dd4fd-6b0c-4c88-9d11-dfd2cf5af61f"
      (reference "VSIN1") (unit 1) (value "ampl=1 f=1k td=1m") (footprint "")
    )
    (path "/1035e4d2-6278-482b-8c00-dddd61b84994"
      (reference "VWHITENOISE1") (unit 1) (value "rms=1m dt=1u") (footprint "")
    )
  )
)
