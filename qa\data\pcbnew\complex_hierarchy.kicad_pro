{"board": {"3dviewports": [], "design_settings": {"defaults": {"board_outline_line_width": 0.2032, "copper_line_width": 0.381, "copper_text_italic": false, "copper_text_size_h": 1.524, "copper_text_size_v": 2.032, "copper_text_thickness": 0.30479999999999996, "copper_text_upright": false, "courtyard_line_width": 0.049999999999999996, "dimension_precision": 4, "dimension_units": 3, "dimensions": {"arrow_length": 1270000, "extension_offset": 500000, "keep_text_aligned": true, "suppress_zeroes": false, "text_position": 0, "units_format": 1}, "fab_line_width": 0.09999999999999999, "fab_text_italic": false, "fab_text_size_h": 1.0, "fab_text_size_v": 1.0, "fab_text_thickness": 0.15, "fab_text_upright": false, "other_line_width": 0.09999999999999999, "other_text_italic": false, "other_text_size_h": 1.0, "other_text_size_v": 1.0, "other_text_thickness": 0.15, "other_text_upright": false, "pads": {"drill": 0.8128, "height": 2.286, "width": 2.286}, "silk_line_width": 0.381, "silk_text_italic": false, "silk_text_size_h": 1.524, "silk_text_size_v": 1.524, "silk_text_thickness": 0.30479999999999996, "silk_text_upright": false, "zones": {"45_degree_only": false, "min_clearance": 0.508}}, "diff_pair_dimensions": [], "drc_exclusions": [], "meta": {"filename": "board_design_settings.json", "version": 2}, "rule_severities": {"annular_width": "error", "clearance": "error", "connection_width": "warning", "copper_edge_clearance": "error", "copper_sliver": "warning", "courtyards_overlap": "error", "diff_pair_gap_out_of_range": "error", "diff_pair_uncoupled_length_too_long": "error", "drill_out_of_range": "error", "duplicate_footprints": "warning", "extra_footprint": "warning", "footprint": "error", "footprint_type_mismatch": "ignore", "hole_clearance": "error", "hole_near_hole": "error", "invalid_outline": "error", "isolated_copper": "warning", "item_on_disabled_layer": "error", "items_not_allowed": "error", "length_out_of_range": "error", "lib_footprint_issues": "warning", "lib_footprint_mismatch": "warning", "malformed_courtyard": "error", "microvia_drill_out_of_range": "error", "missing_courtyard": "ignore", "missing_footprint": "warning", "net_conflict": "warning", "npth_inside_courtyard": "ignore", "padstack": "warning", "pth_inside_courtyard": "ignore", "shorting_items": "error", "silk_edge_clearance": "warning", "silk_over_copper": "warning", "silk_overlap": "warning", "skew_out_of_range": "error", "solder_mask_bridge": "error", "starved_thermal": "error", "text_height": "warning", "text_thickness": "warning", "through_hole_pad_without_hole": "error", "too_many_vias": "error", "track_dangling": "warning", "track_width": "error", "tracks_crossing": "error", "unconnected_items": "error", "unresolved_variable": "error", "via_dangling": "warning", "zone_has_empty_net": "error", "zones_intersect": "error"}, "rules": {"allow_blind_buried_vias": false, "allow_microvias": false, "max_error": 0.005, "min_clearance": 0.0, "min_connection": 0.0, "min_copper_edge_clearance": 0.1016, "min_hole_clearance": 0.25, "min_hole_to_hole": 0.25, "min_microvia_diameter": 0.508, "min_microvia_drill": 0.2032, "min_resolved_spokes": 2, "min_silk_clearance": 0.0, "min_text_height": 0.7999999999999999, "min_text_thickness": 0.08, "min_through_hole_diameter": 0.508, "min_track_width": 0.2032, "min_via_annular_width": 0.09999999999999999, "min_via_diameter": 0.889, "solder_mask_to_copper_clearance": 0.0, "use_height_for_length_calcs": true}, "teardrop_options": [{"td_onpadsmd": true, "td_onroundshapesonly": false, "td_ontrackend": false, "td_onviapad": true}], "teardrop_parameters": [{"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_round_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_rect_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_track_end", "td_width_to_size_filter_ratio": 0.9}], "track_widths": [], "via_dimensions": [], "zones_allow_external_fillets": false, "zones_use_no_outline": true}, "layer_presets": [], "viewports": []}, "boards": [], "cvpcb": {"equivalence_files": []}, "libraries": {"pinned_footprint_libs": [], "pinned_symbol_libs": []}, "meta": {"filename": "complex_hierarchy.kicad_pro", "version": 1}, "net_settings": {"classes": [{"bus_width": 12, "clearance": 0.2794, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.508, "microvia_drill": 0.2032, "name": "<PERSON><PERSON><PERSON>", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.4318, "via_diameter": 1.651, "via_drill": 0.635, "wire_width": 6}, {"bus_width": 12, "clearance": 0.381, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.508, "microvia_drill": 0.2032, "name": "power", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.6096, "via_diameter": 1.651, "via_drill": 0.635, "wire_width": 6}], "meta": {"version": 3}, "net_colors": null, "netclass_assignments": null, "netclass_patterns": [{"netclass": "<PERSON><PERSON><PERSON>", "pattern": "+12V"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/ampli_h1"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/ampli_h2"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/ampli_h3"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/ampli_h4"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/ampli_h5"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/ampli_h6"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/ampli_h7"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/ampli_h8"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000005"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000010"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000011"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000012"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000013"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000014"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000015"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000016"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000017"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000018"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000019"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000020"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000021"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000022"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000023"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000024"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000025"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000026"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000027"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000032"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000033"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000034"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000035"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000036"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000037"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000038"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000039"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000040"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000041"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000042"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000043"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000044"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000045"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000046"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000047"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "N-000048"}, {"netclass": "power", "pattern": "-VAA"}, {"netclass": "power", "pattern": "/12Vext"}, {"netclass": "power", "pattern": "GND"}, {"netclass": "power", "pattern": "HT"}, {"netclass": "power", "pattern": "VCC"}]}, "pcbnew": {"last_paths": {"gencad": "", "idf": "", "netlist": "", "plot": "", "pos_files": "", "specctra_dsn": "", "step": "", "svg": "", "vmrl": "", "vrml": ""}, "page_layout_descr_file": ""}, "schematic": {"legacy_lib_dir": "", "legacy_lib_list": []}, "sheets": [], "text_variables": {}}