(kicad_sch (version 20210621) (generator eeschema)

  (uuid 4b0cf473-7673-420c-86ad-82669944f979)

  (paper "A4")

  (lib_symbols
    (symbol "Connector:TestPoint" (pin_numbers hide) (pin_names (offset 0.762) hide) (in_bom yes) (on_board yes)
      (property "Reference" "TP" (id 0) (at 0 6.858 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "TestPoint" (id 1) (at 0 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 5.08 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 5.08 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "test point tp" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "test point" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Pin* Test*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "TestPoint_0_1"
        (circle (center 0 3.302) (radius 0.762) (stroke (width 0)) (fill (type none)))
      )
      (symbol "TestPoint_1_1"
        (pin passive line (at 0 0 90) (length 2.54)
          (name "1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )


  (junction (at 25.4 26.67) (diameter 0.9144) (color 0 0 0 0))

  (no_connect (at 25.4 29.21) (uuid f3fa9d77-a633-4b7c-b697-53a982f2dbdb))

  (wire (pts (xy 25.4 24.13) (xy 25.4 26.67))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid 30e8acf8-7226-4732-b3c9-71b04b8ebe5e)
  )
  (wire (pts (xy 25.4 26.67) (xy 25.4 29.21))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid a7a79bf9-01f7-45de-ba64-0493fd7cdfd0)
  )
  (wire (pts (xy 25.4 26.67) (xy 43.18 26.67))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid 30e8acf8-7226-4732-b3c9-71b04b8ebe5e)
  )
  (wire (pts (xy 43.18 26.67) (xy 43.18 24.13))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid 30e8acf8-7226-4732-b3c9-71b04b8ebe5e)
  )

  (symbol (lib_id "Connector:TestPoint") (at 25.4 24.13 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 75662485-d96e-4e3d-93ab-28c4521800ac)
    (property "Reference" "TP1" (id 0) (at 27.94 21.0184 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "TestPoint" (id 1) (at 27.94 23.5584 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 30.48 24.13 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 30.48 24.13 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 4317837e-7c6c-4d62-8a51-13b25e87a167))
  )

  (symbol (lib_id "Connector:TestPoint") (at 43.18 24.13 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 87711a3d-275a-423c-bbd3-157dc0279528)
    (property "Reference" "TP2" (id 0) (at 45.72 21.0184 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "TestPoint" (id 1) (at 45.72 23.5584 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 48.26 24.13 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 48.26 24.13 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f46cb911-417d-4412-b794-9db01889e9e8))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/75662485-d96e-4e3d-93ab-28c4521800ac"
      (reference "TP1") (unit 1) (value "TestPoint") (footprint "")
    )
    (path "/87711a3d-275a-423c-bbd3-157dc0279528"
      (reference "TP2") (unit 1) (value "TestPoint") (footprint "")
    )
  )
)
