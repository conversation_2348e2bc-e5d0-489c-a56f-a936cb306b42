(footprint "UBLOX_ZOE_M8-0-10" (version 20231007) (generator pcbnew)
  (layer "F.Cu")
  (descr "UBLOX ZOE-M8Q-0-10\n\nThe ZOE-M8G and ZOE-M8Q are u-blox’s super small, highly integrated GNSS SiP (System in Package) modules based on the high performing u-blox M8 concurrent positioning engine. The ultra-miniature form factor integrates a complete GNSS receiver including SAW filter, LNA and TCXO. The ZOE-M8Q is the 3 V variant.\n\nPhyical Characteristics\n\n• LGA\n• 51 Pins\n• 4.5mm X 4.5mm X 1mm\n• 51 Pins")
  (property "Reference" "REF**" (at -2.246 -3.135 0 unlocked) (layer "F.SilkS") (tstamp 1254997f-3253-4626-94a4-607634aa33b3)
    (effects (font (size 0.92 0.92) (thickness 0.08)) (justify left bottom))
  )
  (property "Value" "UBLOX_ZOE_M8-0-10" (at -2.373 3.865 0 unlocked) (layer "F.Fab") (tstamp 9e6a6ea5-abfc-480e-b194-2efb0b19a501)
    (effects (font (size 0.92 0.92) (thickness 0.08)) (justify left bottom))
  )
  (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 86bee379-cb40-4db3-93f5-16cb4148fea7)
    (effects (font (size 1.27 1.27)))
  )
  (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp a3577dd7-6d58-43ac-9a2e-b1357581e1cb)
    (effects (font (size 1.27 1.27)))
  )
  (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp b1fdb283-9be2-4b00-bae2-5a079f4a22d5)
    (effects (font (size 1.27 1.27)))
  )
  (fp_line (start -2.346 1.746) (end -2.346 -1.754)
    (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 746b9cc8-594c-4845-828b-9c90eaddaf3f))
  (fp_line (start -1.746 -2.354) (end 1.754 -2.354)
    (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 382bcfa7-f1ab-474f-bf70-e550b8042037))
  (fp_line (start -1.746 2.346) (end 1.754 2.346)
    (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 97c0f265-1170-4618-89c3-09993035acbe))
  (fp_line (start 2.354 1.746) (end 2.354 -1.754)
    (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 34fba464-266c-42b0-9970-def34c71d489))
  (fp_circle (center -2.738 -2.754) (end -2.611 -2.754)
    (stroke (width 0.254) (type solid)) (fill solid) (layer "F.SilkS") (tstamp bfb30ecf-f15e-49f9-8f70-0a0da1cd3f3f))
  (fp_circle (center -1.9982 -1.5039) (end -1.9232 -1.5039)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 8b36e068-ff03-47c7-b832-d477f711f61a))
  (fp_circle (center -1.9982 -1.0039) (end -1.9232 -1.0039)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 2807284f-ce28-4dbc-a500-2673b61d5d40))
  (fp_circle (center -1.9982 -0.5039) (end -1.9232 -0.5039)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 24db1ff9-5ea0-40b6-a7c6-2acd4a68c815))
  (fp_circle (center -1.9982 -0.0039) (end -1.9232 -0.0039)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 9a53adf4-83c0-4f8d-acd7-fe90e197a11f))
  (fp_circle (center -1.9982 0.4961) (end -1.9232 0.4961)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp e3b57303-b85a-4deb-97b6-4dad71e33af6))
  (fp_circle (center -1.9982 0.9961) (end -1.9232 0.9961)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 8d708968-d278-4f6c-8c8c-8a182139341e))
  (fp_circle (center -1.9982 1.4961) (end -1.9232 1.4961)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 9e510e30-d175-4200-9d95-056c883d6a50))
  (fp_circle (center -1.9965 -2.0059) (end -1.9215 -2.0059)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp a938258c-a5ea-4c35-9ce9-4f8d5053436b))
  (fp_circle (center -1.9965 1.9941) (end -1.9215 1.9941)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 4dba243e-d94b-419c-ab23-eb1cff48f42a))
  (fp_circle (center -1.4965 -2.0059) (end -1.4215 -2.0059)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 05bbd521-8b61-46d4-a2b3-393184ff842e))
  (fp_circle (center -1.4965 1.9941) (end -1.4215 1.9941)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp afea2e08-4093-4875-9bd6-c7344a46ea0a))
  (fp_circle (center -0.9983 -0.0042) (end -0.9233 -0.0042)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 81de9841-c301-415d-bd6d-a2fbc8795f91))
  (fp_circle (center -0.9973 -0.5051) (end -0.9223 -0.5051)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp bb8646a4-bc89-41c8-b7cd-6360fa043a3c))
  (fp_circle (center -0.9973 0.9973) (end -0.9223 0.9973)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp e463ec42-ca0d-4ed8-b846-a0d175f39069))
  (fp_circle (center -0.9965 -2.0059) (end -0.9215 -2.0059)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 36750786-cabf-4a55-9ae7-9bea1f6a892f))
  (fp_circle (center -0.9965 1.9941) (end -0.9215 1.9941)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 7ac19f28-1b9c-4990-9ed1-4c40548bd0ea))
  (fp_circle (center -0.9952 0.498) (end -0.9202 0.498)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 677e029a-9a17-473f-b284-71264aff8734))
  (fp_circle (center -0.9948 -1.0038) (end -0.9198 -1.0038)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 90824b89-35d6-40fa-9301-0114ed497fc7))
  (fp_circle (center -0.499 -0.5035) (end -0.424 -0.5035)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp bf493f24-3283-4987-a263-b8ab37b735f5))
  (fp_circle (center -0.4965 -2.0059) (end -0.4215 -2.0059)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 470ef538-181c-4625-a451-11727bfb9112))
  (fp_circle (center -0.4965 1.9941) (end -0.4215 1.9941)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp c7e9289d-954c-4278-b875-9d29de949da8))
  (fp_circle (center -0.4964 0.9972) (end -0.4214 0.9972)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 855aa8f3-8f64-4264-a47a-889786672b36))
  (fp_circle (center -0.4951 0.4989) (end -0.4201 0.4989)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 6b0c3454-d8f7-499a-90cc-2f2b65c8b7cf))
  (fp_circle (center -0.4948 -1.0038) (end -0.4198 -1.0038)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp c0c3e063-cbc0-4fad-9a88-4276c9ff9594))
  (fp_circle (center 0.0027 0.9972) (end 0.0777 0.9972)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp f16e1d51-7574-4618-a004-3df818e49471))
  (fp_circle (center 0.0035 -2.0059) (end 0.0785 -2.0059)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp a55b9d55-3ced-4d2a-b25b-fd7a72f0486c))
  (fp_circle (center 0.0035 1.9941) (end 0.0785 1.9941)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp c4e87ca6-8d3e-4925-bcba-730b14c66c04))
  (fp_circle (center 0.0052 -1.0038) (end 0.0802 -1.0038)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 6729c9cb-9283-4d7e-8ae5-bc17f7e08d87))
  (fp_circle (center 0.501 -0.5051) (end 0.576 -0.5051)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 05f97476-b68c-4566-9ebf-c17f437eb47d))
  (fp_circle (center 0.5024 0.9966) (end 0.5774 0.9966)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp ccf0d5d8-00ff-43b8-8cd6-dc7ebfbf1261))
  (fp_circle (center 0.5035 -2.0059) (end 0.5785 -2.0059)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 5a241a95-b3a1-491a-a79e-b3ba725ff369))
  (fp_circle (center 0.5035 1.9941) (end 0.5785 1.9941)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 89ec86d8-9de1-4a62-af5b-9742b5d82b79))
  (fp_circle (center 0.5039 0.4989) (end 0.5789 0.4989)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp a4ce1456-79ab-4080-a005-0f3056e846f6))
  (fp_circle (center 0.5052 -1.0038) (end 0.5802 -1.0038)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 36891102-0db3-4b67-b71e-9542d9d21a20))
  (fp_circle (center 1.0018 -0.0029) (end 1.0768 -0.0029)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 4abc6735-688c-480c-a4cf-5f4c35c1b314))
  (fp_circle (center 1.002 0.4999) (end 1.077 0.4999)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 0830b485-6fca-461b-86b9-37bdcb215c48))
  (fp_circle (center 1.0032 0.9982) (end 1.0782 0.9982)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp f1d7724a-3ebf-431e-ac5e-acc54478ad51))
  (fp_circle (center 1.0035 -2.0059) (end 1.0785 -2.0059)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 85b4189f-35f8-4d7c-b8de-194de42894ea))
  (fp_circle (center 1.0035 1.9941) (end 1.0785 1.9941)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 1f18db75-11cc-46c3-a615-5a0bfe5662c6))
  (fp_circle (center 1.0052 -1.0038) (end 1.0802 -1.0038)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 76aba746-c3cc-425b-b028-977739943e61))
  (fp_circle (center 1.5035 -2.0059) (end 1.5785 -2.0059)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 96f01a85-fd06-42cf-8514-e32ca35e320b))
  (fp_circle (center 1.5035 1.9941) (end 1.5785 1.9941)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp b28dbc88-2b5a-4ce8-8e39-1406f114f0f9))
  (fp_circle (center 2.0035 -2.0059) (end 2.0785 -2.0059)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 294422f1-3436-461a-bba4-301a1cc70d69))
  (fp_circle (center 2.0035 -1.5022) (end 2.0785 -1.5022)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp f4eb9ab0-eaff-4bf6-953b-80542a3d9817))
  (fp_circle (center 2.0035 -1.0022) (end 2.0785 -1.0022)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 4bf519bc-738f-41b1-b93d-ae8e403c5491))
  (fp_circle (center 2.0035 -0.5022) (end 2.0785 -0.5022)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 9b45d86f-2869-4671-b2e6-32be15bbed8f))
  (fp_circle (center 2.0035 -0.0022) (end 2.0785 -0.0022)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 44f53bbb-4403-443c-958b-d70b5000942f))
  (fp_circle (center 2.0035 0.4978) (end 2.0785 0.4978)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 956920db-34a8-422e-90db-5c55c70bb6ec))
  (fp_circle (center 2.0035 0.9978) (end 2.0785 0.9978)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp e0c766dc-d222-4898-bfd4-299883d5a914))
  (fp_circle (center 2.0035 1.4978) (end 2.0785 1.4978)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 3be72ab1-635a-4592-864a-e29d6b1ac2b3))
  (fp_circle (center 2.0035 1.9941) (end 2.0785 1.9941)
    (stroke (width 0.15) (type solid)) (fill solid) (layer "F.Mask") (tstamp 631d4ee1-f472-4050-b118-41e4865b2d31))
  (fp_line (start -2.413 -2.413) (end -2.413 2.413)
    (stroke (width 0.0508) (type solid)) (layer "F.CrtYd") (tstamp dde994e1-19c4-4473-b137-8f885cc15984))
  (fp_line (start -2.413 2.413) (end 2.413 2.413)
    (stroke (width 0.0508) (type solid)) (layer "F.CrtYd") (tstamp 6e9eefd6-569b-4667-ac5f-f9b46d284d0c))
  (fp_line (start 2.413 -2.413) (end -2.413 -2.413)
    (stroke (width 0.0508) (type solid)) (layer "F.CrtYd") (tstamp 48af237b-5dd5-428b-8786-fdfb78ef4b85))
  (fp_line (start 2.413 2.413) (end 2.413 -2.413)
    (stroke (width 0.0508) (type solid)) (layer "F.CrtYd") (tstamp 20fb4555-48bb-4bea-8fc9-b268e2d1f4a6))
  (fp_line (start -2.246 -2.254) (end -2.246 2.246)
    (stroke (width 0.05) (type solid)) (layer "F.Fab") (tstamp 56b454ef-1cea-4aef-8782-893c0985016e))
  (fp_line (start -2.246 2.246) (end 2.254 2.246)
    (stroke (width 0.05) (type solid)) (layer "F.Fab") (tstamp cf5b2313-03fb-4f8d-8d33-3cd78c2a7c77))
  (fp_line (start 2.254 -2.254) (end -2.246 -2.254)
    (stroke (width 0.05) (type solid)) (layer "F.Fab") (tstamp 0b9fdd02-a71a-438c-a20d-2fead52cad73))
  (fp_line (start 2.254 2.246) (end 2.254 -2.254)
    (stroke (width 0.05) (type solid)) (layer "F.Fab") (tstamp fe0c31bd-0c8b-4213-b431-337608941566))
  (pad "1" smd roundrect (at -1.996 -2.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp fd6f2537-8a6d-421b-9ed2-527b71a6baf2)
  )
  (pad "2" smd roundrect (at -1.496 -2.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp cb6938ba-de84-41d6-a447-e8736a5eeb11)
  )
  (pad "3" smd roundrect (at -0.996 -2.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp be85b150-2cee-4dbe-bf90-bd86eb4698b5)
  )
  (pad "4" smd roundrect (at -0.496 -2.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp f9b77d59-f27f-4e93-a2b9-17da540992c6)
  )
  (pad "5" smd roundrect (at 0.004 -2.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 7be67da3-0146-4a8f-8e41-f130080b338e)
  )
  (pad "6" smd roundrect (at 0.504 -2.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 03ab869b-129d-44bc-b096-d382c8d317ad)
  )
  (pad "7" smd roundrect (at 1.004 -2.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 4a9c2c85-9e53-47c3-98af-38582b90bfd4)
  )
  (pad "8" smd roundrect (at 1.504 -2.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 768ab4e4-114b-48ab-8acb-95ca1c23e9f5)
  )
  (pad "9" smd roundrect (at 2.004 -2.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp ebdd7f65-4f5f-44ab-b10a-29d83d5177ae)
  )
  (pad "10" smd roundrect (at 2.004 -1.504) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 1bfa92a8-d0e8-4006-a308-e117f67746cc)
  )
  (pad "11" smd roundrect (at 2.004 -1.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 3500347f-8106-4644-a85e-6c1ac5fa4f01)
  )
  (pad "12" smd roundrect (at 2.004 -0.504) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b6d4d029-02bd-4f41-b995-783ad26f1876)
  )
  (pad "13" smd roundrect (at 2.004 -0.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b4d2a515-bc3a-4c47-b40d-ea16a90825c1)
  )
  (pad "14" smd roundrect (at 2.004 0.496) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b4fc8834-45cb-4a54-a803-7e947c5f6834)
  )
  (pad "15" smd roundrect (at 2.004 0.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 3eb865be-ba07-4399-b633-4a06738f4466)
  )
  (pad "16" smd roundrect (at 2.004 1.496) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp a8e8aceb-6114-4693-812d-8e8dcd95c444)
  )
  (pad "17" smd roundrect (at 2.004 1.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 4dd7c807-8b2c-4d60-bf3b-2fee28992084)
  )
  (pad "18" smd roundrect (at 1.504 1.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 8640202d-f93f-41f2-9bcb-6a682ebaae1d)
  )
  (pad "19" smd roundrect (at 1.004 1.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 83667505-c7bf-4544-a180-be140d688faa)
  )
  (pad "20" smd roundrect (at 0.504 1.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp dcb624db-9c00-42b4-b12e-671a19522778)
  )
  (pad "21" smd roundrect (at 0.004 1.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp df308bd2-70e7-4dfe-a7f9-4869890f0d86)
  )
  (pad "22" smd roundrect (at -0.496 1.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp cfe2cf73-0803-4e70-b758-9dd55d221223)
  )
  (pad "23" smd roundrect (at -0.996 1.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 9733d496-0c71-41f7-84d4-686ac7e74ea2)
  )
  (pad "24" smd roundrect (at -1.496 1.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b28eabff-3d4e-49f0-86ac-2a19be1e3b73)
  )
  (pad "25" smd roundrect (at -1.996 1.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 56ad5e77-2344-40b9-8f5f-f5e8b42a9e62)
  )
  (pad "26" smd roundrect (at -1.996 1.496) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 05ae88bc-7a1a-4b8c-a4fd-2f95730d2c26)
  )
  (pad "27" smd roundrect (at -1.996 0.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 1eb79ba8-f87d-430e-b39c-f2a0469a576b)
  )
  (pad "28" smd roundrect (at -1.996 0.496) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 0e9e6b35-6bbc-48c8-b322-983f98f5ac8e)
  )
  (pad "29" smd roundrect (at -1.996 -0.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp a4db646a-3f54-4382-aae4-73ef928405e4)
  )
  (pad "30" smd roundrect (at -1.996 -0.504) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 0bff2109-f904-410d-a849-8ec82f906ccd)
  )
  (pad "31" smd roundrect (at -1.996 -1.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 40d3a71a-2f25-4d3a-a824-3f5db356b1f3)
  )
  (pad "32" smd roundrect (at -1.996 -1.504) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp f97cf7cf-0655-44d4-b0c8-540d33663406)
  )
  (pad "33" smd roundrect (at -0.996 -1.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp a17cc71e-a19e-46a2-ba6d-0fa08b8f628b)
  )
  (pad "34" smd roundrect (at -0.496 -1.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp f898bce5-2eee-42fc-9fd3-ef393d49b9e3)
  )
  (pad "35" smd roundrect (at 0.004 -1.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp fc2a6d81-3d1e-4ae8-8a56-fc0f532cffbc)
  )
  (pad "36" smd roundrect (at 0.504 -1.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp efe41700-399a-4c2c-8bb0-b4cc03ec82cb)
  )
  (pad "37" smd roundrect (at 1.004 -1.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 0a26080e-f532-4515-ad54-77f5b44e93e7)
  )
  (pad "38" smd roundrect (at 1.004 -0.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 6203cbfa-c266-42dd-ad09-e5c956e4f228)
  )
  (pad "39" smd roundrect (at 1.004 0.496) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 5e2a40e2-7dd5-4157-aeaf-c46317b26549)
  )
  (pad "40" smd roundrect (at 1.004 0.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 49328b30-0d99-42be-b520-aa533836d7e4)
  )
  (pad "41" smd roundrect (at 0.504 0.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 6701c8d1-ad22-4dfb-a8ed-907b2065f4b4)
  )
  (pad "42" smd roundrect (at 0.004 0.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp fafea5a8-a8fa-42c9-8660-a0af60d194a3)
  )
  (pad "43" smd roundrect (at -0.496 0.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b5e129b9-68fe-43e9-a50d-bcbeefaf743d)
  )
  (pad "44" smd roundrect (at -0.996 0.996) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp c0639e6c-bd0f-4faf-a9bc-7852d85813f7)
  )
  (pad "45" smd roundrect (at -0.996 0.496) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp c161ed23-8c45-4444-968d-a9d8ffed9fd3)
  )
  (pad "46" smd roundrect (at -0.996 -0.004) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 8a29d3dd-1108-446b-90ce-c4ea5d8db1c8)
  )
  (pad "47" smd roundrect (at -0.996 -0.504) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 365715b4-ceec-465e-971c-5744701208c6)
  )
  (pad "48" smd roundrect (at -0.496 -0.504) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 3b42ee35-767d-4d81-b530-41e399e3688e)
  )
  (pad "49" smd roundrect (at 0.504 -0.504) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 16255069-d36a-4980-ac32-7cdc97d28055)
  )
  (pad "50" smd roundrect (at 0.504 0.496) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp e93e9f50-8925-49ce-963e-3b92c5e45812)
  )
  (pad "51" smd roundrect (at -0.496 0.496) (size 0.25 0.25) (layers "F.Cu" "F.Paste") (roundrect_rratio 0.5)
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 0eebf0db-bd45-4c7f-b5eb-2deded1224f9)
  )
)
