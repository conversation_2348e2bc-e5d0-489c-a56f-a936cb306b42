(kicad_sch (version 20200828) (generator eeschema)

  (page 4 4)

  (paper "A4")

  (lib_symbols
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254)) (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )


  (bus_entry (at 147.32 67.31) (size 2.54 2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 147.32 77.47) (size 2.54 2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 147.32 92.71) (size 2.54 2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 147.32 102.87) (size 2.54 2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )

  (wire (pts (xy 149.86 69.85) (xy 165.1 69.85))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 149.86 80.01) (xy 165.1 80.01))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 149.86 95.25) (xy 165.1 95.25))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 149.86 105.41) (xy 165.1 105.41))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 165.1 69.85) (xy 165.1 71.12))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 165.1 78.74) (xy 165.1 80.01))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 165.1 95.25) (xy 165.1 96.52))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 165.1 104.14) (xy 165.1 105.41))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 128.27 64.77) (xy 147.32 64.77))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 128.27 90.17) (xy 147.32 90.17))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 147.32 64.77) (xy 147.32 67.31))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 147.32 67.31) (xy 147.32 77.47))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 147.32 77.47) (xy 147.32 85.09))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 147.32 90.17) (xy 147.32 92.71))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 147.32 92.71) (xy 147.32 102.87))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 147.32 102.87) (xy 147.32 110.49))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )

  (label "A1" (at 152.4 69.85 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "A2" (at 152.4 80.01 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "D1" (at 152.4 95.25 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "D2" (at 152.4 105.41 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )

  (hierarchical_label "A[10..0]" (shape input) (at 128.27 64.77 180)
    (effects (font (size 1.27 1.27)) (justify right))
  )
  (hierarchical_label "D[10..0]" (shape input) (at 128.27 90.17 180)
    (effects (font (size 1.27 1.27)) (justify right))
  )

  (symbol (lib_id "Device:R") (at 165.1 74.93 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid "a8053649-8fd9-4481-b56e-122d712ddeb5")
    (property "Reference" "R3" (id 0) (at 166.878 73.787 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 166.8781 76.0793 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 163.322 74.93 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 165.1 74.93 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )

  (symbol (lib_id "Device:R") (at 165.1 100.33 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid "2eeee84e-974a-4768-b3a0-fdfaed463e15")
    (property "Reference" "R4" (id 0) (at 166.878 99.187 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 166.8781 101.4793 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 163.322 100.33 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 165.1 100.33 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )
)
