(kicad_pcb (version 20200512) (host pcbnew "(5.99.0-1700-gd72a778f1)")

  (general
    (thickness 1.6)
    (drawings 4)
    (tracks 14)
    (modules 2)
    (nets 4)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user)
    (33 "<PERSON><PERSON>Ad<PERSON>" user)
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user)
    (37 "F.<PERSON>" user)
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user)
    (41 "Cmts.User" user)
    (42 "Eco1.User" user)
    (43 "Eco2.User" user)
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user)
    (47 "F.CrtYd" user)
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (color "Green") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "core") (thickness 1.51) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (color "Green") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "None")
      (dielectric_constraints no)
    )
    (last_trace_width 0.25)
    (trace_clearance 0.2)
    (zone_clearance 0.508)
    (zone_45_only no)
    (trace_min 0.2)
    (clearance_min 0.15)
    (via_min_annulus 0.05)
    (via_min_size 0.4)
    (through_hole_min 0.3)
    (via_size 0.8)
    (via_drill 0.4)
    (uvia_size 0.3)
    (uvia_drill 0.1)
    (uvias_allowed no)
    (uvia_min_size 0.2)
    (uvia_min_drill 0.1)
    (max_error 0.005)
    (defaults
      (edge_clearance 0.01)
      (edge_cuts_line_width 0.05)
      (courtyard_line_width 0.05)
      (copper_line_width 0.2)
      (copper_text_dims (size 1.5 1.5) (thickness 0.3))
      (silk_line_width 0.12)
      (silk_text_dims (size 1 1) (thickness 0.15))
      (fab_layers_line_width 0.1)
      (fab_layers_text_dims (size 1 1) (thickness 0.15))
      (other_layers_line_width 0.1)
      (other_layers_text_dims (size 1 1) (thickness 0.15))
      (dimension_units 0)
      (dimension_precision 1)
    )
    (pad_size 1.524 1.524)
    (pad_drill 0.762)
    (pad_to_mask_clearance 0.05)
    (aux_axis_origin 0 0)
    (visible_elements 7FFFFFFF)
    (pcbplotparams
      (layerselection 0x010fc_ffffffff)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (svguseinch false)
      (svgprecision 6)
      (excludeedgelayer true)
      (linewidth 0.100000)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "Net-(J1-Pad1)")
  (net 2 "Net-(J1-Pad2)")
  (net 3 "Net-(J1-Pad3)")

  (net_class "Default" "This is the default net class."
    (clearance 0.2)
    (trace_width 0.25)
    (via_dia 0.8)
    (via_drill 0.4)
    (uvia_dia 0.3)
    (uvia_drill 0.1)
    (add_net "Net-(J1-Pad1)")
    (add_net "Net-(J1-Pad2)")
    (add_net "Net-(J1-Pad3)")
  )

  (module "Resistors_SMD:R_0805" (layer "B.Cu") (tedit 58E0A804) (tstamp 3461ae3d-9a09-475a-b78b-3f772cd40bd8)
    (at 120.6246 66.2432 -90)
    (descr "Resistor SMD 0805, reflow soldering, Vishay (see dcrcw.pdf)")
    (tags "resistor 0805")
    (path "/c6c78989-e8f2-4abf-a6f7-7545bc8c1e66")
    (attr smd)
    (fp_text reference "R1" (at 0 1.65 -90) (layer "B.SilkS")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (fp_text value "R" (at 0 -1.75 -90) (layer "B.Fab")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (fp_text user "${REFERENCE}" (at 0 0 -90) (layer "B.Fab")
      (effects (font (size 0.5 0.5) (thickness 0.075)) (justify mirror))
    )
    (fp_line (start -1 -0.62) (end -1 0.62) (layer "B.Fab") (width 0.1))
    (fp_line (start 1 -0.62) (end -1 -0.62) (layer "B.Fab") (width 0.1))
    (fp_line (start 1 0.62) (end 1 -0.62) (layer "B.Fab") (width 0.1))
    (fp_line (start -1 0.62) (end 1 0.62) (layer "B.Fab") (width 0.1))
    (fp_line (start 0.6 -0.88) (end -0.6 -0.88) (layer "B.SilkS") (width 0.12))
    (fp_line (start -0.6 0.88) (end 0.6 0.88) (layer "B.SilkS") (width 0.12))
    (fp_line (start -1.55 0.9) (end 1.55 0.9) (layer "B.CrtYd") (width 0.05))
    (fp_line (start -1.55 0.9) (end -1.55 -0.9) (layer "B.CrtYd") (width 0.05))
    (fp_line (start 1.55 -0.9) (end 1.55 0.9) (layer "B.CrtYd") (width 0.05))
    (fp_line (start 1.55 -0.9) (end -1.55 -0.9) (layer "B.CrtYd") (width 0.05))
    (pad "1" smd rect (at -0.95 0 270) (size 0.7 1.3) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 2 "Net-(J1-Pad2)") (tstamp 90f73341-fd35-4de5-811f-b9b78b7d229a))
    (pad "2" smd rect (at 0.95 0 270) (size 0.7 1.3) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 1 "Net-(J1-Pad1)") (tstamp 47920df1-0b43-4346-93df-a8b91e0462ad))
    (model "${KISYS3DMOD}/Resistors_SMD.3dshapes/R_0805.wrl"
      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (module "Connectors_JST:JST_SH_SM03B-SRSS-TB_03x1.00mm_Angled" (layer "F.Cu") (tedit 56B07436) (tstamp f857536b-311d-42f4-b594-3442deba239a)
    (at 129.4384 68.7324 45)
    (descr "http://www.jst-mfg.com/product/pdf/eng/eSH.pdf")
    (tags "connector jst sh")
    (path "/c9eee9ba-3bcb-475c-9e6f-0d52e9a7040e")
    (attr smd)
    (fp_text reference "J1" (at -1 -4 45) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value "Conn_01x03_MountingPin" (at 0 4.5 45) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_circle (center -2 -2.1875) (end -1.75 -2.1875) (layer "F.SilkS") (width 0.12))
    (fp_line (start -1.4 2.6375) (end 1.4 2.6375) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2.5 0.7375) (end -2.5 -1.6125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2.5 -1.6125) (end -1.6 -1.6125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2 -1.6125) (end -2 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2 -0.4125) (end -2 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2 -0.4125) (end -2 -1.6125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2 -1.6125) (end -2 -1.6125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2 -1.1125) (end -2 -1.1125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2 -1.1125) (end -2.5 -1.1125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2.5 -1.1125) (end -2.5 -1.1125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2.5 -1.1125) (end -2 -1.1125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2 -0.4125) (end -2 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2 -0.4125) (end -2.5 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2.5 -0.4125) (end -2.5 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -2.5 -0.4125) (end -2 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2.5 0.7375) (end 2.5 -1.6125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2.5 -1.6125) (end 1.6 -1.6125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2 -1.6125) (end 2 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2 -0.4125) (end 2 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2 -0.4125) (end 2 -1.6125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2 -1.6125) (end 2 -1.6125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2 -1.1125) (end 2 -1.1125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2 -1.1125) (end 2.5 -1.1125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2.5 -1.1125) (end 2.5 -1.1125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2.5 -1.1125) (end 2 -1.1125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2 -0.4125) (end 2 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2 -0.4125) (end 2.5 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2.5 -0.4125) (end 2.5 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start 2.5 -0.4125) (end 2 -0.4125) (layer "F.SilkS") (width 0.12))
    (fp_line (start -3.4 3.35) (end -3.4 -3.25) (layer "F.CrtYd") (width 0.05))
    (fp_line (start -3.4 -3.25) (end 3.4 -3.25) (layer "F.CrtYd") (width 0.05))
    (fp_line (start 3.4 -3.25) (end 3.4 3.35) (layer "F.CrtYd") (width 0.05))
    (fp_line (start 3.4 3.35) (end -3.4 3.35) (layer "F.CrtYd") (width 0.05))
    (pad "1" smd rect (at -1 -1.9375 45) (size 0.6 1.55) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 1 "Net-(J1-Pad1)") (pinfunction "Pin_1") (tstamp 099c08a9-a815-4ce0-9362-538d726d9c02))
    (pad "2" smd rect (at 0 -1.9375 45) (size 0.6 1.55) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 2 "Net-(J1-Pad2)") (pinfunction "Pin_2") (tstamp 1cf0402d-16ac-4394-a2bd-5110d5319b10))
    (pad "3" smd rect (at 1 -1.9375 45) (size 0.6 1.55) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 3 "Net-(J1-Pad3)") (pinfunction "Pin_3") (tstamp d787d991-8b10-43a9-bda8-a84559d3dc5e))
    (pad "" smd rect (at -2.3 1.9375 45) (size 1.2 1.8) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp cbfaecaf-5ba8-4cbb-9cfa-cbddd3860580))
    (pad "" smd rect (at 2.3 1.9375 45) (size 1.2 1.8) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp e42e1bc3-504c-4630-a5aa-7fd2d9ffd69c))
  )

  (gr_line (start 117.3988 76.708) (end 117.221 61.4934) (layer "Edge.Cuts") (width 0.05) (tstamp a78f509c-e1b9-49ba-b3bc-521279981534))
  (gr_line (start 144.526 76.7334) (end 117.3988 76.708) (layer "Edge.Cuts") (width 0.05) (tstamp 1af6783c-0a42-45b6-abd7-0e08b693db24))
  (gr_line (start 144.4752 61.4934) (end 144.526 76.7334) (layer "Edge.Cuts") (width 0.05) (tstamp 960d3bc1-a0ab-4fa3-a303-ae1d5a790b4e))
  (gr_line (start 117.221 61.4934) (end 144.4752 61.4934) (layer "Edge.Cuts") (width 0.05) (tstamp cad22098-0b03-4042-9a5c-03067b33df4d))

  (via (at 124.46 67.183) (size 0.8) (drill 0.4) (layers "F.Cu" "B.Cu") (net 1) (tstamp 7b7eb598-0a34-4575-82d5-4d2df1fe2e26))
  (segment (start 124.46 67.183) (end 120.6348 67.183) (width 0.25) (layer "B.Cu") (net 1) (tstamp b716ea7e-5437-4b8d-8adf-5979da4797b9))
  (segment (start 124.4702 67.1932) (end 124.46 67.183) (width 0.25) (layer "F.Cu") (net 1) (tstamp 1f4cc5f0-d4f2-4f12-a730-002967d6caba))
  (segment (start 126.484987 67.1932) (end 124.4702 67.1932) (width 0.25) (layer "F.Cu") (net 1) (tstamp ac60baa0-d37f-46b7-9bfd-03e838b95ff8))
  (segment (start 120.6348 67.183) (end 120.6246 67.1932) (width 0.25) (layer "B.Cu") (net 1) (tstamp 053efa3b-b9a1-4cf2-81fd-d147958ee55d))
  (segment (start 122.6972 65.2932) (end 122.7124 65.278) (width 0.25) (layer "B.Cu") (net 2) (tstamp 8c3c3ad4-2e40-4f60-8fe5-e54ba27216a3))
  (segment (start 120.6246 65.2932) (end 122.6972 65.2932) (width 0.25) (layer "B.Cu") (net 2) (tstamp 55d4b264-ae4c-4659-850b-136c1131f417))
  (segment (start 122.809 65.278) (end 125.984 65.278) (width 0.25) (layer "F.Cu") (net 2) (tstamp 1b5d08ed-ff43-4c82-838f-e6a90263c9e3))
  (segment (start 122.7124 65.278) (end 122.809 65.278) (width 0.25) (layer "B.Cu") (net 2) (tstamp b5e28421-7f90-4270-b58e-80ea4c4ea4a7))
  (via (at 122.809 65.278) (size 0.8) (drill 0.4) (layers "F.Cu" "B.Cu") (net 2) (tstamp 72763d28-3414-4cb5-bb01-a5c4893d09aa))
  (segment (start 125.984 65.278) (end 125.9992 65.2932) (width 0.25) (layer "F.Cu") (net 2) (tstamp 031e06b2-25e2-4767-9f44-bb8f03d23c74))
  (segment (start 126.4768 67.1932) (end 126.484987 67.1932) (width 0.25) (layer "F.Cu") (net 1) (tstamp a8f164c7-3d2a-426f-800b-6816271944cc))
  (segment (start 126.484987 67.1932) (end 127.361274 68.069487) (width 0.25) (layer "F.Cu") (net 1) (tstamp 59eb23d6-d62d-41f2-906e-00448418d1b2))
  (segment (start 125.9992 65.2932) (end 127.5842 66.8782) (width 0.25) (layer "F.Cu") (net 2) (tstamp 998f5b2f-974e-461a-af95-91a3471df90b))

)
