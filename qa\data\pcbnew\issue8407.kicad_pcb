(kicad_pcb (version 20210722) (generator pcbnew)

  (general
    (thickness 4.48)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (1 "In1.Cu" power)
    (2 "In2.Cu" power)
    (31 "B.Cu" signal)
    (32 "<PERSON><PERSON><PERSON>" user "B.Adhesive")
    (33 "<PERSON><PERSON>" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.<PERSON>" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (color "Green") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "core") (thickness 1.44) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In1.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 2" (type "prepreg") (thickness 1.44) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In2.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 3" (type "core") (thickness 1.44) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (color "Green") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "None")
      (dielectric_constraints no)
    )
    (pad_to_mask_clearance 0.05)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (svguseinch false)
      (svgprecision 6)
      (excludeedgelayer true)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "gerber")
    )
  )

  (net 0 "")
  (net 1 "GND")
  (net 2 "+12V")
  (net 3 "USER2_L")
  (net 4 "USER3_L")
  (net 5 "CLK_P")
  (net 6 "CLK_N")
  (net 7 "TRIG_P")
  (net 8 "TRIG_N")
  (net 9 "TIMES_P")
  (net 10 "TIMES_N")
  (net 11 "USER1_P")
  (net 12 "USER1_N")
  (net 13 "/MP")
  (net 14 "PEG+12Vo")
  (net 15 "/12Vs")
  (net 16 "/12Vg")
  (net 17 "unconnected-(J10-Pad5)")

  (footprint "BoardIO:Molex_Mini-Fit_Jr_5569-06A1_2x03_P4.20mm_Horizontal" (layer "F.Cu")
    (tedit 60B7A0D6) (tstamp 00000000-0000-0000-0000-00005f8779ed)
    (at 104.648 -92.695 -90)
    (descr "Molex Mini-Fit Jr. Power Connectors, old mpn/engineering number: 5569-06A1, example for new mpn: 39-29-4069, 3 Pins per row, Mounting: PCB Mounting Flange (http://www.molex.com/pdm_docs/sd/039291047_sd.pdf), generated with kicad-footprint-generator")
    (tags "connector Molex Mini-Fit_Jr top entryscrew_flange")
    (property "P/N" "Molex 3929-1067")
    (property "Rating" "600V")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (path "/00000000-0000-0000-0000-00005fa6b57d")
    (fp_text reference "J10" (at 4.2 -15.1 -90) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f9150c7f-0cab-4056-a3ad-f0077aeb48bc)
    )
    (fp_text value "DC PWR IN" (at 4.2 8.55 -90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 8e4fa81f-760e-4887-9550-968f7358c2a5)
    )
    (fp_text user "${REFERENCE}" (at 4.2 -13.2 -90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 2811e306-8059-4de4-b9fc-740b3396d56e)
    )
    (fp_line (start 16.21 -0.99) (end 10.4 -0.99) (layer "F.SilkS") (width 0.12) (tstamp 0f655924-3c84-4d33-8045-96b02e7ccb99))
    (fp_line (start 8.7 2.11) (end 8.7 3.39) (layer "F.SilkS") (width 0.12) (tstamp 1468f825-57d8-4fc0-a8c5-c6847eb72b04))
    (fp_line (start -2 0) (end -2.6 0.3) (layer "F.SilkS") (width 0.12) (tstamp 1f4fd902-8c1a-4932-bd37-127a8bbf17f8))
    (fp_line (start 1.61 -1) (end 2.59 -1) (layer "F.SilkS") (width 0.12) (tstamp 22e61398-3718-40e3-a915-44ca332217af))
    (fp_line (start 4.2 -14.01) (end -2.81 -14.01) (layer "F.SilkS") (width 0.12) (tstamp 2f1909c4-50d1-4327-b1fe-2b1bac70e62d))
    (fp_line (start 8.1 2.11) (end 8.1 3.39) (layer "F.SilkS") (width 0.12) (tstamp 5d02c555-eb23-4bfc-aaf1-18bd8e183078))
    (fp_line (start -2.81 -7.41) (end -7.81 -7.41) (layer "F.SilkS") (width 0.12) (tstamp 65f52b4f-f08d-446b-8695-da9455319234))
    (fp_line (start 0.3 2.11) (end 0.3 3.39) (layer "F.SilkS") (width 0.12) (tstamp 69b95623-97ca-4b89-af39-11027398391f))
    (fp_line (start -0.3 2.11) (end -0.3 3.39) (layer "F.SilkS") (width 0.12) (tstamp 6d66031a-17f2-4dc4-bdf0-303383cae000))
    (fp_line (start 4.5 2.11) (end 4.5 3.39) (layer "F.SilkS") (width 0.12) (tstamp 78e04572-89ea-46d4-b67d-d1a4f2bd6919))
    (fp_line (start 11.21 -7.41) (end 16.21 -7.41) (layer "F.SilkS") (width 0.12) (tstamp 908c0456-b3f4-44fc-8460-9f552175c076))
    (fp_line (start 3.9 2.11) (end 3.9 3.39) (layer "F.SilkS") (width 0.12) (tstamp 96a28fa7-3ba8-4bf7-8687-f4067bb14dcd))
    (fp_line (start -2.81 -14.01) (end -2.81 -7.41) (layer "F.SilkS") (width 0.12) (tstamp a0317497-f037-496f-92e9-3bc6340413f5))
    (fp_line (start 4.2 -14.01) (end 11.21 -14.01) (layer "F.SilkS") (width 0.12) (tstamp a2650778-41f5-42d8-9c83-b32400c80e2e))
    (fp_line (start -7.81 -0.99) (end -2 -0.99) (layer "F.SilkS") (width 0.12) (tstamp b3ec413f-19ea-4b55-8a35-3b04e715831b))
    (fp_line (start -7.81 -7.41) (end -7.81 -0.99) (layer "F.SilkS") (width 0.12) (tstamp d478118a-df38-4fb9-bc90-06ca485b008f))
    (fp_line (start 5.81 -1) (end 6.79 -1) (layer "F.SilkS") (width 0.12) (tstamp e9537094-e506-410d-add8-88d27e3b62a7))
    (fp_line (start -2.6 -0.3) (end -2 0) (layer "F.SilkS") (width 0.12) (tstamp eb16a8ac-6e6b-4048-b014-b53a8beedd38))
    (fp_line (start 16.21 -7.41) (end 16.21 -0.99) (layer "F.SilkS") (width 0.12) (tstamp f4fd9730-cb4f-4ba6-9c27-7f34f68bfac9))
    (fp_line (start 11.21 -14.01) (end 11.21 -7.41) (layer "F.SilkS") (width 0.12) (tstamp f68b2d74-12c2-4be0-9e89-277e5fe95490))
    (fp_line (start -2.6 0.3) (end -2.6 -0.3) (layer "F.SilkS") (width 0.12) (tstamp f68de94a-dafe-4836-b73f-92226e392a1c))
    (fp_line (start -1.85 -0.6) (end -1.85 7.85) (layer "F.CrtYd") (width 0.05) (tstamp 09a25d49-b4e6-4fd7-ad59-9e4f9bd9a5ec))
    (fp_line (start 11.6 -7.8) (end 16.6 -7.8) (layer "F.CrtYd") (width 0.05) (tstamp 20541a28-222d-4a66-9af2-646d48ee7cc5))
    (fp_line (start 16.6 -0.6) (end 10.25 -0.6) (layer "F.CrtYd") (width 0.05) (tstamp 43b89501-2cbe-4c2b-8954-d53f093a0459))
    (fp_line (start 4.2 -14.4) (end -3.2 -14.4) (layer "F.CrtYd") (width 0.05) (tstamp 50670b3b-8b44-4c25-9075-3c2a26caeb50))
    (fp_line (start -8.2 -7.8) (end -8.2 -0.6) (layer "F.CrtYd") (width 0.05) (tstamp 55852c54-32f9-4fa9-be78-c75bddbc466d))
    (fp_line (start 16.6 -7.8) (end 16.6 -0.6) (layer "F.CrtYd") (width 0.05) (tstamp 6aa37e5e-5de3-44c0-9f10-4459a80e40ce))
    (fp_line (start 11.6 -14.4) (end 11.6 -7.8) (layer "F.CrtYd") (width 0.05) (tstamp 6f91cabd-d21e-4600-8687-8510e889ccb8))
    (fp_line (start -3.2 -7.8) (end -8.2 -7.8) (layer "F.CrtYd") (width 0.05) (tstamp 7bc381ba-f2a8-4192-be4f-65e5085d4651))
    (fp_line (start -3.2 -14.4) (end -3.2 -7.8) (layer "F.CrtYd") (width 0.05) (tstamp 91c7caf7-114d-425b-b83f-6be791ae7756))
    (fp_line (start -8.2 -0.6) (end -1.85 -0.6) (layer "F.CrtYd") (width 0.05) (tstamp 923eaba1-27fc-408b-b136-e0244c849760))
    (fp_line (start 10.25 -0.6) (end 10.25 7.85) (layer "F.CrtYd") (width 0.05) (tstamp bd4dd6f9-d695-4414-809c-b177dc1fd149))
    (fp_line (start 4.2 -14.4) (end 11.6 -14.4) (layer "F.CrtYd") (width 0.05) (tstamp ecdc9e59-451d-433d-a3c4-759d40cd0cd3))
    (fp_line (start 10.25 7.85) (end 4.2 7.85) (layer "F.CrtYd") (width 0.05) (tstamp ee874d5d-1982-42bb-ad4b-2ac145bb3935))
    (fp_line (start -1.85 7.85) (end 4.2 7.85) (layer "F.CrtYd") (width 0.05) (tstamp f32ca7e8-c1aa-4c21-9280-93fa714c827d))
    (fp_line (start 16.1 -1.1) (end 11.1 -1.1) (layer "F.Fab") (width 0.1) (tstamp 050e939c-582f-435e-9860-1eaadddd8b6f))
    (fp_line (start -7.7 -7.3) (end -7.7 -1.1) (layer "F.Fab") (width 0.1) (tstamp 27e0bda4-47d5-4702-8cdc-073dc817fbf1))
    (fp_line (start -7.7 -1.1) (end -2.7 -1.1) (layer "F.Fab") (width 0.1) (tstamp 4db98990-c85c-4e65-9b85-516d2d222cda))
    (fp_line (start 0 -2.514214) (end 1 -1.1) (layer "F.Fab") (width 0.1) (tstamp 5430b748-f3fe-4e29-bae0-52bf5ff37988))
    (fp_line (start -1 -1.1) (end 0 -2.514214) (layer "F.Fab") (width 0.1) (tstamp 6537abde-1f77-4647-bf37-170f62b0aaf2))
    (fp_line (start 11.1 -7.3) (end 16.1 -7.3) (layer "F.Fab") (width 0.1) (tstamp 977ab819-581c-49c6-90cc-c8fc69ec0cd0))
    (fp_line (start -2.7 -1.1) (end 11.1 -1.1) (layer "F.Fab") (width 0.1) (tstamp a183641c-c723-41b2-96bd-d06135a53e50))
    (fp_line (start 11.1 -1.1) (end 11.1 -13.9) (layer "F.Fab") (width 0.1) (tstamp bc3cf841-b32d-45f7-a0a4-475b74ab47ee))
    (fp_line (start 11.1 -13.9) (end -2.7 -13.9) (layer "F.Fab") (width 0.1) (tstamp dbeced95-7703-4eed-95f3-83848a05d9a2))
    (fp_line (start 16.1 -7.3) (end 16.1 -1.1) (layer "F.Fab") (width 0.1) (tstamp e6a68da1-39c4-4c3e-8793-e415ed6b1730))
    (fp_line (start -2.7 -7.3) (end -7.7 -7.3) (layer "F.Fab") (width 0.1) (tstamp f7e2f5f3-01aa-4edf-81c0-d5b535e90203))
    (fp_line (start -2.7 -13.9) (end -2.7 -1.1) (layer "F.Fab") (width 0.1) (tstamp fc0bd6ae-c0f1-45bf-a701-32bf715ce875))
    (pad "" np_thru_hole circle locked (at -4.5 -4.2 270) (size 3.2 3.2) (drill 3.2) (layers *.Cu *.Mask) (tstamp 31e7f00b-0078-410e-bff3-7e4994545176))
    (pad "" np_thru_hole circle locked (at 12.9 -4.2 270) (size 3.2 3.2) (drill 3.2) (layers *.Cu *.Mask) (tstamp cd9feb68-34fa-4e70-bfc7-f0d87e8efd9d))
    (pad "1" thru_hole roundrect locked (at 0 0 270) (size 2.7 3.7) (drill 1.8) (layers *.Cu *.Mask) (roundrect_rratio 0.092593)
      (net 14 "PEG+12Vo") (pinfunction "Pin_1") (tstamp ccad2c07-c5f4-4c23-9f67-2997ac4ea0c1))
    (pad "2" thru_hole oval locked (at 4.2 0 270) (size 2.7 3.7) (drill 1.8) (layers *.Cu *.Mask)
      (net 14 "PEG+12Vo") (pinfunction "Pin_2") (tstamp cfae497b-f893-43b6-81eb-8b827be9e757))
    (pad "3" thru_hole oval locked (at 8.4 0 270) (size 2.7 3.7) (drill 1.8) (layers *.Cu *.Mask)
      (net 14 "PEG+12Vo") (pinfunction "Pin_3") (tstamp 788ec48a-76ae-418d-93f3-e9bc21c61240))
    (pad "4" thru_hole oval locked (at 0 5.5 270) (size 2.7 3.7) (drill 1.8) (layers *.Cu *.Mask)
      (net 1 "GND") (pinfunction "Pin_4") (tstamp 0bd7d7f2-ae52-44dd-8ee0-af8229ae10e3))
    (pad "5" thru_hole oval locked (at 4.2 5.5 270) (size 2.7 3.7) (drill 1.8) (layers *.Cu *.Mask)
      (net 17 "unconnected-(J10-Pad5)") (pinfunction "Pin_5") (tstamp e3f47535-ae1c-4a6e-bb8e-e3cdc3b073c7))
    (pad "6" thru_hole oval locked (at 8.4 5.5 270) (size 2.7 3.7) (drill 1.8) (layers *.Cu *.Mask)
      (net 1 "GND") (pinfunction "Pin_6") (tstamp 9a105b1f-ca75-4bf9-8e07-3496bbe60b10))
    (model "${KIPRJMOD}/BoardIO.pretty/39291067.wrl"
      (offset (xyz 4.2 13.8 0))
      (scale (xyz 1 1 1))
      (rotate (xyz -90 0 180))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tedit 5F68FEEE) (tstamp 00000000-0000-0000-0000-00005f8879db)
    (at 10.668 -5.8675 90)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "P/N" "Yageo RC0603FR-070RL")
    (property "Rating" "0.1W")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (path "/00000000-0000-0000-0000-00005fbea531")
    (attr smd)
    (fp_text reference "R71" (at -0.0255 2.032) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 15403f99-1a40-468a-97cb-7a61cf3ede22)
    )
    (fp_text value "0" (at 0 1.43 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 446a7d38-de42-4dc3-ac01-8e199235efb0)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
      (effects (font (size 0.4 0.4) (thickness 0.06)))
      (tstamp 961dad8d-717e-40cd-91ed-f3b405cde52d)
    )
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225) (layer "F.SilkS") (width 0.12) (tstamp 1b2c276d-c9d4-4160-856d-dafe8b688cf0))
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225) (layer "F.SilkS") (width 0.12) (tstamp 400a12a2-f7e0-472c-9aff-35f2920b5c91))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73) (layer "F.CrtYd") (width 0.05) (tstamp 2866b6f3-947d-43a3-a547-43be4b975311))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73) (layer "F.CrtYd") (width 0.05) (tstamp 28a39335-d458-4bd6-9aeb-1445b6791a3e))
    (fp_line (start 1.48 0.73) (end -1.48 0.73) (layer "F.CrtYd") (width 0.05) (tstamp aa4e693a-cb3e-43dd-ba47-54d21e3e9a10))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73) (layer "F.CrtYd") (width 0.05) (tstamp d1a65b9a-903b-4aea-97e1-5dba26c03950))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125) (layer "F.Fab") (width 0.1) (tstamp 0f4b3f9c-519b-4fe3-b85e-92386432faba))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125) (layer "F.Fab") (width 0.1) (tstamp 6115cf44-0c20-491e-8e7d-aad40bce99d8))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125) (layer "F.Fab") (width 0.1) (tstamp c1517c87-585a-49b1-9d99-053a44c6ac11))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125) (layer "F.Fab") (width 0.1) (tstamp fecc9914-a3ea-4347-bda7-b286cdeb5d2f))
    (pad "1" smd roundrect locked (at -0.825 0 90) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 13 "/MP") (tstamp 23807275-b254-45f9-8460-aea734c8a6fd))
    (pad "2" smd roundrect locked (at 0.825 0 90) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (tstamp 013a9301-f87c-44b0-bbaa-5485c4df54b0))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tedit 5F68FEEE) (tstamp 00000000-0000-0000-0000-00005f8879ec)
    (at 8.509 -91.7195 90)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "P/N" "Yageo RC0603FR-070RL")
    (property "Rating" "0.1W")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (path "/00000000-0000-0000-0000-00005fbeb5a3")
    (attr smd)
    (fp_text reference "R72" (at -0.0255 1.651 270) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 84c16166-ee17-4e3b-85be-39132daa969f)
    )
    (fp_text value "0" (at 0 1.43 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 8da450dd-fc9d-470b-a211-b6fc7030381c)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
      (effects (font (size 0.4 0.4) (thickness 0.06)))
      (tstamp 73dd13eb-b550-4d49-b2ff-2ad384f0fe06)
    )
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225) (layer "F.SilkS") (width 0.12) (tstamp 37a3df4d-c86b-46de-9d02-e278f7076860))
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225) (layer "F.SilkS") (width 0.12) (tstamp 57057040-315a-4bd3-a47f-edbd3b010ca7))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73) (layer "F.CrtYd") (width 0.05) (tstamp 1526a76c-571a-40b5-b43e-ae87809bb8aa))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73) (layer "F.CrtYd") (width 0.05) (tstamp 36c4a132-e9ee-4dab-96b1-51885c28f2e3))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73) (layer "F.CrtYd") (width 0.05) (tstamp 5639cd3d-35c1-456a-81fe-fce5b4957d9e))
    (fp_line (start 1.48 0.73) (end -1.48 0.73) (layer "F.CrtYd") (width 0.05) (tstamp f6f64a7a-ff9a-46f9-a017-dc9df1447e9a))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125) (layer "F.Fab") (width 0.1) (tstamp 1ab44849-f3a5-4892-b7d1-3cffa2e8e655))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125) (layer "F.Fab") (width 0.1) (tstamp 1fe36d25-e62d-4e6d-b9a0-43c0bc9403b8))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125) (layer "F.Fab") (width 0.1) (tstamp 8a6dd7d8-5c73-4a61-bc67-a8d1ecb08517))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125) (layer "F.Fab") (width 0.1) (tstamp b241a3cd-ed97-43af-ad41-fe5e23ddd7cd))
    (pad "1" smd roundrect locked (at -0.825 0 90) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 13 "/MP") (tstamp e8f5b32d-796a-489a-ada9-cde304b11a66))
    (pad "2" smd roundrect locked (at 0.825 0 90) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (tstamp d21326c8-3b3d-465b-9a5c-cdf36e6ce3bc))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "BoardIO:LEMO-EPG.00.302.NLN" (layer "F.Cu")
    (tedit 60B81FFE) (tstamp 00000000-0000-0000-0000-00005f88924c)
    (at 11.746 -61.524 180)
    (descr "Triaxial LEMO connector PCB elbow socket https://web.lemo.com/PARTSEARCH_WEB/V1.00Fw/PDF/4241383485754fa8adfc4e1aca22058b/EPG.00.302.NLN.pdf")
    (tags "LEMO THT Female Jack Horizontal")
    (property "P/N" "LEMO EPG.00.302.NLN")
    (property "Rating" "1kV")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (path "/00000000-0000-0000-0000-00005ffc54d9")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "J2" (at 0 -4.6 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 3d58ccc8-19bf-446b-8026-69f501cc3d54)
    )
    (fp_text value "LVDS OUT" (at 0 4.6 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ffb7e098-81e4-4da6-9d1c-915226eb07f1)
    )
    (fp_line (start -3.5 -3.5) (end 14 -3.5) (layer "F.SilkS") (width 0.12) (tstamp 07b38e37-fef9-49fa-985c-2ae9d0740a9d))
    (fp_line (start 14 -3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp 1324a9bb-0f7c-4b59-80b0-cdefab0384f2))
    (fp_line (start -3.5 3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp 95f8e236-4770-423f-8e7b-d99af1a0636d))
    (fp_line (start -3.5 -3.5) (end -3.5 3.5) (layer "F.SilkS") (width 0.12) (tstamp 9ed98a52-f17b-478c-8192-b22ccec5c18d))
    (fp_line (start 3.6 3.6) (end -3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp 093f36c2-729a-46f6-a026-41db5bb63a6b))
    (fp_line (start -3.6 -3.6) (end 3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp 65f76d42-dde0-425f-be35-ecf26e85de5d))
    (fp_line (start -3.6 3.6) (end -3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp 8368861d-fe37-4937-969d-982b7e378aca))
    (fp_line (start 3.6 -3.6) (end 3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp d957206b-9b16-4df8-9924-29b8096a9b71))
    (fp_line (start 13.9 -3.4) (end -3.4 -3.4) (layer "F.Fab") (width 0.1) (tstamp 26cae216-fa3f-4abc-9701-e0f05cb4f3ea))
    (fp_line (start -3.4 3.4) (end 13.9 3.4) (layer "F.Fab") (width 0.1) (tstamp 6f7221c7-43d4-4a29-b36d-b235335685ab))
    (fp_line (start 13.9 3.4) (end 13.9 -3.4) (layer "F.Fab") (width 0.1) (tstamp b1f7aa93-c4b1-4343-b062-7bccee2b61b5))
    (fp_line (start -3.4 -3.4) (end -3.4 3.4) (layer "F.Fab") (width 0.1) (tstamp bc7caf5b-a0ca-44e4-950b-9bfdced504aa))
    (pad "1" thru_hole roundrect locked (at -0.6 0 180) (size 1 1) (drill 0.6) (layers *.Cu *.Paste *.Mask) (roundrect_rratio 0.25)
      (net 7 "TRIG_P") (pinfunction "In1") (tstamp 38d9ede2-d0aa-4d87-86c1-e6815e62a7c9))
    (pad "2" thru_hole circle locked (at 0.6 0 180) (size 1 1) (drill 0.6) (layers *.Cu *.Paste *.Mask)
      (net 8 "TRIG_N") (pinfunction "In2") (tstamp 67c56fa8-c245-4d82-a045-f4803852b88e))
    (pad "3" thru_hole circle locked (at -2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 3a0d2d78-4a4f-4f83-9170-59d631a771d7))
    (pad "3" thru_hole circle locked (at 2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 86311ede-d9d6-406d-b519-33f194dacf81))
    (pad "3" thru_hole circle locked (at 2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp a1978615-05d9-4745-8592-c302f7125672))
    (pad "3" thru_hole circle locked (at -2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp ff974762-5dc5-4e2e-b29e-764bc89d9388))
    (model "${KIPRJMOD}/BoardIO.pretty/EPG.00.302.NLN.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz -90 0 0))
    )
  )

  (footprint "BoardIO:LEMO-EPG.00.302.NLN" (layer "F.Cu")
    (tedit 60B81FFE) (tstamp 00000000-0000-0000-0000-00005f889260)
    (at 11.746 -49.024 180)
    (descr "Triaxial LEMO connector PCB elbow socket https://web.lemo.com/PARTSEARCH_WEB/V1.00Fw/PDF/4241383485754fa8adfc4e1aca22058b/EPG.00.302.NLN.pdf")
    (tags "LEMO THT Female Jack Horizontal")
    (property "P/N" "LEMO EPG.00.302.NLN")
    (property "Rating" "1kV")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (path "/00000000-0000-0000-0000-00005ffc6954")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "J3" (at 0 -4.6 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 86e90c24-b0f7-4f67-bc63-0bb2ef80c02d)
    )
    (fp_text value "LVDS OUT" (at 0 4.6 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp c2b708cc-46dc-4ea4-ae1a-9f93b9f06cc1)
    )
    (fp_line (start -3.5 -3.5) (end 14 -3.5) (layer "F.SilkS") (width 0.12) (tstamp 256b9b36-7f73-45da-9824-ca66f7a656b3))
    (fp_line (start -3.5 -3.5) (end -3.5 3.5) (layer "F.SilkS") (width 0.12) (tstamp 4e24d638-eae0-4583-b15f-31fbf3873dfe))
    (fp_line (start 14 -3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp 9ca1d00d-cde2-4676-abde-7cb0c9809b94))
    (fp_line (start -3.5 3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp a60c9efb-1c2e-43c2-8319-75701f86af2d))
    (fp_line (start 3.6 -3.6) (end 3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp 11a2a721-e2f0-4459-8bed-1e1879367563))
    (fp_line (start -3.6 -3.6) (end 3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp 5ca36c6b-fcbc-4af8-a81e-59646b1764d2))
    (fp_line (start 3.6 3.6) (end -3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp 936febd9-5b8f-4d68-bc6b-85357d6d87b8))
    (fp_line (start -3.6 3.6) (end -3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp fd4f18d3-c226-499b-a9fd-c3ddb830a162))
    (fp_line (start -3.4 3.4) (end 13.9 3.4) (layer "F.Fab") (width 0.1) (tstamp 0235ce0f-14ff-4372-b294-a3b66671a11d))
    (fp_line (start 13.9 -3.4) (end -3.4 -3.4) (layer "F.Fab") (width 0.1) (tstamp 689519a0-c665-412a-9fec-174156743a93))
    (fp_line (start -3.4 -3.4) (end -3.4 3.4) (layer "F.Fab") (width 0.1) (tstamp 8e2b77bf-d2fe-4bad-91b0-e3b4d90fb9c2))
    (fp_line (start 13.9 3.4) (end 13.9 -3.4) (layer "F.Fab") (width 0.1) (tstamp b772b383-4fb5-48bd-9796-2aa7952f85c9))
    (pad "1" thru_hole roundrect locked (at -0.6 0 180) (size 1 1) (drill 0.6) (layers *.Cu *.Paste *.Mask) (roundrect_rratio 0.25)
      (net 9 "TIMES_P") (pinfunction "In1") (tstamp 38095230-89b9-42c7-84c3-baf1fa059e14))
    (pad "2" thru_hole circle locked (at 0.6 0 180) (size 1 1) (drill 0.6) (layers *.Cu *.Paste *.Mask)
      (net 10 "TIMES_N") (pinfunction "In2") (tstamp f3f3097d-c238-4c5e-8546-6c399ce0078f))
    (pad "3" thru_hole circle locked (at 2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 4990058c-37d0-4d48-8617-73a88571bf88))
    (pad "3" thru_hole circle locked (at 2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 7b92f84e-fcdb-4664-af69-d71a70ac2121))
    (pad "3" thru_hole circle locked (at -2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp b832761a-19d0-4154-a60a-83ba527e13c5))
    (pad "3" thru_hole circle locked (at -2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp b9608c3d-8e50-45f0-9541-a5c17a92599e))
    (model "${KIPRJMOD}/BoardIO.pretty/EPG.00.302.NLN.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz -90 0 0))
    )
  )

  (footprint "BoardIO:LEMO-EPL.00.250.NTN" (layer "F.Cu")
    (tedit 60B820E2) (tstamp 00000000-0000-0000-0000-00005f889285)
    (at 11.746 -24.024 180)
    (descr "Coaxial LEMO connector PCB elbow socket https://web.lemo.com/PARTSEARCH_WEB/V1.00Fw/PDF/c05b81de6963431aa48906be13a46183/EPL.00.250.NTN.pdf")
    (tags "LEMO THT Female Jack Horizontal")
    (property "P/N" "LEMO EPL.00.250.NTN")
    (property "Rating" "4A")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (path "/00000000-0000-0000-0000-00005f93ce85")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "J5" (at 0 -4.7 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp e324cf4e-2c97-432e-96f9-5b98f23173af)
    )
    (fp_text value "TTL IN" (at 0 4.7 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 25ba86b3-0395-40a9-b46d-c0cc63899918)
    )
    (fp_line (start -3.5 3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp 0006858f-d07c-4061-8355-25b416fd7874))
    (fp_line (start 14 -3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp 20514fa3-0f9d-4273-a289-4f520ad23c13))
    (fp_line (start -3.5 -3.5) (end -3.5 3.5) (layer "F.SilkS") (width 0.12) (tstamp 84124566-8ad7-4ae7-b99b-a8de4e2dba58))
    (fp_line (start -3.5 -3.5) (end 14 -3.5) (layer "F.SilkS") (width 0.12) (tstamp a9ffc10a-8972-4117-ac03-1fc84f89e3e6))
    (fp_line (start 3.6 3.6) (end -3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp 072e5be5-5b27-4fa4-992c-965b8fe2501f))
    (fp_line (start -3.6 -3.6) (end 3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp 3a18bb70-605c-43c1-a727-1299810a0cad))
    (fp_line (start 3.6 -3.6) (end 3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp 84044ac6-0938-411d-8531-6582a614d2c1))
    (fp_line (start -3.6 3.6) (end -3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp d1e4ce94-ecb8-4f37-9457-23c225be2626))
    (fp_line (start 13.9 3.4) (end 13.9 -3.4) (layer "F.Fab") (width 0.1) (tstamp 100920cb-e618-43a9-bee4-27988ba251cc))
    (fp_line (start 13.9 -3.4) (end -3.4 -3.4) (layer "F.Fab") (width 0.1) (tstamp 2012b133-cb3f-420e-87ac-3a73ce0c4d29))
    (fp_line (start -3.4 3.4) (end 13.9 3.4) (layer "F.Fab") (width 0.1) (tstamp 4c1778b3-3771-4fce-a45b-76ef4fc74a28))
    (fp_line (start -3.4 -3.4) (end -3.4 3.4) (layer "F.Fab") (width 0.1) (tstamp aba46c12-3b85-4261-ac2a-6712a0774bdc))
    (pad "1" thru_hole roundrect locked (at 0 0 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask) (roundrect_rratio 0.25)
      (net 3 "USER2_L") (pinfunction "In") (tstamp 1a145399-d193-4067-bcf3-3e82208305bd))
    (pad "2" thru_hole circle locked (at -2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 70c88d96-586f-466c-9a13-cde32afe3e8c))
    (pad "2" thru_hole circle locked (at -2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp bc59289f-a0ab-44af-83f8-e28b9b01b9af))
    (pad "2" thru_hole circle locked (at 2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp cc681e71-2148-4ca4-bf65-004b360b4c2f))
    (pad "2" thru_hole circle locked (at 2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp dc642028-f017-4ef5-aba2-648487f64fce))
    (model "${KIPRJMOD}/BoardIO.pretty/EPL.00.250.NTN.wrl"
      (offset (xyz -3.5 0 3.5))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 90))
    )
  )

  (footprint "BoardIO:TestPoint_2Pads_Pitch2.54mm_Drill0.8mm" (layer "F.Cu")
    (tedit 60B0211B) (tstamp 00000000-0000-0000-0000-00005f8dd23f)
    (at 102.616 -96.012 180)
    (descr "Test point with 2 pins, pitch 2.54mm, drill diameter 0.8mm")
    (tags "CONN DEV")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (property "exclude_from_bom" "")
    (path "/00000000-0000-0000-0000-00005fb79570")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "TP81" (at 1.27 1.905 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 4de9e8a8-2381-444a-9cca-004365d97d9d)
    )
    (fp_text value "TestPoint_2Pole" (at 1.27 2 180) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f66dbaa2-1501-417c-b62f-a96e1cc6883f)
    )
    (fp_text user "${REFERENCE}" (at 1.3 -2 180) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f6288ad5-6ade-41a2-9a78-4b0b6eb582cd)
    )
    (fp_line (start 0.7 -0.9) (end 2 -0.9) (layer "F.SilkS") (width 0.15) (tstamp 2402e01e-8038-4a14-bee7-b1ebee127feb))
    (fp_line (start 3.57 -0.1) (end 3.57 0.1) (layer "F.SilkS") (width 0.15) (tstamp 30c563b4-a131-40f2-a174-6cff2ef8e223))
    (fp_line (start 2 0.9) (end 0.7 0.9) (layer "F.SilkS") (width 0.15) (tstamp 31d0f3a8-44ab-452f-85cf-5fed4df8e298))
    (fp_line (start -1.03 0.4) (end -1.03 -0.4) (layer "F.SilkS") (width 0.15) (tstamp 7dc02c8c-7a11-4a9c-b86d-14ea953f56f6))
    (fp_line (start -1.3 -0.5) (end -1.3 0.5) (layer "F.CrtYd") (width 0.05) (tstamp 2fb8f92a-65ff-4e6c-a53d-00c487f4c725))
    (fp_line (start -1.3 0.5) (end -0.65 1.15) (layer "F.CrtYd") (width 0.05) (tstamp 4e435d9e-4094-4658-b040-a72d4939bf98))
    (fp_line (start -0.65 1.15) (end 3.15 1.15) (layer "F.CrtYd") (width 0.05) (tstamp 4f1b6e32-675a-498e-806c-7f52fba2b7ae))
    (fp_line (start 3.15 1.15) (end 3.8 0.5) (layer "F.CrtYd") (width 0.05) (tstamp 53ae9080-0b24-43ee-9ce1-9632cbd3dae0))
    (fp_line (start 3.8 0.5) (end 3.8 -0.5) (layer "F.CrtYd") (width 0.05) (tstamp 99079b70-0289-4842-b44a-3fd54e4a93d3))
    (fp_line (start 3.8 -0.5) (end 3.15 -1.15) (layer "F.CrtYd") (width 0.05) (tstamp cd08f043-f211-4728-b09f-699fc59b3d78))
    (fp_line (start 3.15 -1.15) (end -0.65 -1.15) (layer "F.CrtYd") (width 0.05) (tstamp dabec98f-1c2c-4b88-b2e7-f60010f5241c))
    (fp_line (start -0.65 -1.15) (end -1.3 -0.5) (layer "F.CrtYd") (width 0.05) (tstamp e1804ed9-600d-4486-badd-d77c9c307933))
    (pad "1" thru_hole roundrect locked (at 0 0 180) (size 1.4 1.4) (drill 0.8) (layers *.Cu *.Mask) (roundrect_rratio 0.25)
      (net 14 "PEG+12Vo") (pinfunction "1") (tstamp 7f9c9a6b-fec5-44a7-943e-60b43cc4c985))
    (pad "2" thru_hole circle locked (at 2.54 0 180) (size 1.4 1.4) (drill 0.8) (layers *.Cu *.Mask)
      (net 1 "GND") (pinfunction "2") (tstamp 38d7638c-dc45-43d7-bfc2-a93d19251c6b))
  )

  (footprint "BoardIO:TestPoint_2Pads_Pitch2.54mm_Drill0.8mm" (layer "F.Cu")
    (tedit 60B0211B) (tstamp 00000000-0000-0000-0000-00005f8dd256)
    (at 91.44 -94.107 180)
    (descr "Test point with 2 pins, pitch 2.54mm, drill diameter 0.8mm")
    (tags "CONN DEV")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (property "exclude_from_bom" "")
    (path "/00000000-0000-0000-0000-00005fbbe17d")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "TP82" (at 1.3 -2 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 98bb76ce-8cf4-4602-a902-159649e57dbd)
    )
    (fp_text value "TestPoint_2Pole" (at 1.27 2 180) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 084327d1-eb02-44d2-bcea-3dc5effee4a7)
    )
    (fp_text user "${REFERENCE}" (at 1.3 -2 180) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp e0b0a8b3-746f-435a-8404-de369fbc36d5)
    )
    (fp_line (start 2 0.9) (end 0.7 0.9) (layer "F.SilkS") (width 0.15) (tstamp 332b24cd-fa7d-4d70-8c50-d8458c2a2ce4))
    (fp_line (start 0.7 -0.9) (end 2 -0.9) (layer "F.SilkS") (width 0.15) (tstamp 5a4850eb-1248-4052-83e8-b9f1ff26faa6))
    (fp_line (start -1.03 0.4) (end -1.03 -0.4) (layer "F.SilkS") (width 0.15) (tstamp 896b34a3-74fa-4bc5-a24c-3ab21d0a169d))
    (fp_line (start 3.57 -0.1) (end 3.57 0.1) (layer "F.SilkS") (width 0.15) (tstamp b626f5e2-1a91-456a-8333-63a1f9b48bb5))
    (fp_line (start -1.3 0.5) (end -0.65 1.15) (layer "F.CrtYd") (width 0.05) (tstamp 06fb333a-b51b-4434-b9c4-2f5bc72a68d5))
    (fp_line (start -1.3 -0.5) (end -1.3 0.5) (layer "F.CrtYd") (width 0.05) (tstamp 39a2a732-1637-4bf5-a845-397ab4d0a137))
    (fp_line (start -0.65 -1.15) (end -1.3 -0.5) (layer "F.CrtYd") (width 0.05) (tstamp 4e1f2243-5f3b-408e-90e3-f435940c6b7c))
    (fp_line (start 3.8 -0.5) (end 3.15 -1.15) (layer "F.CrtYd") (width 0.05) (tstamp c0757922-1345-453f-a0bd-a4841eef6dae))
    (fp_line (start 3.15 1.15) (end 3.8 0.5) (layer "F.CrtYd") (width 0.05) (tstamp c910855e-cd6a-4636-8f21-b231159a2286))
    (fp_line (start 3.8 0.5) (end 3.8 -0.5) (layer "F.CrtYd") (width 0.05) (tstamp d338ab03-91dc-4bb9-852a-f5ba3cd46763))
    (fp_line (start 3.15 -1.15) (end -0.65 -1.15) (layer "F.CrtYd") (width 0.05) (tstamp de947e2e-806e-4d5e-9ef8-0b421944a49a))
    (fp_line (start -0.65 1.15) (end 3.15 1.15) (layer "F.CrtYd") (width 0.05) (tstamp ed20be16-96f9-4172-b4f7-7b4eff1c4746))
    (pad "1" thru_hole roundrect locked (at 0 0 180) (size 1.4 1.4) (drill 0.8) (layers *.Cu *.Mask) (roundrect_rratio 0.25)
      (net 2 "+12V") (pinfunction "1") (tstamp 512e289b-504b-4661-b9e0-eb87957edbed))
    (pad "2" thru_hole circle locked (at 2.54 0 180) (size 1.4 1.4) (drill 0.8) (layers *.Cu *.Mask)
      (net 1 "GND") (pinfunction "2") (tstamp 317d45ac-5779-4a01-bd02-13f20b128bde))
  )

  (footprint "BoardIO:TestPoint_2Pads_Pitch2.54mm_Drill0.8mm" (layer "F.Cu")
    (tedit 60B0211B) (tstamp 00000000-0000-0000-0000-00005f8dd2e0)
    (at 68.834 -74.676 -90)
    (descr "Test point with 2 pins, pitch 2.54mm, drill diameter 0.8mm")
    (tags "CONN DEV")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (property "exclude_from_bom" "")
    (path "/00000000-0000-0000-0000-00005fc42881")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "TP5" (at 1.3 -2 -90) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f222e3a5-ea10-4a83-81f7-578a668e9730)
    )
    (fp_text value "TestPoint_2Pole" (at 1.27 2 -90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 8e830930-4da0-4161-946e-977af2b11398)
    )
    (fp_text user "${REFERENCE}" (at 1.3 -2 -90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 47bb00ac-d0a6-4be0-aae5-ec1aa60be34b)
    )
    (fp_line (start 0.7 -0.9) (end 2 -0.9) (layer "F.SilkS") (width 0.15) (tstamp 002f2b9e-e9c4-49e0-a736-7c69960f014a))
    (fp_line (start -1.03 0.4) (end -1.03 -0.4) (layer "F.SilkS") (width 0.15) (tstamp 4c7cb5e7-625c-4ca2-8942-750de80aaa6f))
    (fp_line (start 3.57 -0.1) (end 3.57 0.1) (layer "F.SilkS") (width 0.15) (tstamp 89b27917-8b72-443d-894a-3d7269eb0dd5))
    (fp_line (start 2 0.9) (end 0.7 0.9) (layer "F.SilkS") (width 0.15) (tstamp 9dd95dbd-b5ef-4ec8-abf1-458f8ce30777))
    (fp_line (start 3.15 1.15) (end 3.8 0.5) (layer "F.CrtYd") (width 0.05) (tstamp 0d07ab00-0e08-4a68-9b98-cb7d634c8736))
    (fp_line (start 3.15 -1.15) (end -0.65 -1.15) (layer "F.CrtYd") (width 0.05) (tstamp 146ee757-3ef4-466d-bd52-cc69ae2a7760))
    (fp_line (start -0.65 -1.15) (end -1.3 -0.5) (layer "F.CrtYd") (width 0.05) (tstamp 21a2eeaa-84ac-4fc3-b4bf-625094502fcb))
    (fp_line (start -0.65 1.15) (end 3.15 1.15) (layer "F.CrtYd") (width 0.05) (tstamp 4fdaac0c-87a5-4d53-9cd1-b9b58b147164))
    (fp_line (start 3.8 -0.5) (end 3.15 -1.15) (layer "F.CrtYd") (width 0.05) (tstamp 5b12ea67-05f1-4887-a141-473bfab9ef2e))
    (fp_line (start -1.3 0.5) (end -0.65 1.15) (layer "F.CrtYd") (width 0.05) (tstamp 81962e9f-c23a-4705-b6d9-f319498a06d0))
    (fp_line (start 3.8 0.5) (end 3.8 -0.5) (layer "F.CrtYd") (width 0.05) (tstamp a53f6742-44c4-43da-b004-8dd701850cac))
    (fp_line (start -1.3 -0.5) (end -1.3 0.5) (layer "F.CrtYd") (width 0.05) (tstamp a87c2162-be20-4628-be0f-8fcf5cfd8d0e))
    (pad "1" thru_hole roundrect locked (at 0 0 270) (size 1.4 1.4) (drill 0.8) (layers *.Cu *.Mask) (roundrect_rratio 0.25)
      (net 3 "USER2_L") (pinfunction "1") (tstamp 7f969e6c-2bf6-4ee8-a026-7906c5b2a6cb))
    (pad "2" thru_hole circle locked (at 2.54 0 270) (size 1.4 1.4) (drill 0.8) (layers *.Cu *.Mask)
      (net 1 "GND") (pinfunction "2") (tstamp c10bd702-9680-498f-a874-97e6f20df347))
  )

  (footprint "BoardIO:TestPoint_2Pads_Pitch2.54mm_Drill0.8mm" (layer "F.Cu")
    (tedit 60B0211B) (tstamp 00000000-0000-0000-0000-00005f8dd2f7)
    (at 68.834 -64.282 -90)
    (descr "Test point with 2 pins, pitch 2.54mm, drill diameter 0.8mm")
    (tags "CONN DEV")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (property "exclude_from_bom" "")
    (path "/00000000-0000-0000-0000-00005fc42896")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "TP6" (at 1.3 -2 -90) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 7938b5ac-2971-44ed-994f-9f35333a7422)
    )
    (fp_text value "TestPoint_2Pole" (at 1.27 2 -90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 369bfce5-2d52-4d3f-9adc-43b38e72d44d)
    )
    (fp_text user "${REFERENCE}" (at 1.3 -2 -90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 66e4a21c-13fb-4935-8a7d-2e4366ff27fe)
    )
    (fp_line (start -1.03 0.4) (end -1.03 -0.4) (layer "F.SilkS") (width 0.15) (tstamp 06d291f9-802f-4615-9d72-784c440efa6a))
    (fp_line (start 2 0.9) (end 0.7 0.9) (layer "F.SilkS") (width 0.15) (tstamp 48d3836d-83ad-4702-8a9f-ec28e429f440))
    (fp_line (start 0.7 -0.9) (end 2 -0.9) (layer "F.SilkS") (width 0.15) (tstamp be094f5c-da51-4980-ae58-16b13470fabe))
    (fp_line (start 3.57 -0.1) (end 3.57 0.1) (layer "F.SilkS") (width 0.15) (tstamp cacd6d8c-e613-493d-a55f-7f2c8d3ae077))
    (fp_line (start -0.65 1.15) (end 3.15 1.15) (layer "F.CrtYd") (width 0.05) (tstamp 1cbaa37b-5919-4831-9cb2-7647b730aa49))
    (fp_line (start -0.65 -1.15) (end -1.3 -0.5) (layer "F.CrtYd") (width 0.05) (tstamp 228bf6be-e969-408f-934f-9dacc7af5489))
    (fp_line (start 3.8 -0.5) (end 3.15 -1.15) (layer "F.CrtYd") (width 0.05) (tstamp 245b4591-ebf7-4a58-9c92-4e6b251fe183))
    (fp_line (start -1.3 0.5) (end -0.65 1.15) (layer "F.CrtYd") (width 0.05) (tstamp 64c59272-e1f2-4234-b65c-db8eb12b70f1))
    (fp_line (start 3.15 -1.15) (end -0.65 -1.15) (layer "F.CrtYd") (width 0.05) (tstamp 83bfea30-7f59-4dd6-a734-018f954b8447))
    (fp_line (start -1.3 -0.5) (end -1.3 0.5) (layer "F.CrtYd") (width 0.05) (tstamp 879a1c9f-4a21-4600-a135-8f6e28216f50))
    (fp_line (start 3.8 0.5) (end 3.8 -0.5) (layer "F.CrtYd") (width 0.05) (tstamp d7aa1429-fe17-45ce-bb2e-120e2b7c2dc3))
    (fp_line (start 3.15 1.15) (end 3.8 0.5) (layer "F.CrtYd") (width 0.05) (tstamp dcc18a46-887f-4e78-969c-07eca00c4db7))
    (pad "1" thru_hole roundrect locked (at 0 0 270) (size 1.4 1.4) (drill 0.8) (layers *.Cu *.Mask) (roundrect_rratio 0.25)
      (net 4 "USER3_L") (pinfunction "1") (tstamp 8883f9fa-5017-4ccc-b326-2c00d109a170))
    (pad "2" thru_hole circle locked (at 2.54 0 270) (size 1.4 1.4) (drill 0.8) (layers *.Cu *.Mask)
      (net 1 "GND") (pinfunction "2") (tstamp cb237200-ab68-4f03-a8c8-564c6b3569fc))
  )

  (footprint "BoardIO:PCIbracket" (layer "F.Cu")
    (tedit 60B79EBA) (tstamp 00000000-0000-0000-0000-00005f8dea0a)
    (at 0 0)
    (descr "http://www.bracket.com/downloads/brackets/pdf/94560000A.pdf")
    (property "P/N" "Gompf 9456-0000A ; McMaster-Carr 92095A181 90576A102")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (path "/00000000-0000-0000-0000-00005f8e8f5a")
    (fp_text reference "H1" (at 4.4196 -5.2832) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp a160486d-f01e-4a40-a711-8af03c2df4ac)
    )
    (fp_text value "PCIeBracket_Pads" (at 6.2992 -7.2136) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 4fd5936e-f6fb-4235-8657-c18b903d3463)
    )
    (fp_line (start -0.287746 22.617624) (end -0.453778 24.515375) (layer "F.Fab") (width 0.4) (tstamp 0370613e-02b6-424a-be6d-2df587c64eb5))
    (fp_line (start -0.011026 17.250122) (end -1.011026 17.250122) (layer "F.Fab") (width 0.4) (tstamp 04b3e96d-4441-4c0f-a137-9e0f54c52e61))
    (fp_line (start -7.72955 -95.517656) (end -11.396951 -95.517656) (layer "F.Fab") (width 0.4) (tstamp 06a3ee10-6423-447c-abe3-53b2b25eecb3))
    (fp_line (start 0 -2.499999) (end 0 -3.630299) (layer "F.Fab") (width 0.4) (tstamp 07ab6f2e-2699-4459-acae-d001d115d65d))
    (fp_line (start -2.011026 -95.532849) (end -2.011026 -96.532849) (layer "F.Fab") (width 0.4) (tstamp 0cac20b7-d80e-4aa0-9084-8a0045680dd4))
    (fp_line (start 0.988974 3.984207) (end 0.988974 -3.965993) (layer "F.Fab") (width 0.4) (tstamp 198acc06-5574-49eb-abe1-40101032900d))
    (fp_line (start 0 0) (end 0 -2.499999) (layer "F.Fab") (width 0.4) (tstamp 1e9acfd0-3962-4727-ab87-c262fc1051b2))
    (fp_line (start 0.988974 -81.410593) (end 0.988974 -89.360793) (layer "F.Fab") (width 0.4) (tstamp 1fa9a13f-ce81-48f3-9a09-a68096b509b0))
    (fp_line (start 0 0) (end -2.499999 0) (layer "F.Fab") (width 0.4) (tstamp 2570794d-5c3c-40ec-9db8-38ef66e48338))
    (fp_line (start -1.011026 13.118568) (end -1.011026 -89.023478) (layer "F.Fab") (width 0.4) (tstamp 271f60a8-513c-4a1b-825b-a1f8a659b4be))
    (fp_line (start 10.647574 -88.090793) (end 10.647574 -82.680593) (layer "F.Fab") (width 0.4) (tstamp 2bc253d0-51b5-421a-ba1c-d2cbad49dc0c))
    (fp_line (start 0 0) (end 2.499999 0) (layer "F.Fab") (width 0.4) (tstamp 2f0d9cfb-629b-419f-b3a1-2f058aeefa76))
    (fp_line (start -1.011026 -89.023478) (end -1.011026 -91.595849) (layer "F.Fab") (width 0.4) (tstamp 2f252e41-0cbf-419b-af09-ca8c81089de9))
    (fp_line (start -0.287746 22.617624) (end -0.018637 19.541695) (layer "F.Fab") (width 0.4) (tstamp 2ff10265-9ac6-42d1-8c42-c4dbbd4cfa92))
    (fp_line (start 0 0) (end 0 -2.499999) (layer "F.Fab") (width 0.4) (tstamp 3015827f-ab17-4f4d-86d3-3a692aeabe28))
    (fp_line (start -2.011026 -95.532849) (end -9.536026 -95.532849) (layer "F.Fab") (width 0.4) (tstamp 31462424-0cfb-40e4-8541-c2a1c2421311))
    (fp_line (start 0 0) (end -2.499999 0) (layer "F.Fab") (width 0.4) (tstamp 319104b5-f4ec-4a8d-93aa-2d57c0d33de5))
    (fp_line (start -0.011026 -89.023478) (end -0.011026 -91.595849) (layer "F.Fab") (width 0.2) (tstamp 397530dd-a207-4e89-9a7f-4a1c3be3063e))
    (fp_line (start 0 0) (end 0 2.499999) (layer "F.Fab") (width 0.4) (tstamp 3cd87267-8532-408a-812e-2f93ca16e352))
    (fp_line (start -0.011026 -91.595849) (end -1.011026 -91.595849) (layer "F.Fab") (width 0.4) (tstamp 48627da3-a6e7-4e05-a02b-aeab572d378b))
    (fp_line (start 0.988974 -3.965993) (end 9.377574 -3.965993) (layer "F.Fab") (width 0.4) (tstamp 52c65a1f-8d01-43f8-8c85-d70bbd5b453b))
    (fp_line (start -11.411538 -95.522684) (end -11.383022 -95.199238) (layer "F.Fab") (width 0.4) (tstamp 5a494963-0fa8-49b7-9fa7-8b05f72aec1e))
    (fp_line (start -11.483775 -96.502464) (end -11.438784 -95.992155) (layer "F.Fab") (width 0.4) (tstamp 5abd1133-9708-4dc8-a507-fb9ed64eb0b3))
    (fp_line (start -0.011026 -89.023478) (end -1.011026 -89.023478) (layer "F.Fab") (width 0.4) (tstamp 5cc90366-6706-4f7f-af3c-ff76899a4cde))
    (fp_line (start -0.011026 13.118568) (end -1.011026 13.118568) (layer "F.Fab") (width 0.4) (tstamp 5fd9c22a-e174-49c7-b0b3-a34f3fe23832))
    (fp_line (start -0.011026 13.118568) (end -0.011026 -89.023478) (layer "F.Fab") (width 0.2) (tstamp 7032cb59-7b89-4c60-b7d9-7e75a59b4d31))
    (fp_line (start 0 -2.499999) (end 0 -3.630299) (layer "F.Fab") (width 0.4) (tstamp 731dd2e2-b0d0-45e1-a6ab-70431f0e6be8))
    (fp_line (start -0.453778 24.515375) (end -1.449972 24.428219) (layer "F.Fab") (width 0.4) (tstamp 735eb026-27be-474e-afd4-ca55521d8a02))
    (fp_line (start 0.988974 -89.360793) (end 9.377574 -89.360793) (layer "F.Fab") (width 0.4) (tstamp 75ebdecb-9b95-4976-ba1d-d63a0a922131))
    (fp_line (start 0.988974 -3.965993) (end -0.011026 -3.965993) (layer "F.Fab") (width 0.4) (tstamp 792f7214-4b13-430d-8ccc-e6781a568865))
    (fp_line (start 9.377574 -81.410593) (end 0.988974 -81.410593) (layer "F.Fab") (width 0.4) (tstamp 79d9c5a2-6961-4e86-9c3e-b2b0c38b1608))
    (fp_line (start 0 2.499999) (end 0 3.630299) (layer "F.Fab") (width 0.4) (tstamp 81aecc93-f6f4-4e0a-bdcf-1803544ba593))
    (fp_line (start 2.499999 0) (end 3.630299 0) (layer "F.Fab") (width 0.4) (tstamp 827460d0-6c24-4485-80d0-e18fa054b84a))
    (fp_line (start 10.647574 -2.695993) (end 10.647574 2.714207) (layer "F.Fab") (width 0.4) (tstamp 8882a21f-18e6-461b-b71a-2c61bfb26b8d))
    (fp_line (start -9.536026 -96.532849) (end -9.536026 -95.532849) (layer "F.Fab") (width 0.4) (tstamp 8a25a171-6a3a-4a0b-b9bf-754dde668c94))
    (fp_line (start -11.469846 -96.184045) (end -11.441026 -95.857159) (layer "F.Fab") (width 0.4) (tstamp 8c86dc78-b3dd-4649-9d62-f5d3a870e8ea))
    (fp_line (start -0.011026 -81.410593) (end 0.988974 -81.410593) (layer "F.Fab") (width 0.4) (tstamp 906241d8-8148-4607-97cd-f79bf2f3077a))
    (fp_line (start 0 2.499999) (end 0 3.630299) (layer "F.Fab") (width 0.4) (tstamp 947bbc87-c5d6-4157-98e4-b51c2a1813da))
    (fp_line (start 0 0) (end 2.499999 0) (layer "F.Fab") (width 0.4) (tstamp 987f808c-4cf5-4956-9511-e4cdc694ac8e))
    (fp_line (start 0 0) (end 0 2.499999) (layer "F.Fab") (width 0.4) (tstamp 9c115a2e-0fbb-4da2-af2c-7792ec5f05da))
    (fp_line (start 0.988974 -89.360793) (end -0.011026 -89.360793) (layer "F.Fab") (width 0.4) (tstamp a9e5f273-0032-436e-a724-444098de9a77))
    (fp_line (start -0.011026 3.984207) (end 0.988974 3.984207) (layer "F.Fab") (width 0.4) (tstamp ab327a16-92e1-409a-a797-56b3b6f8f36f))
    (fp_line (start -0.011026 19.367384) (end -0.011026 17.250122) (layer "F.Fab") (width 0.4) (tstamp b39b4a08-9f62-43cd-b191-7c38007be84e))
    (fp_line (start -11.483775 -96.502464) (end -11.469846 -96.184045) (layer "F.Fab") (width 0.4) (tstamp b95f1a86-f676-4722-93c6-9ea5467ccb35))
    (fp_line (start -11.441026 -95.532849) (end -11.441026 -96.532849) (layer "F.Fab") (width 0.4) (tstamp ba5295f7-a983-4795-90e6-21c36c08a3de))
    (fp_line (start -2.011026 -96.532849) (end -9.536026 -96.532849) (layer "F.Fab") (width 0.4) (tstamp ba605f8c-837d-46a8-b716-5b558ca8cff1))
    (fp_line (start -1.011026 -94.532849) (end -0.011026 -94.532849) (layer "F.Fab") (width 0.4) (tstamp bc08f89c-348c-45cd-bb80-5f58a8dc960f))
    (fp_line (start 2.499999 0) (end 3.630299 0) (layer "F.Fab") (width 0.4) (tstamp c47810f3-9e5e-4523-93bb-4ae180f58bdb))
    (fp_line (start -7.72955 -95.517656) (end -11.383022 -95.199238) (layer "F.Fab") (width 0.4) (tstamp c49da5cf-5d03-4751-b35f-8e7e1d0ac96b))
    (fp_line (start -0.011026 17.250122) (end -0.011026 13.118568) (layer "F.Fab") (width 0.4) (tstamp ca7fbc78-7be6-4e89-af87-93776ffe183e))
    (fp_line (start -0.011026 -91.595849) (end -0.011026 -94.532849) (layer "F.Fab") (width 0.2) (tstamp d1163535-b436-46e2-b941-2a1a20488d96))
    (fp_line (start -1.011026 19.367384) (end -0.011026 19.367384) (layer "F.Fab") (width 0.4) (tstamp d51e3804-16f7-4618-a51f-bd71ad56901b))
    (fp_line (start -1.449972 24.428219) (end -1.283941 22.530468) (layer "F.Fab") (width 0.4) (tstamp dacb797b-b849-4dc4-a80a-000e2afe178c))
    (fp_line (start -9.536026 -95.532849) (end -11.441026 -95.532849) (layer "F.Fab") (width 0.4) (tstamp e12fa764-7322-4162-8763-d48c55d29864))
    (fp_line (start -1.283941 22.530468) (end -0.287746 22.617624) (layer "F.Fab") (width 0.4) (tstamp e1910d21-67f6-4512-9e9b-6a32a18257ba))
    (fp_line (start -1.011026 17.250122) (end -1.011026 13.118568) (layer "F.Fab") (width 0.4) (tstamp e1ff96ae-a8c3-4a8a-8fcc-e0ed1ad897ed))
    (fp_line (start -11.396951 -95.517656) (end -11.383022 -95.199238) (layer "F.Fab") (width 0.4) (tstamp e3617f0a-ccbe-43df-b371-2a057e65bc4a))
    (fp_line (start -2.499999 0) (end -3.630299 0) (layer "F.Fab") (width 0.4) (tstamp e51d5260-851a-41c2-8f90-1a487e16eba2))
    (fp_line (start -1.011026 19.367384) (end -1.011026 17.250122) (layer "F.Fab") (width 0.4) (tstamp e576d12b-ea1e-4abb-9ead-f14b1a049f71))
    (fp_line (start -1.283941 22.530468) (end -1.014832 19.45454) (layer "F.Fab") (width 0.4) (tstamp e8c0c293-0168-48a8-84a4-65267704429d))
    (fp_line (start -1.011026 -91.595849) (end -1.011026 -94.532849) (layer "F.Fab") (width 0.4) (tstamp eb085d5a-119c-416b-b74a-d427985cabe9))
    (fp_line (start 9.377574 3.984207) (end 0.988974 3.984207) (layer "F.Fab") (width 0.4) (tstamp ed9c7249-b0e9-4d70-8e04-3e6c73ee0adc))
    (fp_line (start -11.441026 -96.532849) (end -9.536026 -96.532849) (layer "F.Fab") (width 0.4) (tstamp f38cfd60-77bc-4c38-a72e-9e870f644f11))
    (fp_line (start -1.014832 19.45454) (end -0.018637 19.541695) (layer "F.Fab") (width 0.4) (tstamp f7047ff7-2d44-4dca-aa78-85072ec6b1f3))
    (fp_line (start -2.499999 0) (end -3.630299 0) (layer "F.Fab") (width 0.4) (tstamp fa5a8318-e40c-4b2f-a918-d0100ada0499))
    (fp_arc (start -2.011026 -94.532849) (end -0.011026 -94.532849) (angle -90) (layer "F.Fab") (width 0.4) (tstamp 221b658f-14a3-48a6-a505-38f53914b8d6))
    (fp_arc (start 8.472574 -85.385693) (end 8.104429 -84.01176) (angle -300) (layer "F.Fab") (width 0.4) (tstamp 3796f4bd-33d2-41ae-809e-dc0d783322df))
    (fp_arc (start -2.011026 -94.532849) (end -1.011026 -94.532849) (angle -90) (layer "F.Fab") (width 0.4) (tstamp 4d27bb39-6409-4a20-904d-44355cd74ae5))
    (fp_arc (start -2.011026 19.367384) (end -0.018637 19.541695) (angle -5) (layer "F.Fab") (width 0.4) (tstamp 67de3098-2456-4ff3-8d9f-b0999e159137))
    (fp_arc (start 9.377574 -88.090793) (end 10.647574 -88.090793) (angle -90) (layer "F.Fab") (width 0.4) (tstamp 6fc73175-6021-46df-9392-4c9a4f5633e7))
    (fp_arc (start 9.377574 -82.680593) (end 9.377574 -81.410593) (angle -90) (layer "F.Fab") (width 0.4) (tstamp 99ac4241-4dd9-498f-b72e-ca93e7bbd757))
    (fp_arc (start -2.011026 19.367384) (end -1.014832 19.45454) (angle -5) (layer "F.Fab") (width 0.4) (tstamp 9e30f0ba-0a62-4276-881a-218fc59d8fac))
    (fp_arc (start 8.472574 0.009107) (end 8.104429 1.38304) (angle -300) (layer "F.Fab") (width 0.4) (tstamp 9fb1fe0b-d93f-4ab0-a66d-b3c670dbcde6))
    (fp_arc (start 9.377574 -2.695993) (end 10.647574 -2.695993) (angle -90) (layer "F.Fab") (width 0.4) (tstamp b8226b90-83b3-4c44-8f36-15580e2bbd71))
    (fp_arc (start 9.377574 2.714207) (end 9.377574 3.984207) (angle -90) (layer "F.Fab") (width 0.4) (tstamp e5e4830d-28f6-4ae6-847d-f9d567fb74ef))
    (fp_circle (center 8.472574 0.009107) (end 9.602874 0.009107) (layer "F.Fab") (width 0.4) (fill none) (tstamp 81c8c5c9-1eb3-451f-9bfb-11b5fd51bf15))
    (fp_circle (center 8.472574 -85.385693) (end 9.602874 -85.385693) (layer "F.Fab") (width 0.4) (fill none) (tstamp a5b023cd-41ea-48c7-ba05-508f7b1f4f58))
    (pad "MP" thru_hole circle locked (at 8.4836 -85.3948) (size 6.4 6.4) (drill oval 3.8 3.2) (layers *.Cu *.Mask)
      (net 13 "/MP") (pinfunction "MP") (tstamp 306ad355-5e23-4c43-81c4-9e05bcd63145))
    (pad "MP" thru_hole circle locked (at 8.4836 0) (size 6.4 6.4) (drill oval 3.8 3.2) (layers *.Cu *.Mask)
      (net 13 "/MP") (pinfunction "MP") (tstamp 5b83cbf4-f3af-451b-b7e2-699fbafaac16))
    (model "${KIPRJMOD}/BoardIO.pretty/Gompf-9456-0000A.step" hide
      (offset (xyz 0 46.35 3))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 90 0))
    )
    (model "${KIPRJMOD}/BoardIO.pretty/M3-Nut.wrl"
      (offset (xyz 8.5 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
    (model "${KIPRJMOD}/BoardIO.pretty/M3x8-Screw.wrl"
      (offset (xyz 8.5 0 -2.75))
      (scale (xyz 1 1 1))
      (rotate (xyz 180 0 0))
    )
    (model "${KIPRJMOD}/BoardIO.pretty/M3-Nut.wrl"
      (offset (xyz 8.5 85.3948 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
    (model "${KIPRJMOD}/BoardIO.pretty/M3x8-Screw.wrl"
      (offset (xyz 8.5 85.3948 -2.75))
      (scale (xyz 1 1 1))
      (rotate (xyz 180 0 0))
    )
    (model "${KIPRJMOD}/BoardIO.pretty/Gompf-9456-0000A.wrl"
      (offset (xyz 0 46.35 3))
      (scale (xyz 394 394 394))
      (rotate (xyz 0 90 0))
    )
  )

  (footprint "Capacitor_SMD:CP_Elec_4x5.4" (layer "F.Cu")
    (tedit 5BCA39CF) (tstamp 00000000-0000-0000-0000-00005f8ee015)
    (at 90.954 -97.917)
    (descr "SMD capacitor, aluminum electrolytic, Panasonic A5 / Nichicon, 4.0x5.4mm")
    (tags "capacitor electrolytic")
    (property "P/N" "Nichicon UWT1C100MCL1GB")
    (property "Rating" "16V")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (path "/00000000-0000-0000-0000-00005fc3a18e")
    (attr smd)
    (fp_text reference "C2" (at 3.324 2.286 270) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 55dce881-011e-4e92-8629-1d62968982b7)
    )
    (fp_text value "10u" (at 0 3.2) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp c0d59b26-6398-461e-9d5b-7dd9c7626117)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
      (effects (font (size 0.8 0.8) (thickness 0.12)))
      (tstamp 9d77f2b5-d327-415d-a557-0f2a6724af7e)
    )
    (fp_line (start -3 -1.56) (end -2.5 -1.56) (layer "F.SilkS") (width 0.12) (tstamp 089871e9-74fc-4d52-b71c-8b4f4d7f6161))
    (fp_line (start -2.26 -1.195563) (end -1.195563 -2.26) (layer "F.SilkS") (width 0.12) (tstamp 161abed1-0d4c-44bf-992d-0c2391096d58))
    (fp_line (start -2.75 -1.81) (end -2.75 -1.31) (layer "F.SilkS") (width 0.12) (tstamp 45781870-f80b-4433-aaec-5af8b60c1db3))
    (fp_line (start 2.26 2.26) (end 2.26 1.06) (layer "F.SilkS") (width 0.12) (tstamp 4d2b978a-8604-4a3d-9687-67a06e8fdb30))
    (fp_line (start 2.26 -2.26) (end 2.26 -1.06) (layer "F.SilkS") (width 0.12) (tstamp 503edeef-83c9-4539-9252-e82331a79345))
    (fp_line (start -2.26 -1.195563) (end -2.26 -1.06) (layer "F.SilkS") (width 0.12) (tstamp 74985f09-c3c9-4c62-b8fb-7547bfafaebe))
    (fp_line (start -1.195563 -2.26) (end 2.26 -2.26) (layer "F.SilkS") (width 0.12) (tstamp c27fbf89-3708-4a1b-a810-6d9d4e267267))
    (fp_line (start -2.26 1.195563) (end -1.195563 2.26) (layer "F.SilkS") (width 0.12) (tstamp cd9e8d6d-c700-46a4-87f7-51f57c124ca3))
    (fp_line (start -2.26 1.195563) (end -2.26 1.06) (layer "F.SilkS") (width 0.12) (tstamp ce337408-b5d8-4a70-bd07-3ee380154061))
    (fp_line (start -1.195563 2.26) (end 2.26 2.26) (layer "F.SilkS") (width 0.12) (tstamp ff4bf033-bc43-47a8-8011-bcc25ffbf74f))
    (fp_line (start -2.4 -1.05) (end -3.35 -1.05) (layer "F.CrtYd") (width 0.05) (tstamp 10f9dde6-1604-45b8-af38-c19de62b47f8))
    (fp_line (start -1.25 2.4) (end 2.4 2.4) (layer "F.CrtYd") (width 0.05) (tstamp 2a4c9f81-4b18-4808-afb4-e706d2fd2a9e))
    (fp_line (start -2.4 -1.25) (end -2.4 -1.05) (layer "F.CrtYd") (width 0.05) (tstamp 3749536a-aa76-4e30-b9fa-0f5183e9ea8d))
    (fp_line (start 2.4 -2.4) (end 2.4 -1.05) (layer "F.CrtYd") (width 0.05) (tstamp 3822628b-5b54-4414-9064-507ade6245bb))
    (fp_line (start 3.35 -1.05) (end 3.35 1.05) (layer "F.CrtYd") (width 0.05) (tstamp 45b939ff-82fe-4d34-80f8-a76ac6505ca3))
    (fp_line (start -3.35 1.05) (end -2.4 1.05) (layer "F.CrtYd") (width 0.05) (tstamp 48ac66b9-e71d-4d55-b816-0a7e748862d4))
    (fp_line (start 3.35 1.05) (end 2.4 1.05) (layer "F.CrtYd") (width 0.05) (tstamp 51b3b4cc-1e8a-43db-9c49-59b4e2d522a0))
    (fp_line (start -2.4 -1.25) (end -1.25 -2.4) (layer "F.CrtYd") (width 0.05) (tstamp 7ca4ffd9-778b-4450-852e-64d241ba5ce5))
    (fp_line (start -2.4 1.05) (end -2.4 1.25) (layer "F.CrtYd") (width 0.05) (tstamp 80497da1-c1e5-41d1-9e4e-d81961320ca4))
    (fp_line (start -3.35 -1.05) (end -3.35 1.05) (layer "F.CrtYd") (width 0.05) (tstamp 9486f679-8152-4c55-907a-1eccd6b40e80))
    (fp_line (start -2.4 1.25) (end -1.25 2.4) (layer "F.CrtYd") (width 0.05) (tstamp 9e106a75-f241-4420-8881-69b7e2f06f99))
    (fp_line (start 2.4 -1.05) (end 3.35 -1.05) (layer "F.CrtYd") (width 0.05) (tstamp af088e35-9885-4f47-8013-f9e0aadcf1cb))
    (fp_line (start -1.25 -2.4) (end 2.4 -2.4) (layer "F.CrtYd") (width 0.05) (tstamp afd1b493-0fda-4799-b52b-353ff55dc2ff))
    (fp_line (start 2.4 1.05) (end 2.4 2.4) (layer "F.CrtYd") (width 0.05) (tstamp c3ab9abb-7dcc-4f63-bfbb-175d5c823a6c))
    (fp_line (start -1.15 2.15) (end 2.15 2.15) (layer "F.Fab") (width 0.1) (tstamp 1b060d37-4069-4029-a2fa-2a0e6688fdac))
    (fp_line (start -2.15 -1.15) (end -2.15 1.15) (layer "F.Fab") (width 0.1) (tstamp 2d6232ee-2439-476e-9bad-ef1ca9ecf648))
    (fp_line (start -2.15 1.15) (end -1.15 2.15) (layer "F.Fab") (width 0.1) (tstamp 5bf4c45c-a7ff-4831-886a-aa57906e68b0))
    (fp_line (start 2.15 -2.15) (end 2.15 2.15) (layer "F.Fab") (width 0.1) (tstamp 999da2e6-d787-4aef-bace-1fa2d323c55b))
    (fp_line (start -1.374773 -1.2) (end -1.374773 -0.8) (layer "F.Fab") (width 0.1) (tstamp d0fcb555-5b1b-4166-b92b-589245fe9bb8))
    (fp_line (start -2.15 -1.15) (end -1.15 -2.15) (layer "F.Fab") (width 0.1) (tstamp d3c1c897-01bf-4a96-ab7e-67706b937ab8))
    (fp_line (start -1.15 -2.15) (end 2.15 -2.15) (layer "F.Fab") (width 0.1) (tstamp e2127693-10cd-4866-a5c4-9561ca4c332b))
    (fp_line (start -1.574773 -1) (end -1.174773 -1) (layer "F.Fab") (width 0.1) (tstamp e94557f6-e1e0-4ab2-af4f-8f20a83b334f))
    (fp_circle (center 0 0) (end 2 0) (layer "F.Fab") (width 0.1) (fill none) (tstamp 9e677e99-391b-44ae-b658-da935d82b669))
    (pad "1" smd roundrect locked (at -1.8 0) (size 2.6 1.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.15625)
      (net 2 "+12V") (tstamp 9de9af38-9c15-4030-a07d-b0cf5104cc19))
    (pad "2" smd roundrect locked (at 1.8 0) (size 2.6 1.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.15625)
      (net 1 "GND") (tstamp a879ac1b-c75c-44e3-9b60-388e43e34b59))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/CP_Elec_4x5.4.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "BoardIO:LEMO-EPG.00.302.NLN" (layer "F.Cu")
    (tedit 60B81FFE) (tstamp 00000000-0000-0000-0000-00005f8ee632)
    (at 11.746 -36.524 180)
    (descr "Triaxial LEMO connector PCB elbow socket https://web.lemo.com/PARTSEARCH_WEB/V1.00Fw/PDF/4241383485754fa8adfc4e1aca22058b/EPG.00.302.NLN.pdf")
    (tags "LEMO THT Female Jack Horizontal")
    (property "P/N" "LEMO EPG.00.302.NLN")
    (property "Rating" "1kV")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (path "/00000000-0000-0000-0000-00005ffc79d3")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "J4" (at 0 -4.6 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b3189e72-f5b0-4336-816e-52ea369665ac)
    )
    (fp_text value "LVDS IN" (at 0 4.6 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 5c45dfa9-5296-4bb2-ac01-529bdb480af4)
    )
    (fp_line (start -3.5 -3.5) (end -3.5 3.5) (layer "F.SilkS") (width 0.12) (tstamp 2127ec44-b4f5-4391-822c-6d862b75f836))
    (fp_line (start -3.5 -3.5) (end 14 -3.5) (layer "F.SilkS") (width 0.12) (tstamp c6926dca-d193-4dc9-84bc-874409b781ec))
    (fp_line (start 14 -3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp ded3fa8a-6c10-4f29-9512-7faeaab26855))
    (fp_line (start -3.5 3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp dfdc3d4f-3301-44b5-a354-267de7274450))
    (fp_line (start 3.6 -3.6) (end 3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp 62e002b3-5859-4edc-bf7f-c10e004927e9))
    (fp_line (start -3.6 3.6) (end -3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp c2ac4e38-8b7a-4a82-9a20-5bb3bb9c7eca))
    (fp_line (start 3.6 3.6) (end -3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp c40773a8-8516-457d-8c9b-aa0de4b9d85d))
    (fp_line (start -3.6 -3.6) (end 3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp c9c3e48e-2b3d-4512-912e-d95c57c0ef6f))
    (fp_line (start -3.4 -3.4) (end -3.4 3.4) (layer "F.Fab") (width 0.1) (tstamp 1c048bf0-715d-4fd2-8b72-4b0ab8114f0b))
    (fp_line (start -3.4 3.4) (end 13.9 3.4) (layer "F.Fab") (width 0.1) (tstamp 24d20627-d2b2-4772-8e2d-f0fe3a850e0a))
    (fp_line (start 13.9 -3.4) (end -3.4 -3.4) (layer "F.Fab") (width 0.1) (tstamp 61f21038-44ba-4328-ab7e-45cfcf333cb9))
    (fp_line (start 13.9 3.4) (end 13.9 -3.4) (layer "F.Fab") (width 0.1) (tstamp c685396a-c968-4e1d-a48a-640d03227ba2))
    (pad "1" thru_hole roundrect locked (at -0.6 0 180) (size 1 1) (drill 0.6) (layers *.Cu *.Paste *.Mask) (roundrect_rratio 0.25)
      (net 11 "USER1_P") (pinfunction "In1") (tstamp 122d8a15-f4ba-46e1-93bd-7ab781cb0c8b))
    (pad "2" thru_hole circle locked (at 0.6 0 180) (size 1 1) (drill 0.6) (layers *.Cu *.Paste *.Mask)
      (net 12 "USER1_N") (pinfunction "In2") (tstamp 4cac0152-c4a8-4395-b818-45db7ef0c450))
    (pad "3" thru_hole circle locked (at -2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 27ba46a0-ffe9-43cb-bccb-56769f009af6))
    (pad "3" thru_hole circle locked (at 2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 3f0cf835-7ea7-4c0e-83b3-ae821c422081))
    (pad "3" thru_hole circle locked (at 2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 601eada6-8e73-4e86-999a-ee372d716f88))
    (pad "3" thru_hole circle locked (at -2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 8793ea23-73e7-4ef9-a3bf-b8a5461912c0))
    (model "${KIPRJMOD}/BoardIO.pretty/EPG.00.302.NLN.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz -90 0 0))
    )
  )

  (footprint "BoardIO:LEMO-EPG.00.302.NLN" (layer "F.Cu")
    (tedit 60B81FFE) (tstamp 00000000-0000-0000-0000-00005f8fd495)
    (at 11.746 -74.024 180)
    (descr "Triaxial LEMO connector PCB elbow socket https://web.lemo.com/PARTSEARCH_WEB/V1.00Fw/PDF/4241383485754fa8adfc4e1aca22058b/EPG.00.302.NLN.pdf")
    (tags "LEMO THT Female Jack Horizontal")
    (property "P/N" "LEMO EPG.00.302.NLN")
    (property "Rating" "1kV")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (path "/00000000-0000-0000-0000-00005f92e70f")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "J1" (at 0 -4.6 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp c2189a85-f43d-4fbb-9882-5baf70bbc598)
    )
    (fp_text value "LVDS OUT" (at 0 4.6 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 5bad2a59-f5b1-4dae-a7ed-9d63d9fa3378)
    )
    (fp_line (start -3.5 -3.5) (end -3.5 3.5) (layer "F.SilkS") (width 0.12) (tstamp 260cd24d-09f6-49ec-9352-3d2c6b88fa54))
    (fp_line (start -3.5 3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp a42d2d74-34ee-4bd5-ba39-6d072e01fa2f))
    (fp_line (start -3.5 -3.5) (end 14 -3.5) (layer "F.SilkS") (width 0.12) (tstamp cd72177b-f155-4793-91af-c83ae130dea1))
    (fp_line (start 14 -3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp f2032134-0198-40c3-ac7c-0cfbd309c08a))
    (fp_line (start 3.6 -3.6) (end 3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp 28766a59-fa4a-41b6-b43f-351ed0a681fb))
    (fp_line (start 3.6 3.6) (end -3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp c48aa623-74e8-4864-9a9d-51bbc63078f6))
    (fp_line (start -3.6 -3.6) (end 3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp cd45b56f-ad59-4bde-96c3-ae2ef754ce15))
    (fp_line (start -3.6 3.6) (end -3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp d5b6b942-70af-43e0-bcb0-15447d7be683))
    (fp_line (start 13.9 3.4) (end 13.9 -3.4) (layer "F.Fab") (width 0.1) (tstamp 8323cbd0-fa61-4522-8a10-d0e682875f8d))
    (fp_line (start -3.4 -3.4) (end -3.4 3.4) (layer "F.Fab") (width 0.1) (tstamp 8800b4a3-6fc6-42c8-b63f-995eb41b3818))
    (fp_line (start 13.9 -3.4) (end -3.4 -3.4) (layer "F.Fab") (width 0.1) (tstamp 93190fff-ac81-4a5e-8a5e-707f2209436f))
    (fp_line (start -3.4 3.4) (end 13.9 3.4) (layer "F.Fab") (width 0.1) (tstamp de14b485-c64f-46f9-88e1-1ab2f66fca05))
    (pad "1" thru_hole roundrect locked (at -0.6 0 180) (size 1 1) (drill 0.6) (layers *.Cu *.Paste *.Mask) (roundrect_rratio 0.25)
      (net 5 "CLK_P") (pinfunction "In1") (tstamp 8d097ee8-62bd-484b-9c97-72568aecaa0e))
    (pad "2" thru_hole circle locked (at 0.6 0 180) (size 1 1) (drill 0.6) (layers *.Cu *.Paste *.Mask)
      (net 6 "CLK_N") (pinfunction "In2") (tstamp 825d2267-170d-4596-9987-6a4212c5929e))
    (pad "3" thru_hole circle locked (at 2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 2e44e4c9-5d5f-4b8d-84a0-b35a8eee91b9))
    (pad "3" thru_hole circle locked (at -2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 47c5b4e8-c7ac-4690-948e-4b59a22c32e4))
    (pad "3" thru_hole circle locked (at -2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp d7f8be1c-736b-4a68-93ab-f6431465c4f7))
    (pad "3" thru_hole circle locked (at 2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp f0828be1-981d-4353-98af-d1fa2267dd59))
    (model "${KIPRJMOD}/BoardIO.pretty/EPG.00.302.NLN.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz -90 0 0))
    )
  )

  (footprint "Capacitor_SMD:CP_Elec_8x10" (layer "F.Cu")
    (tedit 5BCA39D0) (tstamp 00000000-0000-0000-0000-00005f8fdf68)
    (at 99.874 -77.597 180)
    (descr "SMD capacitor, aluminum electrolytic, Nichicon, 8.0x10mm")
    (tags "capacitor electrolytic")
    (property "P/N" "Nichicon UWT1C471MNL1GS")
    (property "Rating" "16V")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (path "/00000000-0000-0000-0000-00005f751351")
    (attr smd)
    (fp_text reference "C1" (at -5.155 -4.064) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 6848e982-b8d0-4dd3-a9f9-5d6173fb9bc2)
    )
    (fp_text value "470u" (at -0.202 -2.032) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp aaa30cf5-25e9-40a0-a096-2972d83f2a98)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 8a6f1f47-f2c5-4965-8f4e-2f117342b03f)
    )
    (fp_line (start 4.26 4.26) (end 4.26 1.51) (layer "F.SilkS") (width 0.12) (tstamp 23db413c-3052-4ab1-9a8b-bb822911e3f3))
    (fp_line (start -3.195563 -4.26) (end 4.26 -4.26) (layer "F.SilkS") (width 0.12) (tstamp 24c8e364-192e-4ffe-81c7-082256e5deb7))
    (fp_line (start -4.26 3.195563) (end -3.195563 4.26) (layer "F.SilkS") (width 0.12) (tstamp 44bc3cb4-3bb7-4474-8e80-0aa4b477f2b4))
    (fp_line (start 4.26 -4.26) (end 4.26 -1.51) (layer "F.SilkS") (width 0.12) (tstamp 4f9a07e2-bb15-473d-a012-7b2888e23e64))
    (fp_line (start -5.5 -2.51) (end -4.5 -2.51) (layer "F.SilkS") (width 0.12) (tstamp 72a4e44b-f722-48cd-9f78-1bcea276583b))
    (fp_line (start -4.26 -3.195563) (end -4.26 -1.51) (layer "F.SilkS") (width 0.12) (tstamp 80ba547c-361d-4d64-9cfe-cce75a4cf738))
    (fp_line (start -4.26 3.195563) (end -4.26 1.51) (layer "F.SilkS") (width 0.12) (tstamp a8278692-478f-4581-a6e0-5f1a40f5e577))
    (fp_line (start -4.26 -3.195563) (end -3.195563 -4.26) (layer "F.SilkS") (width 0.12) (tstamp b474c35c-b046-41e4-b7cc-92ed9036dc7a))
    (fp_line (start -3.195563 4.26) (end 4.26 4.26) (layer "F.SilkS") (width 0.12) (tstamp c48ef195-c5d0-4514-a4bd-02f1dcea5f58))
    (fp_line (start -5 -3.01) (end -5 -2.01) (layer "F.SilkS") (width 0.12) (tstamp cc6ce020-512d-495a-b4cd-ccdb19081574))
    (fp_line (start 5.25 1.5) (end 4.4 1.5) (layer "F.CrtYd") (width 0.05) (tstamp 202ddb01-5527-4674-b3ef-96b06ab645ef))
    (fp_line (start -4.4 -3.25) (end -3.25 -4.4) (layer "F.CrtYd") (width 0.05) (tstamp 21efc2ff-6c0c-4f86-8861-901f5978f0ac))
    (fp_line (start 4.4 1.5) (end 4.4 4.4) (layer "F.CrtYd") (width 0.05) (tstamp 2a60d33c-8604-46b2-84b1-caf93ca219a5))
    (fp_line (start -5.25 1.5) (end -4.4 1.5) (layer "F.CrtYd") (width 0.05) (tstamp 5433e9e9-3994-45c4-94a8-23afdaf8b806))
    (fp_line (start -4.4 1.5) (end -4.4 3.25) (layer "F.CrtYd") (width 0.05) (tstamp 5dfd9a7e-0ecb-4538-b744-e060f63e3a65))
    (fp_line (start 5.25 -1.5) (end 5.25 1.5) (layer "F.CrtYd") (width 0.05) (tstamp 61de50c2-e288-4fa3-b78a-2fe7c4eb8091))
    (fp_line (start -5.25 -1.5) (end -5.25 1.5) (layer "F.CrtYd") (width 0.05) (tstamp 961a3feb-48ae-4dd8-bd5a-a8ac31a5afd2))
    (fp_line (start 4.4 -1.5) (end 5.25 -1.5) (layer "F.CrtYd") (width 0.05) (tstamp 98dc46f9-18c8-470d-bd40-7040c14c5c6b))
    (fp_line (start -3.25 -4.4) (end 4.4 -4.4) (layer "F.CrtYd") (width 0.05) (tstamp a0d4b52c-499f-43de-b223-520ce6c3b88f))
    (fp_line (start -4.4 3.25) (end -3.25 4.4) (layer "F.CrtYd") (width 0.05) (tstamp ac89cdf2-b47e-4aef-b2e4-13b63d901ff2))
    (fp_line (start -4.4 -1.5) (end -5.25 -1.5) (layer "F.CrtYd") (width 0.05) (tstamp c8a583d3-2d82-4d4e-a687-dc3a34fdbaf1))
    (fp_line (start -4.4 -3.25) (end -4.4 -1.5) (layer "F.CrtYd") (width 0.05) (tstamp cb309988-18d0-48af-9b4c-451211bbd4f3))
    (fp_line (start 4.4 -4.4) (end 4.4 -1.5) (layer "F.CrtYd") (width 0.05) (tstamp dc230112-47e2-414e-ba87-940f025b1aee))
    (fp_line (start -3.25 4.4) (end 4.4 4.4) (layer "F.CrtYd") (width 0.05) (tstamp e1eafad5-b46b-4fa2-81c2-93197224fb8c))
    (fp_line (start -4.15 -3.15) (end -3.15 -4.15) (layer "F.Fab") (width 0.1) (tstamp 2b7572d4-a2ef-4c91-afb3-02c9976fd585))
    (fp_line (start -3.15 -4.15) (end 4.15 -4.15) (layer "F.Fab") (width 0.1) (tstamp 357a9a2e-4f0a-4910-ac04-29b6ffa6c71e))
    (fp_line (start 4.15 -4.15) (end 4.15 4.15) (layer "F.Fab") (width 0.1) (tstamp 37faf08b-ea94-49a5-aef1-9342622c3f64))
    (fp_line (start -3.162278 -1.9) (end -3.162278 -1.1) (layer "F.Fab") (width 0.1) (tstamp 409f3ebb-dbbc-4a0e-983a-ff109cca053d))
    (fp_line (start -4.15 -3.15) (end -4.15 3.15) (layer "F.Fab") (width 0.1) (tstamp 4ae3c748-38f7-4057-b856-e86045fd7ab8))
    (fp_line (start -3.15 4.15) (end 4.15 4.15) (layer "F.Fab") (width 0.1) (tstamp 709835dd-38c7-4666-b7d7-b1973b010646))
    (fp_line (start -4.15 3.15) (end -3.15 4.15) (layer "F.Fab") (width 0.1) (tstamp 90da8328-1a23-4156-a0c0-489fd47ce8b1))
    (fp_line (start -3.562278 -1.5) (end -2.762278 -1.5) (layer "F.Fab") (width 0.1) (tstamp a74ed41f-e18d-43ba-9446-e73d4814b918))
    (fp_circle (center 0 0) (end 4 0) (layer "F.Fab") (width 0.1) (fill none) (tstamp 66230d0a-6fa8-4c58-b923-892d3f73d53b))
    (pad "1" smd roundrect locked (at -3.25 0 180) (size 3.5 2.5) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1)
      (net 14 "PEG+12Vo") (tstamp 7a7e7b00-cb54-4e7c-b0a2-239dc437bcd0))
    (pad "2" smd roundrect locked (at 3.25 0 180) (size 3.5 2.5) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.1)
      (net 1 "GND") (tstamp 8610f8b3-0e6a-4e92-bea1-34763c10efb6))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/CP_Elec_8x10.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Connector_BarrelJack:BarrelJack_CUI_PJ-063AH_Horizontal" (layer "F.Cu")
    (tedit 5B0886BD) (tstamp 00000000-0000-0000-0000-00005f924504)
    (at 82.224 -84.851)
    (descr "Barrel Jack, 2.0mm ID, 5.5mm OD, 24V, 8A, no switch, https://www.cui.com/product/resource/pj-063ah.pdf")
    (tags "barrel jack cui dc power")
    (property "P/N" "CUI PJ-063AH")
    (property "Rating" "24V")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (path "/00000000-0000-0000-0000-00005fa3755c")
    (attr through_hole)
    (fp_text reference "J12" (at 0 -2.3) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 6fde0b53-291c-4dee-afe3-f5bb4470e239)
    )
    (fp_text value "DC jack" (at 0 13) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp a72267ef-95a8-47d2-ac7e-0abc947bac8e)
    )
    (fp_text user "${REFERENCE}" (at 0 5.5) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 910d4697-b685-442e-a72b-9f3b7731c6ef)
    )
    (fp_line (start 5.11 12.11) (end -5.11 12.11) (layer "F.SilkS") (width 0.12) (tstamp 1b6d9514-4e02-45ec-befb-864231c12be2))
    (fp_line (start -1 -1.3) (end 1 -1.3) (layer "F.SilkS") (width 0.12) (tstamp 2400faa9-8f73-4423-bc71-25e452fdcc71))
    (fp_line (start 5.11 -1.11) (end 5.11 4.95) (layer "F.SilkS") (width 0.12) (tstamp 3acc49ac-b1da-4c4c-b62e-0d105ed68a4f))
    (fp_line (start 5.11 9.05) (end 5.11 12.11) (layer "F.SilkS") (width 0.12) (tstamp 7ee4c2f0-c46b-4cf4-be01-b84052c4da86))
    (fp_line (start -5.11 4.95) (end -5.11 -1.11) (layer "F.SilkS") (width 0.12) (tstamp 80aaebf0-6ac5-4ee2-b7ae-fcada14b4fd0))
    (fp_line (start -5.11 12.11) (end -5.11 9.05) (layer "F.SilkS") (width 0.12) (tstamp 8c8bad9e-8757-4f26-a60d-52259a4d66bb))
    (fp_line (start -5.11 -1.11) (end -2.3 -1.11) (layer "F.SilkS") (width 0.12) (tstamp bf35d675-9f19-4f7e-ad32-72ae74dce72d))
    (fp_line (start 2.3 -1.11) (end 5.11 -1.11) (layer "F.SilkS") (width 0.12) (tstamp f9005d69-faad-40aa-8956-027e8b6026d2))
    (fp_line (start -6 12.5) (end 6 12.5) (layer "F.CrtYd") (width 0.05) (tstamp 06ec85d8-7bae-48f5-9ab4-fff900ebe83f))
    (fp_line (start 6 12.5) (end 6 -1.5) (layer "F.CrtYd") (width 0.05) (tstamp 0d89820a-38b5-4d19-a2fd-b3632012c99a))
    (fp_line (start -6 -1.5) (end -6 12.5) (layer "F.CrtYd") (width 0.05) (tstamp 48013c88-c0e6-4c19-b335-ed41c5c2c5dd))
    (fp_line (start 6 -1.5) (end -6 -1.5) (layer "F.CrtYd") (width 0.05) (tstamp 57ac7059-6833-42cd-b143-6cd3cea6fc72))
    (fp_line (start -5 -1) (end -1 -1) (layer "F.Fab") (width 0.1) (tstamp 01775138-086d-4e21-8999-3000cb85bcd3))
    (fp_line (start 5 12) (end -5 12) (layer "F.Fab") (width 0.1) (tstamp 237ff0e6-16f9-4a83-96fb-3737fe9eaaff))
    (fp_line (start -5 12) (end -5 -1) (layer "F.Fab") (width 0.1) (tstamp 52411c9c-d324-4a5d-8b7b-ed694f689768))
    (fp_line (start 5 -1) (end 5 12) (layer "F.Fab") (width 0.1) (tstamp 670ffbae-af1d-43bc-ae81-f2ef6facdc59))
    (fp_line (start 0 0) (end 1 -1) (layer "F.Fab") (width 0.1) (tstamp 77053e9b-d522-4e0b-b989-f67d2a8ab2f9))
    (fp_line (start -1 -1) (end 0 0) (layer "F.Fab") (width 0.1) (tstamp 9c442c7e-dbbb-4046-a142-f4539ca186f0))
    (fp_line (start 1 -1) (end 5 -1) (layer "F.Fab") (width 0.1) (tstamp e4223bbc-4c6d-4022-839b-a85588999d3e))
    (pad "" np_thru_hole circle locked (at 0 9) (size 1.6 1.6) (drill 1.6) (layers *.Cu *.Mask) (tstamp 57d134b9-a844-4bb0-b2de-521af90b481e))
    (pad "1" thru_hole rect locked (at 0 0) (size 4 2) (drill oval 3 1) (layers *.Cu *.Mask)
      (net 14 "PEG+12Vo") (tstamp d177827d-80ab-4f9e-b0d5-e296f6acdd57))
    (pad "2" thru_hole oval locked (at 0 6) (size 3.3 2) (drill oval 2.3 1) (layers *.Cu *.Mask)
      (net 1 "GND") (tstamp 260a1ef8-15e2-43b7-aaca-ee272190f45a))
    (pad "MP" thru_hole oval locked (at -4.5 7) (size 2 3.5) (drill oval 1 2.5) (layers *.Cu *.Mask)
      (net 1 "GND") (pinfunction "MountPin") (tstamp 606f6b59-3e56-4b2d-88e2-35ccae66a145))
    (pad "MP" thru_hole oval locked (at 4.5 7) (size 2 3.5) (drill oval 1 2.5) (layers *.Cu *.Mask)
      (net 1 "GND") (pinfunction "MountPin") (tstamp a6329fba-723a-42b2-a232-16a5707a50af))
    (model "${KICAD6_3DMODEL_DIR}/Connector_BarrelJack.3dshapes/BarrelJack_CUI_PJ-063AH_Horizontal.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "BoardIO:Molex_878331031_2x05_P2.00mm_Horizontal" (layer "F.Cu")
    (tedit 60B7A636) (tstamp 00000000-0000-0000-0000-00005f9297ec)
    (at 56.896 -92.202 90)
    (descr "Through hole angled Molex connector, 2x05, 2.00mm pitch, 4.2mm pin length, double rows")
    (tags "Through hole angled Molex connector THT 2x05 2.00mm double row")
    (property "P/N" "Molex 87833-1031")
    (property "Rating" "125V")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (path "/00000000-0000-0000-0000-00005f881a54")
    (clearance 0.2)
    (fp_text reference "J11" (at 6.096 -3.302 90) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 7f424c7d-26ad-4a79-a8e4-e12fc8f59a8e)
    )
    (fp_text value "MultiSync" (at 4.1 10 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp d7aaa383-f308-41e3-9c14-4547fd2d3401)
    )
    (fp_text user "${REFERENCE}" (at 4.25 4) (layer "F.Fab")
      (effects (font (size 0.9 0.9) (thickness 0.135)))
      (tstamp 8a8ebd83-65ee-4258-9bf9-837ffe6527d8)
    )
    (fp_line (start 3.536 -0.13) (end 7.736 -0.13) (layer "F.SilkS") (width 0.12) (tstamp 044ef978-71da-4b96-b406-a01b0d1d4d74))
    (fp_line (start 7.736 2.31) (end 3.536 2.31) (layer "F.SilkS") (width 0.12) (tstamp 1197bad5-b734-4462-9623-f4932211944c))
    (fp_line (start 3.536 -0.25) (end 7.736 -0.25) (layer "F.SilkS") (width 0.12) (tstamp 137e0256-aa18-4271-951c-d363a3b473bc))
    (fp_line (start 3.44 3) (end 8.46 3) (layer "F.SilkS") (width 0.12) (tstamp 23810194-289e-4121-993a-fa3194cdc176))
    (fp_line (start 3.536 7.69) (end 7.736 7.69) (layer "F.SilkS") (width 0.12) (tstamp 33746045-2db1-4f4d-92a3-7d45088add27))
    (fp_line (start 3.536 0.23) (end 7.736 0.23) (layer "F.SilkS") (width 0.12) (tstamp 36bcf4af-92e1-498a-8181-04ee158db926))
    (fp_line (start 0.935 -0.31) (end 1.117886 -0.31) (layer "F.SilkS") (width 0.12) (tstamp 3776dab6-78f9-44cf-ac0b-f6a1ba00b005))
    (fp_line (start 3.536 1.69) (end 7.736 1.69) (layer "F.SilkS") (width 0.12) (tstamp 3fdb5269-fda2-47b7-9854-be3d2f65b115))
    (fp_line (start 0.935 0.31) (end 1.117886 0.31) (layer "F.SilkS") (width 0.12) (tstamp 4005362f-9707-46dd-99dd-2b2fbfb4cd7a))
    (fp_line (start 3.44 1) (end 8.46 1) (layer "F.SilkS") (width 0.12) (tstamp 4415336c-756b-4a39-8a08-4d30c94563ee))
    (fp_line (start 8.46 10.385) (end 8.46 -2.385) (layer "F.SilkS") (width 0.12) (tstamp 4a1de28f-7282-40e0-8430-9b9e35939b4b))
    (fp_line (start 0.882114 8.31) (end 1.117886 8.31) (layer "F.SilkS") (width 0.12) (tstamp 4b9ac969-aa3c-47a1-bfeb-55be7c6231f2))
    (fp_line (start 7.736 1.69) (end 7.736 2.31) (layer "F.SilkS") (width 0.12) (tstamp 4dd053a0-c3ff-4675-9936-741643067ad3))
    (fp_line (start 7.736 7.69) (end 7.736 8.31) (layer "F.SilkS") (width 0.12) (tstamp 53286800-0d07-40c7-b7c3-2e8d02415e5a))
    (fp_line (start 3.44 5) (end 8.46 5) (layer "F.SilkS") (width 0.12) (tstamp 54c103ee-c9b7-4b54-b000-18ae302da734))
    (fp_line (start 7.736 3.69) (end 7.736 4.31) (layer "F.SilkS") (width 0.12) (tstamp 5faadade-9e81-4dc0-9cc3-35e368677862))
    (fp_line (start 3.536 0.11) (end 7.736 0.11) (layer "F.SilkS") (width 0.12) (tstamp 63ed615e-5a26-4968-8830-4dc57a9a4cd5))
    (fp_line (start 0.882114 2.31) (end 1.117886 2.31) (layer "F.SilkS") (width 0.12) (tstamp 67da356a-0062-4cad-807d-a71b4dc5c93f))
    (fp_line (start 0.882114 7.69) (end 1.117886 7.69) (layer "F.SilkS") (width 0.12) (tstamp 6884f37a-1fc5-4f12-b66f-9c44e2a26eba))
    (fp_line (start -1 -1) (end 0 -1) (layer "F.SilkS") (width 0.12) (tstamp 6d0caee2-fb02-4240-bedd-d622e905aabf))
    (fp_line (start 7.736 6.31) (end 3.536 6.31) (layer "F.SilkS") (width 0.12) (tstamp 6ecd1f8c-4459-4d78-a959-3314324ebd58))
    (fp_line (start 3.536 -0.01) (end 7.736 -0.01) (layer "F.SilkS") (width 0.12) (tstamp 700bc666-f2c4-4ede-bf88-cb9400bd0403))
    (fp_line (start 0.882114 1.69) (end 1.117886 1.69) (layer "F.SilkS") (width 0.12) (tstamp 7156137c-6ab7-4bcc-b3e5-25250120616d))
    (fp_line (start 2.882114 1.69) (end 3.44 1.69) (layer "F.SilkS") (width 0.12) (tstamp 722fc9e4-3ae3-4fc3-9bd5-2c8c7017a2a5))
    (fp_line (start 2.882114 0.31) (end 3.44 0.31) (layer "F.SilkS") (width 0.12) (tstamp 747ce6f9-498c-42fc-82e0-5fb30f73080a))
    (fp_line (start 3.44 7) (end 8.46 7) (layer "F.SilkS") (width 0.12) (tstamp 77d80339-9ad2-49e5-8868-c9446da02506))
    (fp_line (start 7.736 8.31) (end 3.536 8.31) (layer "F.SilkS") (width 0.12) (tstamp 78087ce7-79af-4628-bd50-45f110d174ba))
    (fp_line (start 2.882114 8.31) (end 3.44 8.31) (layer "F.SilkS") (width 0.12) (tstamp 7e664113-b095-4770-8c44-e5419dc0c8a8))
    (fp_line (start 2.882114 3.69) (end 3.44 3.69) (layer "F.SilkS") (width 0.12) (tstamp 882d437e-398f-40fd-add3-c354c95add7a))
    (fp_line (start 3.44 -2.385) (end 3.44 10.385) (layer "F.SilkS") (width 0.12) (tstamp 8872bb18-f79e-405d-a080-2c88995cdb04))
    (fp_line (start 2.882114 7.69) (end 3.44 7.69) (layer "F.SilkS") (width 0.12) (tstamp 8c6d108e-4691-473c-9a34-6515cd471e2c))
    (fp_line (start 0.882114 6.31) (end 1.117886 6.31) (layer "F.SilkS") (width 0.12) (tstamp 8cc043bc-a8ce-4132-a186-a0fc83e05a0a))
    (fp_line (start 3.536 3.69) (end 7.736 3.69) (layer "F.SilkS") (width 0.12) (tstamp 9b0962e8-1489-46cb-84ed-afcf0bf59696))
    (fp_line (start 2.882114 6.31) (end 3.44 6.31) (layer "F.SilkS") (width 0.12) (tstamp a44f1656-487a-4b75-b416-4bf2bfe1af2e))
    (fp_line (start 7.736 5.69) (end 7.736 6.31) (layer "F.SilkS") (width 0.12) (tstamp ade445ad-e483-4899-9ced-3f5d41818bc4))
    (fp_line (start 8.46 -2.385) (end 3.44 -2.385) (layer "F.SilkS") (width 0.12) (tstamp b8bc2630-f9d8-49ee-8840-027b6991cec4))
    (fp_line (start 7.736 -0.31) (end 7.736 0.31) (layer "F.SilkS") (width 0.12) (tstamp b8c022dd-2015-4fe2-b885-f0c0760377a0))
    (fp_line (start 0.882114 3.69) (end 1.117886 3.69) (layer "F.SilkS") (width 0.12) (tstamp c5755df6-93c6-44b3-a4d2-0766bb98fce1))
    (fp_line (start 2.882114 -0.31) (end 3.44 -0.31) (layer "F.SilkS") (width 0.12) (tstamp c8d22be8-8f1f-4c7e-bfa6-77b75670c48a))
    (fp_line (start 7.736 0.31) (end 3.536 0.31) (layer "F.SilkS") (width 0.12) (tstamp cad28010-a754-407a-9c38-5e3a55e9c692))
    (fp_line (start 2.882114 4.31) (end 3.44 4.31) (layer "F.SilkS") (width 0.12) (tstamp cce40046-830b-4ef2-a857-4072ac3aa814))
    (fp_line (start 2.882114 5.69) (end 3.44 5.69) (layer "F.SilkS") (width 0.12) (tstamp d32d79d5-be19-43e0-b037-57b2e388c800))
    (fp_line (start 3.536 -0.31) (end 7.736 -0.31) (layer "F.SilkS") (width 0.12) (tstamp db43a5fb-356e-4ad1-b00d-24d284b15c06))
    (fp_line (start 0.882114 5.69) (end 1.117886 5.69) (layer "F.SilkS") (width 0.12) (tstamp dc374079-f50d-4467-b626-e4b720e9a216))
    (fp_line (start 2.882114 2.31) (end 3.44 2.31) (layer "F.SilkS") (width 0.12) (tstamp dc75b41f-67e9-402a-81ce-126fd1eafa74))
    (fp_line (start 3.44 10.385) (end 8.46 10.385) (layer "F.SilkS") (width 0.12) (tstamp ddb1bf8f-5266-4750-a3d1-fc9eb9c9344d))
    (fp_line (start 3.536 5.69) (end 7.736 5.69) (layer "F.SilkS") (width 0.12) (tstamp e2012c39-fec3-4923-9c42-958d226c49f6))
    (fp_line (start -1 0) (end -1 -1) (layer "F.SilkS") (width 0.12) (tstamp e8781797-d6ac-4cec-af72-e023614969b0))
    (fp_line (start 0.882114 4.31) (end 1.117886 4.31) (layer "F.SilkS") (width 0.12) (tstamp e9b0b062-cf20-402f-96cb-919d906485db))
    (fp_line (start 7.736 4.31) (end 3.536 4.31) (layer "F.SilkS") (width 0.12) (tstamp ed5cef4a-38da-405e-9734-e83677965d1d))
    (fp_line (start -1.1 10.5) (end 8.6 10.5) (layer "F.CrtYd") (width 0.05) (tstamp 482d3bf2-92d5-460d-ab45-d0a0ba637ba5))
    (fp_line (start 8.6 -2.5) (end -1.1 -2.5) (layer "F.CrtYd") (width 0.05) (tstamp 89c2ed9a-3187-4106-828f-5b1b5f1b1f06))
    (fp_line (start 8.6 10.5) (end 8.6 -2.5) (layer "F.CrtYd") (width 0.05) (tstamp dd6e7a16-d0df-4e45-a06a-5ad16ea7e7b0))
    (fp_line (start -1.1 -2.5) (end -1.1 10.5) (layer "F.CrtYd") (width 0.05) (tstamp df078000-55c0-4fae-82a2-29de418e7765))
    (fp_line (start -0.25 2.25) (end 3.5 2.25) (layer "F.Fab") (width 0.1) (tstamp 07a2a2fc-25cc-46a1-96b4-afe12dc439a8))
    (fp_line (start -0.25 7.75) (end -0.25 8.25) (layer "F.Fab") (width 0.1) (tstamp 129e7867-e0e9-4ab5-9f38-9b7c87a01fc4))
    (fp_line (start -0.25 5.75) (end 3.5 5.75) (layer "F.Fab") (width 0.1) (tstamp 187d150f-3370-4284-94bd-56479ba28106))
    (fp_line (start -0.25 7.75) (end 3.5 7.75) (layer "F.Fab") (width 0.1) (tstamp 19c046fa-54b8-4ac5-9b31-55086e0d46da))
    (fp_line (start -0.25 1.75) (end 3.5 1.75) (layer "F.Fab") (width 0.1) (tstamp 1f404173-5153-4733-a218-387e0251c027))
    (fp_line (start 3.476 3.75) (end 7.676 3.75) (layer "F.Fab") (width 0.1) (tstamp 24833129-c5c2-45cd-a0c9-3b2811624a2e))
    (fp_line (start 3.476 -0.25) (end 7.676 -0.25) (layer "F.Fab") (width 0.1) (tstamp 2ada198b-4239-45d1-9a0a-88e1f8578330))
    (fp_line (start -0.25 -0.25) (end -0.25 0.25) (layer "F.Fab") (width 0.1) (tstamp 3d9e53e2-ba88-4349-906e-cbbd4eaedc7a))
    (fp_line (start 3.476 0.25) (end 7.676 0.25) (layer "F.Fab") (width 0.1) (tstamp 3fb974e8-36ce-4adc-8b89-52c6825171bc))
    (fp_line (start -0.25 6.25) (end 3.5 6.25) (layer "F.Fab") (width 0.1) (tstamp 4157fce7-05eb-40c6-9697-477b2d2f1d1f))
    (fp_line (start 8.4 -2.325) (end 8.4 10.325) (layer "F.Fab") (width 0.1) (tstamp 65991a8b-9106-4cf4-a310-e51405735008))
    (fp_line (start -0.25 8.25) (end 3.5 8.25) (layer "F.Fab") (width 0.1) (tstamp 6f0dcf31-af76-4b38-9082-d904b7c7df73))
    (fp_line (start 3.476 8.25) (end 7.676 8.25) (layer "F.Fab") (width 0.1) (tstamp 7307a354-e3c0-430b-ab64-07fc14bfe6b9))
    (fp_line (start 3.476 4.25) (end 7.676 4.25) (layer "F.Fab") (width 0.1) (tstamp 79b97cc3-2ea2-4072-9264-12550340fa29))
    (fp_line (start 3.875 -2.325) (end 8.4 -2.325) (layer "F.Fab") (width 0.1) (tstamp 7e8a212b-d2fb-4590-9d35-828757d8bd26))
    (fp_line (start -0.25 1.75) (end -0.25 2.25) (layer "F.Fab") (width 0.1) (tstamp 84ade972-4a1c-4253-ab37-96411cb36e9f))
    (fp_line (start 3.5 10.325) (end 3.5 -2.05) (layer "F.Fab") (width 0.1) (tstamp 85d34708-bc95-4c7f-a51e-dc3e1e97b8f0))
    (fp_line (start -0.25 5.75) (end -0.25 6.25) (layer "F.Fab") (width 0.1) (tstamp 8886bd4d-06dd-475e-af02-eaa272de001a))
    (fp_line (start -0.25 -0.25) (end 3.5 -0.25) (layer "F.Fab") (width 0.1) (tstamp 8ad562b1-2d73-4ef6-b1d4-820e8002bf26))
    (fp_line (start 3.476 1.75) (end 7.676 1.75) (layer "F.Fab") (width 0.1) (tstamp 8b298fe7-ae63-4c89-a038-75b85387f396))
    (fp_line (start 7.676 7.75) (end 7.676 8.25) (layer "F.Fab") (width 0.1) (tstamp 9360d621-6239-461e-b734-35fffade8dc4))
    (fp_line (start -0.25 3.75) (end -0.25 4.25) (layer "F.Fab") (width 0.1) (tstamp 97240820-1356-42d6-a2df-bb344d4262e6))
    (fp_line (start -0.25 0.25) (end 3.5 0.25) (layer "F.Fab") (width 0.1) (tstamp 9985d147-8148-467d-8fcc-483ea720388d))
    (fp_line (start 7.676 3.75) (end 7.676 4.25) (layer "F.Fab") (width 0.1) (tstamp 9a24a0d8-edd1-4b10-bbfb-bc0367d27a72))
    (fp_line (start -0.25 3.75) (end 3.5 3.75) (layer "F.Fab") (width 0.1) (tstamp a8cf298c-9b1b-456e-a5c8-50e5e1c60c52))
    (fp_line (start 3.476 5.75) (end 7.676 5.75) (layer "F.Fab") (width 0.1) (tstamp b4373af4-c1cd-4670-8015-20c3b5d04ac9))
    (fp_line (start 7.676 5.75) (end 7.676 6.25) (layer "F.Fab") (width 0.1) (tstamp c7617210-179d-46f6-bc77-d90d19837017))
    (fp_line (start 3.476 7.75) (end 7.676 7.75) (layer "F.Fab") (width 0.1) (tstamp c8a63e36-b735-41b3-a888-e844107a3f10))
    (fp_line (start 3.476 6.25) (end 7.676 6.25) (layer "F.Fab") (width 0.1) (tstamp cada5ad2-c7a0-4dde-969e-8158ca641f12))
    (fp_line (start 7.676 1.75) (end 7.676 2.25) (layer "F.Fab") (width 0.1) (tstamp cda4107c-cb54-4b01-b835-7ea3868970a2))
    (fp_line (start 7.676 -0.25) (end 7.676 0.25) (layer "F.Fab") (width 0.1) (tstamp d10268ff-3430-4c01-9a42-24dd7298d115))
    (fp_line (start 3.875 -2.325) (end 3.5 -2.05) (layer "F.Fab") (width 0.1) (tstamp d3f86b8a-b4c9-4530-b618-57cc62d7b23b))
    (fp_line (start 3.476 2.25) (end 7.676 2.25) (layer "F.Fab") (width 0.1) (tstamp dfd21e20-b661-4b58-b98b-bdecc7c0e4c5))
    (fp_line (start 8.4 10.325) (end 3.5 10.325) (layer "F.Fab") (width 0.1) (tstamp ed3de991-63fa-4e91-a274-c88f40dabf30))
    (fp_line (start -0.25 4.25) (end 3.5 4.25) (layer "F.Fab") (width 0.1) (tstamp f220b95e-169e-479f-8cf7-879710de557a))
    (pad "1" thru_hole rect locked (at 0 0 90) (size 1.35 1.35) (drill 0.8) (layers *.Cu *.Mask)
      (net 5 "CLK_P") (pinfunction "Pin_1") (tstamp d13db913-8e5a-4e04-890e-b15fb07ef0f4))
    (pad "2" thru_hole oval locked (at 2 0 90) (size 1.35 1.35) (drill 0.8) (layers *.Cu *.Mask)
      (net 6 "CLK_N") (pinfunction "Pin_2") (tstamp 7ed9f579-d987-4220-8325-7bf98000f142))
    (pad "3" thru_hole oval locked (at 0 2 90) (size 1.35 1.35) (drill 0.8) (layers *.Cu *.Mask)
      (net 7 "TRIG_P") (pinfunction "Pin_3") (tstamp f7b47244-9542-4639-a103-799b8dfaf321))
    (pad "4" thru_hole oval locked (at 2 2 90) (size 1.35 1.35) (drill 0.8) (layers *.Cu *.Mask)
      (net 8 "TRIG_N") (pinfunction "Pin_4") (tstamp c5ffe5c8-790e-4cbf-827e-59e98c310b0f))
    (pad "5" thru_hole oval locked (at 0 4 90) (size 1.35 1.35) (drill 0.8) (layers *.Cu *.Mask)
      (net 9 "TIMES_P") (pinfunction "Pin_5") (tstamp 832e4442-f944-4b87-9333-874c45929c10))
    (pad "6" thru_hole oval locked (at 2 4 90) (size 1.35 1.35) (drill 0.8) (layers *.Cu *.Mask)
      (net 10 "TIMES_N") (pinfunction "Pin_6") (tstamp bd3cf8c3-5b82-4a22-9393-0313dbd1de9a))
    (pad "7" thru_hole oval locked (at 0 6 90) (size 1.35 1.35) (drill 0.8) (layers *.Cu *.Mask)
      (net 11 "USER1_P") (pinfunction "Pin_7") (tstamp 1e624997-6264-40b9-bac0-a1f20aab4daa))
    (pad "8" thru_hole oval locked (at 2 6 90) (size 1.35 1.35) (drill 0.8) (layers *.Cu *.Mask)
      (net 12 "USER1_N") (pinfunction "Pin_8") (tstamp 521628a7-b7d5-4c8d-9d63-4582415cbd12))
    (pad "9" thru_hole oval locked (at 0 8 90) (size 1.35 1.35) (drill 0.8) (layers *.Cu *.Mask)
      (net 3 "USER2_L") (pinfunction "Pin_9") (tstamp 70167237-bf7f-462d-bd7b-fb52accd0af5))
    (pad "10" thru_hole oval locked (at 2 8 90) (size 1.35 1.35) (drill 0.8) (layers *.Cu *.Mask)
      (net 4 "USER3_L") (pinfunction "Pin_10") (tstamp b5d70d66-e8a7-4180-97e5-d8382f4c0789))
    (model "${KIPRJMOD}/BoardIO.pretty/878331031.wrl"
      (offset (xyz 5.25 -4 3.25))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 90))
    )
  )

  (footprint "BoardIO:LEMO-EPL.00.250.NTN" (layer "F.Cu")
    (tedit 60B820E2) (tstamp 00000000-0000-0000-0000-00005f978245)
    (at 11.746 -11.524 180)
    (descr "Coaxial LEMO connector PCB elbow socket https://web.lemo.com/PARTSEARCH_WEB/V1.00Fw/PDF/c05b81de6963431aa48906be13a46183/EPL.00.250.NTN.pdf")
    (tags "LEMO THT Female Jack Horizontal")
    (property "P/N" "LEMO EPL.00.250.NTN")
    (property "Rating" "4A")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (path "/00000000-0000-0000-0000-00005f941c17")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "J6" (at -0.192 -4.412 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 6f70b36f-4175-4799-827c-9b1f87bc7e9c)
    )
    (fp_text value "TTL IN" (at 0 4.7 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f64cfd4d-5aa4-4f8b-ab6b-67f1dc86c064)
    )
    (fp_line (start -3.5 -3.5) (end -3.5 3.5) (layer "F.SilkS") (width 0.12) (tstamp 0f4dd3c0-555c-46f9-a882-77d730a915fe))
    (fp_line (start -3.5 -3.5) (end 14 -3.5) (layer "F.SilkS") (width 0.12) (tstamp 44f2052d-4bee-4968-b58c-a34771215427))
    (fp_line (start 14 -3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp 5092cf0f-8816-494b-886c-0fa8c0d09fa6))
    (fp_line (start -3.5 3.5) (end 14 3.5) (layer "F.SilkS") (width 0.12) (tstamp 9e71733c-ac8c-4f23-9984-d639937799e2))
    (fp_line (start 3.6 -3.6) (end 3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp 0ed25d15-330e-486c-99c3-b9608bee8f7c))
    (fp_line (start 3.6 3.6) (end -3.6 3.6) (layer "F.CrtYd") (width 0.05) (tstamp 53585232-ccdf-4cff-aaa1-10426828aa85))
    (fp_line (start -3.6 3.6) (end -3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp 8a35854a-ab92-46f5-a42c-3197cc24b4cb))
    (fp_line (start -3.6 -3.6) (end 3.6 -3.6) (layer "F.CrtYd") (width 0.05) (tstamp d5ed25b4-a8ad-4f5e-9a2b-e8d27517dcc7))
    (fp_line (start -3.4 3.4) (end 13.9 3.4) (layer "F.Fab") (width 0.1) (tstamp 4880ded4-99d7-4abf-9644-dd13174b1b71))
    (fp_line (start 13.9 -3.4) (end -3.4 -3.4) (layer "F.Fab") (width 0.1) (tstamp 551ff931-4e57-4d89-b0b5-ee423b1f833f))
    (fp_line (start -3.4 -3.4) (end -3.4 3.4) (layer "F.Fab") (width 0.1) (tstamp 960c1e05-4129-4b72-9aee-5fbbf8d9b471))
    (fp_line (start 13.9 3.4) (end 13.9 -3.4) (layer "F.Fab") (width 0.1) (tstamp c1692c95-bcb3-4573-b434-818195968003))
    (pad "1" thru_hole roundrect locked (at 0 0 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask) (roundrect_rratio 0.25)
      (net 4 "USER3_L") (pinfunction "In") (tstamp 8a2047ac-2ab6-449d-98fc-04c20ae8c40e))
    (pad "2" thru_hole circle locked (at 2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 6040abab-bd32-4273-be29-82c9d137eba4))
    (pad "2" thru_hole circle locked (at 2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 750f7165-7ef9-4a1f-b719-8d334a7669fa))
    (pad "2" thru_hole circle locked (at -2.54 2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp 9b1c09dd-25f5-4fb5-b1e0-5c498aac1060))
    (pad "2" thru_hole circle locked (at -2.54 -2.54 180) (size 1.3 1.3) (drill 0.8) (layers *.Cu *.Paste *.Mask)
      (net 1 "GND") (pinfunction "Ext") (tstamp ce6f641a-92ed-44c3-a8c7-b47cc5df6348))
    (model "${KIPRJMOD}/BoardIO.pretty/EPL.00.250.NTN.wrl"
      (offset (xyz -3.5 0 3.5))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 90))
    )
  )

  (footprint "BoardIO:SIS1160" locked (layer "F.Cu")
    (tedit 5F8D98B5) (tstamp 00000000-0000-0000-0000-00005f97e415)
    (at 2.073 5)
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Netlist_Enabled" "N")
    (property "exclude_from_bom" "")
    (path "/00000000-0000-0000-0000-00005f9081c6")
    (attr exclude_from_bom)
    (fp_text reference "FID1" (at 11.6332 -0.8128) (layer "F.SilkS") hide
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 06789930-60ca-4e33-b852-83022d8ed9bc)
    )
    (fp_text value "BoardOutline" (at 3.302 -0.8128) (layer "F.Fab") hide
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f1924d4b-e0c7-49f7-8ea8-61b7729f8c5f)
    )
    (fp_line (start 54.85 -39.95) (end 37.55 -39.95) (layer "F.Fab") (width 0.2) (tstamp 14da5e15-39bc-470b-91e0-3cf2c956565e))
    (fp_line (start 54.85 -83.8) (end 37.55 -83.8) (layer "F.Fab") (width 0.2) (tstamp 1e61fcff-7810-4c66-b6b4-8aff027e062c))
    (fp_line (start 40.25 -6.45) (end 40.25 0) (layer "F.Fab") (width 0.2) (tstamp 24186a81-2b9a-4961-83a0-17a2fe1a7184))
    (fp_line (start 55.1 4.5) (end 43.9 4.5) (layer "F.Fab") (width 0.2) (tstamp 2f566fa8-9119-44d9-82fc-6b5619208a5f))
    (fp_line (start 40.25 0) (end 32.25 0) (layer "F.Fab") (width 0.2) (tstamp 3596f286-fb78-4bc9-9f25-1a984cd350ac))
    (fp_line (start 84.4 -76.225) (end 84.4 -32.225) (layer "F.Fab") (width 0.2) (tstamp 3dbeb6a5-2d9b-47c6-bffe-d567340438e2))
    (fp_line (start 65.3 -101.75) (end 65.3 -96.16) (layer "F.Fab") (width 0.2) (tstamp 3e605fa5-33f5-4398-9fd5-d69edb669b16))
    (fp_line (start 32.25 0) (end 32.25 -8.25) (layer "F.Fab") (width 0.2) (tstamp 3f299536-b02e-4588-8eef-a34568edc6a2))
    (fp_line (start 128.4 -32.225) (end 128.4 -76.225) (layer "F.Fab") (width 0.2) (tstamp 4403df02-7b6c-4e49-a00e-ae473ca21b1c))
    (fp_line (start 0 -106.65) (end 166.55 -106.65) (layer "F.Fab") (width 0.2) (tstamp 5a5809ef-fe46-4970-a15f-9ec445a2623d))
    (fp_line (start 52.5 -101.75) (end 65.3 -101.75) (layer "F.Fab") (width 0.2) (tstamp 694e15d1-3947-4599-a755-6d89d346891e))
    (fp_line (start 43.9 4.5) (end 43.899999 -6.45) (layer "F.Fab") (width 0.2) (tstamp 69aa985d-88b5-4aff-817e-a9574e709bc7))
    (fp_line (start 37.55 -57.25) (end 54.85 -57.25) (layer "F.Fab") (width 0.2) (tstamp 69bafa67-f176-46cf-a16a-35b3d3ddc6b7))
    (fp_line (start 84.4 -32.225) (end 128.4 -32.225) (layer "F.Fab") (width 0.2) (tstamp 7c601a3c-e89e-4674-83fc-5e5feed4bca0))
    (fp_line (start 37.55 -83.8) (end 37.55 -66.5) (layer "F.Fab") (width 0.2) (tstamp 804bcd43-01bd-40a6-aaad-ea9244bb1a8d))
    (fp_line (start 95.2 -7.769999) (end 95.2 4.5) (layer "F.Fab") (width 0.2) (tstamp 8aaa3be8-9cc7-4f4e-af15-dfc355b1defc))
    (fp_line (start 37.55 -66.5) (end 54.85 -66.5) (layer "F.Fab") (width 0.2) (tstamp 90b4ad0c-34bf-44c0-a4d2-9a10e19b1b6a))
    (fp_line (start 0 0) (end 0 -106.65) (layer "F.Fab") (width 0.2) (tstamp 91254da3-7a67-4aa7-8c36-ce684407c535))
    (fp_line (start 54.85 -57.25) (end 54.85 -39.95) (layer "F.Fab") (width 0.2) (tstamp aab21a9a-b696-4d64-9871-692da1d0e477))
    (fp_line (start 128.4 -76.225) (end 84.4 -76.225) (layer "F.Fab") (width 0.2) (tstamp abcfd372-a902-42aa-bc63-18bf57d2dadf))
    (fp_line (start 166.55 -106.65) (end 166.55 -8.269999) (layer "F.Fab") (width 0.2) (tstamp af18589c-76e4-4e83-8d0e-c705927343fd))
    (fp_line (start 37.55 -39.95) (end 37.55 -57.25) (layer "F.Fab") (width 0.2) (tstamp b4e8529f-8919-4d42-8d81-e068250a18f8))
    (fp_line (start 13.9 -8.25) (end 13.9 0) (layer "F.Fab") (width 0.2) (tstamp c10938d9-1dfc-4640-ad00-5f0534b0eed0))
    (fp_line (start 166.55 -8.269999) (end 95.7 -8.27) (layer "F.Fab") (width 0.2) (tstamp c3a45bdf-c50e-4fa9-b813-3c6c97f04a43))
    (fp_line (start 32.25 -8.25) (end 13.9 -8.25) (layer "F.Fab") (width 0.2) (tstamp ce2a7c25-79d5-4c10-a1c6-102bb285416c))
    (fp_line (start 65.3 -96.16) (end 52.5 -96.16) (layer "F.Fab") (width 0.2) (tstamp d582db39-587f-451a-90d9-eb8091cf606f))
    (fp_line (start 55.1 -3) (end 55.1 4.5) (layer "F.Fab") (width 0.2) (tstamp ec9a9b6d-dd76-4bef-8437-37d6070bb05a))
    (fp_line (start 52.5 -96.16) (end 52.5 -101.75) (layer "F.Fab") (width 0.2) (tstamp f1a37f7a-fb6c-4449-b974-5745872dba55))
    (fp_line (start 13.9 0) (end 0 0) (layer "F.Fab") (width 0.2) (tstamp f61ccf58-c93f-453a-a6f1-d310ac6366c1))
    (fp_line (start 57 4.5) (end 57 -3) (layer "F.Fab") (width 0.2) (tstamp f6e6acfe-9692-4fc5-aaac-7296870f5338))
    (fp_line (start 95.2 4.5) (end 57 4.5) (layer "F.Fab") (width 0.2) (tstamp fa73ef9b-421c-474b-9329-3bcc0a524f2d))
    (fp_line (start 54.85 -66.5) (end 54.85 -83.8) (layer "F.Fab") (width 0.2) (tstamp fee68043-8542-49b3-b0fc-689530d533ab))
    (fp_arc (start 95.7 -7.77) (end 95.7 -8.27) (angle -90) (layer "F.Fab") (width 0.2) (tstamp 3a8eeba0-f41a-4bf4-97c5-ddb8c94d8979))
    (fp_arc (start 56.05 -3) (end 57 -3) (angle -180) (layer "F.Fab") (width 0.2) (tstamp 98b993d4-a47d-45e6-9d0b-9ae7be78307b))
    (fp_arc (start 42.074999 -6.45) (end 43.899999 -6.45) (angle -180) (layer "F.Fab") (width 0.2) (tstamp 9af77456-7c95-429a-bc4f-618a1272dffd))
    (fp_circle (center 6.4 -96.95) (end 8.15 -96.95) (layer "F.Fab") (width 0.2) (fill none) (tstamp 16d97d15-2e23-4417-8b1c-67b9397e64af))
    (fp_circle (center 46.2 -75.15) (end 54.45 -75.15) (layer "F.Fab") (width 0.2) (fill none) (tstamp 245ca573-ed2b-4f44-9d2a-7851de74e5dd))
    (fp_circle (center 6.4 -4.81) (end 8.15 -4.81) (layer "F.Fab") (width 0.2) (fill none) (tstamp 44fcf1a0-717e-4590-9bab-5889a78fefd8))
    (fp_circle (center 46.2 -48.6) (end 54.45 -48.6) (layer "F.Fab") (width 0.2) (fill none) (tstamp 8390a7d1-7319-4c1b-8459-a21142a1ef6b))
  )

  (footprint "Symbol:WEEE-Logo_4.2x6mm_SilkScreen" (layer "F.Cu")
    (tedit 0) (tstamp 00000000-0000-0000-0000-00005f97fd17)
    (at 65.786 -49.784)
    (descr "Waste Electrical and Electronic Equipment Directive")
    (tags "Logo WEEE")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "exclude_from_bom" "")
    (path "/896c6c55-fe0f-4375-9551-26dd08b1f674")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "FID3" (at 0 0) (layer "F.SilkS") hide
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f5916e08-55a0-4b9c-adb7-86296beef0c7)
    )
    (fp_text value "Logo Trash" (at 0.75 0) (layer "F.Fab") hide
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 9954125e-607b-4a3d-b97b-d77acbc09473)
    )
    (fp_poly (pts (xy 1.747822 3.017822)
      (xy -1.772971 3.017822)
      (xy -1.772971 2.150198)
      (xy 1.747822 2.150198)
      (xy 1.747822 3.017822)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 33b3eecb-4328-42f1-a694-b89410e6b728))
    (fp_poly (pts (xy 2.12443 -2.935152)
      (xy 2.123811 -2.848069)
      (xy 1.672086 -2.389109)
      (xy 1.220361 -1.930148)
      (xy 1.220032 -1.719529)
      (xy 1.219703 -1.508911)
      (xy 0.94461 -1.508911)
      (xy 0.937522 -1.45547)
      (xy 0.934838 -1.431112)
      (xy 0.930313 -1.385241)
      (xy 0.924191 -1.320595)
      (xy 0.916712 -1.239909)
      (xy 0.908119 -1.145919)
      (xy 0.898654 -1.041363)
      (xy 0.888558 -0.928975)
      (xy 0.878074 -0.811493)
      (xy 0.867444 -0.691652)
      (xy 0.856909 -0.572189)
      (xy 0.846713 -0.455841)
      (xy 0.837095 -0.345343)
      (xy 0.8283 -0.243431)
      (xy 0.820568 -0.152842)
      (xy 0.814142 -0.076313)
      (xy 0.809263 -0.016579)
      (xy 0.806175 0.023624)
      (xy 0.805117 0.041559)
      (xy 0.805118 0.041644)
      (xy 0.812827 0.056035)
      (xy 0.835981 0.085748)
      (xy 0.874895 0.131131)
      (xy 0.929884 0.192529)
      (xy 1.001264 0.270288)
      (xy 1.089349 0.364754)
      (xy 1.194454 0.476272)
      (xy 1.316895 0.605188)
      (xy 1.35131 0.641287)
      (xy 1.897137 1.213416)
      (xy 1.808881 1.301436)
      (xy 1.737485 1.223758)
      (xy 1.711366 1.195686)
      (xy 1.670566 1.152274)
      (xy 1.617777 1.096366)
      (xy 1.555691 1.030808)
      (xy 1.487 0.958441)
      (xy 1.414396 0.882112)
      (xy 1.37096 0.836524)
      (xy 1.289416 0.751119)
      (xy 1.223504 0.68271)
      (xy 1.171544 0.630053)
      (xy 1.131855 0.591905)
      (xy 1.102757 0.56702)
      (xy 1.082569 0.554156)
      (xy 1.06961 0.552068)
      (xy 1.0622 0.559513)
      (xy 1.058658 0.575246)
      (xy 1.057303 0.598023)
      (xy 1.057121 0.604239)
      (xy 1.047703 0.647061)
      (xy 1.024497 0.698819)
      (xy 0.992136 0.751328)
      (xy 0.955252 0.796403)
      (xy 0.940493 0.810328)
      (xy 0.864767 0.859047)
      (xy 0.776308 0.886306)
      (xy 0.6981 0.892773)
      (xy 0.609468 0.880576)
      (xy 0.527612 0.844813)
      (xy 0.455164 0.786722)
      (xy 0.441797 0.772262)
      (xy 0.392918 0.716733)
      (xy -0.452674 0.716733)
      (xy -0.452674 0.892773)
      (xy -0.67901 0.892773)
      (xy -0.67901 0.810531)
      (xy -0.68185 0.754386)
      (xy -0.691393 0.715416)
      (xy -0.702991 0.694219)
      (xy -0.711277 0.679052)
      (xy -0.718373 0.657062)
      (xy -0.724748 0.624987)
      (xy -0.730872 0.579569)
      (xy -0.737216 0.517548)
      (xy -0.74425 0.435662)
      (xy -0.749066 0.374746)
      (xy -0.771161 0.089343)
      (xy -1.313565 0.638805)
      (xy -1.411637 0.738228)
      (xy -1.505784 0.833815)
      (xy -1.594285 0.92381)
      (xy -1.67542 1.006457)
      (xy -1.747469 1.080001)
      (xy -1.808712 1.142684)
      (xy -1.857427 1.192752)
      (xy -1.891896 1.228448)
      (xy -1.910379 1.247995)
      (xy -1.940743 1.278944)
      (xy -1.966071 1.30053)
      (xy -1.979695 1.307723)
      (xy -1.997095 1.299297)
      (xy -2.02246 1.278245)
      (xy -2.031058 1.269671)
      (xy -2.067514 1.23162)
      (xy -1.866802 1.027658)
      (xy -1.815596 0.975699)
      (xy -1.749569 0.90882)
      (xy -1.671618 0.82995)
      (xy -1.584638 0.742014)
      (xy -1.491526 0.647941)
      (xy -1.395179 0.550658)
      (xy -1.298492 0.453093)
      (xy -1.229134 0.383145)
      (xy -1.123703 0.27655)
      (xy -1.035129 0.186307)
      (xy -0.962281 0.111192)
      (xy -0.904023 0.049986)
      (xy -0.859225 0.001466)
      (xy -0.837021 -0.023871)
      (xy -0.658724 -0.023871)
      (xy -0.636401 0.261555)
      (xy -0.629669 0.345219)
      (xy -0.623157 0.421727)
      (xy -0.617234 0.487081)
      (xy -0.612268 0.537281)
      (xy -0.608629 0.568329)
      (xy -0.607458 0.575273)
      (xy -0.600838 0.603565)
      (xy 0.348636 0.603565)
      (xy 0.354974 0.524606)
      (xy 0.37411 0.431315)
      (xy 0.414154 0.348791)
      (xy 0.472582 0.280038)
      (xy 0.546871 0.228063)
      (xy 0.630252 0.196863)
      (xy 0.657302 0.182228)
      (xy 0.670844 0.150819)
      (xy 0.671128 0.149434)
      (xy 0.672753 0.136174)
      (xy 0.670744 0.122595)
      (xy 0.663142 0.106181)
      (xy 0.647984 0.084411)
      (xy 0.623312 0.054767)
      (xy 0.587164 0.014732)
      (xy 0.53758 -0.038215)
      (xy 0.472599 -0.106591)
      (xy 0.468401 -0.110995)
      (xy 0.398507 -0.184389)
      (xy 0.3242 -0.262563)
      (xy 0.250586 -0.340136)
      (xy 0.182771 -0.411725)
      (xy 0.12586 -0.471949)
      (xy 0.113168 -0.485413)
      (xy 0.064513 -0.53618)
      (xy 0.021291 -0.579625)
      (xy -0.013395 -0.612759)
      (xy -0.036444 -0.632595)
      (xy -0.044182 -0.636954)
      (xy -0.055722 -0.62783)
      (xy -0.08271 -0.6028)
      (xy -0.123021 -0.563948)
      (xy -0.174529 -0.513357)
      (xy -0.235109 -0.453112)
      (xy -0.302636 -0.385296)
      (xy -0.357826 -0.329435)
      (xy -0.658724 -0.023871)
      (xy -0.837021 -0.023871)
      (xy -0.826751 -0.035589)
      (xy -0.805471 -0.062401)
      (xy -0.794251 -0.080192)
      (xy -0.791754 -0.08843)
      (xy -0.7927 -0.10641)
      (xy -0.795573 -0.147108)
      (xy -0.800187 -0.208181)
      (xy -0.806358 -0.287287)
      (xy -0.813898 -0.382086)
      (xy -0.822621 -0.490233)
      (xy -0.832343 -0.609388)
      (xy -0.842876 -0.737209)
      (xy -0.851365 -0.839365)
      (xy -0.899396 -1.415326)
      (xy -0.775805 -1.415326)
      (xy -0.775273 -1.402896)
      (xy -0.772769 -1.36789)
      (xy -0.768496 -1.312785)
      (xy -0.762653 -1.240057)
      (xy -0.755443 -1.152186)
      (xy -0.747066 -1.051649)
      (xy -0.737723 -0.940923)
      (xy -0.728758 -0.835795)
      (xy -0.718602 -0.716517)
      (xy -0.709142 -0.60392)
      (xy -0.700596 -0.500695)
      (xy -0.693179 -0.409527)
      (xy -0.687108 -0.333105)
      (xy -0.682601 -0.274117)
      (xy -0.679873 -0.235251)
      (xy -0.679116 -0.220156)
      (xy -0.677935 -0.210762)
      (xy -0.673256 -0.207034)
      (xy -0.663276 -0.210529)
      (xy -0.64619 -0.222801)
      (xy -0.620196 -0.245406)
      (xy -0.58349 -0.2799)
      (xy -0.534267 -0.327838)
      (xy -0.470726 -0.390776)
      (xy -0.403305 -0.458032)
      (xy -0.127601 -0.733523)
      (xy -0.129533 -0.735594)
      (xy 0.05271 -0.735594)
      (xy 0.061016 -0.72422)
      (xy 0.084267 -0.697437)
      (xy 0.120135 -0.657708)
      (xy 0.166287 -0.607493)
      (xy 0.220394 -0.549254)
      (xy 0.280126 -0.485453)
      (xy 0.343152 -0.418551)
      (xy 0.407142 -0.35101)
      (xy 0.469764 -0.28529)
      (xy 0.52869 -0.223854)
      (xy 0.581588 -0.169163)
      (xy 0.626128 -0.123678)
      (xy 0.65998 -0.089862)
      (xy 0.680812 -0.070174)
      (xy 0.686494 -0.066163)
      (xy 0.688366 -0.079109)
      (xy 0.692254 -0.114866)
      (xy 0.697943 -0.171196)
      (xy 0.705219 -0.24586)
      (xy 0.713869 -0.33662)
      (xy 0.723678 -0.441238)
      (xy 0.734434 -0.557474)
      (xy 0.745921 -0.683092)
      (xy 0.755093 -0.784382)
      (xy 0.766826 -0.915721)
      (xy 0.777665 -1.039448)
      (xy 0.78743 -1.153319)
      (xy 0.795937 -1.255089)
      (xy 0.803005 -1.342513)
      (xy 0.808451 -1.413347)
      (xy 0.812092 -1.465347)
      (xy 0.813747 -1.496268)
      (xy 0.813558 -1.504297)
      (xy 0.803666 -1.497146)
      (xy 0.778476 -1.474159)
      (xy 0.74019 -1.437561)
      (xy 0.691011 -1.389578)
      (xy 0.633139 -1.332434)
      (xy 0.568778 -1.268353)
      (xy 0.500129 -1.199562)
      (xy 0.429395 -1.128284)
      (xy 0.358778 -1.056745)
      (xy 0.29048 -0.98717)
      (xy 0.226704 -0.921783)
      (xy 0.16965 -0.862809)
      (xy 0.121522 -0.812473)
      (xy 0.084522 -0.773001)
      (xy 0.060852 -0.746617)
      (xy 0.05271 -0.735594)
      (xy -0.129533 -0.735594)
      (xy -0.230409 -0.843705)
      (xy -0.282768 -0.899623)
      (xy -0.341535 -0.962052)
      (xy -0.404385 -1.028557)
      (xy -0.468995 -1.096702)
      (xy -0.533042 -1.164052)
      (xy -0.594203 -1.228172)
      (xy -0.650153 -1.286628)
      (xy -0.69857 -1.336982)
      (xy -0.73713 -1.376802)
      (xy -0.763509 -1.40365)
      (xy -0.775384 -1.415092)
      (xy -0.775805 -1.415326)
      (xy -0.899396 -1.415326)
      (xy -0.911401 -1.559274)
      (xy -1.511938 -2.190842)
      (xy -2.112475 -2.822411)
      (xy -2.112034 -2.910685)
      (xy -2.111592 -2.99896)
      (xy -2.014583 -2.895334)
      (xy -1.960291 -2.837537)
      (xy -1.896192 -2.769632)
      (xy -1.824016 -2.693428)
      (xy -1.745492 -2.610731)
      (xy -1.662349 -2.523347)
      (xy -1.576319 -2.433085)
      (xy -1.48913 -2.34175)
      (xy -1.402513 -2.251151)
      (xy -1.318197 -2.163093)
      (xy -1.237912 -2.079385)
      (xy -1.163387 -2.001833)
      (xy -1.096354 -1.932243)
      (xy -1.038541 -1.872424)
      (xy -0.991679 -1.824182)
      (xy -0.957496 -1.789324)
      (xy -0.937724 -1.769657)
      (xy -0.93339 -1.765884)
      (xy -0.933092 -1.779008)
      (xy -0.934731 -1.812611)
      (xy -0.938023 -1.86212)
      (xy -0.942682 -1.922963)
      (xy -0.944682 -1.947268)
      (xy -0.959577 -2.125049)
      (xy -0.842955 -2.125049)
      (xy -0.836934 -2.096757)
      (xy -0.833863 -2.074382)
      (xy -0.829548 -2.032283)
      (xy -0.824488 -1.975822)
      (xy -0.819181 -1.910365)
      (xy -0.817344 -1.886138)
      (xy -0.811927 -1.816579)
      (xy -0.806459 -1.751982)
      (xy -0.801488 -1.698452)
      (xy -0.797561 -1.66209)
      (xy -0.796675 -1.655491)
      (xy -0.793334 -1.641944)
      (xy -0.786101 -1.626086)
      (xy -0.77344 -1.606139)
      (xy -0.753811 -1.580327)
      (xy -0.725678 -1.546871)
      (xy -0.687502 -1.503993)
      (xy -0.637746 -1.449917)
      (xy -0.574871 -1.382864)
      (xy -0.497341 -1.301057)
      (xy -0.418251 -1.21805)
      (xy -0.339564 -1.135906)
      (xy -0.266112 -1.059831)
      (xy -0.199724 -0.991675)
      (xy -0.142227 -0.933288)
      (xy -0.095451 -0.886519)
      (xy -0.061224 -0.853218)
      (xy -0.041373 -0.835233)
      (xy -0.03714 -0.832558)
      (xy -0.026003 -0.842259)
      (xy 0.000029 -0.867559)
      (xy 0.03843 -0.905918)
      (xy 0.086672 -0.9548)
      (xy 0.14223 -1.011666)
      (xy 0.182408 -1.053094)
      (xy 0.392169 -1.27)
      (xy -0.226337 -1.27)
      (xy -0.226337 -1.508911)
      (xy 0.528119 -1.508911)
      (xy 0.528119 -1.402458)
      (xy 0.666435 -1.540346)
      (xy 0.764553 -1.63816)
      (xy 0.955643 -1.63816)
      (xy 0.957471 -1.62273)
      (xy 0.966723 -1.614133)
      (xy 0.98905 -1.610387)
      (xy 1.030105 -1.609511)
      (xy 1.037376 -1.609505)
      (xy 1.119109 -1.609505)
      (xy 1.119109 -1.828828)
      (xy 1.037376 -1.747821)
      (xy 0.99127 -1.698572)
      (xy 0.963694 -1.660841)
      (xy 0.955643 -1.63816)
      (xy 0.764553 -1.63816)
      (xy 0.804752 -1.678234)
      (xy 0.804752 -1.801048)
      (xy 0.805137 -1.85755)
      (xy 0.8069 -1.893495)
      (xy 0.81095 -1.91347)
      (xy 0.818199 -1.922063)
      (xy 0.82913 -1.923861)
      (xy 0.841288 -1.926502)
      (xy 0.850273 -1.937088)
      (xy 0.857174 -1.959619)
      (xy 0.863076 -1.998091)
      (xy 0.869065 -2.056502)
      (xy 0.870987 -2.077896)
      (xy 0.875148 -2.125049)
      (xy -0.842955 -2.125049)
      (xy -0.959577 -2.125049)
      (xy -1.119109 -2.125049)
      (xy -1.119109 -2.238218)
      (xy -1.051314 -2.238218)
      (xy -1.011662 -2.239304)
      (xy -0.990116 -2.244546)
      (xy -0.98748 -2.247666)
      (xy -0.848616 -2.247666)
      (xy -0.841308 -2.240538)
      (xy -0.815993 -2.238338)
      (xy -0.798908 -2.238218)
      (xy -0.741881 -2.238218)
      (xy -0.529221 -2.238218)
      (xy 0.885302 -2.238218)
      (xy 0.837458 -2.287214)
      (xy 0.76315 -2.347676)
      (xy 0.671184 -2.394309)
      (xy 0.560002 -2.427751)
      (xy 0.449529 -2.446247)
      (xy 0.377227 -2.454878)
      (xy 0.377227 -2.36396)
      (xy -0.201188 -2.36396)
      (xy -0.201188 -2.467107)
      (xy -0.286065 -2.458504)
      (xy -0.345368 -2.451244)
      (xy -0.408551 -2.441621)
      (xy -0.446386 -2.434748)
      (xy -0.521832 -2.419593)
      (xy -0.525526 -2.328905)
      (xy -0.529221 -2.238218)
      (xy -0.741881 -2.238218)
      (xy -0.741881 -2.288515)
      (xy -0.743544 -2.320024)
      (xy -0.747697 -2.337537)
      (xy -0.749371 -2.338812)
      (xy -0.767987 -2.330746)
      (xy -0.795183 -2.31118)
      (xy -0.822448 -2.287056)
      (xy -0.841267 -2.265318)
      (xy -0.842943 -2.262492)
      (xy -0.848616 -2.247666)
      (xy -0.98748 -2.247666)
      (xy -0.979662 -2.256919)
      (xy -0.975442 -2.270396)
      (xy -0.958219 -2.305373)
      (xy -0.925138 -2.347421)
      (xy -0.881893 -2.390644)
      (xy -0.834174 -2.429146)
      (xy -0.80283 -2.449199)
      (xy -0.767123 -2.471149)
      (xy -0.748819 -2.489589)
      (xy -0.742388 -2.511332)
      (xy -0.741894 -2.524282)
      (xy -0.741894 -2.527425)
      (xy -0.100594 -2.527425)
      (xy -0.100594 -2.464554)
      (xy 0.276633 -2.464554)
      (xy 0.276633 -2.527425)
      (xy -0.100594 -2.527425)
      (xy -0.741894 -2.527425)
      (xy -0.741881 -2.565148)
      (xy -0.636048 -2.565148)
      (xy -0.587355 -2.563971)
      (xy -0.549405 -2.560835)
      (xy -0.528308 -2.556329)
      (xy -0.526023 -2.554505)
      (xy -0.512641 -2.551705)
      (xy -0.480074 -2.552852)
      (xy -0.433916 -2.557607)
      (xy -0.402376 -2.561997)
      (xy -0.345188 -2.570622)
      (xy -0.292886 -2.578409)
      (xy -0.253582 -2.584153)
      (xy -0.242055 -2.585785)
      (xy -0.211937 -2.595112)
      (xy -0.201188 -2.609728)
      (xy -0.19792 -2.61568)
      (xy -0.18623 -2.620222)
      (xy -0.163288 -2.62353)
      (xy -0.126265 -2.625785)
      (xy -0.072332 -2.627166)
      (xy 0.00134 -2.62785)
      (xy 0.08802 -2.62802)
      (xy 0.180529 -2.627923)
      (xy 0.250906 -2.62747)
      (xy 0.302164 -2.62641)
      (xy 0.33732 -2.624497)
      (xy 0.359389 -2.621481)
      (xy 0.371385 -2.617115)
      (xy 0.376324 -2.611151)
      (xy 0.377227 -2.604216)
      (xy 0.384921 -2.582205)
      (xy 0.410121 -2.569679)
      (xy 0.456009 -2.565212)
      (xy 0.464264 -2.565148)
      (xy 0.541973 -2.557132)
      (xy 0.630233 -2.535064)
      (xy 0.721085 -2.501916)
      (xy 0.80657 -2.460661)
      (xy 0.878726 -2.414269)
      (xy 0.888072 -2.406918)
      (xy 0.918533 -2.383002)
      (xy 0.936572 -2.373424)
      (xy 0.949169 -2.37652)
      (xy 0.9621 -2.389296)
      (xy 1.000293 -2.414322)
      (xy 1.049998 -2.423929)
      (xy 1.103524 -2.418933)
      (xy 1.153178 -2.400149)
      (xy 1.191267 -2.368394)
      (xy 1.194025 -2.364703)
      (xy 1.222526 -2.305425)
      (xy 1.227828 -2.244066)
      (xy 1.210518 -2.185573)
      (xy 1.17118 -2.134896)
      (xy 1.16637 -2.130711)
      (xy 1.13844 -2.110833)
      (xy 1.110102 -2.102079)
      (xy 1.070263 -2.101447)
      (xy 1.060311 -2.102008)
      (xy 1.021332 -2.103438)
      (xy 1.001254 -2.100161)
      (xy 0.993985 -2.090272)
      (xy 0.99324 -2.081039)
      (xy 0.991716 -2.054256)
      (xy 0.987935 -2.013975)
      (xy 0.985218 -1.989876)
      (xy 0.981277 -1.951599)
      (xy 0.982916 -1.932004)
      (xy 0.992421 -1.924842)
      (xy 1.009351 -1.923861)
      (xy 1.019392 -1.927099)
      (xy 1.03559 -1.93758)
      (xy 1.059145 -1.956452)
      (xy 1.091257 -1.984865)
      (xy 1.133128 -2.023965)
      (xy 1.185957 -2.074903)
      (xy 1.250945 -2.138827)
      (xy 1.329291 -2.216886)
      (xy 1.422197 -2.310228)
      (xy 1.530863 -2.420002)
      (xy 1.583231 -2.473048)
      (xy 2.125049 -3.022233)
      (xy 2.12443 -2.935152)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 82b79943-66ee-47bc-ad87-537f9e49345e))
  )

  (footprint "Symbol:KiCad-Logo2_5mm_Copper" (layer "F.Cu")
    (tedit 0) (tstamp 00000000-0000-0000-0000-00005f98088a)
    (at 48.26 -50.038)
    (descr "KiCad Logo")
    (tags "Logo KiCad")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "exclude_from_bom" "")
    (path "/18701acf-39bb-48d6-b36d-869fa2c5e4f6")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "FID2" (at 0 -5.08) (layer "F.SilkS") hide
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 290a7a15-0b06-429b-bb68-5274f1392848)
    )
    (fp_text value "Logo KiCAD" (at 0 5.08) (layer "F.Fab") hide
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp cf03ccb1-a469-4a37-9c3d-5f78db3d107e)
    )
    (fp_poly (pts (xy 4.188614 2.275877)
      (xy 4.212327 2.290647)
      (xy 4.238978 2.312227)
      (xy 4.238978 2.633773)
      (xy 4.238893 2.72783)
      (xy 4.238529 2.801932)
      (xy 4.237724 2.858704)
      (xy 4.236313 2.900768)
      (xy 4.234133 2.930748)
      (xy 4.231021 2.951267)
      (xy 4.226814 2.964949)
      (xy 4.221348 2.974416)
      (xy 4.217472 2.979082)
      (xy 4.186034 2.999575)
      (xy 4.150233 2.998739)
      (xy 4.118873 2.981264)
      (xy 4.092222 2.959684)
      (xy 4.092222 2.312227)
      (xy 4.118873 2.290647)
      (xy 4.144594 2.274949)
      (xy 4.1656 2.269067)
      (xy 4.188614 2.275877)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp 0f0cf69d-4a34-41d0-bd61-fcfdd99eac1e))
    (fp_poly (pts (xy 6.186507 -0.527755)
      (xy 6.186526 -0.293338)
      (xy 6.186552 -0.080397)
      (xy 6.186625 0.112168)
      (xy 6.186782 0.285459)
      (xy 6.187064 0.440576)
      (xy 6.187509 0.57862)
      (xy 6.188156 0.700692)
      (xy 6.189045 0.807894)
      (xy 6.190213 0.901326)
      (xy 6.191701 0.98209)
      (xy 6.193546 1.051286)
      (xy 6.195789 1.110015)
      (xy 6.198469 1.159379)
      (xy 6.201623 1.200478)
      (xy 6.205292 1.234413)
      (xy 6.209513 1.262286)
      (xy 6.214327 1.285198)
      (xy 6.219773 1.304249)
      (xy 6.225888 1.32054)
      (xy 6.232712 1.335173)
      (xy 6.240285 1.349249)
      (xy 6.248645 1.363868)
      (xy 6.253839 1.372974)
      (xy 6.288104 1.433689)
      (xy 5.429955 1.433689)
      (xy 5.429955 1.337733)
      (xy 5.429224 1.29437)
      (xy 5.427272 1.261205)
      (xy 5.424463 1.243424)
      (xy 5.423221 1.241778)
      (xy 5.411799 1.248662)
      (xy 5.389084 1.266505)
      (xy 5.366385 1.285879)
      (xy 5.3118 1.326614)
      (xy 5.242321 1.367617)
      (xy 5.16527 1.405123)
      (xy 5.087965 1.435364)
      (xy 5.057113 1.445012)
      (xy 4.988616 1.459578)
      (xy 4.905764 1.469539)
      (xy 4.816371 1.474583)
      (xy 4.728248 1.474396)
      (xy 4.649207 1.468666)
      (xy 4.611511 1.462858)
      (xy 4.473414 1.424797)
      (xy 4.346113 1.367073)
      (xy 4.230292 1.290211)
      (xy 4.126637 1.194739)
      (xy 4.035833 1.081179)
      (xy 3.969031 0.970381)
      (xy 3.914164 0.853625)
      (xy 3.872163 0.734276)
      (xy 3.842167 0.608283)
      (xy 3.823311 0.471594)
      (xy 3.814732 0.320158)
      (xy 3.814006 0.242711)
      (xy 3.8161 0.185934)
      (xy 4.645217 0.185934)
      (xy 4.645424 0.279002)
      (xy 4.648337 0.366692)
      (xy 4.654 0.443772)
      (xy 4.662455 0.505009)
      (xy 4.665038 0.51735)
      (xy 4.69684 0.624633)
      (xy 4.738498 0.711658)
      (xy 4.790363 0.778642)
      (xy 4.852781 0.825805)
      (xy 4.9261 0.853365)
      (xy 5.010669 0.861541)
      (xy 5.106835 0.850551)
      (xy 5.170311 0.834829)
      (xy 5.219454 0.816639)
      (xy 5.273583 0.790791)
      (xy 5.314244 0.767089)
      (xy 5.3848 0.720721)
      (xy 5.3848 -0.42947)
      (xy 5.317392 -0.473038)
      (xy 5.238867 -0.51396)
      (xy 5.154681 -0.540611)
      (xy 5.069557 -0.552535)
      (xy 4.988216 -0.549278)
      (xy 4.91538 -0.530385)
      (xy 4.883426 -0.514816)
      (xy 4.825501 -0.471819)
      (xy 4.776544 -0.415047)
      (xy 4.73539 -0.342425)
      (xy 4.700874 -0.251879)
      (xy 4.671833 -0.141334)
      (xy 4.670552 -0.135467)
      (xy 4.660381 -0.073212)
      (xy 4.652739 0.004594)
      (xy 4.64767 0.09272)
      (xy 4.645217 0.185934)
      (xy 3.8161 0.185934)
      (xy 3.821857 0.029895)
      (xy 3.843802 -0.165941)
      (xy 3.879786 -0.344668)
      (xy 3.929759 -0.506155)
      (xy 3.993668 -0.650274)
      (xy 4.071462 -0.776894)
      (xy 4.163089 -0.885885)
      (xy 4.268497 -0.977117)
      (xy 4.313662 -1.008068)
      (xy 4.414611 -1.064215)
      (xy 4.517901 -1.103826)
      (xy 4.627989 -1.127986)
      (xy 4.74933 -1.137781)
      (xy 4.841836 -1.136735)
      (xy 4.97149 -1.125769)
      (xy 5.084084 -1.103954)
      (xy 5.182875 -1.070286)
      (xy 5.271121 -1.023764)
      (xy 5.319986 -0.989552)
      (xy 5.349353 -0.967638)
      (xy 5.371043 -0.952667)
      (xy 5.379253 -0.948267)
      (xy 5.380868 -0.959096)
      (xy 5.382159 -0.989749)
      (xy 5.383138 -1.037474)
      (xy 5.383817 -1.099521)
      (xy 5.38421 -1.173138)
      (xy 5.38433 -1.255573)
      (xy 5.384188 -1.344075)
      (xy 5.383797 -1.435893)
      (xy 5.383171 -1.528276)
      (xy 5.38232 -1.618472)
      (xy 5.38126 -1.703729)
      (xy 5.380001 -1.781297)
      (xy 5.378556 -1.848424)
      (xy 5.376938 -1.902359)
      (xy 5.375161 -1.94035)
      (xy 5.374669 -1.947333)
      (xy 5.367092 -2.017749)
      (xy 5.355531 -2.072898)
      (xy 5.337792 -2.120019)
      (xy 5.311682 -2.166353)
      (xy 5.305415 -2.175933)
      (xy 5.280983 -2.212622)
      (xy 6.186311 -2.212622)
      (xy 6.186507 -0.527755)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp 123add17-d8a5-4b99-8fc5-4be35a86beb0))
    (fp_poly (pts (xy -1.300114 2.273448)
      (xy -1.276548 2.287273)
      (xy -1.245735 2.309881)
      (xy -1.206078 2.342338)
      (xy -1.15598 2.385708)
      (xy -1.093843 2.441058)
      (xy -1.018072 2.509451)
      (xy -0.931334 2.588084)
      (xy -0.750711 2.751878)
      (xy -0.745067 2.532029)
      (xy -0.743029 2.456351)
      (xy -0.741063 2.399994)
      (xy -0.738734 2.359706)
      (xy -0.735606 2.332235)
      (xy -0.731245 2.314329)
      (xy -0.725216 2.302737)
      (xy -0.717084 2.294208)
      (xy -0.712772 2.290623)
      (xy -0.678241 2.27167)
      (xy -0.645383 2.274441)
      (xy -0.619318 2.290633)
      (xy -0.592667 2.312199)
      (xy -0.589352 2.627151)
      (xy -0.588435 2.719779)
      (xy -0.587968 2.792544)
      (xy -0.588113 2.848161)
      (xy -0.589032 2.889342)
      (xy -0.590887 2.918803)
      (xy -0.593839 2.939255)
      (xy -0.59805 2.953413)
      (xy -0.603682 2.963991)
      (xy -0.609927 2.972474)
      (xy -0.623439 2.988207)
      (xy -0.636883 2.998636)
      (xy -0.652124 3.002639)
      (xy -0.671026 2.999094)
      (xy -0.695455 2.986879)
      (xy -0.727273 2.964871)
      (xy -0.768348 2.931949)
      (xy -0.820542 2.886991)
      (xy -0.885722 2.828875)
      (xy -0.959556 2.762099)
      (xy -1.224845 2.521458)
      (xy -1.230489 2.740589)
      (xy -1.232531 2.816128)
      (xy -1.234502 2.872354)
      (xy -1.236839 2.912524)
      (xy -1.239981 2.939896)
      (xy -1.244364 2.957728)
      (xy -1.250424 2.969279)
      (xy -1.2586 2.977807)
      (xy -1.262784 2.981282)
      (xy -1.299765 3.000372)
      (xy -1.334708 2.997493)
      (xy -1.365136 2.9731)
      (xy -1.372097 2.963286)
      (xy -1.377523 2.951826)
      (xy -1.381603 2.935968)
      (xy -1.384529 2.912963)
      (xy -1.386492 2.880062)
      (xy -1.387683 2.834516)
      (xy -1.388292 2.773573)
      (xy -1.388511 2.694486)
      (xy -1.388534 2.635956)
      (xy -1.38846 2.544407)
      (xy -1.388113 2.472687)
      (xy -1.387301 2.418045)
      (xy -1.385833 2.377732)
      (xy -1.383519 2.348998)
      (xy -1.380167 2.329093)
      (xy -1.375588 2.315268)
      (xy -1.369589 2.304772)
      (xy -1.365136 2.298811)
      (xy -1.35385 2.284691)
      (xy -1.343301 2.274029)
      (xy -1.331893 2.267892)
      (xy -1.31803 2.267343)
      (xy -1.300114 2.273448)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp 432e7533-c2a1-47d5-9680-1aa74020d3e4))
    (fp_poly (pts (xy 0.328429 -2.050929)
      (xy 0.48857 -2.029755)
      (xy 0.65251 -1.989615)
      (xy 0.822313 -1.930111)
      (xy 1.000043 -1.850846)
      (xy 1.01131 -1.845301)
      (xy 1.069005 -1.817275)
      (xy 1.120552 -1.793198)
      (xy 1.162191 -1.774751)
      (xy 1.190162 -1.763614)
      (xy 1.199733 -1.761067)
      (xy 1.21895 -1.756059)
      (xy 1.223561 -1.751853)
      (xy 1.218458 -1.74142)
      (xy 1.202418 -1.715132)
      (xy 1.177288 -1.675743)
      (xy 1.144914 -1.626009)
      (xy 1.107143 -1.568685)
      (xy 1.065822 -1.506524)
      (xy 1.022798 -1.442282)
      (xy 0.979917 -1.378715)
      (xy 0.939026 -1.318575)
      (xy 0.901971 -1.26462)
      (xy 0.8706 -1.219603)
      (xy 0.846759 -1.186279)
      (xy 0.832294 -1.167403)
      (xy 0.830309 -1.165213)
      (xy 0.820191 -1.169862)
      (xy 0.79785 -1.187038)
      (xy 0.76728 -1.21356)
      (xy 0.751536 -1.228036)
      (xy 0.655047 -1.303318)
      (xy 0.548336 -1.358759)
      (xy 0.432832 -1.393859)
      (xy 0.309962 -1.40812)
      (xy 0.240561 -1.406949)
      (xy 0.119423 -1.389788)
      (xy 0.010205 -1.353906)
      (xy -0.087418 -1.299041)
      (xy -0.173772 -1.22493)
      (xy -0.249185 -1.131312)
      (xy -0.313982 -1.017924)
      (xy -0.351399 -0.931333)
      (xy -0.395252 -0.795634)
      (xy -0.427572 -0.64815)
      (xy -0.448443 -0.492686)
      (xy -0.457949 -0.333044)
      (xy -0.456173 -0.173027)
      (xy -0.443197 -0.016439)
      (xy -0.419106 0.132918)
      (xy -0.383982 0.27124)
      (xy -0.337908 0.394724)
      (xy -0.321627 0.428978)
      (xy -0.25338 0.543064)
      (xy -0.172921 0.639557)
      (xy -0.08143 0.71767)
      (xy 0.019911 0.776617)
      (xy 0.12992 0.815612)
      (xy 0.247415 0.833868)
      (xy 0.288883 0.835211)
      (xy 0.410441 0.82429)
      (xy 0.530878 0.791474)
      (xy 0.648666 0.737439)
      (xy 0.762277 0.662865)
      (xy 0.853685 0.584539)
      (xy 0.900215 0.540008)
      (xy 1.081483 0.837271)
      (xy 1.12658 0.911433)
      (xy 1.167819 0.979646)
      (xy 1.203735 1.039459)
      (xy 1.232866 1.08842)
      (xy 1.25375 1.124079)
      (xy 1.264924 1.143984)
      (xy 1.266375 1.147079)
      (xy 1.258146 1.156718)
      (xy 1.232567 1.173999)
      (xy 1.192873 1.197283)
      (xy 1.142297 1.224934)
      (xy 1.084074 1.255315)
      (xy 1.021437 1.28679)
      (xy 0.957621 1.317722)
      (xy 0.89586 1.346473)
      (xy 0.839388 1.371408)
      (xy 0.791438 1.390889)
      (xy 0.767986 1.399318)
      (xy 0.634221 1.437133)
      (xy 0.496327 1.462136)
      (xy 0.348622 1.47514)
      (xy 0.221833 1.477468)
      (xy 0.153878 1.476373)
      (xy 0.088277 1.474275)
      (xy 0.030847 1.471434)
      (xy -0.012597 1.468106)
      (xy -0.026702 1.466422)
      (xy -0.165716 1.437587)
      (xy -0.307243 1.392468)
      (xy -0.444725 1.33375)
      (xy -0.571606 1.26412)
      (xy -0.649111 1.211441)
      (xy -0.776519 1.103239)
      (xy -0.894822 0.976671)
      (xy -1.001828 0.834866)
      (xy -1.095348 0.680951)
      (xy -1.17319 0.518053)
      (xy -1.217044 0.400756)
      (xy -1.267292 0.217128)
      (xy -1.300791 0.022581)
      (xy -1.317551 -0.178675)
      (xy -1.317584 -0.382432)
      (xy -1.300899 -0.584479)
      (xy -1.267507 -0.780608)
      (xy -1.21742 -0.966609)
      (xy -1.213603 -0.978197)
      (xy -1.150719 -1.14025)
      (xy -1.073972 -1.288168)
      (xy -0.980758 -1.426135)
      (xy -0.868473 -1.558339)
      (xy -0.824608 -1.603601)
      (xy -0.688466 -1.727543)
      (xy -0.548509 -1.830085)
      (xy -0.402589 -1.912344)
      (xy -0.248558 -1.975436)
      (xy -0.084268 -2.020477)
      (xy 0.011289 -2.037967)
      (xy 0.170023 -2.053534)
      (xy 0.328429 -2.050929)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp 5308a329-efc6-4ecf-a737-6e9cb477d196))
    (fp_poly (pts (xy 4.963065 2.269163)
      (xy 5.041772 2.269542)
      (xy 5.102863 2.270333)
      (xy 5.148817 2.27167)
      (xy 5.182114 2.273683)
      (xy 5.205236 2.276506)
      (xy 5.220662 2.280269)
      (xy 5.230871 2.285105)
      (xy 5.235813 2.288822)
      (xy 5.261457 2.321358)
      (xy 5.264559 2.355138)
      (xy 5.248711 2.385826)
      (xy 5.238348 2.398089)
      (xy 5.227196 2.40645)
      (xy 5.211035 2.411657)
      (xy 5.185642 2.414457)
      (xy 5.146798 2.415596)
      (xy 5.09028 2.415821)
      (xy 5.07918 2.415822)
      (xy 4.933244 2.415822)
      (xy 4.933244 2.686756)
      (xy 4.933148 2.772154)
      (xy 4.932711 2.837864)
      (xy 4.931712 2.886774)
      (xy 4.929928 2.921773)
      (xy 4.927137 2.945749)
      (xy 4.923117 2.961593)
      (xy 4.917645 2.972191)
      (xy 4.910666 2.980267)
      (xy 4.877734 3.000112)
      (xy 4.843354 2.998548)
      (xy 4.812176 2.975906)
      (xy 4.809886 2.9731)
      (xy 4.802429 2.962492)
      (xy 4.796747 2.950081)
      (xy 4.792601 2.93285)
      (xy 4.78975 2.907784)
      (xy 4.787954 2.871867)
      (xy 4.786972 2.822083)
      (xy 4.786564 2.755417)
      (xy 4.786489 2.679589)
      (xy 4.786489 2.415822)
      (xy 4.647127 2.415822)
      (xy 4.587322 2.415418)
      (xy 4.545918 2.41384)
      (xy 4.518748 2.410547)
      (xy 4.501646 2.404992)
      (xy 4.490443 2.396631)
      (xy 4.489083 2.395178)
      (xy 4.472725 2.361939)
      (xy 4.474172 2.324362)
      (xy 4.492978 2.291645)
      (xy 4.50025 2.285298)
      (xy 4.509627 2.280266)
      (xy 4.523609 2.276396)
      (xy 4.544696 2.273537)
      (xy 4.575389 2.271535)
      (xy 4.618189 2.270239)
      (xy 4.675595 2.269498)
      (xy 4.75011 2.269158)
      (xy 4.844233 2.269068)
      (xy 4.86426 2.269067)
      (xy 4.963065 2.269163)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp 558dba53-f895-4df6-a391-ea2c8410808a))
    (fp_poly (pts (xy -2.923822 2.291645)
      (xy -2.917242 2.299218)
      (xy -2.912079 2.308987)
      (xy -2.908164 2.323571)
      (xy -2.905324 2.345585)
      (xy -2.903387 2.377648)
      (xy -2.902183 2.422375)
      (xy -2.901539 2.482385)
      (xy -2.901284 2.560294)
      (xy -2.901245 2.635956)
      (xy -2.901314 2.729802)
      (xy -2.901638 2.803689)
      (xy -2.902386 2.860232)
      (xy -2.903732 2.902049)
      (xy -2.905846 2.931757)
      (xy -2.9089 2.951973)
      (xy -2.913066 2.965314)
      (xy -2.918516 2.974398)
      (xy -2.923822 2.980267)
      (xy -2.956826 2.999947)
      (xy -2.991991 2.998181)
      (xy -3.023455 2.976717)
      (xy -3.030684 2.968337)
      (xy -3.036334 2.958614)
      (xy -3.040599 2.944861)
      (xy -3.043673 2.924389)
      (xy -3.045752 2.894512)
      (xy -3.04703 2.852541)
      (xy -3.047701 2.795789)
      (xy -3.047959 2.721567)
      (xy -3.048 2.637537)
      (xy -3.048 2.324485)
      (xy -3.020291 2.296776)
      (xy -2.986137 2.273463)
      (xy -2.953006 2.272623)
      (xy -2.923822 2.291645)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp 648b2617-00a0-4d76-8786-3a1304a68f87))
    (fp_poly (pts (xy -4.712794 2.269146)
      (xy -4.643386 2.269518)
      (xy -4.590997 2.270385)
      (xy -4.552847 2.271946)
      (xy -4.526159 2.274403)
      (xy -4.508153 2.277957)
      (xy -4.496049 2.28281)
      (xy -4.487069 2.289161)
      (xy -4.483818 2.292084)
      (xy -4.464043 2.323142)
      (xy -4.460482 2.358828)
      (xy -4.473491 2.39051)
      (xy -4.479506 2.396913)
      (xy -4.489235 2.403121)
      (xy -4.504901 2.40791)
      (xy -4.529408 2.411514)
      (xy -4.565661 2.414164)
      (xy -4.616565 2.416095)
      (xy -4.685026 2.417539)
      (xy -4.747617 2.418418)
      (xy -4.995334 2.421467)
      (xy -4.998719 2.486378)
      (xy -5.002105 2.551289)
      (xy -4.833958 2.551289)
      (xy -4.760959 2.551919)
      (xy -4.707517 2.554553)
      (xy -4.670628 2.560309)
      (xy -4.647288 2.570304)
      (xy -4.634494 2.585656)
      (xy -4.629242 2.607482)
      (xy -4.628445 2.627738)
      (xy -4.630923 2.652592)
      (xy -4.640277 2.670906)
      (xy -4.659383 2.683637)
      (xy -4.691118 2.691741)
      (xy -4.738359 2.696176)
      (xy -4.803983 2.697899)
      (xy -4.839801 2.698045)
      (xy -5.000978 2.698045)
      (xy -5.000978 2.856089)
      (xy -4.752622 2.856089)
      (xy -4.671213 2.856202)
      (xy -4.609342 2.856712)
      (xy -4.563968 2.85787)
      (xy -4.532054 2.85993)
      (xy -4.510559 2.863146)
      (xy -4.496443 2.867772)
      (xy -4.486668 2.874059)
      (xy -4.481689 2.878667)
      (xy -4.46461 2.90556)
      (xy -4.459111 2.929467)
      (xy -4.466963 2.958667)
      (xy -4.481689 2.980267)
      (xy -4.489546 2.987066)
      (xy -4.499688 2.992346)
      (xy -4.514844 2.996298)
      (xy -4.537741 2.999113)
      (xy -4.571109 3.000982)
      (xy -4.617675 3.002098)
      (xy -4.680167 3.002651)
      (xy -4.761314 3.002833)
      (xy -4.803422 3.002845)
      (xy -4.893598 3.002765)
      (xy -4.963924 3.002398)
      (xy -5.017129 3.001552)
      (xy -5.05594 3.000036)
      (xy -5.083087 2.997659)
      (xy -5.101298 2.994229)
      (xy -5.1133 2.989554)
      (xy -5.121822 2.983444)
      (xy -5.125156 2.980267)
      (xy -5.131755 2.97267)
      (xy -5.136927 2.96287)
      (xy -5.140846 2.948239)
      (xy -5.143684 2.926152)
      (xy -5.145615 2.893982)
      (xy -5.146812 2.849103)
      (xy -5.147448 2.788889)
      (xy -5.147697 2.710713)
      (xy -5.147734 2.637923)
      (xy -5.1477 2.544707)
      (xy -5.147465 2.471431)
      (xy -5.14683 2.415458)
      (xy -5.145594 2.374151)
      (xy -5.143556 2.344872)
      (xy -5.140517 2.324984)
      (xy -5.136277 2.31185)
      (xy -5.130635 2.302832)
      (xy -5.123391 2.295293)
      (xy -5.121606 2.293612)
      (xy -5.112945 2.286172)
      (xy -5.102882 2.280409)
      (xy -5.088625 2.276112)
      (xy -5.067383 2.273064)
      (xy -5.036364 2.271051)
      (xy -4.992777 2.26986)
      (xy -4.933831 2.269275)
      (xy -4.856734 2.269083)
      (xy -4.802001 2.269067)
      (xy -4.712794 2.269146)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp 67fe020e-41c8-477a-bb9d-b295d0005a9f))
    (fp_poly (pts (xy -2.273043 -2.973429)
      (xy -2.176768 -2.949191)
      (xy -2.090184 -2.906359)
      (xy -2.015373 -2.846581)
      (xy -1.954418 -2.771506)
      (xy -1.909399 -2.68278)
      (xy -1.883136 -2.58647)
      (xy -1.877286 -2.489205)
      (xy -1.89214 -2.395346)
      (xy -1.92584 -2.307489)
      (xy -1.976528 -2.22823)
      (xy -2.042345 -2.160164)
      (xy -2.121434 -2.105888)
      (xy -2.211934 -2.067998)
      (xy -2.2632 -2.055574)
      (xy -2.307698 -2.048053)
      (xy -2.341999 -2.045081)
      (xy -2.37496 -2.046906)
      (xy -2.415434 -2.053775)
      (xy -2.448531 -2.06075)
      (xy -2.541947 -2.092259)
      (xy -2.625619 -2.143383)
      (xy -2.697665 -2.212571)
      (xy -2.7562 -2.298272)
      (xy -2.770148 -2.325511)
      (xy -2.786586 -2.361878)
      (xy -2.796894 -2.392418)
      (xy -2.80246 -2.42455)
      (xy -2.804669 -2.465693)
      (xy -2.804948 -2.511778)
      (xy -2.800861 -2.596135)
      (xy -2.787446 -2.665414)
      (xy -2.762256 -2.726039)
      (xy -2.722846 -2.784433)
      (xy -2.684298 -2.828698)
      (xy -2.612406 -2.894516)
      (xy -2.537313 -2.939947)
      (xy -2.454562 -2.96715)
      (xy -2.376928 -2.977424)
      (xy -2.273043 -2.973429)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp 6891389b-4ca3-47e5-8974-73af36057b58))
    (fp_poly (pts (xy 0.230343 2.26926)
      (xy 0.306701 2.270174)
      (xy 0.365217 2.272311)
      (xy 0.408255 2.276175)
      (xy 0.438183 2.282267)
      (xy 0.457368 2.29109)
      (xy 0.468176 2.303146)
      (xy 0.472973 2.318939)
      (xy 0.474127 2.33897)
      (xy 0.474133 2.341335)
      (xy 0.473131 2.363992)
      (xy 0.468396 2.381503)
      (xy 0.457333 2.394574)
      (xy 0.437348 2.403913)
      (xy 0.405846 2.410227)
      (xy 0.360232 2.414222)
      (xy 0.297913 2.416606)
      (xy 0.216293 2.418086)
      (xy 0.191277 2.418414)
      (xy -0.0508 2.421467)
      (xy -0.054186 2.486378)
      (xy -0.057571 2.551289)
      (xy 0.110576 2.551289)
      (xy 0.176266 2.551531)
      (xy 0.223172 2.552556)
      (xy 0.255083 2.554811)
      (xy 0.275791 2.558742)
      (xy 0.289084 2.564798)
      (xy 0.298755 2.573424)
      (xy 0.298817 2.573493)
      (xy 0.316356 2.607112)
      (xy 0.315722 2.643448)
      (xy 0.297314 2.674423)
      (xy 0.293671 2.677607)
      (xy 0.280741 2.685812)
      (xy 0.263024 2.691521)
      (xy 0.23657 2.695162)
      (xy 0.197432 2.697167)
      (xy 0.141662 2.697964)
      (xy 0.105994 2.698045)
      (xy -0.056445 2.698045)
      (xy -0.056445 2.856089)
      (xy 0.190161 2.856089)
      (xy 0.27158 2.856231)
      (xy 0.33341 2.856814)
      (xy 0.378637 2.858068)
      (xy 0.410248 2.860227)
      (xy 0.431231 2.863523)
      (xy 0.444573 2.868189)
      (xy 0.453261 2.874457)
      (xy 0.45545 2.876733)
      (xy 0.471614 2.90828)
      (xy 0.472797 2.944168)
      (xy 0.459536 2.975285)
      (xy 0.449043 2.985271)
      (xy 0.438129 2.990769)
      (xy 0.421217 2.995022)
      (xy 0.395633 2.99818)
      (xy 0.358701 3.000392)
      (xy 0.307746 3.001806)
      (xy 0.240094 3.002572)
      (xy 0.153069 3.002838)
      (xy 0.133394 3.002845)
      (xy 0.044911 3.002787)
      (xy -0.023773 3.002467)
      (xy -0.075436 3.001667)
      (xy -0.112855 3.000167)
      (xy -0.13881 2.997749)
      (xy -0.156078 2.994194)
      (xy -0.167438 2.989282)
      (xy -0.175668 2.982795)
      (xy -0.180183 2.978138)
      (xy -0.186979 2.969889)
      (xy -0.192288 2.959669)
      (xy -0.196294 2.9448)
      (xy -0.199179 2.922602)
      (xy -0.201126 2.890393)
      (xy -0.202319 2.845496)
      (xy -0.202939 2.785228)
      (xy -0.203171 2.706911)
      (xy -0.2032 2.640994)
      (xy -0.203129 2.548628)
      (xy -0.202792 2.476117)
      (xy -0.202002 2.420737)
      (xy -0.200574 2.379765)
      (xy -0.198321 2.350478)
      (xy -0.195057 2.330153)
      (xy -0.190596 2.316066)
      (xy -0.184752 2.305495)
      (xy -0.179803 2.298811)
      (xy -0.156406 2.269067)
      (xy 0.133774 2.269067)
      (xy 0.230343 2.26926)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp a12a6def-e6ff-4eab-9b6b-2cb997b1b86f))
    (fp_poly (pts (xy -3.691703 2.270351)
      (xy -3.616888 2.275581)
      (xy -3.547306 2.28375)
      (xy -3.487002 2.29455)
      (xy -3.44002 2.307673)
      (xy -3.410406 2.322813)
      (xy -3.40586 2.327269)
      (xy -3.390054 2.36185)
      (xy -3.394847 2.397351)
      (xy -3.419364 2.427725)
      (xy -3.420534 2.428596)
      (xy -3.434954 2.437954)
      (xy -3.450008 2.442876)
      (xy -3.471005 2.443473)
      (xy -3.503257 2.439861)
      (xy -3.552073 2.432154)
      (xy -3.556 2.431505)
      (xy -3.628739 2.422569)
      (xy -3.707217 2.418161)
      (xy -3.785927 2.418119)
      (xy -3.859361 2.422279)
      (xy -3.922011 2.430479)
      (xy -3.96837 2.442557)
      (xy -3.971416 2.443771)
      (xy -4.005048 2.462615)
      (xy -4.016864 2.481685)
      (xy -4.007614 2.500439)
      (xy -3.978047 2.518337)
      (xy -3.928911 2.534837)
      (xy -3.860957 2.549396)
      (xy -3.815645 2.556406)
      (xy -3.721456 2.569889)
      (xy -3.646544 2.582214)
      (xy -3.587717 2.594449)
      (xy -3.541785 2.607661)
      (xy -3.505555 2.622917)
      (xy -3.475838 2.641285)
      (xy -3.449442 2.663831)
      (xy -3.42823 2.685971)
      (xy -3.403065 2.716819)
      (xy -3.390681 2.743345)
      (xy -3.386808 2.776026)
      (xy -3.386667 2.787995)
      (xy -3.389576 2.827712)
      (xy -3.401202 2.857259)
      (xy -3.421323 2.883486)
      (xy -3.462216 2.923576)
      (xy -3.507817 2.954149)
      (xy -3.561513 2.976203)
      (xy -3.626692 2.990735)
      (xy -3.706744 2.998741)
      (xy -3.805057 3.001218)
      (xy -3.821289 3.001177)
      (xy -3.886849 2.999818)
      (xy -3.951866 2.99673)
      (xy -4.009252 2.992356)
      (xy -4.051922 2.98714)
      (xy -4.055372 2.986541)
      (xy -4.097796 2.976491)
      (xy -4.13378 2.963796)
      (xy -4.15415 2.95219)
      (xy -4.173107 2.921572)
      (xy -4.174427 2.885918)
      (xy -4.158085 2.854144)
      (xy -4.154429 2.850551)
      (xy -4.139315 2.839876)
      (xy -4.120415 2.835276)
      (xy -4.091162 2.836059)
      (xy -4.055651 2.840127)
      (xy -4.01597 2.843762)
      (xy -3.960345 2.846828)
      (xy -3.895406 2.849053)
      (xy -3.827785 2.850164)
      (xy -3.81 2.850237)
      (xy -3.742128 2.849964)
      (xy -3.692454 2.848646)
      (xy -3.65661 2.845827)
      (xy -3.630224 2.84105)
      (xy -3.608926 2.833857)
      (xy -3.596126 2.827867)
      (xy -3.568 2.811233)
      (xy -3.550068 2.796168)
      (xy -3.547447 2.791897)
      (xy -3.552976 2.774263)
      (xy -3.57926 2.757192)
      (xy -3.624478 2.741458)
      (xy -3.686808 2.727838)
      (xy -3.705171 2.724804)
      (xy -3.80109 2.709738)
      (xy -3.877641 2.697146)
      (xy -3.93778 2.686111)
      (xy -3.98446 2.67572)
      (xy -4.020637 2.665056)
      (xy -4.049265 2.653205)
      (xy -4.073298 2.639251)
      (xy -4.095692 2.622281)
      (xy -4.119402 2.601378)
      (xy -4.12738 2.594049)
      (xy -4.155353 2.566699)
      (xy -4.17016 2.545029)
      (xy -4.175952 2.520232)
      (xy -4.176889 2.488983)
      (xy -4.166575 2.427705)
      (xy -4.135752 2.37564)
      (xy -4.084595 2.332958)
      (xy -4.013283 2.299825)
      (xy -3.9624 2.284964)
      (xy -3.9071 2.275366)
      (xy -3.840853 2.269936)
      (xy -3.767706 2.268367)
      (xy -3.691703 2.270351)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp b3bf493d-1536-46ce-a63c-f620270b8313))
    (fp_poly (pts (xy 6.228823 2.274533)
      (xy 6.260202 2.296776)
      (xy 6.287911 2.324485)
      (xy 6.287911 2.63392)
      (xy 6.287838 2.725799)
      (xy 6.287495 2.79784)
      (xy 6.286692 2.85278)
      (xy 6.285241 2.89336)
      (xy 6.282952 2.922317)
      (xy 6.279636 2.942391)
      (xy 6.275105 2.956321)
      (xy 6.269169 2.966845)
      (xy 6.264514 2.9731)
      (xy 6.233783 2.997673)
      (xy 6.198496 3.000341)
      (xy 6.166245 2.985271)
      (xy 6.155588 2.976374)
      (xy 6.148464 2.964557)
      (xy 6.144167 2.945526)
      (xy 6.141991 2.914992)
      (xy 6.141228 2.868662)
      (xy 6.141155 2.832871)
      (xy 6.141155 2.698045)
      (xy 5.644444 2.698045)
      (xy 5.644444 2.8207)
      (xy 5.643931 2.876787)
      (xy 5.641876 2.915333)
      (xy 5.637508 2.941361)
      (xy 5.630056 2.959897)
      (xy 5.621047 2.9731)
      (xy 5.590144 2.997604)
      (xy 5.555196 3.000506)
      (xy 5.521738 2.983089)
      (xy 5.512604 2.973959)
      (xy 5.506152 2.961855)
      (xy 5.501897 2.943001)
      (xy 5.499352 2.91362)
      (xy 5.498029 2.869937)
      (xy 5.497443 2.808175)
      (xy 5.497375 2.794)
      (xy 5.496891 2.677631)
      (xy 5.496641 2.581727)
      (xy 5.496723 2.504177)
      (xy 5.497231 2.442869)
      (xy 5.498262 2.39569)
      (xy 5.499913 2.36053)
      (xy 5.502279 2.335276)
      (xy 5.505457 2.317817)
      (xy 5.509544 2.306041)
      (xy 5.514634 2.297835)
      (xy 5.520266 2.291645)
      (xy 5.552128 2.271844)
      (xy 5.585357 2.274533)
      (xy 5.616735 2.296776)
      (xy 5.629433 2.311126)
      (xy 5.637526 2.326978)
      (xy 5.642042 2.349554)
      (xy 5.644006 2.384078)
      (xy 5.644444 2.435776)
      (xy 5.644444 2.551289)
      (xy 6.141155 2.551289)
      (xy 6.141155 2.432756)
      (xy 6.141662 2.378148)
      (xy 6.143698 2.341275)
      (xy 6.148035 2.317307)
      (xy 6.155447 2.301415)
      (xy 6.163733 2.291645)
      (xy 6.195594 2.271844)
      (xy 6.228823 2.274533)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp b460ec43-d4d9-4d08-8593-6c70e959864c))
    (fp_poly (pts (xy 2.673574 -1.133448)
      (xy 2.825492 -1.113433)
      (xy 2.960756 -1.079798)
      (xy 3.080239 -1.032275)
      (xy 3.184815 -0.970595)
      (xy 3.262424 -0.907035)
      (xy 3.331265 -0.832901)
      (xy 3.385006 -0.753129)
      (xy 3.42791 -0.660909)
      (xy 3.443384 -0.617839)
      (xy 3.456244 -0.578858)
      (xy 3.467446 -0.542711)
      (xy 3.47712 -0.507566)
      (xy 3.485396 -0.47159)
      (xy 3.492403 -0.43295)
      (xy 3.498272 -0.389815)
      (xy 3.503131 -0.340351)
      (xy 3.50711 -0.282727)
      (xy 3.51034 -0.215109)
      (xy 3.512949 -0.135666)
      (xy 3.515067 -0.042564)
      (xy 3.516824 0.066027)
      (xy 3.518349 0.191942)
      (xy 3.519772 0.337012)
      (xy 3.521025 0.479778)
      (xy 3.522351 0.635968)
      (xy 3.523556 0.771239)
      (xy 3.524766 0.887246)
      (xy 3.526106 0.985645)
      (xy 3.5277 1.068093)
      (xy 3.529675 1.136246)
      (xy 3.532156 1.19176)
      (xy 3.535269 1.236292)
      (xy 3.539138 1.271498)
      (xy 3.543889 1.299034)
      (xy 3.549648 1.320556)
      (xy 3.556539 1.337722)
      (xy 3.564689 1.352186)
      (xy 3.574223 1.365606)
      (xy 3.585266 1.379638)
      (xy 3.589566 1.385071)
      (xy 3.605386 1.40791)
      (xy 3.612422 1.423463)
      (xy 3.612444 1.423922)
      (xy 3.601567 1.426121)
      (xy 3.570582 1.428147)
      (xy 3.521957 1.429942)
      (xy 3.458163 1.431451)
      (xy 3.381669 1.432616)
      (xy 3.294944 1.43338)
      (xy 3.200457 1.433686)
      (xy 3.18955 1.433689)
      (xy 2.766657 1.433689)
      (xy 2.763395 1.337622)
      (xy 2.760133 1.241556)
      (xy 2.698044 1.292543)
      (xy 2.600714 1.360057)
      (xy 2.490813 1.414749)
      (xy 2.404349 1.444978)
      (xy 2.335278 1.459666)
      (xy 2.251925 1.469659)
      (xy 2.162159 1.474646)
      (xy 2.073845 1.474313)
      (xy 1.994851 1.468351)
      (xy 1.958622 1.462638)
      (xy 1.818603 1.424776)
      (xy 1.692178 1.369932)
      (xy 1.58026 1.298924)
      (xy 1.483762 1.212568)
      (xy 1.4036 1.111679)
      (xy 1.340687 0.997076)
      (xy 1.296312 0.870984)
      (xy 1.283978 0.814401)
      (xy 1.276368 0.752202)
      (xy 1.272739 0.677363)
      (xy 1.272245 0.643467)
      (xy 1.27231 0.640282)
      (xy 2.032248 0.640282)
      (xy 2.041541 0.715333)
      (xy 2.069728 0.77916)
      (xy 2.118197 0.834798)
      (xy 2.123254 0.839211)
      (xy 2.171548 0.874037)
      (xy 2.223257 0.89662)
      (xy 2.283989 0.90854)
      (xy 2.359352 0.911383)
      (xy 2.377459 0.910978)
      (xy 2.431278 0.908325)
      (xy 2.471308 0.902909)
      (xy 2.506324 0.892745)
      (xy 2.545103 0.87585)
      (xy 2.555745 0.870672)
      (xy 2.616396 0.834844)
      (xy 2.663215 0.792212)
      (xy 2.675952 0.776973)
      (xy 2.720622 0.720462)
      (xy 2.720622 0.524586)
      (xy 2.720086 0.445939)
      (xy 2.718396 0.387988)
      (xy 2.715428 0.348875)
      (xy 2.711057 0.326741)
      (xy 2.706972 0.320274)
      (xy 2.691047 0.317111)
      (xy 2.657264 0.314488)
      (xy 2.61034 0.312655)
      (xy 2.554993 0.311857)
      (xy 2.546106 0.311842)
      (xy 2.42533 0.317096)
      (xy 2.32266 0.333263)
      (xy 2.236106 0.360961)
      (xy 2.163681 0.400808)
      (xy 2.108751 0.447758)
      (xy 2.064204 0.505645)
      (xy 2.03948 0.568693)
      (xy 2.032248 0.640282)
      (xy 1.27231 0.640282)
      (xy 1.274178 0.549712)
      (xy 1.282522 0.470812)
      (xy 1.298768 0.39959)
      (xy 1.324405 0.328864)
      (xy 1.348401 0.276493)
      (xy 1.40702 0.181196)
      (xy 1.485117 0.09317)
      (xy 1.580315 0.014017)
      (xy 1.690238 -0.05466)
      (xy 1.81251 -0.111259)
      (xy 1.944755 -0.154179)
      (xy 2.009422 -0.169118)
      (xy 2.145604 -0.191223)
      (xy 2.294049 -0.205806)
      (xy 2.445505 -0.212187)
      (xy 2.572064 -0.210555)
      (xy 2.73395 -0.203776)
      (xy 2.72653 -0.262755)
      (xy 2.707238 -0.361908)
      (xy 2.676104 -0.442628)
      (xy 2.632269 -0.505534)
      (xy 2.574871 -0.551244)
      (xy 2.503048 -0.580378)
      (xy 2.415941 -0.593553)
      (xy 2.312686 -0.591389)
      (xy 2.274711 -0.587388)
      (xy 2.13352 -0.56222)
      (xy 1.996707 -0.521186)
      (xy 1.902178 -0.483185)
      (xy 1.857018 -0.46381)
      (xy 1.818585 -0.44824)
      (xy 1.792234 -0.438595)
      (xy 1.784546 -0.436548)
      (xy 1.774802 -0.445626)
      (xy 1.758083 -0.474595)
      (xy 1.734232 -0.523783)
      (xy 1.703093 -0.593516)
      (xy 1.664507 -0.684121)
      (xy 1.65791 -0.699911)
      (xy 1.627853 -0.772228)
      (xy 1.600874 -0.837575)
      (xy 1.578136 -0.893094)
      (xy 1.560806 -0.935928)
      (xy 1.550048 -0.963219)
      (xy 1.546941 -0.972058)
      (xy 1.55694 -0.976813)
      (xy 1.583217 -0.98209)
      (xy 1.611489 -0.985769)
      (xy 1.641646 -0.990526)
      (xy 1.689433 -0.999972)
      (xy 1.750612 -1.01318)
      (xy 1.820946 -1.029224)
      (xy 1.896194 -1.04718)
      (xy 1.924755 -1.054203)
      (xy 2.029816 -1.079791)
      (xy 2.11748 -1.099853)
      (xy 2.192068 -1.115031)
      (xy 2.257903 -1.125965)
      (xy 2.319307 -1.133296)
      (xy 2.380602 -1.137665)
      (xy 2.44611 -1.139713)
      (xy 2.504128 -1.140111)
      (xy 2.673574 -1.133448)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp c6b7d9d2-5d62-4ae1-9221-1051406de405))
    (fp_poly (pts (xy 1.018309 2.269275)
      (xy 1.147288 2.273636)
      (xy 1.256991 2.286861)
      (xy 1.349226 2.309741)
      (xy 1.425802 2.34307)
      (xy 1.488527 2.387638)
      (xy 1.539212 2.444236)
      (xy 1.579663 2.513658)
      (xy 1.580459 2.515351)
      (xy 1.604601 2.577483)
      (xy 1.613203 2.632509)
      (xy 1.606231 2.687887)
      (xy 1.583654 2.751073)
      (xy 1.579372 2.760689)
      (xy 1.550172 2.816966)
      (xy 1.517356 2.860451)
      (xy 1.475002 2.897417)
      (xy 1.41719 2.934135)
      (xy 1.413831 2.936052)
      (xy 1.363504 2.960227)
      (xy 1.306621 2.978282)
      (xy 1.239527 2.990839)
      (xy 1.158565 2.998522)
      (xy 1.060082 3.001953)
      (xy 1.025286 3.002251)
      (xy 0.859594 3.002845)
      (xy 0.836197 2.9731)
      (xy 0.829257 2.963319)
      (xy 0.823842 2.951897)
      (xy 0.819765 2.936095)
      (xy 0.816837 2.913175)
      (xy 0.814867 2.880396)
      (xy 0.814225 2.856089)
      (xy 0.970844 2.856089)
      (xy 1.064726 2.856089)
      (xy 1.119664 2.854483)
      (xy 1.17606 2.850255)
      (xy 1.222345 2.844292)
      (xy 1.225139 2.84379)
      (xy 1.307348 2.821736)
      (xy 1.371114 2.7886)
      (xy 1.418452 2.742847)
      (xy 1.451382 2.682939)
      (xy 1.457108 2.667061)
      (xy 1.462721 2.642333)
      (xy 1.460291 2.617902)
      (xy 1.448467 2.5854)
      (xy 1.44134 2.569434)
      (xy 1.418 2.527006)
      (xy 1.38988 2.49724)
      (xy 1.35894 2.476511)
      (xy 1.296966 2.449537)
      (xy 1.217651 2.429998)
      (xy 1.125253 2.418746)
      (xy 1.058333 2.41627)
      (xy 0.970844 2.415822)
      (xy 0.970844 2.856089)
      (xy 0.814225 2.856089)
      (xy 0.813668 2.835021)
      (xy 0.81305 2.774311)
      (xy 0.812825 2.695526)
      (xy 0.8128 2.63392)
      (xy 0.8128 2.324485)
      (xy 0.840509 2.296776)
      (xy 0.852806 2.285544)
      (xy 0.866103 2.277853)
      (xy 0.884672 2.27304)
      (xy 0.912786 2.270446)
      (xy 0.954717 2.26941)
      (xy 1.014737 2.26927)
      (xy 1.018309 2.269275)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp db8d6e9c-2fd9-4f84-b635-6492602ee0e3))
    (fp_poly (pts (xy -6.121371 2.269066)
      (xy -6.081889 2.269467)
      (xy -5.9662 2.272259)
      (xy -5.869311 2.28055)
      (xy -5.787919 2.295232)
      (xy -5.718723 2.317193)
      (xy -5.65842 2.347322)
      (xy -5.603708 2.38651)
      (xy -5.584167 2.403532)
      (xy -5.55175 2.443363)
      (xy -5.52252 2.497413)
      (xy -5.499991 2.557323)
      (xy -5.487679 2.614739)
      (xy -5.4864 2.635956)
      (xy -5.494417 2.694769)
      (xy -5.515899 2.759013)
      (xy -5.546999 2.819821)
      (xy -5.583866 2.86833)
      (xy -5.589854 2.874182)
      (xy -5.640579 2.915321)
      (xy -5.696125 2.947435)
      (xy -5.759696 2.971365)
      (xy -5.834494 2.987953)
      (xy -5.923722 2.998041)
      (xy -6.030582 3.002469)
      (xy -6.079528 3.002845)
      (xy -6.141762 3.002545)
      (xy -6.185528 3.001292)
      (xy -6.214931 2.998554)
      (xy -6.234079 2.993801)
      (xy -6.247077 2.986501)
      (xy -6.254045 2.980267)
      (xy -6.260626 2.972694)
      (xy -6.265788 2.962924)
      (xy -6.269703 2.94834)
      (xy -6.272543 2.926326)
      (xy -6.27448 2.894264)
      (xy -6.275684 2.849536)
      (xy -6.276328 2.789526)
      (xy -6.276583 2.711617)
      (xy -6.276622 2.635956)
      (xy -6.27687 2.535041)
      (xy -6.276817 2.454427)
      (xy -6.275857 2.415822)
      (xy -6.129867 2.415822)
      (xy -6.129867 2.856089)
      (xy -6.036734 2.856004)
      (xy -5.980693 2.854396)
      (xy -5.921999 2.850256)
      (xy -5.873028 2.844464)
      (xy -5.871538 2.844226)
      (xy -5.792392 2.82509)
      (xy -5.731002 2.795287)
      (xy -5.684305 2.752878)
      (xy -5.654635 2.706961)
      (xy -5.636353 2.656026)
      (xy -5.637771 2.6082)
      (xy -5.658988 2.556933)
      (xy -5.700489 2.503899)
      (xy -5.757998 2.4646)
      (xy -5.83275 2.438331)
      (xy -5.882708 2.429035)
      (xy -5.939416 2.422507)
      (xy -5.999519 2.417782)
      (xy -6.050639 2.415817)
      (xy -6.053667 2.415808)
      (xy -6.129867 2.415822)
      (xy -6.275857 2.415822)
      (xy -6.27526 2.391851)
      (xy -6.270998 2.345055)
      (xy -6.26283 2.311778)
      (xy -6.249556 2.289759)
      (xy -6.229974 2.276739)
      (xy -6.202883 2.270457)
      (xy -6.167082 2.268653)
      (xy -6.121371 2.269066)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp e0352667-1693-462c-a48c-8cad8bc62c7a))
    (fp_poly (pts (xy -1.950081 2.274599)
      (xy -1.881565 2.286095)
      (xy -1.828943 2.303967)
      (xy -1.794708 2.327499)
      (xy -1.785379 2.340924)
      (xy -1.775893 2.372148)
      (xy -1.782277 2.400395)
      (xy -1.80243 2.427182)
      (xy -1.833745 2.439713)
      (xy -1.879183 2.438696)
      (xy -1.914326 2.431906)
      (xy -1.992419 2.418971)
      (xy -2.072226 2.417742)
      (xy -2.161555 2.428241)
      (xy -2.186229 2.43269)
      (xy -2.269291 2.456108)
      (xy -2.334273 2.490945)
      (xy -2.380461 2.536604)
      (xy -2.407145 2.592494)
      (xy -2.412663 2.621388)
      (xy -2.409051 2.680012)
      (xy -2.385729 2.731879)
      (xy -2.344824 2.775978)
      (xy -2.288459 2.811299)
      (xy -2.21876 2.836829)
      (xy -2.137852 2.851559)
      (xy -2.04786 2.854478)
      (xy -1.95091 2.844575)
      (xy -1.945436 2.843641)
      (xy -1.906875 2.836459)
      (xy -1.885494 2.829521)
      (xy -1.876227 2.819227)
      (xy -1.874006 2.801976)
      (xy -1.873956 2.792841)
      (xy -1.873956 2.754489)
      (xy -1.942431 2.754489)
      (xy -2.0029 2.750347)
      (xy -2.044165 2.737147)
      (xy -2.068175 2.71373)
      (xy -2.076877 2.678936)
      (xy -2.076983 2.674394)
      (xy -2.071892 2.644654)
      (xy -2.054433 2.623419)
      (xy -2.021939 2.609366)
      (xy -1.971743 2.601173)
      (xy -1.923123 2.598161)
      (xy -1.852456 2.596433)
      (xy -1.801198 2.59907)
      (xy -1.766239 2.6088)
      (xy -1.74447 2.628353)
      (xy -1.73278 2.660456)
      (xy -1.72806 2.707838)
      (xy -1.7272 2.770071)
      (xy -1.728609 2.839535)
      (xy -1.732848 2.886786)
      (xy -1.739936 2.912012)
      (xy -1.741311 2.913988)
      (xy -1.780228 2.945508)
      (xy -1.837286 2.97047)
      (xy -1.908869 2.98834)
      (xy -1.991358 2.998586)
      (xy -2.081139 3.000673)
      (xy -2.174592 2.994068)
      (xy -2.229556 2.985956)
      (xy -2.315766 2.961554)
      (xy -2.395892 2.921662)
      (xy -2.462977 2.869887)
      (xy -2.473173 2.859539)
      (xy -2.506302 2.816035)
      (xy -2.536194 2.762118)
      (xy -2.559357 2.705592)
      (xy -2.572298 2.654259)
      (xy -2.573858 2.634544)
      (xy -2.567218 2.593419)
      (xy -2.549568 2.542252)
      (xy -2.524297 2.488394)
      (xy -2.494789 2.439195)
      (xy -2.468719 2.406334)
      (xy -2.407765 2.357452)
      (xy -2.328969 2.318545)
      (xy -2.235157 2.290494)
      (xy -2.12915 2.274179)
      (xy -2.032 2.270192)
      (xy -1.950081 2.274599)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp e0438b48-6dd7-431e-b999-4eb25cae4264))
    (fp_poly (pts (xy 3.744665 2.271034)
      (xy 3.764255 2.278035)
      (xy 3.76501 2.278377)
      (xy 3.791613 2.298678)
      (xy 3.80627 2.319561)
      (xy 3.809138 2.329352)
      (xy 3.808996 2.342361)
      (xy 3.804961 2.360895)
      (xy 3.796146 2.387257)
      (xy 3.781669 2.423752)
      (xy 3.760645 2.472687)
      (xy 3.732188 2.536365)
      (xy 3.695415 2.617093)
      (xy 3.675175 2.661216)
      (xy 3.638625 2.739985)
      (xy 3.604315 2.812423)
      (xy 3.573552 2.87588)
      (xy 3.547648 2.927708)
      (xy 3.52791 2.965259)
      (xy 3.51565 2.985884)
      (xy 3.513224 2.988733)
      (xy 3.482183 3.001302)
      (xy 3.447121 2.999619)
      (xy 3.419 2.984332)
      (xy 3.417854 2.983089)
      (xy 3.406668 2.966154)
      (xy 3.387904 2.93317)
      (xy 3.363875 2.88838)
      (xy 3.336897 2.836032)
      (xy 3.327201 2.816742)
      (xy 3.254014 2.67015)
      (xy 3.17424 2.829393)
      (xy 3.145767 2.884415)
      (xy 3.11935 2.932132)
      (xy 3.097148 2.968893)
      (xy 3.081319 2.991044)
      (xy 3.075954 2.995741)
      (xy 3.034257 3.002102)
      (xy 2.999849 2.988733)
      (xy 2.989728 2.974446)
      (xy 2.972214 2.942692)
      (xy 2.948735 2.896597)
      (xy 2.92072 2.839285)
      (xy 2.889599 2.77388)
      (xy 2.856799 2.703507)
      (xy 2.82375 2.631291)
      (xy 2.791881 2.560355)
      (xy 2.762619 2.493825)
      (xy 2.737395 2.434826)
      (xy 2.717636 2.386481)
      (xy 2.704772 2.351915)
      (xy 2.700231 2.334253)
      (xy 2.700277 2.333613)
      (xy 2.711326 2.311388)
      (xy 2.73341 2.288753)
      (xy 2.73471 2.287768)
      (xy 2.761853 2.272425)
      (xy 2.786958 2.272574)
      (xy 2.796368 2.275466)
      (xy 2.807834 2.281718)
      (xy 2.82001 2.294014)
      (xy 2.834357 2.314908)
      (xy 2.852336 2.346949)
      (xy 2.875407 2.392688)
      (xy 2.90503 2.454677)
      (xy 2.931745 2.511898)
      (xy 2.96248 2.578226)
      (xy 2.990021 2.637874)
      (xy 3.012938 2.687725)
      (xy 3.029798 2.724664)
      (xy 3.039173 2.745573)
      (xy 3.04054 2.748845)
      (xy 3.046689 2.743497)
      (xy 3.060822 2.721109)
      (xy 3.081057 2.684946)
      (xy 3.105515 2.638277)
      (xy 3.115248 2.619022)
      (xy 3.148217 2.554004)
      (xy 3.173643 2.506654)
      (xy 3.193612 2.474219)
      (xy 3.21021 2.453946)
      (xy 3.225524 2.443082)
      (xy 3.24164 2.438875)
      (xy 3.252143 2.4384)
      (xy 3.27067 2.440042)
      (xy 3.286904 2.446831)
      (xy 3.303035 2.461566)
      (xy 3.321251 2.487044)
      (xy 3.343739 2.526061)
      (xy 3.372689 2.581414)
      (xy 3.388662 2.612903)
      (xy 3.41457 2.663087)
      (xy 3.437167 2.704704)
      (xy 3.454458 2.734242)
      (xy 3.46445 2.748189)
      (xy 3.465809 2.74877)
      (xy 3.472261 2.737793)
      (xy 3.486708 2.70929)
      (xy 3.507703 2.666244)
      (xy 3.533797 2.611638)
      (xy 3.563546 2.548454)
      (xy 3.57818 2.517071)
      (xy 3.61625 2.436078)
      (xy 3.646905 2.373756)
      (xy 3.671737 2.328071)
      (xy 3.692337 2.296989)
      (xy 3.710298 2.278478)
      (xy 3.72721 2.270504)
      (xy 3.744665 2.271034)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp e4ed3daf-28c1-4ccd-a97a-ae5e1074e385))
    (fp_poly (pts (xy -2.9464 -2.510946)
      (xy -2.935535 -2.397007)
      (xy -2.903918 -2.289384)
      (xy -2.853015 -2.190385)
      (xy -2.784293 -2.102316)
      (xy -2.699219 -2.027484)
      (xy -2.602232 -1.969616)
      (xy -2.495964 -1.929995)
      (xy -2.38895 -1.911427)
      (xy -2.2833 -1.912566)
      (xy -2.181125 -1.93207)
      (xy -2.084534 -1.968594)
      (xy -1.995638 -2.020795)
      (xy -1.916546 -2.087327)
      (xy -1.849369 -2.166848)
      (xy -1.796217 -2.258013)
      (xy -1.759199 -2.359477)
      (xy -1.740427 -2.469898)
      (xy -1.738489 -2.519794)
      (xy -1.738489 -2.607733)
      (xy -1.68656 -2.607733)
      (xy -1.650253 -2.604889)
      (xy -1.623355 -2.593089)
      (xy -1.596249 -2.569351)
      (xy -1.557867 -2.530969)
      (xy -1.557867 -0.339398)
      (xy -1.557876 -0.077261)
      (xy -1.557908 0.163241)
      (xy -1.557972 0.383048)
      (xy -1.558076 0.583101)
      (xy -1.558227 0.764344)
      (xy -1.558434 0.927716)
      (xy -1.558706 1.07416)
      (xy -1.55905 1.204617)
      (xy -1.559474 1.320029)
      (xy -1.559987 1.421338)
      (xy -1.560597 1.509484)
      (xy -1.561312 1.58541)
      (xy -1.56214 1.650057)
      (xy -1.563089 1.704367)
      (xy -1.564167 1.74928)
      (xy -1.565383 1.78574)
      (xy -1.566745 1.814687)
      (xy -1.568261 1.837063)
      (xy -1.569938 1.853809)
      (xy -1.571786 1.865868)
      (xy -1.573813 1.87418)
      (xy -1.576025 1.879687)
      (xy -1.577108 1.881537)
      (xy -1.581271 1.888549)
      (xy -1.584805 1.894996)
      (xy -1.588635 1.9009)
      (xy -1.593682 1.906286)
      (xy -1.600871 1.911178)
      (xy -1.611123 1.915598)
      (xy -1.625364 1.919572)
      (xy -1.644514 1.923121)
      (xy -1.669499 1.92627)
      (xy -1.70124 1.929042)
      (xy -1.740662 1.931461)
      (xy -1.788686 1.933551)
      (xy -1.846237 1.935335)
      (xy -1.914237 1.936837)
      (xy -1.99361 1.93808)
      (xy -2.085279 1.939089)
      (xy -2.190166 1.939885)
      (xy -2.309196 1.940494)
      (xy -2.44329 1.940939)
      (xy -2.593373 1.941243)
      (xy -2.760367 1.94143)
      (xy -2.945196 1.941524)
      (xy -3.148783 1.941548)
      (xy -3.37205 1.941525)
      (xy -3.615922 1.94148)
      (xy -3.881321 1.941437)
      (xy -3.919704 1.941432)
      (xy -4.186682 1.941389)
      (xy -4.432002 1.941318)
      (xy -4.656583 1.941213)
      (xy -4.861345 1.941066)
      (xy -5.047206 1.940869)
      (xy -5.215088 1.940616)
      (xy -5.365908 1.9403)
      (xy -5.500587 1.939913)
      (xy -5.620044 1.939447)
      (xy -5.725199 1.938897)
      (xy -5.816971 1.938253)
      (xy -5.896279 1.937511)
      (xy -5.964043 1.936661)
      (xy -6.021182 1.935697)
      (xy -6.068617 1.934611)
      (xy -6.107266 1.933397)
      (xy -6.138049 1.932047)
      (xy -6.161885 1.930555)
      (xy -6.179694 1.928911)
      (xy -6.192395 1.927111)
      (xy -6.200908 1.925145)
      (xy -6.205266 1.923477)
      (xy -6.213728 1.919906)
      (xy -6.221497 1.91727)
      (xy -6.228602 1.914634)
      (xy -6.235073 1.911062)
      (xy -6.240939 1.905621)
      (xy -6.246229 1.897375)
      (xy -6.250974 1.88539)
      (xy -6.255202 1.868731)
      (xy -6.258943 1.846463)
      (xy -6.262227 1.817652)
      (xy -6.265083 1.781363)
      (xy -6.26754 1.736661)
      (xy -6.269629 1.682611)
      (xy -6.271378 1.618279)
      (xy -6.272817 1.54273)
      (xy -6.273976 1.45503)
      (xy -6.274883 1.354243)
      (xy -6.275569 1.239434)
      (xy -6.276063 1.10967)
      (xy -6.276395 0.964015)
      (xy -6.276593 0.801535)
      (xy -6.276687 0.621295)
      (xy -6.276708 0.42236)
      (xy -6.276685 0.203796)
      (xy -6.276646 -0.035332)
      (xy -6.276622 -0.29596)
      (xy -6.276622 -0.338111)
      (xy -6.276636 -0.601008)
      (xy -6.276661 -0.842268)
      (xy -6.276671 -1.062835)
      (xy -6.276642 -1.263648)
      (xy -6.276548 -1.445651)
      (xy -6.276362 -1.609784)
      (xy -6.276059 -1.756989)
      (xy -6.275614 -1.888208)
      (xy -6.275034 -1.998133)
      (xy -5.972197 -1.998133)
      (xy -5.932407 -1.940289)
      (xy -5.921236 -1.924521)
      (xy -5.911166 -1.910559)
      (xy -5.902138 -1.897216)
      (xy -5.894097 -1.883307)
      (xy -5.886986 -1.867644)
      (xy -5.880747 -1.849042)
      (xy -5.875325 -1.826314)
      (xy -5.870662 -1.798273)
      (xy -5.866701 -1.763733)
      (xy -5.863385 -1.721508)
      (xy -5.860659 -1.670411)
      (xy -5.858464 -1.609256)
      (xy -5.856745 -1.536856)
      (xy -5.855444 -1.452025)
      (xy -5.854505 -1.353578)
      (xy -5.85387 -1.240326)
      (xy -5.853484 -1.111084)
      (xy -5.853288 -0.964666)
      (xy -5.853227 -0.799884)
      (xy -5.853243 -0.615553)
      (xy -5.85328 -0.410487)
      (xy -5.853289 -0.287867)
      (xy -5.853265 -0.070918)
      (xy -5.853231 0.124642)
      (xy -5.853243 0.299999)
      (xy -5.853358 0.456341)
      (xy -5.85363 0.594857)
      (xy -5.854118 0.716734)
      (xy -5.854876 0.82316)
      (xy -5.855962 0.915322)
      (xy -5.857431 0.994409)
      (xy -5.85934 1.061608)
      (xy -5.861744 1.118107)
      (xy -5.864701 1.165093)
      (xy -5.868266 1.203755)
      (xy -5.872495 1.23528)
      (xy -5.877446 1.260855)
      (xy -5.883173 1.28167)
      (xy -5.889733 1.298911)
      (xy -5.897183 1.313765)
      (xy -5.905579 1.327422)
      (xy -5.914976 1.341069)
      (xy -5.925432 1.355893)
      (xy -5.931523 1.364783)
      (xy -5.970296 1.4224)
      (xy -5.438732 1.4224)
      (xy -5.315483 1.422365)
      (xy -5.212987 1.422215)
      (xy -5.12942 1.421878)
      (xy -5.062956 1.421286)
      (xy -5.011771 1.420367)
      (xy -4.974041 1.419051)
      (xy -4.94794 1.417269)
      (xy -4.931644 1.414951)
      (xy -4.923328 1.412026)
      (xy -4.921168 1.408424)
      (xy -4.923339 1.404075)
      (xy -4.924535 1.402645)
      (xy -4.949685 1.365573)
      (xy -4.975583 1.312772)
      (xy -4.999192 1.25077)
      (xy -5.007461 1.224357)
      (xy -5.012078 1.206416)
      (xy -5.015979 1.185355)
      (xy -5.019248 1.159089)
      (xy -5.021966 1.125532)
      (xy -5.024215 1.082599)
      (xy -5.026077 1.028204)
      (xy -5.027636 0.960262)
      (xy -5.028972 0.876688)
      (xy -5.030169 0.775395)
      (xy -5.031308 0.6543)
      (xy -5.031685 0.6096)
      (xy -5.032702 0.484449)
      (xy -5.03346 0.380082)
      (xy -5.033903 0.294707)
      (xy -5.03397 0.226533)
      (xy -5.033605 0.173765)
      (xy -5.032748 0.134614)
      (xy -5.031341 0.107285)
      (xy -5.029325 0.089986)
      (xy -5.026643 0.080926)
      (xy -5.023236 0.078312)
      (xy -5.019044 0.080351)
      (xy -5.014571 0.084667)
      (xy -5.004216 0.097602)
      (xy -4.982158 0.126676)
      (xy -4.949957 0.169759)
      (xy -4.909174 0.224718)
      (xy -4.86137 0.289423)
      (xy -4.808105 0.361742)
      (xy -4.75094 0.439544)
      (xy -4.691437 0.520698)
      (xy -4.631155 0.603072)
      (xy -4.571655 0.684536)
      (xy -4.514498 0.762957)
      (xy -4.461245 0.836204)
      (xy -4.413457 0.902147)
      (xy -4.372693 0.958654)
      (xy -4.340516 1.003593)
      (xy -4.318485 1.034834)
      (xy -4.313917 1.041466)
      (xy -4.290996 1.078369)
      (xy -4.264188 1.126359)
      (xy -4.238789 1.175897)
      (xy -4.235568 1.182577)
      (xy -4.21389 1.230772)
      (xy -4.201304 1.268334)
      (xy -4.195574 1.30416)
      (xy -4.194456 1.3462)
      (xy -4.19509 1.4224)
      (xy -3.040651 1.4224)
      (xy -3.131815 1.328669)
      (xy -3.178612 1.278775)
      (xy -3.228899 1.222295)
      (xy -3.274944 1.168026)
      (xy -3.295369 1.142673)
      (xy -3.325807 1.103128)
      (xy -3.365862 1.049916)
      (xy -3.414361 0.984667)
      (xy -3.470135 0.909011)
      (xy -3.532011 0.824577)
      (xy -3.598819 0.732994)
      (xy -3.669387 0.635892)
      (xy -3.742545 0.534901)
      (xy -3.817121 0.43165)
      (xy -3.891944 0.327768)
      (xy -3.965843 0.224885)
      (xy -4.037646 0.124631)
      (xy -4.106184 0.028636)
      (xy -4.170284 -0.061473)
      (xy -4.228775 -0.144064)
      (xy -4.280486 -0.217508)
      (xy -4.324247 -0.280176)
      (xy -4.358885 -0.330439)
      (xy -4.38323 -0.366666)
      (xy -4.396111 -0.387229)
      (xy -4.397869 -0.391332)
      (xy -4.38991 -0.402658)
      (xy -4.369115 -0.429838)
      (xy -4.336847 -0.471171)
      (xy -4.29447 -0.524956)
      (xy -4.243347 -0.589494)
      (xy -4.184841 -0.663082)
      (xy -4.120314 -0.744022)
      (xy -4.051131 -0.830612)
      (xy -3.978653 -0.921152)
      (xy -3.904246 -1.01394)
      (xy -3.844517 -1.088298)
      (xy -2.833511 -1.088298)
      (xy -2.827602 -1.075341)
      (xy -2.813272 -1.053092)
      (xy -2.812225 -1.051609)
      (xy -2.793438 -1.021456)
      (xy -2.773791 -0.984625)
      (xy -2.769892 -0.976489)
      (xy -2.766356 -0.96806)
      (xy -2.76323 -0.957941)
      (xy -2.760486 -0.94474)
      (xy -2.758092 -0.927062)
      (xy -2.756019 -0.903516)
      (xy -2.754235 -0.872707)
      (xy -2.752712 -0.833243)
      (xy -2.751419 -0.783731)
      (xy -2.750326 -0.722777)
      (xy -2.749403 -0.648989)
      (xy -2.748619 -0.560972)
      (xy -2.747945 -0.457335)
      (xy -2.74735 -0.336684)
      (xy -2.746805 -0.197626)
      (xy -2.746279 -0.038768)
      (xy -2.745745 0.140089)
      (xy -2.745206 0.325207)
      (xy -2.744772 0.489145)
      (xy -2.744509 0.633303)
      (xy -2.744484 0.759079)
      (xy -2.744765 0.867871)
      (xy -2.745419 0.961077)
      (xy -2.746514 1.040097)
      (xy -2.748118 1.106328)
      (xy -2.750297 1.16117)
      (xy -2.753119 1.206021)
      (xy -2.756651 1.242278)
      (xy -2.760961 1.271341)
      (xy -2.766117 1.294609)
      (xy -2.772185 1.313479)
      (xy -2.779233 1.329351)
      (xy -2.787329 1.343622)
      (xy -2.79654 1.357691)
      (xy -2.80504 1.370158)
      (xy -2.822176 1.396452)
      (xy -2.832322 1.414037)
      (xy -2.833511 1.417257)
      (xy -2.822604 1.418334)
      (xy -2.791411 1.419335)
      (xy -2.742223 1.420235)
      (xy -2.677333 1.42101)
      (xy -2.59903 1.421637)
      (xy -2.509607 1.422091)
      (xy -2.411356 1.422349)
      (xy -2.342445 1.4224)
      (xy -2.237452 1.42218)
      (xy -2.14061 1.421548)
      (xy -2.054107 1.420549)
      (xy -1.980132 1.419227)
      (xy -1.920874 1.417626)
      (xy -1.87852 1.415791)
      (xy -1.85526 1.413765)
      (xy -1.851378 1.412493)
      (xy -1.859076 1.397591)
      (xy -1.867074 1.38956)
      (xy -1.880246 1.372434)
      (xy -1.897485 1.342183)
      (xy -1.909407 1.317622)
      (xy -1.936045 1.258711)
      (xy -1.93912 0.081845)
      (xy -1.942195 -1.095022)
      (xy -2.387853 -1.095022)
      (xy -2.48567 -1.094858)
      (xy -2.576064 -1.094389)
      (xy -2.65663 -1.093653)
      (xy -2.724962 -1.092684)
      (xy -2.778656 -1.09152)
      (xy -2.815305 -1.090197)
      (xy -2.832504 -1.088751)
      (xy -2.833511 -1.088298)
      (xy -3.844517 -1.088298)
      (xy -3.82927 -1.107278)
      (xy -3.75509 -1.199463)
      (xy -3.683069 -1.288796)
      (xy -3.614569 -1.373576)
      (xy -3.550955 -1.452102)
      (xy -3.493588 -1.522674)
      (xy -3.443833 -1.583591)
      (xy -3.403052 -1.633153)
      (xy -3.385888 -1.653822)
      (xy -3.299596 -1.754484)
      (xy -3.222997 -1.837741)
      (xy -3.154183 -1.905562)
      (xy -3.091248 -1.959911)
      (xy -3.081867 -1.967278)
      (xy -3.042356 -1.997883)
      (xy -4.174116 -1.998133)
      (xy -4.168827 -1.950156)
      (xy -4.17213 -1.892812)
      (xy -4.193661 -1.824537)
      (xy -4.233635 -1.744788)
      (xy -4.278943 -1.672505)
      (xy -4.295161 -1.64986)
      (xy -4.323214 -1.612304)
      (xy -4.36143 -1.561979)
      (xy -4.408137 -1.501027)
      (xy -4.461661 -1.431589)
      (xy -4.520331 -1.355806)
      (xy -4.582475 -1.27582)
      (xy -4.646421 -1.193772)
      (xy -4.710495 -1.111804)
      (xy -4.773027 -1.032057)
      (xy -4.832343 -0.956673)
      (xy -4.886771 -0.887793)
      (xy -4.934639 -0.827558)
      (xy -4.974275 -0.778111)
      (xy -5.004006 -0.741592)
      (xy -5.022161 -0.720142)
      (xy -5.02522 -0.716844)
      (xy -5.028079 -0.724851)
      (xy -5.030293 -0.755145)
      (xy -5.031857 -0.807444)
      (xy -5.032767 -0.881469)
      (xy -5.03302 -0.976937)
      (xy -5.032613 -1.093566)
      (xy -5.031704 -1.213555)
      (xy -5.030382 -1.345667)
      (xy -5.028857 -1.457406)
      (xy -5.026881 -1.550975)
      (xy -5.024206 -1.628581)
      (xy -5.020582 -1.692426)
      (xy -5.015761 -1.744717)
      (xy -5.009494 -1.787656)
      (xy -5.001532 -1.823449)
      (xy -4.991627 -1.8543)
      (xy -4.979531 -1.882414)
      (xy -4.964993 -1.909995)
      (xy -4.950311 -1.935034)
      (xy -4.912314 -1.998133)
      (xy -5.972197 -1.998133)
      (xy -6.275034 -1.998133)
      (xy -6.275001 -2.004383)
      (xy -6.274195 -2.106456)
      (xy -6.27317 -2.195367)
      (xy -6.2719 -2.272059)
      (xy -6.27036 -2.337473)
      (xy -6.268524 -2.392551)
      (xy -6.266367 -2.438235)
      (xy -6.263863 -2.475466)
      (xy -6.260987 -2.505187)
      (xy -6.257713 -2.528338)
      (xy -6.254015 -2.545861)
      (xy -6.249869 -2.558699)
      (xy -6.245247 -2.567792)
      (xy -6.240126 -2.574082)
      (xy -6.234478 -2.578512)
      (xy -6.228279 -2.582022)
      (xy -6.221504 -2.585555)
      (xy -6.215508 -2.589124)
      (xy -6.210275 -2.5917)
      (xy -6.202099 -2.594028)
      (xy -6.189886 -2.596122)
      (xy -6.172541 -2.597993)
      (xy -6.148969 -2.599653)
      (xy -6.118077 -2.601116)
      (xy -6.078768 -2.602392)
      (xy -6.02995 -2.603496)
      (xy -5.970527 -2.604439)
      (xy -5.899404 -2.605233)
      (xy -5.815488 -2.605891)
      (xy -5.717683 -2.606425)
      (xy -5.604894 -2.606847)
      (xy -5.476029 -2.607171)
      (xy -5.329991 -2.607408)
      (xy -5.165686 -2.60757)
      (xy -4.98202 -2.60767)
      (xy -4.777897 -2.60772)
      (xy -4.566753 -2.607733)
      (xy -2.9464 -2.607733)
      (xy -2.9464 -2.510946)) (layer "F.Cu") (width 0.01) (fill solid) (tstamp f3963048-e8fa-4b75-ab08-3a6417523c2c))
  )

  (footprint "Symbol:OSHW-Logo2_7.3x6mm_SilkScreen" (layer "F.Cu")
    (tedit 0) (tstamp 07dd0982-cc5f-457d-85a4-3c46ad687e4d)
    (at 29.972 -49.53)
    (descr "Open Source Hardware Symbol")
    (tags "Logo Symbol OSHW")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "exclude_from_bom" "")
    (path "/a7d6d8d4-7f7c-48ff-bd98-79960a140ba0")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "FID4" (at 0 0) (layer "F.SilkS") hide
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 1318504f-01db-4f21-8dda-95669e0ded3c)
    )
    (fp_text value "Licensed under CERN-OHL-W v2 or later" (at 0.75 0) (layer "F.Fab") hide
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp c25c0c51-834e-4750-ac40-12f84bbbc4e5)
    )
    (fp_poly (pts (xy 1.190117 2.065358)
      (xy 1.189933 2.173837)
      (xy 1.189219 2.257287)
      (xy 1.187675 2.319704)
      (xy 1.185001 2.365085)
      (xy 1.180894 2.397429)
      (xy 1.175055 2.420733)
      (xy 1.167182 2.438995)
      (xy 1.161221 2.449418)
      (xy 1.111855 2.505945)
      (xy 1.049264 2.541377)
      (xy 0.980013 2.55409)
      (xy 0.910668 2.542463)
      (xy 0.869375 2.521568)
      (xy 0.826025 2.485422)
      (xy 0.796481 2.441276)
      (xy 0.778655 2.383462)
      (xy 0.770463 2.306313)
      (xy 0.769302 2.249714)
      (xy 0.769458 2.245647)
      (xy 0.870857 2.245647)
      (xy 0.871476 2.31055)
      (xy 0.874314 2.353514)
      (xy 0.88084 2.381622)
      (xy 0.892523 2.401953)
      (xy 0.906483 2.417288)
      (xy 0.953365 2.44689)
      (xy 1.003701 2.449419)
      (xy 1.051276 2.424705)
      (xy 1.054979 2.421356)
      (xy 1.070783 2.403935)
      (xy 1.080693 2.383209)
      (xy 1.086058 2.352362)
      (xy 1.088228 2.304577)
      (xy 1.088571 2.251748)
      (xy 1.087827 2.185381)
      (xy 1.084748 2.141106)
      (xy 1.078061 2.112009)
      (xy 1.066496 2.091173)
      (xy 1.057013 2.080107)
      (xy 1.01296 2.052198)
      (xy 0.962224 2.048843)
      (xy 0.913796 2.070159)
      (xy 0.90445 2.078073)
      (xy 0.88854 2.095647)
      (xy 0.87861 2.116587)
      (xy 0.873278 2.147782)
      (xy 0.871163 2.196122)
      (xy 0.870857 2.245647)
      (xy 0.769458 2.245647)
      (xy 0.77281 2.158568)
      (xy 0.784726 2.090086)
      (xy 0.807135 2.0386)
      (xy 0.842124 1.998443)
      (xy 0.869375 1.977861)
      (xy 0.918907 1.955625)
      (xy 0.976316 1.945304)
      (xy 1.029682 1.948067)
      (xy 1.059543 1.959212)
      (xy 1.071261 1.962383)
      (xy 1.079037 1.950557)
      (xy 1.084465 1.918866)
      (xy 1.088571 1.870593)
      (xy 1.093067 1.816829)
      (xy 1.099313 1.784482)
      (xy 1.110676 1.765985)
      (xy 1.130528 1.75377)
      (xy 1.143 1.748362)
      (xy 1.190171 1.728601)
      (xy 1.190117 2.065358)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 05769e33-3dfd-4026-a748-97222101a3c7))
    (fp_poly (pts (xy 0.10391 -2.757652)
      (xy 0.182454 -2.757222)
      (xy 0.239298 -2.756058)
      (xy 0.278105 -2.753793)
      (xy 0.302538 -2.75006)
      (xy 0.316262 -2.744494)
      (xy 0.32294 -2.736727)
      (xy 0.326236 -2.726395)
      (xy 0.326556 -2.725057)
      (xy 0.331562 -2.700921)
      (xy 0.340829 -2.653299)
      (xy 0.353392 -2.587259)
      (xy 0.368287 -2.507872)
      (xy 0.384551 -2.420204)
      (xy 0.385119 -2.417125)
      (xy 0.40141 -2.331211)
      (xy 0.416652 -2.255304)
      (xy 0.429861 -2.193955)
      (xy 0.440054 -2.151718)
      (xy 0.446248 -2.133145)
      (xy 0.446543 -2.132816)
      (xy 0.464788 -2.123747)
      (xy 0.502405 -2.108633)
      (xy 0.551271 -2.090738)
      (xy 0.551543 -2.090642)
      (xy 0.613093 -2.067507)
      (xy 0.685657 -2.038035)
      (xy 0.754057 -2.008403)
      (xy 0.757294 -2.006938)
      (xy 0.868702 -1.956374)
      (xy 1.115399 -2.12484)
      (xy 1.191077 -2.176197)
      (xy 1.259631 -2.222111)
      (xy 1.317088 -2.25997)
      (xy 1.359476 -2.287163)
      (xy 1.382825 -2.301079)
      (xy 1.385042 -2.302111)
      (xy 1.40201 -2.297516)
      (xy 1.433701 -2.275345)
      (xy 1.481352 -2.234553)
      (xy 1.546198 -2.174095)
      (xy 1.612397 -2.109773)
      (xy 1.676214 -2.046388)
      (xy 1.733329 -1.988549)
      (xy 1.780305 -1.939825)
      (xy 1.813703 -1.90379)
      (xy 1.830085 -1.884016)
      (xy 1.830694 -1.882998)
      (xy 1.832505 -1.869428)
      (xy 1.825683 -1.847267)
      (xy 1.80854 -1.813522)
      (xy 1.779393 -1.7652)
      (xy 1.736555 -1.699308)
      (xy 1.679448 -1.614483)
      (xy 1.628766 -1.539823)
      (xy 1.583461 -1.47286)
      (xy 1.54615 -1.417484)
      (xy 1.519452 -1.37758)
      (xy 1.505985 -1.357038)
      (xy 1.505137 -1.355644)
      (xy 1.506781 -1.335962)
      (xy 1.519245 -1.297707)
      (xy 1.540048 -1.248111)
      (xy 1.547462 -1.232272)
      (xy 1.579814 -1.16171)
      (xy 1.614328 -1.081647)
      (xy 1.642365 -1.012371)
      (xy 1.662568 -0.960955)
      (xy 1.678615 -0.921881)
      (xy 1.687888 -0.901459)
      (xy 1.689041 -0.899886)
      (xy 1.706096 -0.897279)
      (xy 1.746298 -0.890137)
      (xy 1.804302 -0.879477)
      (xy 1.874763 -0.866315)
      (xy 1.952335 -0.851667)
      (xy 2.031672 -0.836551)
      (xy 2.107431 -0.821982)
      (xy 2.174264 -0.808978)
      (xy 2.226828 -0.798555)
      (xy 2.259776 -0.79173)
      (xy 2.267857 -0.789801)
      (xy 2.276205 -0.785038)
      (xy 2.282506 -0.774282)
      (xy 2.287045 -0.753902)
      (xy 2.290104 -0.720266)
      (xy 2.291967 -0.669745)
      (xy 2.292918 -0.598708)
      (xy 2.29324 -0.503524)
      (xy 2.293257 -0.464508)
      (xy 2.293257 -0.147201)
      (xy 2.217057 -0.132161)
      (xy 2.174663 -0.124005)
      (xy 2.1114 -0.112101)
      (xy 2.034962 -0.097884)
      (xy 1.953043 -0.08279)
      (xy 1.9304 -0.078645)
      (xy 1.854806 -0.063947)
      (xy 1.788953 -0.049495)
      (xy 1.738366 -0.036625)
      (xy 1.708574 -0.026678)
      (xy 1.703612 -0.023713)
      (xy 1.691426 -0.002717)
      (xy 1.673953 0.037967)
      (xy 1.654577 0.090322)
      (xy 1.650734 0.1016)
      (xy 1.625339 0.171523)
      (xy 1.593817 0.250418)
      (xy 1.562969 0.321266)
      (xy 1.562817 0.321595)
      (xy 1.511447 0.432733)
      (xy 1.680399 0.681253)
      (xy 1.849352 0.929772)
      (xy 1.632429 1.147058)
      (xy 1.566819 1.211726)
      (xy 1.506979 1.268733)
      (xy 1.456267 1.315033)
      (xy 1.418046 1.347584)
      (xy 1.395675 1.363343)
      (xy 1.392466 1.364343)
      (xy 1.373626 1.356469)
      (xy 1.33518 1.334578)
      (xy 1.28133 1.301267)
      (xy 1.216276 1.259131)
      (xy 1.14594 1.211943)
      (xy 1.074555 1.16381)
      (xy 1.010908 1.121928)
      (xy 0.959041 1.088871)
      (xy 0.922995 1.067218)
      (xy 0.906867 1.059543)
      (xy 0.887189 1.066037)
      (xy 0.849875 1.08315)
      (xy 0.802621 1.107326)
      (xy 0.797612 1.110013)
      (xy 0.733977 1.141927)
      (xy 0.690341 1.157579)
      (xy 0.663202 1.157745)
      (xy 0.649057 1.143204)
      (xy 0.648975 1.143)
      (xy 0.641905 1.125779)
      (xy 0.625042 1.084899)
      (xy 0.599695 1.023525)
      (xy 0.567171 0.944819)
      (xy 0.528778 0.851947)
      (xy 0.485822 0.748072)
      (xy 0.444222 0.647502)
      (xy 0.398504 0.536516)
      (xy 0.356526 0.433703)
      (xy 0.319548 0.342215)
      (xy 0.288827 0.265201)
      (xy 0.265622 0.205815)
      (xy 0.25119 0.167209)
      (xy 0.246743 0.1528)
      (xy 0.257896 0.136272)
      (xy 0.287069 0.10993)
      (xy 0.325971 0.080887)
      (xy 0.436757 -0.010961)
      (xy 0.523351 -0.116241)
      (xy 0.584716 -0.232734)
      (xy 0.619815 -0.358224)
      (xy 0.627608 -0.490493)
      (xy 0.621943 -0.551543)
      (xy 0.591078 -0.678205)
      (xy 0.53792 -0.790059)
      (xy 0.465767 -0.885999)
      (xy 0.377917 -0.964924)
      (xy 0.277665 -1.02573)
      (xy 0.16831 -1.067313)
      (xy 0.053147 -1.088572)
      (xy -0.064525 -1.088401)
      (xy -0.18141 -1.065699)
      (xy -0.294211 -1.019362)
      (xy -0.399631 -0.948287)
      (xy -0.443632 -0.908089)
      (xy -0.528021 -0.804871)
      (xy -0.586778 -0.692075)
      (xy -0.620296 -0.57299)
      (xy -0.628965 -0.450905)
      (xy -0.613177 -0.329107)
      (xy -0.573322 -0.210884)
      (xy -0.509793 -0.099525)
      (xy -0.422979 0.001684)
      (xy -0.325971 0.080887)
      (xy -0.285563 0.111162)
      (xy -0.257018 0.137219)
      (xy -0.246743 0.152825)
      (xy -0.252123 0.169843)
      (xy -0.267425 0.2105)
      (xy -0.291388 0.271642)
      (xy -0.322756 0.350119)
      (xy -0.360268 0.44278)
      (xy -0.402667 0.546472)
      (xy -0.444337 0.647526)
      (xy -0.49031 0.758607)
      (xy -0.532893 0.861541)
      (xy -0.570779 0.953165)
      (xy -0.60266 1.030316)
      (xy -0.627229 1.089831)
      (xy -0.64318 1.128544)
      (xy -0.64909 1.143)
      (xy -0.663052 1.157685)
      (xy -0.69006 1.157642)
      (xy -0.733587 1.142099)
      (xy -0.79711 1.110284)
      (xy -0.797612 1.110013)
      (xy -0.84544 1.085323)
      (xy -0.884103 1.067338)
      (xy -0.905905 1.059614)
      (xy -0.906867 1.059543)
      (xy -0.923279 1.067378)
      (xy -0.959513 1.089165)
      (xy -1.011526 1.122328)
      (xy -1.075275 1.164291)
      (xy -1.14594 1.211943)
      (xy -1.217884 1.260191)
      (xy -1.282726 1.302151)
      (xy -1.336265 1.335227)
      (xy -1.374303 1.356821)
      (xy -1.392467 1.364343)
      (xy -1.409192 1.354457)
      (xy -1.44282 1.326826)
      (xy -1.48999 1.284495)
      (xy -1.547342 1.230505)
      (xy -1.611516 1.167899)
      (xy -1.632503 1.146983)
      (xy -1.849501 0.929623)
      (xy -1.684332 0.68722)
      (xy -1.634136 0.612781)
      (xy -1.590081 0.545972)
      (xy -1.554638 0.490665)
      (xy -1.530281 0.450729)
      (xy -1.519478 0.430036)
      (xy -1.519162 0.428563)
      (xy -1.524857 0.409058)
      (xy -1.540174 0.369822)
      (xy -1.562463 0.31743)
      (xy -1.578107 0.282355)
      (xy -1.607359 0.215201)
      (xy -1.634906 0.147358)
      (xy -1.656263 0.090034)
      (xy -1.662065 0.072572)
      (xy -1.678548 0.025938)
      (xy -1.69466 -0.010095)
      (xy -1.70351 -0.023713)
      (xy -1.72304 -0.032048)
      (xy -1.765666 -0.043863)
      (xy -1.825855 -0.057819)
      (xy -1.898078 -0.072578)
      (xy -1.9304 -0.078645)
      (xy -2.012478 -0.093727)
      (xy -2.091205 -0.108331)
      (xy -2.158891 -0.12102)
      (xy -2.20784 -0.130358)
      (xy -2.217057 -0.132161)
      (xy -2.293257 -0.147201)
      (xy -2.293257 -0.464508)
      (xy -2.293086 -0.568846)
      (xy -2.292384 -0.647787)
      (xy -2.290866 -0.704962)
      (xy -2.288251 -0.744001)
      (xy -2.284254 -0.768535)
      (xy -2.278591 -0.782195)
      (xy -2.27098 -0.788611)
      (xy -2.267857 -0.789801)
      (xy -2.249022 -0.79402)
      (xy -2.207412 -0.802438)
      (xy -2.14837 -0.814039)
      (xy -2.077243 -0.827805)
      (xy -1.999375 -0.84272)
      (xy -1.920113 -0.857768)
      (xy -1.844802 -0.871931)
      (xy -1.778787 -0.884194)
      (xy -1.727413 -0.893539)
      (xy -1.696025 -0.89895)
      (xy -1.689041 -0.899886)
      (xy -1.682715 -0.912404)
      (xy -1.66871 -0.945754)
      (xy -1.649645 -0.993623)
      (xy -1.642366 -1.012371)
      (xy -1.613004 -1.084805)
      (xy -1.578429 -1.16483)
      (xy -1.547463 -1.232272)
      (xy -1.524677 -1.283841)
      (xy -1.509518 -1.326215)
      (xy -1.504458 -1.352166)
      (xy -1.505264 -1.355644)
      (xy -1.515959 -1.372064)
      (xy -1.54038 -1.408583)
      (xy -1.575905 -1.461313)
      (xy -1.619913 -1.526365)
      (xy -1.669783 -1.599849)
      (xy -1.679644 -1.614355)
      (xy -1.737508 -1.700296)
      (xy -1.780044 -1.765739)
      (xy -1.808946 -1.813696)
      (xy -1.82591 -1.84718)
      (xy -1.832633 -1.869205)
      (xy -1.83081 -1.882783)
      (xy -1.830764 -1.882869)
      (xy -1.816414 -1.900703)
      (xy -1.784677 -1.935183)
      (xy -1.73899 -1.982732)
      (xy -1.682796 -2.039778)
      (xy -1.619532 -2.102745)
      (xy -1.612398 -2.109773)
      (xy -1.53267 -2.18698)
      (xy -1.471143 -2.24367)
      (xy -1.426579 -2.28089)
      (xy -1.397743 -2.299685)
      (xy -1.385042 -2.302111)
      (xy -1.366506 -2.291529)
      (xy -1.328039 -2.267084)
      (xy -1.273614 -2.231388)
      (xy -1.207202 -2.187053)
      (xy -1.132775 -2.136689)
      (xy -1.115399 -2.12484)
      (xy -0.868703 -1.956374)
      (xy -0.757294 -2.006938)
      (xy -0.689543 -2.036405)
      (xy -0.616817 -2.066041)
      (xy -0.554297 -2.08967)
      (xy -0.551543 -2.090642)
      (xy -0.50264 -2.108543)
      (xy -0.464943 -2.12368)
      (xy -0.446575 -2.13279)
      (xy -0.446544 -2.132816)
      (xy -0.440715 -2.149283)
      (xy -0.430808 -2.189781)
      (xy -0.417805 -2.249758)
      (xy -0.402691 -2.32466)
      (xy -0.386448 -2.409936)
      (xy -0.385119 -2.417125)
      (xy -0.368825 -2.504986)
      (xy -0.353867 -2.58474)
      (xy -0.341209 -2.651319)
      (xy -0.331814 -2.699653)
      (xy -0.326646 -2.724675)
      (xy -0.326556 -2.725057)
      (xy -0.323411 -2.735701)
      (xy -0.317296 -2.743738)
      (xy -0.304547 -2.749533)
      (xy -0.2815 -2.753453)
      (xy -0.244491 -2.755865)
      (xy -0.189856 -2.757135)
      (xy -0.113933 -2.757629)
      (xy -0.013056 -2.757714)
      (xy 0 -2.757714)
      (xy 0.10391 -2.757652)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 07e872b3-1250-4cb7-95e5-8d96b44ae905))
    (fp_poly (pts (xy 1.779833 1.958663)
      (xy 1.782048 1.99685)
      (xy 1.783784 2.054886)
      (xy 1.784899 2.12818)
      (xy 1.785257 2.205055)
      (xy 1.785257 2.465196)
      (xy 1.739326 2.511127)
      (xy 1.707675 2.539429)
      (xy 1.67989 2.550893)
      (xy 1.641915 2.550168)
      (xy 1.62684 2.548321)
      (xy 1.579726 2.542948)
      (xy 1.540756 2.539869)
      (xy 1.531257 2.539585)
      (xy 1.499233 2.541445)
      (xy 1.453432 2.546114)
      (xy 1.435674 2.548321)
      (xy 1.392057 2.551735)
      (xy 1.362745 2.54432)
      (xy 1.33368 2.521427)
      (xy 1.323188 2.511127)
      (xy 1.277257 2.465196)
      (xy 1.277257 1.978602)
      (xy 1.314226 1.961758)
      (xy 1.346059 1.949282)
      (xy 1.364683 1.944914)
      (xy 1.369458 1.958718)
      (xy 1.373921 1.997286)
      (xy 1.377775 2.056356)
      (xy 1.380722 2.131663)
      (xy 1.382143 2.195286)
      (xy 1.386114 2.445657)
      (xy 1.420759 2.450556)
      (xy 1.452268 2.447131)
      (xy 1.467708 2.436041)
      (xy 1.472023 2.415308)
      (xy 1.475708 2.371145)
      (xy 1.478469 2.309146)
      (xy 1.480012 2.234909)
      (xy 1.480235 2.196706)
      (xy 1.480457 1.976783)
      (xy 1.526166 1.960849)
      (xy 1.558518 1.950015)
      (xy 1.576115 1.944962)
      (xy 1.576623 1.944914)
      (xy 1.578388 1.958648)
      (xy 1.580329 1.99673)
      (xy 1.582282 2.054482)
      (xy 1.584084 2.127227)
      (xy 1.585343 2.195286)
      (xy 1.589314 2.445657)
      (xy 1.6764 2.445657)
      (xy 1.680396 2.21724)
      (xy 1.684392 1.988822)
      (xy 1.726847 1.966868)
      (xy 1.758192 1.951793)
      (xy 1.776744 1.944951)
      (xy 1.777279 1.944914)
      (xy 1.779833 1.958663)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 11bc7550-4437-43b1-862e-9517e0140b34))
    (fp_poly (pts (xy -2.400256 1.919918)
      (xy -2.344799 1.947568)
      (xy -2.295852 1.99848)
      (xy -2.282371 2.017338)
      (xy -2.267686 2.042015)
      (xy -2.258158 2.068816)
      (xy -2.252707 2.104587)
      (xy -2.250253 2.156169)
      (xy -2.249714 2.224267)
      (xy -2.252148 2.317588)
      (xy -2.260606 2.387657)
      (xy -2.276826 2.439931)
      (xy -2.302546 2.479869)
      (xy -2.339503 2.512929)
      (xy -2.342218 2.514886)
      (xy -2.37864 2.534908)
      (xy -2.422498 2.544815)
      (xy -2.478276 2.547257)
      (xy -2.568952 2.547257)
      (xy -2.56899 2.635283)
      (xy -2.569834 2.684308)
      (xy -2.574976 2.713065)
      (xy -2.588413 2.730311)
      (xy -2.614142 2.744808)
      (xy -2.620321 2.747769)
      (xy -2.649236 2.761648)
      (xy -2.671624 2.770414)
      (xy -2.688271 2.771171)
      (xy -2.699964 2.761023)
      (xy -2.70749 2.737073)
      (xy -2.711634 2.696426)
      (xy -2.713185 2.636186)
      (xy -2.712929 2.553455)
      (xy -2.711651 2.445339)
      (xy -2.711252 2.413)
      (xy -2.709815 2.301524)
      (xy -2.708528 2.228603)
      (xy -2.569029 2.228603)
      (xy -2.568245 2.290499)
      (xy -2.56476 2.330997)
      (xy -2.556876 2.357708)
      (xy -2.542895 2.378244)
      (xy -2.533403 2.38826)
      (xy -2.494596 2.417567)
      (xy -2.460237 2.419952)
      (xy -2.424784 2.39575)
      (xy -2.423886 2.394857)
      (xy -2.409461 2.376153)
      (xy -2.400687 2.350732)
      (xy -2.396261 2.311584)
      (xy -2.394882 2.251697)
      (xy -2.394857 2.23843)
      (xy -2.398188 2.155901)
      (xy -2.409031 2.098691)
      (xy -2.42866 2.063766)
      (xy -2.45835 2.048094)
      (xy -2.475509 2.046514)
      (xy -2.516234 2.053926)
      (xy -2.544168 2.07833)
      (xy -2.560983 2.12298)
      (xy -2.56835 2.19113)
      (xy -2.569029 2.228603)
      (xy -2.708528 2.228603)
      (xy -2.708292 2.215245)
      (xy -2.706323 2.150333)
      (xy -2.70355 2.102958)
      (xy -2.699612 2.06929)
      (xy -2.694151 2.045498)
      (xy -2.686808 2.027753)
      (xy -2.677223 2.012224)
      (xy -2.673113 2.006381)
      (xy -2.618595 1.951185)
      (xy -2.549664 1.91989)
      (xy -2.469928 1.911165)
      (xy -2.400256 1.919918)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 19fbb572-a46b-46aa-af0a-fc01a8bf046d))
    (fp_poly (pts (xy 3.153595 1.966966)
      (xy 3.211021 2.004497)
      (xy 3.238719 2.038096)
      (xy 3.260662 2.099064)
      (xy 3.262405 2.147308)
      (xy 3.258457 2.211816)
      (xy 3.109686 2.276934)
      (xy 3.037349 2.310202)
      (xy 2.990084 2.336964)
      (xy 2.965507 2.360144)
      (xy 2.961237 2.382667)
      (xy 2.974889 2.407455)
      (xy 2.989943 2.423886)
      (xy 3.033746 2.450235)
      (xy 3.081389 2.452081)
      (xy 3.125145 2.431546)
      (xy 3.157289 2.390752)
      (xy 3.163038 2.376347)
      (xy 3.190576 2.331356)
      (xy 3.222258 2.312182)
      (xy 3.265714 2.295779)
      (xy 3.265714 2.357966)
      (xy 3.261872 2.400283)
      (xy 3.246823 2.435969)
      (xy 3.21528 2.476943)
      (xy 3.210592 2.482267)
      (xy 3.175506 2.51872)
      (xy 3.145347 2.538283)
      (xy 3.107615 2.547283)
      (xy 3.076335 2.55023)
      (xy 3.020385 2.550965)
      (xy 2.980555 2.54166)
      (xy 2.955708 2.527846)
      (xy 2.916656 2.497467)
      (xy 2.889625 2.464613)
      (xy 2.872517 2.423294)
      (xy 2.863238 2.367521)
      (xy 2.859693 2.291305)
      (xy 2.85941 2.252622)
      (xy 2.860372 2.206247)
      (xy 2.948007 2.206247)
      (xy 2.949023 2.231126)
      (xy 2.951556 2.2352)
      (xy 2.968274 2.229665)
      (xy 3.004249 2.215017)
      (xy 3.052331 2.19419)
      (xy 3.062386 2.189714)
      (xy 3.123152 2.158814)
      (xy 3.156632 2.131657)
      (xy 3.16399 2.10622)
      (xy 3.146391 2.080481)
      (xy 3.131856 2.069109)
      (xy 3.07941 2.046364)
      (xy 3.030322 2.050122)
      (xy 2.989227 2.077884)
      (xy 2.960758 2.127152)
      (xy 2.951631 2.166257)
      (xy 2.948007 2.206247)
      (xy 2.860372 2.206247)
      (xy 2.861285 2.162249)
      (xy 2.868196 2.095384)
      (xy 2.881884 2.046695)
      (xy 2.904096 2.010849)
      (xy 2.936574 1.982513)
      (xy 2.950733 1.973355)
      (xy 3.015053 1.949507)
      (xy 3.085473 1.948006)
      (xy 3.153595 1.966966)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 1da64e94-ab0f-4990-8bc1-1d6393d05353))
    (fp_poly (pts (xy 0.039744 1.950968)
      (xy 0.096616 1.972087)
      (xy 0.097267 1.972493)
      (xy 0.13244 1.99838)
      (xy 0.158407 2.028633)
      (xy 0.17667 2.068058)
      (xy 0.188732 2.121462)
      (xy 0.196096 2.193651)
      (xy 0.200264 2.289432)
      (xy 0.200629 2.303078)
      (xy 0.205876 2.508842)
      (xy 0.161716 2.531678)
      (xy 0.129763 2.54711)
      (xy 0.11047 2.554423)
      (xy 0.109578 2.554514)
      (xy 0.106239 2.541022)
      (xy 0.103587 2.504626)
      (xy 0.101956 2.451452)
      (xy 0.1016 2.408393)
      (xy 0.101592 2.338641)
      (xy 0.098403 2.294837)
      (xy 0.087288 2.273944)
      (xy 0.063501 2.272925)
      (xy 0.022296 2.288741)
      (xy -0.039914 2.317815)
      (xy -0.085659 2.341963)
      (xy -0.109187 2.362913)
      (xy -0.116104 2.385747)
      (xy -0.116114 2.386877)
      (xy -0.104701 2.426212)
      (xy -0.070908 2.447462)
      (xy -0.019191 2.450539)
      (xy 0.018061 2.450006)
      (xy 0.037703 2.460735)
      (xy 0.049952 2.486505)
      (xy 0.057002 2.519337)
      (xy 0.046842 2.537966)
      (xy 0.043017 2.540632)
      (xy 0.007001 2.55134)
      (xy -0.043434 2.552856)
      (xy -0.095374 2.545759)
      (xy -0.132178 2.532788)
      (xy -0.183062 2.489585)
      (xy -0.211986 2.429446)
      (xy -0.217714 2.382462)
      (xy -0.213343 2.340082)
      (xy -0.197525 2.305488)
      (xy -0.166203 2.274763)
      (xy -0.115322 2.24399)
      (xy -0.040824 2.209252)
      (xy -0.036286 2.207288)
      (xy 0.030821 2.176287)
      (xy 0.072232 2.150862)
      (xy 0.089981 2.128014)
      (xy 0.086107 2.104745)
      (xy 0.062643 2.078056)
      (xy 0.055627 2.071914)
      (xy 0.00863 2.0481)
      (xy -0.040067 2.049103)
      (xy -0.082478 2.072451)
      (xy -0.110616 2.115675)
      (xy -0.113231 2.12416)
      (xy -0.138692 2.165308)
      (xy -0.170999 2.185128)
      (xy -0.217714 2.20477)
      (xy -0.217714 2.15395)
      (xy -0.203504 2.080082)
      (xy -0.161325 2.012327)
      (xy -0.139376 1.989661)
      (xy -0.089483 1.960569)
      (xy -0.026033 1.9474)
      (xy 0.039744 1.950968)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 5bf42bc8-ab98-49d6-9855-47a16749119a))
    (fp_poly (pts (xy 2.144876 1.956335)
      (xy 2.186667 1.975344)
      (xy 2.219469 1.998378)
      (xy 2.243503 2.024133)
      (xy 2.260097 2.057358)
      (xy 2.270577 2.1028)
      (xy 2.276271 2.165207)
      (xy 2.278507 2.249327)
      (xy 2.278743 2.304721)
      (xy 2.278743 2.520826)
      (xy 2.241774 2.53767)
      (xy 2.212656 2.549981)
      (xy 2.198231 2.554514)
      (xy 2.195472 2.541025)
      (xy 2.193282 2.504653)
      (xy 2.191942 2.451542)
      (xy 2.191657 2.409372)
      (xy 2.190434 2.348447)
      (xy 2.187136 2.300115)
      (xy 2.182321 2.270518)
      (xy 2.178496 2.264229)
      (xy 2.152783 2.270652)
      (xy 2.112418 2.287125)
      (xy 2.065679 2.309458)
      (xy 2.020845 2.333457)
      (xy 1.986193 2.35493)
      (xy 1.970002 2.369685)
      (xy 1.969938 2.369845)
      (xy 1.97133 2.397152)
      (xy 1.983818 2.423219)
      (xy 2.005743 2.444392)
      (xy 2.037743 2.451474)
      (xy 2.065092 2.450649)
      (xy 2.103826 2.450042)
      (xy 2.124158 2.459116)
      (xy 2.136369 2.483092)
      (xy 2.137909 2.487613)
      (xy 2.143203 2.521806)
      (xy 2.129047 2.542568)
      (xy 2.092148 2.552462)
      (xy 2.052289 2.554292)
      (xy 1.980562 2.540727)
      (xy 1.943432 2.521355)
      (xy 1.897576 2.475845)
      (xy 1.873256 2.419983)
      (xy 1.871073 2.360957)
      (xy 1.891629 2.305953)
      (xy 1.922549 2.271486)
      (xy 1.95342 2.252189)
      (xy 2.001942 2.227759)
      (xy 2.058485 2.202985)
      (xy 2.06791 2.199199)
      (xy 2.130019 2.171791)
      (xy 2.165822 2.147634)
      (xy 2.177337 2.123619)
      (xy 2.16658 2.096635)
      (xy 2.148114 2.075543)
      (xy 2.104469 2.049572)
      (xy 2.056446 2.047624)
      (xy 2.012406 2.067637)
      (xy 1.980709 2.107551)
      (xy 1.976549 2.117848)
      (xy 1.952327 2.155724)
      (xy 1.916965 2.183842)
      (xy 1.872343 2.206917)
      (xy 1.872343 2.141485)
      (xy 1.874969 2.101506)
      (xy 1.88623 2.069997)
      (xy 1.911199 2.036378)
      (xy 1.935169 2.010484)
      (xy 1.972441 1.973817)
      (xy 2.001401 1.954121)
      (xy 2.032505 1.94622)
      (xy 2.067713 1.944914)
      (xy 2.144876 1.956335)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 6ecae48a-787e-4342-8888-58893e2ab8fe))
    (fp_poly (pts (xy -0.624114 1.851289)
      (xy -0.619861 1.910613)
      (xy -0.614975 1.945572)
      (xy -0.608205 1.96082)
      (xy -0.598298 1.961015)
      (xy -0.595086 1.959195)
      (xy -0.552356 1.946015)
      (xy -0.496773 1.946785)
      (xy -0.440263 1.960333)
      (xy -0.404918 1.977861)
      (xy -0.368679 2.005861)
      (xy -0.342187 2.037549)
      (xy -0.324001 2.077813)
      (xy -0.312678 2.131543)
      (xy -0.306778 2.203626)
      (xy -0.304857 2.298951)
      (xy -0.304823 2.317237)
      (xy -0.3048 2.522646)
      (xy -0.350509 2.53858)
      (xy -0.382973 2.54942)
      (xy -0.400785 2.554468)
      (xy -0.401309 2.554514)
      (xy -0.403063 2.540828)
      (xy -0.404556 2.503076)
      (xy -0.405674 2.446224)
      (xy -0.406303 2.375234)
      (xy -0.4064 2.332073)
      (xy -0.406602 2.246973)
      (xy -0.407642 2.185981)
      (xy -0.410169 2.144177)
      (xy -0.414836 2.116642)
      (xy -0.422293 2.098456)
      (xy -0.433189 2.084698)
      (xy -0.439993 2.078073)
      (xy -0.486728 2.051375)
      (xy -0.537728 2.049375)
      (xy -0.583999 2.071955)
      (xy -0.592556 2.080107)
      (xy -0.605107 2.095436)
      (xy -0.613812 2.113618)
      (xy -0.619369 2.139909)
      (xy -0.622474 2.179562)
      (xy -0.623824 2.237832)
      (xy -0.624114 2.318173)
      (xy -0.624114 2.522646)
      (xy -0.669823 2.53858)
      (xy -0.702287 2.54942)
      (xy -0.720099 2.554468)
      (xy -0.720623 2.554514)
      (xy -0.721963 2.540623)
      (xy -0.723172 2.501439)
      (xy -0.724199 2.4407)
      (xy -0.724998 2.362141)
      (xy -0.725519 2.269498)
      (xy -0.725714 2.166509)
      (xy -0.725714 1.769342)
      (xy -0.678543 1.749444)
      (xy -0.631371 1.729547)
      (xy -0.624114 1.851289)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 71df5b4a-edf5-4341-90b1-b2dd03cbbdd4))
    (fp_poly (pts (xy -1.831697 1.931239)
      (xy -1.774473 1.969735)
      (xy -1.730251 2.025335)
      (xy -1.703833 2.096086)
      (xy -1.69849 2.148162)
      (xy -1.699097 2.169893)
      (xy -1.704178 2.186531)
      (xy -1.718145 2.201437)
      (xy -1.745411 2.217973)
      (xy -1.790388 2.239498)
      (xy -1.857489 2.269374)
      (xy -1.857829 2.269524)
      (xy -1.919593 2.297813)
      (xy -1.970241 2.322933)
      (xy -2.004596 2.342179)
      (xy -2.017482 2.352848)
      (xy -2.017486 2.352934)
      (xy -2.006128 2.376166)
      (xy -1.979569 2.401774)
      (xy -1.949077 2.420221)
      (xy -1.93363 2.423886)
      (xy -1.891485 2.411212)
      (xy -1.855192 2.379471)
      (xy -1.837483 2.344572)
      (xy -1.820448 2.318845)
      (xy -1.787078 2.289546)
      (xy -1.747851 2.264235)
      (xy -1.713244 2.250471)
      (xy -1.706007 2.249714)
      (xy -1.697861 2.26216)
      (xy -1.69737 2.293972)
      (xy -1.703357 2.336866)
      (xy -1.714643 2.382558)
      (xy -1.73005 2.422761)
      (xy -1.730829 2.424322)
      (xy -1.777196 2.489062)
      (xy -1.837289 2.533097)
      (xy -1.905535 2.554711)
      (xy -1.976362 2.552185)
      (xy -2.044196 2.523804)
      (xy -2.047212 2.521808)
      (xy -2.100573 2.473448)
      (xy -2.13566 2.410352)
      (xy -2.155078 2.327387)
      (xy -2.157684 2.304078)
      (xy -2.162299 2.194055)
      (xy -2.156767 2.142748)
      (xy -2.017486 2.142748)
      (xy -2.015676 2.174753)
      (xy -2.005778 2.184093)
      (xy -1.981102 2.177105)
      (xy -1.942205 2.160587)
      (xy -1.898725 2.139881)
      (xy -1.897644 2.139333)
      (xy -1.860791 2.119949)
      (xy -1.846 2.107013)
      (xy -1.849647 2.093451)
      (xy -1.865005 2.075632)
      (xy -1.904077 2.049845)
      (xy -1.946154 2.04795)
      (xy -1.983897 2.066717)
      (xy -2.009966 2.102915)
      (xy -2.017486 2.142748)
      (xy -2.156767 2.142748)
      (xy -2.152806 2.106027)
      (xy -2.12845 2.036212)
      (xy -2.094544 1.987302)
      (xy -2.033347 1.937878)
      (xy -1.965937 1.913359)
      (xy -1.89712 1.911797)
      (xy -1.831697 1.931239)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp 8e307683-740a-4ced-90b9-7c95a115e98e))
    (fp_poly (pts (xy -2.958885 1.921962)
      (xy -2.890855 1.957733)
      (xy -2.840649 2.015301)
      (xy -2.822815 2.052312)
      (xy -2.808937 2.107882)
      (xy -2.801833 2.178096)
      (xy -2.80116 2.254727)
      (xy -2.806573 2.329552)
      (xy -2.81773 2.394342)
      (xy -2.834286 2.440873)
      (xy -2.839374 2.448887)
      (xy -2.899645 2.508707)
      (xy -2.971231 2.544535)
      (xy -3.048908 2.55502)
      (xy -3.127452 2.53881)
      (xy -3.149311 2.529092)
      (xy -3.191878 2.499143)
      (xy -3.229237 2.459433)
      (xy -3.232768 2.454397)
      (xy -3.247119 2.430124)
      (xy -3.256606 2.404178)
      (xy -3.26221 2.370022)
      (xy -3.264914 2.321119)
      (xy -3.265701 2.250935)
      (xy -3.265714 2.2352)
      (xy -3.265678 2.230192)
      (xy -3.120571 2.230192)
      (xy -3.119727 2.29643)
      (xy -3.116404 2.340386)
      (xy -3.109417 2.368779)
      (xy -3.097584 2.388325)
      (xy -3.091543 2.394857)
      (xy -3.056814 2.41968)
      (xy -3.023097 2.418548)
      (xy -2.989005 2.397016)
      (xy -2.968671 2.374029)
      (xy -2.956629 2.340478)
      (xy -2.949866 2.287569)
      (xy -2.949402 2.281399)
      (xy -2.948248 2.185513)
      (xy -2.960312 2.114299)
      (xy -2.98543 2.068194)
      (xy -3.02344 2.047635)
      (xy -3.037008 2.046514)
      (xy -3.072636 2.052152)
      (xy -3.097006 2.071686)
      (xy -3.111907 2.109042)
      (xy -3.119125 2.16815)
      (xy -3.120571 2.230192)
      (xy -3.265678 2.230192)
      (xy -3.265174 2.160413)
      (xy -3.262904 2.108159)
      (xy -3.257932 2.071949)
      (xy -3.249287 2.045299)
      (xy -3.235995 2.021722)
      (xy -3.233057 2.017338)
      (xy -3.183687 1.958249)
      (xy -3.129891 1.923947)
      (xy -3.064398 1.910331)
      (xy -3.042158 1.909665)
      (xy -2.958885 1.921962)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp d066992b-8fb2-4194-8b65-7e62cb972ff2))
    (fp_poly (pts (xy 2.6526 1.958752)
      (xy 2.669948 1.966334)
      (xy 2.711356 1.999128)
      (xy 2.746765 2.046547)
      (xy 2.768664 2.097151)
      (xy 2.772229 2.122098)
      (xy 2.760279 2.156927)
      (xy 2.734067 2.175357)
      (xy 2.705964 2.186516)
      (xy 2.693095 2.188572)
      (xy 2.686829 2.173649)
      (xy 2.674456 2.141175)
      (xy 2.669028 2.126502)
      (xy 2.63859 2.075744)
      (xy 2.59452 2.050427)
      (xy 2.53801 2.051206)
      (xy 2.533825 2.052203)
      (xy 2.503655 2.066507)
      (xy 2.481476 2.094393)
      (xy 2.466327 2.139287)
      (xy 2.45725 2.204615)
      (xy 2.453286 2.293804)
      (xy 2.452914 2.341261)
      (xy 2.45273 2.416071)
      (xy 2.451522 2.467069)
      (xy 2.448309 2.499471)
      (xy 2.442109 2.518495)
      (xy 2.43194 2.529356)
      (xy 2.416819 2.537272)
      (xy 2.415946 2.53767)
      (xy 2.386828 2.549981)
      (xy 2.372403 2.554514)
      (xy 2.370186 2.540809)
      (xy 2.368289 2.502925)
      (xy 2.366847 2.445715)
      (xy 2.365998 2.374027)
      (xy 2.365829 2.321565)
      (xy 2.366692 2.220047)
      (xy 2.37007 2.143032)
      (xy 2.377142 2.086023)
      (xy 2.389088 2.044526)
      (xy 2.40709 2.014043)
      (xy 2.432327 1.99008)
      (xy 2.457247 1.973355)
      (xy 2.517171 1.951097)
      (xy 2.586911 1.946076)
      (xy 2.6526 1.958752)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp efce131a-d994-4320-bc56-1c3c15380a7a))
    (fp_poly (pts (xy 0.529926 1.949755)
      (xy 0.595858 1.974084)
      (xy 0.649273 2.017117)
      (xy 0.670164 2.047409)
      (xy 0.692939 2.102994)
      (xy 0.692466 2.143186)
      (xy 0.668562 2.170217)
      (xy 0.659717 2.174813)
      (xy 0.62153 2.189144)
      (xy 0.602028 2.185472)
      (xy 0.595422 2.161407)
      (xy 0.595086 2.148114)
      (xy 0.582992 2.09921)
      (xy 0.551471 2.064999)
      (xy 0.507659 2.048476)
      (xy 0.458695 2.052634)
      (xy 0.418894 2.074227)
      (xy 0.40545 2.086544)
      (xy 0.395921 2.101487)
      (xy 0.389485 2.124075)
      (xy 0.385317 2.159328)
      (xy 0.382597 2.212266)
      (xy 0.380502 2.287907)
      (xy 0.37996 2.311857)
      (xy 0.377981 2.39379)
      (xy 0.375731 2.451455)
      (xy 0.372357 2.489608)
      (xy 0.367006 2.513004)
      (xy 0.358824 2.526398)
      (xy 0.346959 2.534545)
      (xy 0.339362 2.538144)
      (xy 0.307102 2.550452)
      (xy 0.288111 2.554514)
      (xy 0.281836 2.540948)
      (xy 0.278006 2.499934)
      (xy 0.2766 2.430999)
      (xy 0.277598 2.333669)
      (xy 0.277908 2.318657)
      (xy 0.280101 2.229859)
      (xy 0.282693 2.165019)
      (xy 0.286382 2.119067)
      (xy 0.291864 2.086935)
      (xy 0.299835 2.063553)
      (xy 0.310993 2.043852)
      (xy 0.31683 2.03541)
      (xy 0.350296 1.998057)
      (xy 0.387727 1.969003)
      (xy 0.392309 1.966467)
      (xy 0.459426 1.946443)
      (xy 0.529926 1.949755)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp f637d83a-45a5-4fb8-ba45-e351fc41980e))
    (fp_poly (pts (xy -1.283907 1.92778)
      (xy -1.237328 1.954723)
      (xy -1.204943 1.981466)
      (xy -1.181258 2.009484)
      (xy -1.164941 2.043748)
      (xy -1.154661 2.089227)
      (xy -1.149086 2.150892)
      (xy -1.146884 2.233711)
      (xy -1.146629 2.293246)
      (xy -1.146629 2.512391)
      (xy -1.208314 2.540044)
      (xy -1.27 2.567697)
      (xy -1.277257 2.32767)
      (xy -1.280256 2.238028)
      (xy -1.283402 2.172962)
      (xy -1.287299 2.128026)
      (xy -1.292553 2.09877)
      (xy -1.299769 2.080748)
      (xy -1.30955 2.069511)
      (xy -1.312688 2.067079)
      (xy -1.360239 2.048083)
      (xy -1.408303 2.0556)
      (xy -1.436914 2.075543)
      (xy -1.448553 2.089675)
      (xy -1.456609 2.10822)
      (xy -1.461729 2.136334)
      (xy -1.464559 2.179173)
      (xy -1.465744 2.241895)
      (xy -1.465943 2.307261)
      (xy -1.465982 2.389268)
      (xy -1.467386 2.447316)
      (xy -1.472086 2.486465)
      (xy -1.482013 2.51178)
      (xy -1.499097 2.528323)
      (xy -1.525268 2.541156)
      (xy -1.560225 2.554491)
      (xy -1.598404 2.569007)
      (xy -1.593859 2.311389)
      (xy -1.592029 2.218519)
      (xy -1.589888 2.149889)
      (xy -1.586819 2.100711)
      (xy -1.582206 2.066198)
      (xy -1.575432 2.041562)
      (xy -1.565881 2.022016)
      (xy -1.554366 2.00477)
      (xy -1.49881 1.94968)
      (xy -1.43102 1.917822)
      (xy -1.357287 1.910191)
      (xy -1.283907 1.92778)) (layer "F.SilkS") (width 0.01) (fill solid) (tstamp fe007b34-c074-4051-a92a-9e6a6b70d706))
  )

  (footprint "BoardIO:L_Bourns-SRU1028_10.0x10.0mm" (layer "B.Cu")
    (tedit 60B7A4CB) (tstamp 00000000-0000-0000-0000-00005f8edfbc)
    (at 91.313 -85.543 90)
    (descr "Bourns SRU1028 series SMD inductor, https://www.bourns.com/docs/Product-Datasheets/SRU1028.pdf")
    (tags "Bourns SRU1028 SMD inductor")
    (property "P/N" "Bourns SRU1028-820Y")
    (property "Rating" "0.9A")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Primitive" "L")
    (path "/00000000-0000-0000-0000-00005fddc73f")
    (attr smd)
    (fp_text reference "FB1" (at -4.009 4.953 270) (layer "B.SilkS")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 76829885-8a3d-4778-94c8-4053dbd5c474)
    )
    (fp_text value "82u" (at 0 -6.2 270) (layer "B.Fab")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 63d0ca66-ae81-4913-bad3-f2af9ace365f)
    )
    (fp_text user "${REFERENCE}" (at 0 0 270) (layer "B.Fab")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 0316646d-e254-467d-bde3-96c1bad66fee)
    )
    (fp_line (start 1.8 -5.2) (end -1.8 -5.2) (layer "B.SilkS") (width 0.12) (tstamp 22d4f19a-4823-464c-b677-d8d6e4670892))
    (fp_line (start 1.8 -5.2) (end 5 -2) (layer "B.SilkS") (width 0.12) (tstamp 5a81c4ac-c541-493f-8c64-ccf406e9ccb8))
    (fp_line (start -1.8 5.2) (end -5 2) (layer "B.SilkS") (width 0.12) (tstamp 81ff3516-2938-49dd-8e8d-6a54856d07f1))
    (fp_line (start -1.8 5.2) (end 1.8 5.2) (layer "B.SilkS") (width 0.12) (tstamp aaaeb62d-3977-4e1c-84cb-0144189c7a28))
    (fp_line (start 1.8 5.2) (end 5 2) (layer "B.SilkS") (width 0.12) (tstamp d2c6149a-d46b-47cf-ba02-c5e2853a73ff))
    (fp_line (start -1.8 -5.2) (end -5 -2) (layer "B.SilkS") (width 0.12) (tstamp da7f803e-1389-4782-af3c-6f193d8f4453))
    (fp_line (start -5.65 5.25) (end 5.65 5.25) (layer "B.CrtYd") (width 0.05) (tstamp 4820babf-5b3d-4315-9b22-e4e536a4c769))
    (fp_line (start 5.65 5.25) (end 5.65 -5.25) (layer "B.CrtYd") (width 0.05) (tstamp 4b137e24-21e1-4cad-91a8-6f3f792bc09b))
    (fp_line (start -5.65 -5.25) (end -5.65 5.25) (layer "B.CrtYd") (width 0.05) (tstamp 80234f43-d50b-433a-8d28-ad6680a78841))
    (fp_line (start 5.65 -5.25) (end -5.65 -5.25) (layer "B.CrtYd") (width 0.05) (tstamp d2c4d823-3fba-4f59-81d4-50307f5e0fbd))
    (fp_line (start -5 -1.7) (end -5 1.7) (layer "B.Fab") (width 0.1) (tstamp 14eabf19-5ff6-425e-9afc-547d8b71d56f))
    (fp_line (start 1.7 5) (end 5 1.7) (layer "B.Fab") (width 0.1) (tstamp 1a0a129e-c943-4b73-af80-ce15608ed864))
    (fp_line (start -1.7 5) (end 1.7 5) (layer "B.Fab") (width 0.1) (tstamp 53ca91ea-c7c9-4b79-8db8-a8e65e1ee9fe))
    (fp_line (start -1.7 -5) (end -5 -1.7) (layer "B.Fab") (width 0.1) (tstamp 97e2feb9-d22f-4bf0-a6b2-da4ee554782d))
    (fp_line (start 1.7 -5) (end -1.7 -5) (layer "B.Fab") (width 0.1) (tstamp c863d3e1-a3d3-4f5b-8af1-a37a31145ee6))
    (fp_line (start -5 1.7) (end -1.7 5) (layer "B.Fab") (width 0.1) (tstamp c889cd75-881b-4765-9bc9-49861a810eb3))
    (fp_line (start 5 -1.7) (end 1.7 -5) (layer "B.Fab") (width 0.1) (tstamp f6b35aa8-6f13-493a-a234-4c7792fb1d2b))
    (fp_line (start 5 1.7) (end 5 -1.7) (layer "B.Fab") (width 0.1) (tstamp fe5bc4ef-96ff-4c5d-8bf9-7d6b4a20b60b))
    (fp_circle (center 0 0) (end 3.7 0) (layer "B.Fab") (width 0.1) (fill none) (tstamp 62a47ad4-861e-4be4-a12f-a60e7b742e02))
    (pad "1" smd rect locked (at -4.5 0 90) (size 1.8 3.6) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 15 "/12Vs") (tstamp 107fcea5-92c4-4432-9e79-86e003c6d3f2))
    (pad "2" smd rect locked (at 4.5 0 90) (size 1.8 3.6) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 2 "+12V") (tstamp 508d2bba-6dda-41fb-a2e5-72de4a204eab))
    (model "${KISYS3DMOD}/Inductor_SMD.3dshapes/L_Bourns-SRU1028_10.0x10.0mm.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
    (model "${KIPRJMOD}/BoardIO.pretty/SRU1028.wrl"
      (offset (xyz 0 0 0.15))
      (scale (xyz 1 1 1))
      (rotate (xyz -90 0 0))
    )
  )

  (footprint "Package_TO_SOT_SMD:SOT-23" (layer "B.Cu")
    (tedit 5FA16958) (tstamp 00000000-0000-0000-0000-00005f9185e6)
    (at 98.536 -80.198)
    (descr "SOT, 3 Pin (https://www.jedec.org/system/files/docs/to-236h.pdf variant AB), generated with kicad-footprint-generator ipc_gullwing_generator.py")
    (tags "SOT TO_SOT_SMD")
    (property "P/N" "Infineon IRLML5203TRPBF")
    (property "Rating" "30V")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (property "Spice_Lib_File" "models/irlml5203.lib")
    (property "Spice_Model" "irlml5203")
    (property "Spice_Netlist_Enabled" "Y")
    (property "Spice_Node_Sequence" "3 1 2")
    (property "Spice_Primitive" "X")
    (path "/00000000-0000-0000-0000-00005f91dc52")
    (attr smd)
    (fp_text reference "Q1" (at 1.794 1.331) (layer "B.SilkS")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp ba923b9d-3960-4826-b36f-635a800a306b)
    )
    (fp_text value "IRLML5203" (at 0 -2.5) (layer "B.Fab")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 43a690bf-bffb-45a2-821f-6769be466411)
    )
    (fp_text user "${REFERENCE}" (at 0 0 -90) (layer "B.Fab")
      (effects (font (size 0.5 0.5) (thickness 0.075)) (justify mirror))
      (tstamp 3b64927a-75fc-40d2-b0f4-c175e7a04f49)
    )
    (fp_line (start 0 1.56) (end 0.65 1.56) (layer "B.SilkS") (width 0.12) (tstamp 1a921951-4be6-4e8c-a92e-f3000dc68d91))
    (fp_line (start 0 1.56) (end -1.675 1.56) (layer "B.SilkS") (width 0.12) (tstamp 5f343b01-2f78-40d7-923b-093ef43f93b0))
    (fp_line (start 0 -1.56) (end 0.65 -1.56) (layer "B.SilkS") (width 0.12) (tstamp 86f82877-6bfa-4361-8710-297e55a1306e))
    (fp_line (start 0 -1.56) (end -0.65 -1.56) (layer "B.SilkS") (width 0.12) (tstamp ba9e54d2-32f9-4d1f-847b-e3cdbfceec92))
    (fp_line (start 1.92 1.7) (end -1.92 1.7) (layer "B.CrtYd") (width 0.05) (tstamp 4030eb4c-2546-459a-9118-5af7c061cd40))
    (fp_line (start -1.92 -1.7) (end 1.92 -1.7) (layer "B.CrtYd") (width 0.05) (tstamp b7b0b401-aaa4-4bd9-af93-c423ebae3471))
    (fp_line (start 1.92 -1.7) (end 1.92 1.7) (layer "B.CrtYd") (width 0.05) (tstamp b8942ee4-5d65-493b-aaf3-981b0f5ff4b5))
    (fp_line (start -1.92 1.7) (end -1.92 -1.7) (layer "B.CrtYd") (width 0.05) (tstamp e0b3f7ad-663b-4464-88b7-93d5097136ee))
    (fp_line (start -0.65 1.125) (end -0.325 1.45) (layer "B.Fab") (width 0.1) (tstamp 06cd5171-3694-44eb-8e3c-e619272de00d))
    (fp_line (start 0.65 1.45) (end 0.65 -1.45) (layer "B.Fab") (width 0.1) (tstamp 3b9800d3-b4df-429f-a897-1ba6bbcdf5a0))
    (fp_line (start -0.325 1.45) (end 0.65 1.45) (layer "B.Fab") (width 0.1) (tstamp 8127430b-e3f3-4373-99c8-03a292bc9be3))
    (fp_line (start -0.65 -1.45) (end -0.65 1.125) (layer "B.Fab") (width 0.1) (tstamp 92034318-e952-4d66-bb5d-397e8d0cce6c))
    (fp_line (start 0.65 -1.45) (end -0.65 -1.45) (layer "B.Fab") (width 0.1) (tstamp d331c65c-4305-41c6-b28f-2b79eff66f55))
    (pad "1" smd roundrect locked (at -0.9375 0.95) (size 1.475 0.6) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 16 "/12Vg") (pinfunction "G") (tstamp 1af57218-4c58-4444-9175-4ea1fbf07502))
    (pad "2" smd roundrect locked (at -0.9375 -0.95) (size 1.475 0.6) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 15 "/12Vs") (pinfunction "S") (tstamp 3fc3beb0-8f0b-43b7-978e-87dbe0f08f1a))
    (pad "3" smd roundrect locked (at 0.9375 0) (size 1.475 0.6) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 14 "PEG+12Vo") (pinfunction "D") (tstamp e520b9b4-d37a-4751-9ad2-bd1d0b1bb2a1))
    (model "${KICAD6_3DMODEL_DIR}/Package_TO_SOT_SMD.3dshapes/SOT-23.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "B.Cu")
    (tedit 5F68FEEE) (tstamp 00000000-0000-0000-0000-00005f918777)
    (at 98.298 -77.597)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "P/N" "Yageo RT0603FRE07100KL")
    (property "Rating" "0.1W")
    (property "Sheetfile" "BoardIO.kicad_sch")
    (property "Sheetname" "")
    (path "/00000000-0000-0000-0000-00005f9734a7")
    (attr smd)
    (fp_text reference "R73" (at -1.143 1.397) (layer "B.SilkS")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp d4ecdc4e-ed2d-44b4-b68d-04b01747d4c6)
    )
    (fp_text value "100k" (at 0 -1.43) (layer "B.Fab")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 46928fa2-007e-4721-863d-4c1fe18aa797)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "B.Fab")
      (effects (font (size 0.4 0.4) (thickness 0.06)) (justify mirror))
      (tstamp 15f7726d-369c-4392-bfec-c54119ecf892)
    )
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225) (layer "B.SilkS") (width 0.12) (tstamp 52cf2370-dac7-43c1-af25-868f7d66ac03))
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225) (layer "B.SilkS") (width 0.12) (tstamp d59e97c5-4e0a-4b60-baf1-5ade2b488233))
    (fp_line (start -1.48 0.73) (end 1.48 0.73) (layer "B.CrtYd") (width 0.05) (tstamp 00558973-a5b6-4b9f-a930-c7b5e635765f))
    (fp_line (start 1.48 -0.73) (end -1.48 -0.73) (layer "B.CrtYd") (width 0.05) (tstamp 2dce1300-04d0-45f2-8965-21dc1c237938))
    (fp_line (start -1.48 -0.73) (end -1.48 0.73) (layer "B.CrtYd") (width 0.05) (tstamp 329eb0d7-0ec1-4149-8bcb-57a10340b573))
    (fp_line (start 1.48 0.73) (end 1.48 -0.73) (layer "B.CrtYd") (width 0.05) (tstamp 68dc4e5f-6660-44a3-8725-e1684b2e3ace))
    (fp_line (start 0.8 -0.4125) (end -0.8 -0.4125) (layer "B.Fab") (width 0.1) (tstamp 33e3b485-905e-4811-95ac-d89c7a5db252))
    (fp_line (start -0.8 -0.4125) (end -0.8 0.4125) (layer "B.Fab") (width 0.1) (tstamp 4dca181d-6df0-4c19-b0c8-228b386b2b18))
    (fp_line (start 0.8 0.4125) (end 0.8 -0.4125) (layer "B.Fab") (width 0.1) (tstamp 70040bab-4087-4b3f-95de-e6f87e3f68da))
    (fp_line (start -0.8 0.4125) (end 0.8 0.4125) (layer "B.Fab") (width 0.1) (tstamp e3551698-c273-4cc5-a9a2-95ae34037d51))
    (pad "1" smd roundrect locked (at -0.825 0) (size 0.8 0.95) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 16 "/12Vg") (tstamp f2b9f80b-75c8-4896-8d11-3896afeff9a8))
    (pad "2" smd roundrect locked (at 0.825 0) (size 0.8 0.95) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (tstamp b906e8b9-434e-432a-8fb4-8c8f4c65afb8))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_line (start 119.38 -101.6) (end 0.127 -101.6) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f8f1d77))
  (gr_line (start 0.127 -89.535) (end 1.143 -89.535) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f8f212c))
  (gr_line (start 1.143 -89.535) (end 1.143 -81.28) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f8f212f))
  (gr_line (start 1.143 -81.28) (end 0.127 -81.28) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f8f2149))
  (gr_line (start 0.127 -81.28) (end 0.127 -4.191) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f8f214d))
  (gr_line (start 0.127 4.191) (end 0.127 4.953) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f8f2155))
  (gr_line (start 1.143 4.191) (end 0.127 4.191) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f8f2158))
  (gr_line (start 1.143 -4.191) (end 1.143 4.191) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f8f216c))
  (gr_line (start 0.127 -4.191) (end 1.143 -4.191) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f8f2170))
  (gr_line (start 20.32 -40.64) (end 25.4 -45.72) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f9038de))
  (gr_line (start 25.4 -45.72) (end 68.58 -45.72) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f9353c6))
  (gr_line (start 68.58 -45.72) (end 76.2 -53.34) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f9353e4))
  (gr_line (start 119.38 -74.93) (end 119.38 -101.6) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f93663b))
  (gr_line (start 116.84 -72.39) (end 119.38 -74.93) (layer "Edge.Cuts") (width 0.05) (tstamp 00000000-0000-0000-0000-00005f9837f0))
  (gr_line (start 20.32 -3.429) (end 20.32 -40.64) (layer "Edge.Cuts") (width 0.05) (tstamp 2516cd70-9cc6-43e7-8128-21ba7540de02))
  (gr_line (start 13.843 4.953) (end 13.843 -3.429) (layer "Edge.Cuts") (width 0.05) (tstamp 43f2961f-adf4-444b-9229-f98f1b6c35d3))
  (gr_line (start 13.843 -3.429) (end 20.32 -3.429) (layer "Edge.Cuts") (width 0.05) (tstamp 9435efd0-1a5d-4fe2-a1ef-18d8a8b4ec44))
  (gr_line (start 116.84 -72.39) (end 76.2 -72.39) (layer "Edge.Cuts") (width 0.05) (tstamp a38647dd-a63f-4b5d-bac8-57050020c426))
  (gr_line (start 0.127 -101.6) (end 0.127 -89.535) (layer "Edge.Cuts") (width 0.05) (tstamp be622b65-f033-46ec-8ae6-798b8d2f0b97))
  (gr_line (start 0.127 4.953) (end 13.843 4.953) (layer "Edge.Cuts") (width 0.05) (tstamp cff4bfc8-571b-48a1-90c3-747755bb3665))
  (gr_line (start 76.2 -72.39) (end 76.2 -53.34) (layer "Edge.Cuts") (width 0.05) (tstamp d8056ed4-2c99-419c-aad6-5f7cb5d0a52f))
  (gr_text "12V" (at 93.472 -93.218 90) (layer "F.SilkS") (tstamp 00000000-0000-0000-0000-00005f979457)
    (effects (font (size 1 1) (thickness 0.25)))
  )
  (gr_text "USER3_L" (at 72.39 -65.806) (layer "F.SilkS") (tstamp 00000000-0000-0000-0000-00005f979c37)
    (effects (font (size 1 1) (thickness 0.25)))
  )
  (gr_text "USER2_L" (at 72.39 -76.2) (layer "F.SilkS") (tstamp 00000000-0000-0000-0000-00005f979ffe)
    (effects (font (size 1 1) (thickness 0.25)))
  )
  (gr_text "+12Vo" (at 103.886 -98.806 90) (layer "F.SilkS") (tstamp 9cc0e470-bea9-4967-982c-f8344ef1d84e)
    (effects (font (size 1 1) (thickness 0.25)))
  )
  (gr_text "SIS1160 PCI-L IO add on\nRev. 0\nIFIC (CSIC/UV) - IRIS\nCPGM - CDEIGENT/2019/011\nLicensed under CERN-OHL-W v2 or later\nhttps://ohwr.org/project/sis1160-pci-io" (at 31.623 -93.98) (layer "F.SilkS") (tstamp a8f3c6a4-8c17-4cd6-8ef9-59a0072044e1)
    (effects (font (size 1.27 1.27) (thickness 0.3)))
  )

  (segment (start 10.668 -6.655) (end 10.668 -7.522) (width 1) (layer "F.Cu") (net 1) (tstamp 0f4c812e-cc2a-417b-8d0c-bb78d6d1066f))
  (segment (start 8.509 -92.507) (end 8.509 -93.726) (width 1) (layer "F.Cu") (net 1) (tstamp 1d528473-c203-4673-a88e-3d5e38f6e426))
  (segment (start 92.754 -97.917) (end 92.754 -99.485) (width 1) (layer "F.Cu") (net 1) (tstamp 74a8b736-ffc8-4f80-a62e-b03abd972d73))
  (segment (start 96.624 -77.597) (end 97.968 -77.597) (width 1) (layer "F.Cu") (net 1) (tstamp e041e2d5-18dc-4deb-84d7-37f8b87f75b4))
  (segment (start 97.968 -77.597) (end 99.0855 -76.4795) (width 1) (layer "F.Cu") (net 1) (tstamp ec6dc835-ad21-446d-b339-751f7e02b614))
  (segment (start 10.668 -7.522) (end 9.206 -8.984) (width 1) (layer "F.Cu") (net 1) (tstamp ee05edc0-5d2e-4892-a087-9f1cf2c32e9a))
  (via (at 8.509 -93.726) (size 0.8) (drill 0.4) (layers "F.Cu" "B.Cu") (net 1) (tstamp 6e6731a7-7ad9-438c-a48b-1c4e2439c27b))
  (via (at 99.0855 -76.4795) (size 0.8) (drill 0.4) (layers "F.Cu" "B.Cu") (net 1) (tstamp b3992b17-bf3b-4274-8538-6a82e494b878))
  (via (at 92.754 -99.485) (size 0.8) (drill 0.4) (layers "F.Cu" "B.Cu") (net 1) (tstamp e518d4ca-57cf-4bb9-8d9d-64dc73454378))
  (segment (start 99.0855 -77.597) (end 99.0855 -76.4795) (width 1) (layer "B.Cu") (net 1) (tstamp 11ad9e6b-2f72-416d-b4e7-fa474eea6944))
  (segment (start 91.44 -94.615) (end 89.154 -96.901) (width 1) (layer "F.Cu") (net 2) (tstamp 2e36e6a5-35e4-45a9-9a5c-4cf3c2cac541))
  (segment (start 91.44 -94.107) (end 91.44 -94.615) (width 1) (layer "F.Cu") (net 2) (tstamp 37d2592e-ad11-444c-b199-50023b558a21))
  (segment (start 89.154 -96.901) (end 89.154 -97.917) (width 1) (layer "F.Cu") (net 2) (tstamp f946c236-95dd-4009-940b-82b4ecca6c6a))
  (segment (start 91.313 -90.043) (end 91.313 -93.98) (width 1) (layer "B.Cu") (net 2) (tstamp 79558add-0796-4aa4-81c4-aebf16521087))
  (segment (start 22.86 -74.676) (end 68.834 -74.676) (width 0.25) (layer "F.Cu") (net 3) (tstamp 4a96873e-c598-4d3b-afd8-fcdcb2f49c1a))
  (segment (start 17.272 -69.088) (end 22.86 -74.676) (width 0.25) (layer "F.Cu") (net 3) (tstamp 4ca1cd1c-c1e3-4c42-ad6b-c352e5f13cf7))
  (segment (start 64.896 -78.614) (end 68.834 -74.676) (width 0.25) (layer "F.Cu") (net 3) (tstamp 526d7a4d-d995-4e34-acb2-ad47094e05ff))
  (segment (start 68.834 -74.676) (end 69.534 -74.676) (width 0.25) (layer "F.Cu") (net 3) (tstamp 5869d5b8-908c-4587-8438-95bb2947d069))
  (segment (start 64.896 -92.202) (end 64.896 -78.614) (width 0.25) (layer "F.Cu") (net 3) (tstamp 63011425-889c-442f-af2b-48eba3ab13ec))
  (segment (start 11.746 -24.024) (end 11.746 -25.538753) (width 0.25) (layer "F.Cu") (net 3) (tstamp 9fdd5d6b-648b-4753-a46c-d1aa1b52ac9e))
  (segment (start 17.272 -31.064753) (end 17.272 -69.088) (width 0.25) (layer "F.Cu") (net 3) (tstamp e235f879-3bf3-48ce-ad41-c4a2a1fc5bde))
  (segment (start 11.746 -25.538753) (end 17.272 -31.064753) (width 0.25) (layer "F.Cu") (net 3) (tstamp f6c2a7c6-ee44-4cb9-a857-7e90581a51ff))
  (segment (start 69.98352 -89.11448) (end 64.896 -94.202) (width 0.25) (layer "F.Cu") (net 4) (tstamp 15e6fe07-429e-47f3-b36c-a399a0aee686))
  (segment (start 22.987 -64.135) (end 68.687 -64.135) (width 0.25) (layer "F.Cu") (net 4) (tstamp 51fca901-4682-458d-a0a9-408e5176d795))
  (segment (start 69.98352 -65.43152) (end 69.98352 -89.11448) (width 0.25) (layer "F.Cu") (net 4) (tstamp 53f18d0e-c58d-47a4-a228-baedee908cba))
  (segment (start 11.746 -11.524) (end 11.746 -13.038753) (width 0.25) (layer "F.Cu") (net 4) (tstamp 55160379-159c-415b-8585-09243b51194f))
  (segment (start 68.834 -64.282) (end 69.98352 -65.43152) (width 0.25) (layer "F.Cu") (net 4) (tstamp 5980f1fc-6db3-4a71-966b-3dd5631a8f7a))
  (segment (start 18.288 -59.436) (end 22.987 -64.135) (width 0.25) (layer "F.Cu") (net 4) (tstamp 7701b488-b2b3-4d97-a1f0-ba2d873d88d5))
  (segment (start 18.288 -19.580753) (end 18.288 -59.436) (width 0.25) (layer "F.Cu") (net 4) (tstamp 7902bb19-9394-451d-890f-937eca240c85))
  (segment (start 11.746 -13.038753) (end 18.288 -19.580753) (width 0.25) (layer "F.Cu") (net 4) (tstamp b5e718cf-b767-429e-8dee-ef01b9931649))
  (segment (start 44.998 -93.427) (end 44.998 -92.402) (width 0.2) (layer "B.Cu") (net 5) (tstamp 02d1d92e-0934-47d7-9db2-ce3c7de927b3))
  (segment (start 46.698 -94.002) (end 46.698 -93.427) (width 0.2) (layer "B.Cu") (net 5) (tstamp 08c94c62-2d86-457c-bcf8-1d7a96e9ac76))
  (segment (start 47.998 -92.977) (end 47.998 -94.002) (width 0.2) (layer "B.Cu") (net 5) (tstamp 0c8086c8-faa4-424d-b446-d972a62c2cea))
  (segment (start 55.108499 -92.977) (end 55.883499 -92.202) (width 0.2) (layer "B.Cu") (net 5) (tstamp 1ae1f8b0-b802-4154-8657-99e5a97dbb30))
  (segment (start 44.598 -92.402) (end 44.598 -92.777) (width 0.2) (layer "B.Cu") (net 5) (tstamp 2a93d4a5-c42e-48b6-a7f2-1003f5048d59))
  (segment (start 51.598 -92.977) (end 52.071249 -92.977) (width 0.2) (layer "B.Cu") (net 5) (tstamp 2b8849fd-7576-4cef-9b8b-1713dbf8adbe))
  (segment (start 47.998 -92.402) (end 47.998 -92.977) (width 0.2) (layer "B.Cu") (net 5) (tstamp 34d66532-b4b8-45ef-abfc-1f64a833d0ec))
  (segment (start 55.883499 -92.202) (end 56.896 -92.202) (width 0.2) (layer "B.Cu") (net 5) (tstamp 3dc3f8f0-5d2f-4bca-9f60-e11606bb4b34))
  (segment (start 46.298 -92.977) (end 46.298 -94.002) (width 0.2) (layer "B.Cu") (net 5) (tstamp 4497e3f1-06cd-408b-b8f7-9f4b29347433))
  (segment (start 50.098 -93.427) (end 50.098 -92.402) (width 0.2) (layer "B.Cu") (net 5) (tstamp 44edab4e-ef0b-480c-b088-9bebaea7fce0))
  (segment (start 30.48 -92.977) (end 43.098 -92.977) (width 0.2) (layer "B.Cu") (net 5) (tstamp 47b8d31e-12b7-4c54-8753-7d134df1944f))
  (segment (start 11.971001 -74.398999) (end 12.346 -74.024) (width 0.25) (layer "B.Cu") (net 5) (tstamp 48267284-ff64-46a3-8452-225c3c858f8d))
  (segment (start 46.298 -92.402) (end 46.298 -92.977) (width 0.2) (layer "B.Cu") (net 5) (tstamp 4b512a19-c5fe-4015-8871-017194858a97))
  (segment (start 50.098 -94.002) (end 50.098 -93.427) (width 0.2) (layer "B.Cu") (net 5) (tstamp 4e430d77-e0a8-4302-8bc1-ee6766b34b51))
  (segment (start 44.998 -94.002) (end 44.998 -93.427) (width 0.2) (layer "B.Cu") (net 5) (tstamp 534fff4f-2ced-415e-add2-8eef147d5356))
  (segment (start 11.971001 -76.676801) (end 27.422671 -92.128471) (width 0.25) (layer "B.Cu") (net 5) (tstamp 5de03374-3f2f-4e90-8bc1-e924136e095d))
  (segment (start 46.698 -93.427) (end 46.698 -92.402) (width 0.2) (layer "B.Cu") (net 5) (tstamp 61bba4c1-275a-4ac2-b8a0-815678eb8a97))
  (segment (start 11.971001 -76.676801) (end 11.971001 -74.398999) (width 0.25) (layer "B.Cu") (net 5) (tstamp 646b406f-0051-43df-bb18-b63e3ee34f87))
  (segment (start 51.398 -92.402) (end 51.398 -92.777) (width 0.2) (layer "B.Cu") (net 5) (tstamp 832d20e8-a040-4c63-9451-eaa9d9e6d91e))
  (segment (start 49.698 -92.402) (end 49.698 -92.977) (width 0.2) (layer "B.Cu") (net 5) (tstamp 87b0f6cf-53a3-4666-aa4f-dd87bb1cdc76))
  (segment (start 52.071249 -92.977) (end 55.108499 -92.977) (width 0.2) (layer "B.Cu") (net 5) (tstamp 87b3d224-46c8-48d4-8886-8acdb9451557))
  (segment (start 48.398 -94.002) (end 48.398 -93.427) (width 0.2) (layer "B.Cu") (net 5) (tstamp 9ec3a0ec-e959-4f30-a6a5-02fabd048f2a))
  (segment (start 43.298 -92.777) (end 43.298 -92.402) (width 0.2) (layer "B.Cu") (net 5) (tstamp c90358b0-a4eb-4543-84af-cb8fd4fa3b37))
  (segment (start 44.598 -92.777) (end 44.598 -94.002) (width 0.2) (layer "B.Cu") (net 5) (tstamp d72e1b02-95c0-4fb7-afef-1c883f2de4f4))
  (segment (start 49.698 -92.977) (end 49.698 -94.002) (width 0.2) (layer "B.Cu") (net 5) (tstamp ebc414fb-5b86-40fc-b445-76f1b6a96d1e))
  (segment (start 48.398 -93.427) (end 48.398 -92.402) (width 0.2) (layer "B.Cu") (net 5) (tstamp edb574d8-8747-4119-b6f9-d07db6d5e39c))
  (arc (start 51.398 -92.777) (mid 51.456579 -92.918421) (end 51.598 -92.977) (width 0.2) (layer "B.Cu") (net 5) (tstamp 011c73b5-2abb-4679-8adf-a6c82ebfc443))
  (arc (start 43.098 -92.977) (mid 43.239421 -92.918421) (end 43.298 -92.777) (width 0.2) (layer "B.Cu") (net 5) (tstamp 0b4513be-3a98-44f6-8c94-75a917fe6b81))
  (arc (start 44.598 -94.002) (mid 44.656579 -94.143421) (end 44.798 -94.202) (width 0.2) (layer "B.Cu") (net 5) (tstamp 23cc5034-eecd-465d-bb12-7c408bfd8878))
  (arc (start 50.748 -91.752) (mid 51.207619 -91.942381) (end 51.398 -92.402) (width 0.2) (layer "B.Cu") (net 5) (tstamp 2c72f63d-fdd0-41cf-995a-245c5022d42f))
  (arc (start 43.948 -91.752) (mid 44.407619 -91.942381) (end 44.598 -92.402) (width 0.2) (layer "B.Cu") (net 5) (tstamp 394ac763-1d95-4720-8386-49ec00b6602a))
  (arc (start 43.298 -92.402) (mid 43.488381 -91.942381) (end 43.948 -91.752) (width 0.2) (layer "B.Cu") (net 5) (tstamp 41db31e6-99af-4ffa-9fc7-f307540fd324))
  (arc (start 46.498 -94.202) (mid 46.639421 -94.143421) (end 46.698 -94.002) (width 0.2) (layer "B.Cu") (net 5) (tstamp 44d3dd27-978e-45e9-9321-652a277961d3))
  (arc (start 44.998 -92.402) (mid 45.188381 -91.942381) (end 45.648 -91.752) (width 0.2) (layer "B.Cu") (net 5) (tstamp 5a85b7e2-55e0-46bc-8777-2d19ffc3c1b0))
  (arc (start 45.648 -91.752) (mid 46.107619 -91.942381) (end 46.298 -92.402) (width 0.2) (layer "B.Cu") (net 5) (tstamp 65bcbce2-782e-40c6-a2da-2ca9aa556535))
  (arc (start 44.798 -94.202) (mid 44.939421 -94.143421) (end 44.998 -94.002) (width 0.2) (layer "B.Cu") (net 5) (tstamp 6653f087-8b45-4ca1-87fa-285fa44c0337))
  (arc (start 47.348 -91.752) (mid 47.807619 -91.942381) (end 47.998 -92.402) (width 0.2) (layer "B.Cu") (net 5) (tstamp 765dbb08-71fe-4ca5-8304-43c0c6d38cb0))
  (arc (start 50.098 -92.402) (mid 50.288381 -91.942381) (end 50.748 -91.752) (width 0.2) (layer "B.Cu") (net 5) (tstamp 7781b6fa-07ee-4feb-8cbe-40bb994ffe3f))
  (arc (start 47.998 -94.002) (mid 48.056579 -94.143421) (end 48.198 -94.202) (width 0.2) (layer "B.Cu") (net 5) (tstamp 803efbb7-582d-4c50-a44d-9b00c10d15b1))
  (arc (start 46.298 -94.002) (mid 46.356579 -94.143421) (end 46.498 -94.202) (width 0.2) (layer "B.Cu") (net 5) (tstamp 87c88c19-7bad-4ce6-afa8-a86fa0603449))
  (arc (start 49.898 -94.202) (mid 50.039421 -94.143421) (end 50.098 -94.002) (width 0.2) (layer "B.Cu") (net 5) (tstamp 8d4e29a2-c8c9-4a3a-9d65-2c5fad1f87fe))
  (arc (start 48.198 -94.202) (mid 48.339421 -94.143421) (end 48.398 -94.002) (width 0.2) (layer "B.Cu") (net 5) (tstamp 9d74d6be-30c2-475e-8674-667fa335d503))
  (arc (start 46.698 -92.402) (mid 46.888381 -91.942381) (end 47.348 -91.752) (width 0.2) (layer "B.Cu") (net 5) (tstamp abcfa8f0-bb0e-4eec-846a-74d504a08e07))
  (arc (start 49.048 -91.752) (mid 49.507619 -91.942381) (end 49.698 -92.402) (width 0.2) (layer "B.Cu") (net 5) (tstamp b598c8b9-1409-4f2a-b788-003e36ca8f28))
  (arc (start 49.698 -94.002) (mid 49.756579 -94.143421) (end 49.898 -94.202) (width 0.2) (layer "B.Cu") (net 5) (tstamp c6a16947-c08b-481d-a214-1e46e988a921))
  (arc (start 48.398 -92.402) (mid 48.588381 -91.942381) (end 49.048 -91.752) (width 0.2) (layer "B.Cu") (net 5) (tstamp f3a254c7-293c-4fcf-9bad-ca3f3b3c4e51))
  (segment (start 50.948 -92.402) (end 50.948 -92.777) (width 0.2) (layer "B.Cu") (net 6) (tstamp 0a9b0580-7464-41a7-8a9c-97f65764ab53))
  (segment (start 48.848 -94.002) (end 48.848 -93.427) (width 0.2) (layer "B.Cu") (net 6) (tstamp 0eccb8b2-b8f9-43b5-b9a4-98ee3f510416))
  (segment (start 47.548 -92.402) (end 47.548 -92.977) (width 0.2) (layer "B.Cu") (net 6) (tstamp 15264fc1-f64d-41d8-bca1-cdcf1b111454))
  (segment (start 55.108499 -93.427) (end 55.883499 -94.202) (width 0.2) (layer "B.Cu") (net 6) (tstamp 2239713e-37c0-4178-80ed-c294b504d12e))
  (segment (start 50.548 -93.427) (end 50.548 -92.402) (width 0.2) (layer "B.Cu") (net 6) (tstamp 2314b4d6-4585-4c4b-810c-e476072c4c97))
  (segment (start 30.48 -93.427) (end 43.098 -93.427) (width 0.2) (layer "B.Cu") (net 6) (tstamp 2411d988-8b14-45da-a23a-eef3cca99323))
  (segment (start 11.520999 -76.863199) (end 28.0848 -93.427) (width 0.25) (layer "B.Cu") (net 6) (tstamp 2945f46b-3a75-4d33-9b59-43ac1dedc69d))
  (segment (start 11.146 -74.024) (end 11.520999 -74.398999) (width 0.25) (layer "B.Cu") (net 6) (tstamp 303bfc95-18d7-43fb-9fb2-c3db5a84dad6))
  (segment (start 49.248 -92.402) (end 49.248 -92.977) (width 0.2) (layer "B.Cu") (net 6) (tstamp 3118a643-aad9-42db-8367-901596af4274))
  (segment (start 45.448 -94.002) (end 45.448 -93.427) (width 0.2) (layer "B.Cu") (net 6) (tstamp 324cdb65-de4c-47fb-a22c-54d83225011d))
  (segment (start 28.0848 -93.427) (end 30.551 -93.427) (width 0.25) (layer "B.Cu") (net 6) (tstamp 39c8575e-866d-4919-af0b-a0c751409a32))
  (segment (start 11.520999 -74.398999) (end 11.520999 -76.863199) (width 0.25) (layer "B.Cu") (net 6) (tstamp 3a83d0f3-26d1-44a4-a367-5dbaffd601b1))
  (segment (start 45.448 -93.427) (end 45.448 -92.402) (width 0.2) (layer "B.Cu") (net 6) (tstamp 50a91a5d-5081-4a27-9e72-e93e2508c8d0))
  (segment (start 44.148 -92.402) (end 44.148 -92.777) (width 0.2) (layer "B.Cu") (net 6) (tstamp 5be3bad8-f34b-4642-b94e-9178f3a2571d))
  (segment (start 48.848 -93.427) (end 48.848 -92.402) (width 0.2) (layer "B.Cu") (net 6) (tstamp 63c7a6c3-c861-4c01-a11d-d6bd859e0f02))
  (segment (start 45.848 -92.977) (end 45.848 -94.002) (width 0.2) (layer "B.Cu") (net 6) (tstamp 6fb52504-ed02-40cc-98db-3356c5b0ad4a))
  (segment (start 43.748 -92.777) (end 43.748 -92.402) (width 0.2) (layer "B.Cu") (net 6) (tstamp 727b306b-8f0f-429f-b280-0629f41fcd0d))
  (segment (start 44.148 -92.777) (end 44.148 -94.002) (width 0.2) (layer "B.Cu") (net 6) (tstamp 7ebb20e2-52a1-4d7d-b9b1-21dbbf27dd4f))
  (segment (start 47.148 -94.002) (end 47.148 -93.427) (width 0.2) (layer "B.Cu") (net 6) (tstamp 8d11016a-dd87-4c27-aabb-b0766b84904e))
  (segment (start 52.071249 -93.427) (end 55.108499 -93.427) (width 0.2) (layer "B.Cu") (net 6) (tstamp a134696c-b24a-48bb-bb26-34810930962b))
  (segment (start 49.248 -92.977) (end 49.248 -94.002) (width 0.2) (layer "B.Cu") (net 6) (tstamp a454196f-e225-4d54-a115-2795a57a30b1))
  (segment (start 45.848 -92.402) (end 45.848 -92.977) (width 0.2) (layer "B.Cu") (net 6) (tstamp b10a9b63-4a61-4a54-9060-bdd2522f2ea7))
  (segment (start 51.598 -93.427) (end 52.071249 -93.427) (width 0.2) (layer "B.Cu") (net 6) (tstamp b1b63df4-4639-47ef-a2f4-f63ad0bd2fe9))
  (segment (start 55.883499 -94.202) (end 56.896 -94.202) (width 0.2) (layer "B.Cu") (net 6) (tstamp b43cf621-579d-4f66-a165-491d06a44aff))
  (segment (start 47.148 -93.427) (end 47.148 -92.402) (width 0.2) (layer "B.Cu") (net 6) (tstamp c70551ca-7d67-4f76-ab89-f3f48da489e2))
  (segment (start 47.548 -92.977) (end 47.548 -94.002) (width 0.2) (layer "B.Cu") (net 6) (tstamp ed54c7fb-97f4-483e-825b-12a99a079c9e))
  (segment (start 50.548 -94.002) (end 50.548 -93.427) (width 0.2) (layer "B.Cu") (net 6) (tstamp efbdbcb0-285d-465b-9062-055b60275426))
  (arc (start 46.498 -94.652) (mid 46.957619 -94.461619) (end 47.148 -94.002) (width 0.2) (layer "B.Cu") (net 6) (tstamp 1f13e3e4-6903-45dc-bc8b-d5fbfd240183))
  (arc (start 45.648 -92.202) (mid 45.789421 -92.260579) (end 45.848 -92.402) (width 0.2) (layer "B.Cu") (net 6) (tstamp 3df33f2a-97f6-4f61-a329-babd3923b6f8))
  (arc (start 49.048 -92.202) (mid 49.189421 -92.260579) (end 49.248 -92.402) (width 0.2) (layer "B.Cu") (net 6) (tstamp 4736379e-30cf-4977-9cbe-365668842457))
  (arc (start 43.948 -92.202) (mid 44.089421 -92.260579) (end 44.148 -92.402) (width 0.2) (layer "B.Cu") (net 6) (tstamp 48ccde23-6fdd-482a-86ce-6681754ee4f7))
  (arc (start 47.148 -92.402) (mid 47.206579 -92.260579) (end 47.348 -92.202) (width 0.2) (layer "B.Cu") (net 6) (tstamp 5ae2edc8-4811-4df4-925b-42190f4ac647))
  (arc (start 48.198 -94.652) (mid 48.657619 -94.461619) (end 48.848 -94.002) (width 0.2) (layer "B.Cu") (net 6) (tstamp 789c993b-021d-4495-9e29-bd63e1a4df3b))
  (arc (start 47.548 -94.002) (mid 47.738381 -94.461619) (end 48.198 -94.652) (width 0.2) (layer "B.Cu") (net 6) (tstamp 8a162fec-6f4c-44c6-b45e-fd693f260c2e))
  (arc (start 49.248 -94.002) (mid 49.438381 -94.461619) (end 49.898 -94.652) (width 0.2) (layer "B.Cu") (net 6) (tstamp 8a406a80-9bef-4407-a6d6-c04f4f38e99c))
  (arc (start 43.098 -93.427) (mid 43.557619 -93.236619) (end 43.748 -92.777) (width 0.2) (layer "B.Cu") (net 6) (tstamp 8b9c1e7f-1006-471b-bd56-eaf3eb8379ec))
  (arc (start 43.748 -92.402) (mid 43.806579 -92.260579) (end 43.948 -92.202) (width 0.2) (layer "B.Cu") (net 6) (tstamp 8f831ffe-7dad-4d4e-94e3-649c314c08ee))
  (arc (start 49.898 -94.652) (mid 50.357619 -94.461619) (end 50.548 -94.002) (width 0.2) (layer "B.Cu") (net 6) (tstamp 987cc7e6-4bfa-477e-bfb8-48f7463296e6))
  (arc (start 45.448 -92.402) (mid 45.506579 -92.260579) (end 45.648 -92.202) (width 0.2) (layer "B.Cu") (net 6) (tstamp a98616fd-b5f4-4934-9921-b99726806018))
  (arc (start 44.798 -94.652) (mid 45.257619 -94.461619) (end 45.448 -94.002) (width 0.2) (layer "B.Cu") (net 6) (tstamp b87997a0-e32a-4fcc-8626-8865ea2b939d))
  (arc (start 50.748 -92.202) (mid 50.889421 -92.260579) (end 50.948 -92.402) (width 0.2) (layer "B.Cu") (net 6) (tstamp c59ba396-28ad-411b-a444-2bc26b754d0d))
  (arc (start 48.848 -92.402) (mid 48.906579 -92.260579) (end 49.048 -92.202) (width 0.2) (layer "B.Cu") (net 6) (tstamp c7b336a5-595c-4dbf-8e33-6e541508fb0e))
  (arc (start 45.848 -94.002) (mid 46.038381 -94.461619) (end 46.498 -94.652) (width 0.2) (layer "B.Cu") (net 6) (tstamp ea06d955-65bb-4b0d-afd7-ccbf4b47ab00))
  (arc (start 47.348 -92.202) (mid 47.489421 -92.260579) (end 47.548 -92.402) (width 0.2) (layer "B.Cu") (net 6) (tstamp eae077c4-1065-477d-89a7-44a728b8fa2c))
  (arc (start 44.148 -94.002) (mid 44.338381 -94.461619) (end 44.798 -94.652) (width 0.2) (layer "B.Cu") (net 6) (tstamp ed85885c-2b6b-4c9f-b9f2-28c9625250d0))
  (arc (start 50.548 -92.402) (mid 50.606579 -92.260579) (end 50.748 -92.202) (width 0.2) (layer "B.Cu") (net 6) (tstamp f1073d42-f24b-47e9-9a78-950671fd4003))
  (arc (start 50.948 -92.777) (mid 51.138381 -93.236619) (end 51.598 -93.427) (width 0.2) (layer "B.Cu") (net 6) (tstamp f54da3ac-419d-443a-95ad-3a8dcb7023ba))
  (segment (start 11.971001 -65.627801) (end 16.227 -69.8838) (width 0.25) (layer "B.Cu") (net 7) (tstamp 628d9ce3-c95a-46c4-ac16-5b9ff1873718))
  (segment (start 16.227 -69.8838) (end 16.227 -73.1858) (width 0.25) (layer "B.Cu") (net 7) (tstamp 63d8bc18-1f0a-44e7-b55e-d21f44ca60be))
  (segment (start 11.971001 -61.898999) (end 11.971001 -65.627801) (width 0.25) (layer "B.Cu") (net 7) (tstamp 753a6215-5b83-4b12-a6e0-11ecbdbea3e1))
  (segment (start 30.9542 -87.913) (end 36.385 -87.913) (width 0.25) (layer "B.Cu") (net 7) (tstamp 9629862d-9ad9-4488-bcf9-4fc72975ea93))
  (segment (start 12.346 -61.524) (end 11.971001 -61.898999) (width 0.25) (layer "B.Cu") (net 7) (tstamp d47687d7-ee8e-4815-8b52-8ff11476a4c1))
  (segment (start 16.227 -73.1858) (end 30.9542 -87.913) (width 0.25) (layer "B.Cu") (net 7) (tstamp dd11a815-f5b8-4d70-be7c-730ca734501c))
  (segment (start 30.7678 -88.363) (end 15.777 -73.3722) (width 0.25) (layer "B.Cu") (net 8) (tstamp 0fb0a7be-cc92-4007-8bbe-e348e2a649ea))
  (segment (start 15.777 -70.0702) (end 11.520999 -65.814199) (width 0.25) (layer "B.Cu") (net 8) (tstamp 11c1e218-4702-420a-9562-09d3821f8c65))
  (segment (start 11.520999 -61.898999) (end 11.146 -61.524) (width 0.25) (layer "B.Cu") (net 8) (tstamp 64eb3673-6b07-4473-ae36-53de87abb0bf))
  (segment (start 15.777 -73.3722) (end 15.777 -70.0702) (width 0.25) (layer "B.Cu") (net 8) (tstamp 84188c60-9991-4d22-a0be-bc7eb5aaf770))
  (segment (start 30.7678 -88.363) (end 36.385 -88.363) (width 0.25) (layer "B.Cu") (net 8) (tstamp 993c9331-3456-466a-a2e7-ab1112b2a0e4))
  (segment (start 11.520999 -65.814199) (end 11.520999 -61.898999) (width 0.25) (layer "B.Cu") (net 8) (tstamp a5ed2698-7ba2-4be2-8c27-17fcf9bd9840))
  (segment (start 11.971001 -52.673801) (end 17.497 -58.1998) (width 0.25) (layer "B.Cu") (net 9) (tstamp 0c822804-39bc-4761-a551-d65471b0ab7f))
  (segment (start 11.971001 -49.398999) (end 11.971001 -52.673801) (width 0.25) (layer "B.Cu") (net 9) (tstamp 4350c5f3-de0c-4994-b628-d112145d4627))
  (segment (start 31.9702 -83.595) (end 42.463481 -83.595) (width 0.25) (layer "B.Cu") (net 9) (tstamp 52c6b271-df8b-4bf6-84cb-cfeeff8a919d))
  (segment (start 12.346 -49.024) (end 11.971001 -49.398999) (width 0.25) (layer "B.Cu") (net 9) (tstamp 8657536e-60c8-45d1-94e3-958446bb772b))
  (segment (start 17.497 -69.1218) (end 31.9702 -83.595) (width 0.25) (layer "B.Cu") (net 9) (tstamp b64cfc46-374d-4f6b-a8c8-03aec77d0ae9))
  (segment (start 17.497 -58.1998) (end 17.497 -69.1218) (width 0.25) (layer "B.Cu") (net 9) (tstamp fa7f9b59-4e4c-4d44-a3c3-3d7b23c157ca))
  (segment (start 17.047 -69.3082) (end 17.047 -58.3862) (width 0.25) (layer "B.Cu") (net 10) (tstamp 1b30f622-c656-4599-8997-6696eb3fb4bb))
  (segment (start 11.520999 -52.860199) (end 11.520999 -49.398999) (width 0.25) (layer "B.Cu") (net 10) (tstamp 6fe19526-8423-4277-8442-b14c25cbf8a6))
  (segment (start 31.7838 -84.045) (end 42.463481 -84.045) (width 0.25) (layer "B.Cu") (net 10) (tstamp 7058f4f6-d9b6-4899-b57d-6d196a3155c9))
  (segment (start 17.047 -58.3862) (end 11.520999 -52.860199) (width 0.25) (layer "B.Cu") (net 10) (tstamp a712eea0-ecff-43ac-b7ea-ecf0a35745ba))
  (segment (start 31.7838 -84.045) (end 17.047 -69.3082) (width 0.25) (layer "B.Cu") (net 10) (tstamp dbd23d5f-a93f-4dde-a5a2-0d4d13ced6f1))
  (segment (start 11.520999 -49.398999) (end 11.146 -49.024) (width 0.25) (layer "B.Cu") (net 10) (tstamp fb3d931b-6280-4a5e-9441-8f53bd5514b3))
  (segment (start 8.4836 0) (end 8.4836 -2.84559) (width 1) (layer "F.Cu") (net 13) (tstamp 23417850-4169-4771-83d2-efa3b9e86e03))
  (segment (start 8.509 -90.932) (end 8.509 -85.4202) (width 1) (layer "F.Cu") (net 13) (tstamp 48554e82-5241-48f1-b2ce-182f035c01dd))
  (segment (start 8.4836 -2.84559) (end 10.668 -5.02999) (width 1) (layer "F.Cu") (net 13) (tstamp b8b58706-d967-4f8e-b699-12171064c421))
  (segment (start 8.4836 -85.3948) (end 1.920333 -78.831533) (width 1) (layer "In2.Cu") (net 13) (tstamp 5e492e07-39bd-47ac-b2cf-a3f5d325aae7))
  (segment (start 1.920333 -78.831533) (end 1.920333 -6.563267) (width 1) (layer "In2.Cu") (net 13) (tstamp a752b986-a89a-4eb4-b357-c4d2ece6e776))
  (segment (start 1.920333 -6.563267) (end 8.4836 0) (width 1) (layer "In2.Cu") (net 13) (tstamp f15de93e-7902-42c6-aa55-2f57e6c931bf))
  (segment (start 101.125 -80.772) (end 104.648 -84.295) (width 1) (layer "F.Cu") (net 14) (tstamp 1666bed7-db40-4a27-9786-cd53b092e546))
  (segment (start 104.648 -92.695) (end 104.648 -93.98) (width 1) (layer "F.Cu") (net 14) (tstamp 33e3de9d-e53f-4b2c-9e22-a3a2d6d9b517))
  (segment (start 101.125 -79.596) (end 101.125 -80.772) (width 1) (layer "F.Cu") (net 14) (tstamp 3c5ac6e7-a0f1-4e1c-964f-a6ac57004832))
  (segment (start 84.567 -84.851) (end 88.646 -80.772) (width 1) (layer "F.Cu") (net 14) (tstamp 94f4b7ba-e1e4-436f-8c53-e2edba634f50))
  (segment (start 104.648 -92.695) (end 104.648 -84.295) (width 1) (layer "F.Cu") (net 14) (tstamp b5122b0a-b23b-475e-851c-f316a4c931a5))
  (segment (start 104.648 -93.98) (end 102.616 -96.012) (width 1) (layer "F.Cu") (net 14) (tstamp c139dfd1-f420-4997-91e8-1299bde47a78))
  (segment (start 103.124 -77.597) (end 101.125 -79.596) (width 1) (layer "F.Cu") (net 14) (tstamp c4f3a362-8c51-4761-8038-8c15e27de2ad))
  (segment (start 82.224 -84.851) (end 84.567 -84.851) (width 1) (layer "F.Cu") (net 14) (tstamp e1fa0da7-4f56-4c0f-9f18-c3550d0ebc2e))
  (segment (start 88.646 -80.772) (end 101.125 -80.772) (width 1) (layer "F.Cu") (net 14) (tstamp f74ff20d-7ae4-4368-8a6d-1f7edc1cb1b1))
  (segment (start 100.551 -80.198) (end 99.536 -80.198) (width 1) (layer "B.Cu") (net 14) (tstamp 7af2927f-fc33-4d26-9c6d-066f8f6d5f8f))
  (segment (start 104.648 -84.295) (end 100.551 -80.198) (width 1) (layer "B.Cu") (net 14) (tstamp d657cbf4-bc73-4a33-aa22-f13a10de4425))
  (segment (start 97.16 -80.772) (end 91.584 -80.772) (width 1) (layer "B.Cu") (net 15) (tstamp 430ae39d-e7f5-4773-adaf-4c9ee6e8cd13))
  (segment (start 97.536 -81.148) (end 97.16 -80.772) (width 1) (layer "B.Cu") (net 15) (tstamp f7f24da4-561d-4b3c-97c2-b31462616985))
  (segment (start 97.536 -77.6225) (end 97.5105 -77.597) (width 1) (layer "B.Cu") (net 16) (tstamp 7cb8fac2-a715-4139-8b24-30ab7fb3e903))
  (segment (start 97.536 -79.248) (end 97.536 -77.6225) (width 1) (layer "B.Cu") (net 16) (tstamp e5aec775-2028-421f-af73-d56b819d1ede))

  (zone (net 1) (net_name "GND") (layer "In2.Cu") (tstamp 00000000-0000-0000-0000-00005f8f3254) (hatch edge 0.508)
    (priority 2)
    (connect_pads yes (clearance 0.508))
    (min_thickness 0.254) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 166.35319 -3.556)
        (xy 1.524 -3.556)
        (xy 1.524 -101.346)
        (xy 166.35319 -101.346)
      )
    )
    (filled_polygon
      (layer "In2.Cu")
      (pts
        (xy 118.814121 -101.071998)
        (xy 118.860614 -101.018342)
        (xy 118.872 -100.966)
        (xy 118.872 -75.19261)
        (xy 118.851998 -75.124489)
        (xy 118.835095 -75.103515)
        (xy 116.666485 -72.934905)
        (xy 116.604173 -72.900879)
        (xy 116.57739 -72.898)
        (xy 76.208702 -72.898)
        (xy 76.207932 -72.898002)
        (xy 76.207078 -72.898007)
        (xy 76.130348 -72.898476)
        (xy 76.121719 -72.89601)
        (xy 76.121714 -72.896009)
        (xy 76.101952 -72.890361)
        (xy 76.085191 -72.886783)
        (xy 76.064848 -72.88387)
        (xy 76.064838 -72.883867)
        (xy 76.055955 -72.882595)
        (xy 76.032605 -72.871979)
        (xy 76.015093 -72.865536)
        (xy 76.0022 -72.861851)
        (xy 75.990435 -72.858488)
        (xy 75.965452 -72.842726)
        (xy 75.950386 -72.834596)
        (xy 75.92349 -72.822367)
        (xy 75.904061 -72.805626)
        (xy 75.889053 -72.794521)
        (xy 75.867369 -72.78084)
        (xy 75.861427 -72.774112)
        (xy 75.847819 -72.758704)
        (xy 75.835627 -72.74666)
        (xy 75.813253 -72.727381)
        (xy 75.808374 -72.719853)
        (xy 75.808371 -72.71985)
        (xy 75.799304 -72.705861)
        (xy 75.788014 -72.690987)
        (xy 75.771044 -72.671772)
        (xy 75.75849 -72.645034)
        (xy 75.750176 -72.630065)
        (xy 75.734107 -72.605273)
        (xy 75.731535 -72.596673)
        (xy 75.726761 -72.58071)
        (xy 75.720099 -72.563264)
        (xy 75.709201 -72.540052)
        (xy 75.70782 -72.531179)
        (xy 75.704658 -72.510872)
        (xy 75.700874 -72.494151)
        (xy 75.694986 -72.474464)
        (xy 75.694985 -72.474461)
        (xy 75.692413 -72.465859)
        (xy 75.692358 -72.456884)
        (xy 75.692358 -72.456883)
        (xy 75.692203 -72.431454)
        (xy 75.69217 -72.430672)
        (xy 75.692 -72.429577)
        (xy 75.692 -72.398702)
        (xy 75.691998 -72.397932)
        (xy 75.691524 -72.320348)
        (xy 75.691908 -72.319004)
        (xy 75.692 -72.317659)
        (xy 75.692 -53.60261)
        (xy 75.671998 -53.534489)
        (xy 75.655095 -53.513515)
        (xy 68.406485 -46.264905)
        (xy 68.344173 -46.230879)
        (xy 68.31739 -46.228)
        (xy 25.47102 -46.228)
        (xy 25.458904 -46.229354)
        (xy 25.458865 -46.22887)
        (xy 25.449914 -46.22959)
        (xy 25.44116 -46.231571)
        (xy 25.387499 -46.228242)
        (xy 25.379697 -46.228)
        (xy 25.363523 -46.228)
        (xy 25.359082 -46.227364)
        (xy 25.359081 -46.227364)
        (xy 25.353187 -46.22652)
        (xy 25.343128 -46.22549)
        (xy 25.325804 -46.224415)
        (xy 25.304883 -46.223117)
        (xy 25.304881 -46.223117)
        (xy 25.295925 -46.222561)
        (xy 25.287487 -46.219515)
        (xy 25.284576 -46.218912)
        (xy 25.2677 -46.214704)
        (xy 25.264838 -46.213867)
        (xy 25.255955 -46.212595)
        (xy 25.24779 -46.208883)
        (xy 25.247781 -46.20888)
        (xy 25.212899 -46.19302)
        (xy 25.203534 -46.189208)
        (xy 25.167505 -46.176201)
        (xy 25.1675 -46.176198)
        (xy 25.159056 -46.17315)
        (xy 25.151805 -46.167853)
        (xy 25.149159 -46.166446)
        (xy 25.134171 -46.157688)
        (xy 25.131658 -46.156081)
        (xy 25.12349 -46.152367)
        (xy 25.116695 -46.146512)
        (xy 25.116692 -46.14651)
        (xy 25.100803 -46.132819)
        (xy 25.087655 -46.12149)
        (xy 25.079742 -46.115207)
        (xy 25.068775 -46.107195)
        (xy 25.057823 -46.096243)
        (xy 25.050975 -46.089885)
        (xy 25.020056 -46.063244)
        (xy 25.020052 -46.063239)
        (xy 25.013253 -46.057381)
        (xy 25.00837 -46.049847)
        (xy 25.002896 -46.043572)
        (xy 24.993332 -46.031752)
        (xy 20.01101 -41.04943)
        (xy 20.001481 -41.041817)
        (xy 20.001796 -41.041447)
        (xy 19.994961 -41.03563)
        (xy 19.987369 -41.03084)
        (xy 19.981428 -41.024113)
        (xy 19.951766 -40.990527)
        (xy 19.946419 -40.984839)
        (xy 19.934997 -40.973417)
        (xy 19.932311 -40.969833)
        (xy 19.932309 -40.969831)
        (xy 19.928732 -40.965058)
        (xy 19.922361 -40.957232)
        (xy 19.891044 -40.921772)
        (xy 19.887228 -40.913643)
        (xy 19.885588 -40.911147)
        (xy 19.876652 -40.896277)
        (xy 19.87521 -40.893643)
        (xy 19.869828 -40.886462)
        (xy 19.853232 -40.842192)
        (xy 19.849304 -40.832871)
        (xy 19.833016 -40.79818)
        (xy 19.833014 -40.798173)
        (xy 19.829201 -40.790052)
        (xy 19.82782 -40.781185)
        (xy 19.826942 -40.778311)
        (xy 19.822544 -40.761548)
        (xy 19.821898 -40.75861)
        (xy 19.818748 -40.750208)
        (xy 19.818083 -40.741258)
        (xy 19.815243 -40.70304)
        (xy 19.814091 -40.693004)
        (xy 19.812 -40.679577)
        (xy 19.812 -40.664085)
        (xy 19.811654 -40.654747)
        (xy 19.807964 -40.605093)
        (xy 19.809839 -40.596311)
        (xy 19.810406 -40.587997)
        (xy 19.812 -40.57289)
        (xy 19.812 -4.063)
        (xy 19.791998 -3.994879)
        (xy 19.738342 -3.948386)
        (xy 19.686 -3.937)
        (xy 13.851702 -3.937)
        (xy 13.850932 -3.937002)
        (xy 13.850078 -3.937007)
        (xy 13.773348 -3.937476)
        (xy 13.764719 -3.93501)
        (xy 13.764714 -3.935009)
        (xy 13.744952 -3.929361)
        (xy 13.728191 -3.925783)
        (xy 13.707848 -3.92287)
        (xy 13.707838 -3.922867)
        (xy 13.698955 -3.921595)
        (xy 13.675605 -3.910979)
        (xy 13.658093 -3.904536)
        (xy 13.6452 -3.900851)
        (xy 13.633435 -3.897488)
        (xy 13.608452 -3.881726)
        (xy 13.593386 -3.873596)
        (xy 13.56649 -3.861367)
        (xy 13.547061 -3.844626)
        (xy 13.532053 -3.833521)
        (xy 13.510369 -3.81984)
        (xy 13.504427 -3.813112)
        (xy 13.490819 -3.797704)
        (xy 13.478627 -3.78566)
        (xy 13.456253 -3.766381)
        (xy 13.451374 -3.758853)
        (xy 13.451371 -3.75885)
        (xy 13.442304 -3.744861)
        (xy 13.431014 -3.729987)
        (xy 13.414044 -3.710772)
        (xy 13.40149 -3.684034)
        (xy 13.393176 -3.669065)
        (xy 13.377107 -3.644273)
        (xy 13.374536 -3.635675)
        (xy 13.371519 -3.629147)
        (xy 13.324788 -3.575699)
        (xy 13.25714 -3.556)
        (xy 9.562476 -3.556)
        (xy 9.524588 -3.561832)
        (xy 9.419979 -3.594815)
        (xy 9.061483 -3.669056)
        (xy 9.055032 -3.670392)
        (xy 9.055029 -3.670392)
        (xy 9.051907 -3.671039)
        (xy 8.746063 -3.702645)
        (xy 8.681172 -3.709351)
        (xy 8.681169 -3.709351)
        (xy 8.678016 -3.709677)
        (xy 8.67485 -3.709683)
        (xy 8.674841 -3.709683)
        (xy 8.489371 -3.710006)
        (xy 8.302134 -3.710333)
        (xy 8.153197 -3.695467)
        (xy 7.931284 -3.673318)
        (xy 7.931278 -3.673317)
        (xy 7.928111 -3.673001)
        (xy 7.823915 -3.651802)
        (xy 7.5629 -3.598698)
        (xy 7.562896 -3.598697)
        (xy 7.559775 -3.598062)
        (xy 7.556733 -3.597115)
        (xy 7.556727 -3.597113)
        (xy 7.44303 -3.5617)
        (xy 7.405561 -3.556)
        (xy 6.408146 -3.556)
        (xy 6.340025 -3.576002)
        (xy 6.319051 -3.592905)
        (xy 2.967238 -6.944717)
        (xy 2.933212 -7.007029)
        (xy 2.930333 -7.033812)
        (xy 2.930333 -11.921229)
        (xy 10.586 -11.921229)
        (xy 10.586001 -11.126772)
        (xy 10.588847 -11.085009)
        (xy 10.632737 -10.908976)
        (xy 10.635769 -10.902869)
        (xy 10.63577 -10.902865)
        (xy 10.67307 -10.827725)
        (xy 10.713404 -10.746473)
        (xy 10.827084 -10.605084)
        (xy 10.832401 -10.600809)
        (xy 10.963153 -10.495681)
        (xy 10.963155 -10.495679)
        (xy 10.968473 -10.491404)
        (xy 11.049724 -10.451071)
        (xy 11.124865 -10.41377)
        (xy 11.124869 -10.413769)
        (xy 11.130976 -10.410737)
        (xy 11.307009 -10.366847)
        (xy 11.327533 -10.365448)
        (xy 11.346624 -10.364146)
        (xy 11.346635 -10.364146)
        (xy 11.348771 -10.364)
        (xy 11.745815 -10.364)
        (xy 12.143228 -10.364001)
        (xy 12.174459 -10.366129)
        (xy 12.179445 -10.366469)
        (xy 12.179446 -10.366469)
        (xy 12.184991 -10.366847)
        (xy 12.361024 -10.410737)
        (xy 12.367131 -10.413769)
        (xy 12.367135 -10.41377)
        (xy 12.442276 -10.451071)
        (xy 12.523527 -10.491404)
        (xy 12.528845 -10.495679)
        (xy 12.528847 -10.495681)
        (xy 12.659599 -10.600809)
        (xy 12.664916 -10.605084)
        (xy 12.778596 -10.746473)
        (xy 12.818929 -10.827724)
        (xy 12.85623 -10.902865)
        (xy 12.856231 -10.902869)
        (xy 12.859263 -10.908976)
        (xy 12.903153 -11.085009)
        (xy 12.904552 -11.105533)
        (xy 12.905854 -11.124624)
        (xy 12.905854 -11.124635)
        (xy 12.906 -11.126771)
        (xy 12.905999 -11.921228)
        (xy 12.903153 -11.962991)
        (xy 12.859263 -12.139024)
        (xy 12.856231 -12.145131)
        (xy 12.85623 -12.145135)
        (xy 12.81893 -12.220275)
        (xy 12.778596 -12.301527)
        (xy 12.664916 -12.442916)
        (xy 12.659599 -12.447191)
        (xy 12.528847 -12.552319)
        (xy 12.528845 -12.552321)
        (xy 12.523527 -12.556596)
        (xy 12.442275 -12.59693)
        (xy 12.367135 -12.63423)
        (xy 12.367131 -12.634231)
        (xy 12.361024 -12.637263)
        (xy 12.184991 -12.681153)
        (xy 12.164467 -12.682552)
        (xy 12.145376 -12.683854)
        (xy 12.145365 -12.683854)
        (xy 12.143229 -12.684)
        (xy 11.746185 -12.684)
        (xy 11.348772 -12.683999)
        (xy 11.317541 -12.681871)
        (xy 11.312555 -12.681531)
        (xy 11.312554 -12.681531)
        (xy 11.307009 -12.681153)
        (xy 11.130976 -12.637263)
        (xy 11.124869 -12.634231)
        (xy 11.124865 -12.63423)
        (xy 11.049725 -12.59693)
        (xy 10.968473 -12.556596)
        (xy 10.963155 -12.552321)
        (xy 10.963153 -12.552319)
        (xy 10.832401 -12.447191)
        (xy 10.827084 -12.442916)
        (xy 10.713404 -12.301527)
        (xy 10.67307 -12.220275)
        (xy 10.63577 -12.145135)
        (xy 10.635769 -12.145131)
        (xy 10.632737 -12.139024)
        (xy 10.588847 -11.962991)
        (xy 10.588469 -11.957445)
        (xy 10.588469 -11.957442)
        (xy 10.586147 -11.92338)
        (xy 10.586 -11.921229)
        (xy 2.930333 -11.921229)
        (xy 2.930333 -24.421229)
        (xy 10.586 -24.421229)
        (xy 10.586001 -23.626772)
        (xy 10.588847 -23.585009)
        (xy 10.632737 -23.408976)
        (xy 10.635769 -23.402869)
        (xy 10.63577 -23.402865)
        (xy 10.67307 -23.327725)
        (xy 10.713404 -23.246473)
        (xy 10.827084 -23.105084)
        (xy 10.832401 -23.100809)
        (xy 10.963153 -22.995681)
        (xy 10.963155 -22.995679)
        (xy 10.968473 -22.991404)
        (xy 11.049724 -22.951071)
        (xy 11.124865 -22.91377)
        (xy 11.124869 -22.913769)
        (xy 11.130976 -22.910737)
        (xy 11.307009 -22.866847)
        (xy 11.327533 -22.865448)
        (xy 11.346624 -22.864146)
        (xy 11.346635 -22.864146)
        (xy 11.348771 -22.864)
        (xy 11.745815 -22.864)
        (xy 12.143228 -22.864001)
        (xy 12.174459 -22.866129)
        (xy 12.179445 -22.866469)
        (xy 12.179446 -22.866469)
        (xy 12.184991 -22.866847)
        (xy 12.361024 -22.910737)
        (xy 12.367131 -22.913769)
        (xy 12.367135 -22.91377)
        (xy 12.442276 -22.951071)
        (xy 12.523527 -22.991404)
        (xy 12.528845 -22.995679)
        (xy 12.528847 -22.995681)
        (xy 12.659599 -23.100809)
        (xy 12.664916 -23.105084)
        (xy 12.778596 -23.246473)
        (xy 12.81893 -23.327725)
        (xy 12.85623 -23.402865)
        (xy 12.856231 -23.402869)
        (xy 12.859263 -23.408976)
        (xy 12.903153 -23.585009)
        (xy 12.904552 -23.605533)
        (xy 12.905854 -23.624624)
        (xy 12.905854 -23.624635)
        (xy 12.906 -23.626771)
        (xy 12.905999 -24.421228)
        (xy 12.903153 -24.462991)
        (xy 12.859263 -24.639024)
        (xy 12.856231 -24.645131)
        (xy 12.85623 -24.645135)
        (xy 12.818929 -24.720276)
        (xy 12.778596 -24.801527)
        (xy 12.664916 -24.942916)
        (xy 12.659599 -24.947191)
        (xy 12.528847 -25.052319)
        (xy 12.528845 -25.052321)
        (xy 12.523527 -25.056596)
        (xy 12.442276 -25.096929)
        (xy 12.367135 -25.13423)
        (xy 12.367131 -25.134231)
        (xy 12.361024 -25.137263)
        (xy 12.184991 -25.181153)
        (xy 12.164467 -25.182552)
        (xy 12.145376 -25.183854)
        (xy 12.145365 -25.183854)
        (xy 12.143229 -25.184)
        (xy 11.746185 -25.184)
        (xy 11.348772 -25.183999)
        (xy 11.317541 -25.181871)
        (xy 11.312555 -25.181531)
        (xy 11.312554 -25.181531)
        (xy 11.307009 -25.181153)
        (xy 11.130976 -25.137263)
        (xy 11.124869 -25.134231)
        (xy 11.124865 -25.13423)
        (xy 11.049724 -25.096929)
        (xy 10.968473 -25.056596)
        (xy 10.963155 -25.052321)
        (xy 10.963153 -25.052319)
        (xy 10.832401 -24.947191)
        (xy 10.827084 -24.942916)
        (xy 10.713404 -24.801527)
        (xy 10.673071 -24.720276)
        (xy 10.63577 -24.645135)
        (xy 10.635769 -24.645131)
        (xy 10.632737 -24.639024)
        (xy 10.588847 -24.462991)
        (xy 10.588469 -24.457445)
        (xy 10.588469 -24.457442)
        (xy 10.586147 -24.42338)
        (xy 10.586 -24.421229)
        (xy 2.930333 -24.421229)
        (xy 2.930333 -36.53817)
        (xy 10.131212 -36.53817)
        (xy 10.147786 -36.340793)
        (xy 10.202382 -36.150395)
        (xy 10.2052 -36.144913)
        (xy 10.205201 -36.144909)
        (xy 10.290099 -35.979715)
        (xy 10.290102 -35.979711)
        (xy 10.29292 -35.974227)
        (xy 10.415951 -35.819)
        (xy 10.56679 -35.690626)
        (xy 10.739691 -35.593995)
        (xy 10.928068 -35.532788)
        (xy 11.124746 -35.509336)
        (xy 11.130881 -35.509808)
        (xy 11.130883 -35.509808)
        (xy 11.316092 -35.524058)
        (xy 11.316097 -35.524059)
        (xy 11.322233 -35.524531)
        (xy 11.328165 -35.526187)
        (xy 11.328169 -35.526188)
        (xy 11.507064 -35.576137)
        (xy 11.507068 -35.576138)
        (xy 11.513008 -35.577797)
        (xy 11.551028 -35.597002)
        (xy 11.595201 -35.619315)
        (xy 11.665023 -35.632175)
        (xy 11.718127 -35.614109)
        (xy 11.772623 -35.580517)
        (xy 11.779571 -35.578212)
        (xy 11.779572 -35.578212)
        (xy 11.934301 -35.52689)
        (xy 11.934303 -35.52689)
        (xy 11.940832 -35.524724)
        (xy 12.045501 -35.514)
        (xy 12.343682 -35.514)
        (xy 12.646498 -35.514001)
        (xy 12.649742 -35.514338)
        (xy 12.64975 -35.514338)
        (xy 12.69679 -35.519219)
        (xy 12.752475 -35.524996)
        (xy 12.920586 -35.581082)
        (xy 13.071286 -35.674338)
        (xy 13.091532 -35.694619)
        (xy 13.191319 -35.79458)
        (xy 13.191323 -35.794585)
        (xy 13.19649 -35.799761)
        (xy 13.289483 -35.950623)
        (xy 13.297312 -35.974227)
        (xy 13.34311 -36.112301)
        (xy 13.34311 -36.112303)
        (xy 13.345276 -36.118832)
        (xy 13.356 -36.223501)
        (xy 13.355999 -36.824498)
        (xy 13.345004 -36.930475)
        (xy 13.288918 -37.098586)
        (xy 13.195662 -37.249286)
        (xy 13.19048 -37.254459)
        (xy 13.07542 -37.369319)
        (xy 13.075415 -37.369323)
        (xy 13.070239 -37.37449)
        (xy 12.919377 -37.467483)
        (xy 12.912428 -37.469788)
        (xy 12.757699 -37.52111)
        (xy 12.757697 -37.52111)
        (xy 12.751168 -37.523276)
        (xy 12.646499 -37.534)
        (xy 12.348318 -37.534)
        (xy 12.045502 -37.533999)
        (xy 12.042258 -37.533662)
        (xy 12.04225 -37.533662)
        (xy 11.99521 -37.528781)
        (xy 11.939525 -37.523004)
        (xy 11.771414 -37.466918)
        (xy 11.765182 -37.463062)
        (xy 11.765183 -37.463062)
        (xy 11.718893 -37.434417)
        (xy 11.650441 -37.415579)
        (xy 11.592663 -37.430725)
        (xy 11.539285 -37.459587)
        (xy 11.456015 -37.485363)
        (xy 11.355958 -37.516336)
        (xy 11.355955 -37.516337)
        (xy 11.350071 -37.518158)
        (xy 11.343946 -37.518802)
        (xy 11.343945 -37.518802)
        (xy 11.159213 -37.538218)
        (xy 11.159212 -37.538218)
        (xy 11.153085 -37.538862)
        (xy 11.032606 -37.527898)
        (xy 10.96197 -37.52147)
        (xy 10.961969 -37.52147)
        (xy 10.955829 -37.520911)
        (xy 10.765817 -37.464987)
        (xy 10.590285 -37.373221)
        (xy 10.435921 -37.249109)
        (xy 10.308603 -37.097378)
        (xy 10.213181 -36.923807)
        (xy 10.153291 -36.735007)
        (xy 10.131212 -36.53817)
        (xy 2.930333 -36.53817)
        (xy 2.930333 -49.03817)
        (xy 10.131212 -49.03817)
        (xy 10.147786 -48.840793)
        (xy 10.202382 -48.650395)
        (xy 10.2052 -48.644913)
        (xy 10.205201 -48.644909)
        (xy 10.290099 -48.479715)
        (xy 10.290102 -48.479711)
        (xy 10.29292 -48.474227)
        (xy 10.415951 -48.319)
        (xy 10.56679 -48.190626)
        (xy 10.739691 -48.093995)
        (xy 10.928068 -48.032788)
        (xy 11.124746 -48.009336)
        (xy 11.130881 -48.009808)
        (xy 11.130883 -48.009808)
        (xy 11.316092 -48.024058)
        (xy 11.316097 -48.024059)
        (xy 11.322233 -48.024531)
        (xy 11.328165 -48.026187)
        (xy 11.328169 -48.026188)
        (xy 11.507064 -48.076137)
        (xy 11.507068 -48.076138)
        (xy 11.513008 -48.077797)
        (xy 11.551028 -48.097002)
        (xy 11.595201 -48.119315)
        (xy 11.665023 -48.132175)
        (xy 11.718127 -48.114109)
        (xy 11.772623 -48.080517)
        (xy 11.779571 -48.078212)
        (xy 11.779572 -48.078212)
        (xy 11.934301 -48.02689)
        (xy 11.934303 -48.02689)
        (xy 11.940832 -48.024724)
        (xy 12.045501 -48.014)
        (xy 12.343682 -48.014)
        (xy 12.646498 -48.014001)
        (xy 12.649742 -48.014338)
        (xy 12.64975 -48.014338)
        (xy 12.69679 -48.019219)
        (xy 12.752475 -48.024996)
        (xy 12.920586 -48.081082)
        (xy 13.071286 -48.174338)
        (xy 13.091532 -48.194619)
        (xy 13.191319 -48.29458)
        (xy 13.191323 -48.294585)
        (xy 13.19649 -48.299761)
        (xy 13.289483 -48.450623)
        (xy 13.297312 -48.474227)
        (xy 13.34311 -48.612301)
        (xy 13.34311 -48.612303)
        (xy 13.345276 -48.618832)
        (xy 13.356 -48.723501)
        (xy 13.355999 -49.324498)
        (xy 13.345004 -49.430475)
        (xy 13.288918 -49.598586)
        (xy 13.195662 -49.749286)
        (xy 13.19048 -49.754459)
        (xy 13.07542 -49.869319)
        (xy 13.075415 -49.869323)
        (xy 13.070239 -49.87449)
        (xy 12.919377 -49.967483)
        (xy 12.912428 -49.969788)
        (xy 12.757699 -50.02111)
        (xy 12.757697 -50.02111)
        (xy 12.751168 -50.023276)
        (xy 12.646499 -50.034)
        (xy 12.348318 -50.034)
        (xy 12.045502 -50.033999)
        (xy 12.042258 -50.033662)
        (xy 12.04225 -50.033662)
        (xy 11.99521 -50.028781)
        (xy 11.939525 -50.023004)
        (xy 11.771414 -49.966918)
        (xy 11.765182 -49.963062)
        (xy 11.765183 -49.963062)
        (xy 11.718893 -49.934417)
        (xy 11.650441 -49.915579)
        (xy 11.592663 -49.930725)
        (xy 11.539285 -49.959587)
        (xy 11.456015 -49.985363)
        (xy 11.355958 -50.016336)
        (xy 11.355955 -50.016337)
        (xy 11.350071 -50.018158)
        (xy 11.343946 -50.018802)
        (xy 11.343945 -50.018802)
        (xy 11.159213 -50.038218)
        (xy 11.159212 -50.038218)
        (xy 11.153085 -50.038862)
        (xy 11.032606 -50.027898)
        (xy 10.96197 -50.02147)
        (xy 10.961969 -50.02147)
        (xy 10.955829 -50.020911)
        (xy 10.765817 -49.964987)
        (xy 10.590285 -49.873221)
        (xy 10.435921 -49.749109)
        (xy 10.308603 -49.597378)
        (xy 10.213181 -49.423807)
        (xy 10.153291 -49.235007)
        (xy 10.131212 -49.03817)
        (xy 2.930333 -49.03817)
        (xy 2.930333 -61.53817)
        (xy 10.131212 -61.53817)
        (xy 10.147786 -61.340793)
        (xy 10.202382 -61.150395)
        (xy 10.2052 -61.144913)
        (xy 10.205201 -61.144909)
        (xy 10.290099 -60.979715)
        (xy 10.290102 -60.979711)
        (xy 10.29292 -60.974227)
        (xy 10.415951 -60.819)
        (xy 10.56679 -60.690626)
        (xy 10.739691 -60.593995)
        (xy 10.928068 -60.532788)
        (xy 11.124746 -60.509336)
        (xy 11.130881 -60.509808)
        (xy 11.130883 -60.509808)
        (xy 11.316092 -60.524058)
        (xy 11.316097 -60.524059)
        (xy 11.322233 -60.524531)
        (xy 11.328165 -60.526187)
        (xy 11.328169 -60.526188)
        (xy 11.507064 -60.576137)
        (xy 11.507068 -60.576138)
        (xy 11.513008 -60.577797)
        (xy 11.551028 -60.597002)
        (xy 11.595201 -60.619315)
        (xy 11.665023 -60.632175)
        (xy 11.718127 -60.614109)
        (xy 11.772623 -60.580517)
        (xy 11.779571 -60.578212)
        (xy 11.779572 -60.578212)
        (xy 11.934301 -60.52689)
        (xy 11.934303 -60.52689)
        (xy 11.940832 -60.524724)
        (xy 12.045501 -60.514)
        (xy 12.343682 -60.514)
        (xy 12.646498 -60.514001)
        (xy 12.649742 -60.514338)
        (xy 12.64975 -60.514338)
        (xy 12.69679 -60.519219)
        (xy 12.752475 -60.524996)
        (xy 12.920586 -60.581082)
        (xy 13.071286 -60.674338)
        (xy 13.091532 -60.694619)
        (xy 13.191319 -60.79458)
        (xy 13.191323 -60.794585)
        (xy 13.19649 -60.799761)
        (xy 13.289483 -60.950623)
        (xy 13.297312 -60.974227)
        (xy 13.34311 -61.112301)
        (xy 13.34311 -61.112303)
        (xy 13.345276 -61.118832)
        (xy 13.356 -61.223501)
        (xy 13.355999 -61.824498)
        (xy 13.345004 -61.930475)
        (xy 13.288918 -62.098586)
        (xy 13.195662 -62.249286)
        (xy 13.19048 -62.254459)
        (xy 13.07542 -62.369319)
        (xy 13.075415 -62.369323)
        (xy 13.070239 -62.37449)
        (xy 12.919377 -62.467483)
        (xy 12.912428 -62.469788)
        (xy 12.757699 -62.52111)
        (xy 12.757697 -62.52111)
        (xy 12.751168 -62.523276)
        (xy 12.646499 -62.534)
        (xy 12.348318 -62.534)
        (xy 12.045502 -62.533999)
        (xy 12.042258 -62.533662)
        (xy 12.04225 -62.533662)
        (xy 11.99521 -62.528781)
        (xy 11.939525 -62.523004)
        (xy 11.771414 -62.466918)
        (xy 11.765182 -62.463062)
        (xy 11.765183 -62.463062)
        (xy 11.718893 -62.434417)
        (xy 11.650441 -62.415579)
        (xy 11.592663 -62.430725)
        (xy 11.539285 -62.459587)
        (xy 11.456015 -62.485363)
        (xy 11.355958 -62.516336)
        (xy 11.355955 -62.516337)
        (xy 11.350071 -62.518158)
        (xy 11.343946 -62.518802)
        (xy 11.343945 -62.518802)
        (xy 11.159213 -62.538218)
        (xy 11.159212 -62.538218)
        (xy 11.153085 -62.538862)
        (xy 11.032606 -62.527898)
        (xy 10.96197 -62.52147)
        (xy 10.961969 -62.52147)
        (xy 10.955829 -62.520911)
        (xy 10.765817 -62.464987)
        (xy 10.590285 -62.373221)
        (xy 10.435921 -62.249109)
        (xy 10.308603 -62.097378)
        (xy 10.213181 -61.923807)
        (xy 10.153291 -61.735007)
        (xy 10.131212 -61.53817)
        (xy 2.930333 -61.53817)
        (xy 2.930333 -64.706382)
        (xy 67.624 -64.706382)
        (xy 67.624 -63.857618)
        (xy 67.626933 -63.814596)
        (xy 67.672137 -63.633293)
        (xy 67.675169 -63.627186)
        (xy 67.67517 -63.627182)
        (xy 67.752186 -63.472034)
        (xy 67.755219 -63.465925)
        (xy 67.872302 -63.320302)
        (xy 68.017925 -63.203219)
        (xy 68.024031 -63.200188)
        (xy 68.024034 -63.200186)
        (xy 68.179182 -63.12317)
        (xy 68.179186 -63.123169)
        (xy 68.185293 -63.120137)
        (xy 68.366596 -63.074933)
        (xy 68.387947 -63.073477)
        (xy 68.407471 -63.072146)
        (xy 68.407482 -63.072146)
        (xy 68.409618 -63.072)
        (xy 69.258382 -63.072)
        (xy 69.260518 -63.072146)
        (xy 69.260529 -63.072146)
        (xy 69.280053 -63.073477)
        (xy 69.301404 -63.074933)
        (xy 69.482707 -63.120137)
        (xy 69.488814 -63.123169)
        (xy 69.488818 -63.12317)
        (xy 69.643966 -63.200186)
        (xy 69.643969 -63.200188)
        (xy 69.650075 -63.203219)
        (xy 69.795698 -63.320302)
        (xy 69.912781 -63.465925)
        (xy 69.915814 -63.472034)
        (xy 69.99283 -63.627182)
        (xy 69.992831 -63.627186)
        (xy 69.995863 -63.633293)
        (xy 70.041067 -63.814596)
        (xy 70.044 -63.857618)
        (xy 70.044 -64.706382)
        (xy 70.041067 -64.749404)
        (xy 69.995863 -64.930707)
        (xy 69.992831 -64.936814)
        (xy 69.99283 -64.936818)
        (xy 69.915814 -65.091966)
        (xy 69.915812 -65.091969)
        (xy 69.912781 -65.098075)
        (xy 69.795698 -65.243698)
        (xy 69.650075 -65.360781)
        (xy 69.643969 -65.363812)
        (xy 69.643966 -65.363814)
        (xy 69.488818 -65.44083)
        (xy 69.488814 -65.440831)
        (xy 69.482707 -65.443863)
        (xy 69.301404 -65.489067)
        (xy 69.280053 -65.490523)
        (xy 69.260529 -65.491854)
        (xy 69.260518 -65.491854)
        (xy 69.258382 -65.492)
        (xy 68.409618 -65.492)
        (xy 68.407482 -65.491854)
        (xy 68.407471 -65.491854)
        (xy 68.387947 -65.490523)
        (xy 68.366596 -65.489067)
        (xy 68.185293 -65.443863)
        (xy 68.179186 -65.440831)
        (xy 68.179182 -65.44083)
        (xy 68.024034 -65.363814)
        (xy 68.024031 -65.363812)
        (xy 68.017925 -65.360781)
        (xy 67.872302 -65.243698)
        (xy 67.755219 -65.098075)
        (xy 67.752188 -65.091969)
        (xy 67.752186 -65.091966)
        (xy 67.67517 -64.936818)
        (xy 67.675169 -64.936814)
        (xy 67.672137 -64.930707)
        (xy 67.626933 -64.749404)
        (xy 67.624 -64.706382)
        (xy 2.930333 -64.706382)
        (xy 2.930333 -74.03817)
        (xy 10.131212 -74.03817)
        (xy 10.147786 -73.840793)
        (xy 10.202382 -73.650395)
        (xy 10.2052 -73.644913)
        (xy 10.205201 -73.644909)
        (xy 10.290099 -73.479715)
        (xy 10.290102 -73.479711)
        (xy 10.29292 -73.474227)
        (xy 10.415951 -73.319)
        (xy 10.56679 -73.190626)
        (xy 10.739691 -73.093995)
        (xy 10.928068 -73.032788)
        (xy 11.124746 -73.009336)
        (xy 11.130881 -73.009808)
        (xy 11.130883 -73.009808)
        (xy 11.316092 -73.024058)
        (xy 11.316097 -73.024059)
        (xy 11.322233 -73.024531)
        (xy 11.328165 -73.026187)
        (xy 11.328169 -73.026188)
        (xy 11.507064 -73.076137)
        (xy 11.507068 -73.076138)
        (xy 11.513008 -73.077797)
        (xy 11.551028 -73.097002)
        (xy 11.595201 -73.119315)
        (xy 11.665023 -73.132175)
        (xy 11.718127 -73.114109)
        (xy 11.772623 -73.080517)
        (xy 11.779571 -73.078212)
        (xy 11.779572 -73.078212)
        (xy 11.934301 -73.02689)
        (xy 11.934303 -73.02689)
        (xy 11.940832 -73.024724)
        (xy 12.045501 -73.014)
        (xy 12.343682 -73.014)
        (xy 12.646498 -73.014001)
        (xy 12.649742 -73.014338)
        (xy 12.64975 -73.014338)
        (xy 12.69679 -73.019219)
        (xy 12.752475 -73.024996)
        (xy 12.920586 -73.081082)
        (xy 13.071286 -73.174338)
        (xy 13.091532 -73.194619)
        (xy 13.191319 -73.29458)
        (xy 13.191323 -73.294585)
        (xy 13.19649 -73.299761)
        (xy 13.289483 -73.450623)
        (xy 13.297312 -73.474227)
        (xy 13.34311 -73.612301)
        (xy 13.34311 -73.612303)
        (xy 13.345276 -73.618832)
        (xy 13.356 -73.723501)
        (xy 13.355999 -74.324498)
        (xy 13.345004 -74.430475)
        (xy 13.288918 -74.598586)
        (xy 13.195662 -74.749286)
        (xy 13.19048 -74.754459)
        (xy 13.07542 -74.869319)
        (xy 13.075415 -74.869323)
        (xy 13.070239 -74.87449)
        (xy 12.919377 -74.967483)
        (xy 12.870144 -74.983813)
        (xy 12.757699 -75.02111)
        (xy 12.757697 -75.02111)
        (xy 12.751168 -75.023276)
        (xy 12.646499 -75.034)
        (xy 12.348318 -75.034)
        (xy 12.045502 -75.033999)
        (xy 12.042258 -75.033662)
        (xy 12.04225 -75.033662)
        (xy 11.99521 -75.028781)
        (xy 11.939525 -75.023004)
        (xy 11.771414 -74.966918)
        (xy 11.765182 -74.963062)
        (xy 11.765183 -74.963062)
        (xy 11.718893 -74.934417)
        (xy 11.650441 -74.915579)
        (xy 11.592663 -74.930725)
        (xy 11.539285 -74.959587)
        (xy 11.402803 -75.001835)
        (xy 11.355958 -75.016336)
        (xy 11.355955 -75.016337)
        (xy 11.350071 -75.018158)
        (xy 11.343946 -75.018802)
        (xy 11.343945 -75.018802)
        (xy 11.159213 -75.038218)
        (xy 11.159212 -75.038218)
        (xy 11.153085 -75.038862)
        (xy 11.032606 -75.027898)
        (xy 10.96197 -75.02147)
        (xy 10.961969 -75.02147)
        (xy 10.955829 -75.020911)
        (xy 10.765817 -74.964987)
        (xy 10.590285 -74.873221)
        (xy 10.585485 -74.869361)
        (xy 10.585484 -74.869361)
        (xy 10.553504 -74.843648)
        (xy 10.435921 -74.749109)
        (xy 10.308603 -74.597378)
        (xy 10.213181 -74.423807)
        (xy 10.153291 -74.235007)
        (xy 10.152605 -74.22889)
        (xy 10.152604 -74.228886)
        (xy 10.149723 -74.203199)
        (xy 10.131212 -74.03817)
        (xy 2.930333 -74.03817)
        (xy 2.930333 -75.100382)
        (xy 67.624 -75.100382)
        (xy 67.624 -74.251618)
        (xy 67.626933 -74.208596)
        (xy 67.672137 -74.027293)
        (xy 67.675169 -74.021186)
        (xy 67.67517 -74.021182)
        (xy 67.752186 -73.866034)
        (xy 67.755219 -73.859925)
        (xy 67.759492 -73.85461)
        (xy 67.759493 -73.854609)
        (xy 67.862319 -73.726718)
        (xy 67.872302 -73.714302)
        (xy 67.877614 -73.710031)
        (xy 67.999167 -73.612301)
        (xy 68.017925 -73.597219)
        (xy 68.024031 -73.594188)
        (xy 68.024034 -73.594186)
        (xy 68.179182 -73.51717)
        (xy 68.179186 -73.517169)
        (xy 68.185293 -73.514137)
        (xy 68.366596 -73.468933)
        (xy 68.387947 -73.467477)
        (xy 68.407471 -73.466146)
        (xy 68.407482 -73.466146)
        (xy 68.409618 -73.466)
        (xy 69.258382 -73.466)
        (xy 69.260518 -73.466146)
        (xy 69.260529 -73.466146)
        (xy 69.280053 -73.467477)
        (xy 69.301404 -73.468933)
        (xy 69.482707 -73.514137)
        (xy 69.488814 -73.517169)
        (xy 69.488818 -73.51717)
        (xy 69.643966 -73.594186)
        (xy 69.643969 -73.594188)
        (xy 69.650075 -73.597219)
        (xy 69.668834 -73.612301)
        (xy 69.790386 -73.710031)
        (xy 69.795698 -73.714302)
        (xy 69.805681 -73.726718)
        (xy 69.908507 -73.854609)
        (xy 69.908508 -73.85461)
        (xy 69.912781 -73.859925)
        (xy 69.915814 -73.866034)
        (xy 69.99283 -74.021182)
        (xy 69.992831 -74.021186)
        (xy 69.995863 -74.027293)
        (xy 70.041067 -74.208596)
        (xy 70.044 -74.251618)
        (xy 70.044 -75.100382)
        (xy 70.043787 -75.103515)
        (xy 70.042523 -75.122053)
        (xy 70.041067 -75.143404)
        (xy 69.995863 -75.324707)
        (xy 69.992831 -75.330814)
        (xy 69.99283 -75.330818)
        (xy 69.915814 -75.485966)
        (xy 69.915812 -75.485969)
        (xy 69.912781 -75.492075)
        (xy 69.795698 -75.637698)
        (xy 69.650075 -75.754781)
        (xy 69.643969 -75.757812)
        (xy 69.643966 -75.757814)
        (xy 69.488818 -75.83483)
        (xy 69.488814 -75.834831)
        (xy 69.482707 -75.837863)
        (xy 69.430017 -75.851)
        (xy 80.908996 -75.851)
        (xy 80.928974 -75.622652)
        (xy 80.930398 -75.617338)
        (xy 80.930398 -75.617337)
        (xy 80.965599 -75.485966)
        (xy 80.9883 -75.401242)
        (xy 80.990622 -75.396262)
        (xy 80.990623 -75.39626)
        (xy 81.082847 -75.198485)
        (xy 81.08285 -75.19848)
        (xy 81.085173 -75.193498)
        (xy 81.088329 -75.188991)
        (xy 81.08833 -75.188989)
        (xy 81.207239 -75.01917)
        (xy 81.216648 -75.005732)
        (xy 81.378732 -74.843648)
        (xy 81.38324 -74.840491)
        (xy 81.383243 -74.840489)
        (xy 81.561989 -74.71533)
        (xy 81.566498 -74.712173)
        (xy 81.57148 -74.70985)
        (xy 81.571485 -74.709847)
        (xy 81.76926 -74.617623)
        (xy 81.774242 -74.6153)
        (xy 81.77955 -74.613878)
        (xy 81.779552 -74.613877)
        (xy 81.990337 -74.557398)
        (xy 81.990338 -74.557398)
        (xy 81.995652 -74.555974)
        (xy 82.224 -74.535996)
        (xy 82.452348 -74.555974)
        (xy 82.457662 -74.557398)
        (xy 82.457663 -74.557398)
        (xy 82.668448 -74.613877)
        (xy 82.66845 -74.613878)
        (xy 82.673758 -74.6153)
        (xy 82.67874 -74.617623)
        (xy 82.876515 -74.709847)
        (xy 82.87652 -74.70985)
        (xy 82.881502 -74.712173)
        (xy 82.886011 -74.71533)
        (xy 83.064757 -74.840489)
        (xy 83.06476 -74.840491)
        (xy 83.069268 -74.843648)
        (xy 83.231352 -75.005732)
        (xy 83.240762 -75.01917)
        (xy 83.35967 -75.188989)
        (xy 83.359671 -75.188991)
        (xy 83.362827 -75.193498)
        (xy 83.36515 -75.19848)
        (xy 83.365153 -75.198485)
        (xy 83.457377 -75.39626)
        (xy 83.457378 -75.396262)
        (xy 83.4597 -75.401242)
        (xy 83.482402 -75.485966)
        (xy 83.517602 -75.617337)
        (xy 83.517602 -75.617338)
        (xy 83.519026 -75.622652)
        (xy 83.539004 -75.851)
        (xy 83.519026 -76.079348)
        (xy 83.4597 -76.300758)
        (xy 83.457377 -76.30574)
        (xy 83.365153 -76.503515)
        (xy 83.36515 -76.50352)
        (xy 83.362827 -76.508502)
        (xy 83.231352 -76.696268)
        (xy 83.069268 -76.858352)
        (xy 83.06476 -76.861509)
        (xy 83.064757 -76.861511)
        (xy 82.886011 -76.98667)
        (xy 82.886009 -76.986671)
        (xy 82.881502 -76.989827)
        (xy 82.87652 -76.99215)
        (xy 82.876515 -76.992153)
        (xy 82.67874 -77.084377)
        (xy 82.678738 -77.084378)
        (xy 82.673758 -77.0867)
        (xy 82.66845 -77.088122)
        (xy 82.668448 -77.088123)
        (xy 82.457663 -77.144602)
        (xy 82.457662 -77.144602)
        (xy 82.452348 -77.146026)
        (xy 82.224 -77.166004)
        (xy 81.995652 -77.146026)
        (xy 81.990338 -77.144602)
        (xy 81.990337 -77.144602)
        (xy 81.779552 -77.088123)
        (xy 81.77955 -77.088122)
        (xy 81.774242 -77.0867)
        (xy 81.769262 -77.084378)
        (xy 81.76926 -77.084377)
        (xy 81.571485 -76.992153)
        (xy 81.57148 -76.99215)
        (xy 81.566498 -76.989827)
        (xy 81.561991 -76.986671)
        (xy 81.561989 -76.98667)
        (xy 81.383243 -76.861511)
        (xy 81.38324 -76.861509)
        (xy 81.378732 -76.858352)
        (xy 81.216648 -76.696268)
        (xy 81.085173 -76.508502)
        (xy 81.08285 -76.50352)
        (xy 81.082847 -76.503515)
        (xy 80.990623 -76.30574)
        (xy 80.9883 -76.300758)
        (xy 80.928974 -76.079348)
        (xy 80.908996 -75.851)
        (xy 69.430017 -75.851)
        (xy 69.301404 -75.883067)
        (xy 69.280053 -75.884523)
        (xy 69.260529 -75.885854)
        (xy 69.260518 -75.885854)
        (xy 69.258382 -75.886)
        (xy 68.409618 -75.886)
        (xy 68.407482 -75.885854)
        (xy 68.407471 -75.885854)
        (xy 68.387947 -75.884523)
        (xy 68.366596 -75.883067)
        (xy 68.185293 -75.837863)
        (xy 68.179186 -75.834831)
        (xy 68.179182 -75.83483)
        (xy 68.024034 -75.757814)
        (xy 68.024031 -75.757812)
        (xy 68.017925 -75.754781)
        (xy 67.872302 -75.637698)
        (xy 67.755219 -75.492075)
        (xy 67.752188 -75.485969)
        (xy 67.752186 -75.485966)
        (xy 67.67517 -75.330818)
        (xy 67.675169 -75.330814)
        (xy 67.672137 -75.324707)
        (xy 67.626933 -75.143404)
        (xy 67.625477 -75.122053)
        (xy 67.624214 -75.103515)
        (xy 67.624 -75.100382)
        (xy 2.930333 -75.100382)
        (xy 2.930333 -78.360987)
        (xy 2.950335 -78.429108)
        (xy 2.967238 -78.450082)
        (xy 4.334303 -79.817147)
        (xy 106.733186 -79.817147)
        (xy 106.749747 -79.529929)
        (xy 106.750572 -79.525724)
        (xy 106.750573 -79.525716)
        (xy 106.774274 -79.404911)
        (xy 106.805134 -79.247616)
        (xy 106.806521 -79.243566)
        (xy 106.806522 -79.243561)
        (xy 106.896935 -78.979488)
        (xy 106.898324 -78.975432)
        (xy 107.027591 -78.718413)
        (xy 107.190543 -78.481316)
        (xy 107.384166 -78.268528)
        (xy 107.387455 -78.265778)
        (xy 107.601583 -78.086738)
        (xy 107.601588 -78.086734)
        (xy 107.604875 -78.083986)
        (xy 107.726732 -78.007545)
        (xy 107.844948 -77.933388)
        (xy 107.844952 -77.933386)
        (xy 107.848588 -77.931105)
        (xy 107.852498 -77.92934)
        (xy 107.852499 -77.929339)
        (xy 108.106883 -77.81448)
        (xy 108.106887 -77.814478)
        (xy 108.110795 -77.812714)
        (xy 108.114914 -77.811494)
        (xy 108.382529 -77.732222)
        (xy 108.382534 -77.732221)
        (xy 108.386642 -77.731004)
        (xy 108.390876 -77.730356)
        (xy 108.390881 -77.730355)
        (xy 108.64045 -77.692166)
        (xy 108.671027 -77.687487)
        (xy 108.817483 -77.685186)
        (xy 108.954396 -77.683035)
        (xy 108.954402 -77.683035)
        (xy 108.958687 -77.682968)
        (xy 109.244298 -77.717531)
        (xy 109.522577 -77.790536)
        (xy 109.526537 -77.792176)
        (xy 109.526542 -77.792178)
        (xy 109.66967 -77.851464)
        (xy 109.788372 -77.900632)
        (xy 109.837499 -77.929339)
        (xy 110.033069 -78.043621)
        (xy 110.03307 -78.043621)
        (xy 110.036767 -78.045782)
        (xy 110.082579 -78.081703)
        (xy 110.259792 -78.220657)
        (xy 110.263164 -78.223301)
        (xy 110.463375 -78.429902)
        (xy 110.633694 -78.661764)
        (xy 110.635737 -78.665527)
        (xy 110.635741 -78.665533)
        (xy 110.768921 -78.91082)
        (xy 110.770971 -78.914595)
        (xy 110.872664 -79.183718)
        (xy 110.936892 -79.464152)
        (xy 110.962466 -79.750708)
        (xy 110.96293 -79.795)
        (xy 110.943362 -80.082029)
        (xy 110.937955 -80.108141)
        (xy 110.885892 -80.359541)
        (xy 110.885021 -80.363747)
        (xy 110.877479 -80.385047)
        (xy 110.790418 -80.630899)
        (xy 110.788987 -80.63494)
        (xy 110.657036 -80.890591)
        (xy 110.594436 -80.979662)
        (xy 110.494073 -81.122464)
        (xy 110.494068 -81.12247)
        (xy 110.491609 -81.125969)
        (xy 110.356561 -81.271298)
        (xy 110.29869 -81.333575)
        (xy 110.298687 -81.333578)
        (xy 110.295769 -81.336718)
        (xy 110.073139 -81.518938)
        (xy 109.827839 -81.669259)
        (xy 109.564407 -81.784897)
        (xy 109.560279 -81.786073)
        (xy 109.560276 -81.786074)
        (xy 109.291847 -81.862538)
        (xy 109.291848 -81.862538)
        (xy 109.287719 -81.863714)
        (xy 109.075268 -81.89395)
        (xy 109.007146 -81.903645)
        (xy 109.007144 -81.903645)
        (xy 109.002894 -81.90425)
        (xy 108.998605 -81.904272)
        (xy 108.998598 -81.904273)
        (xy 108.719488 -81.905735)
        (xy 108.719481 -81.905735)
        (xy 108.715202 -81.905757)
        (xy 108.710958 -81.905198)
        (xy 108.710954 -81.905198)
        (xy 108.585208 -81.888643)
        (xy 108.429969 -81.868205)
        (xy 108.425829 -81.867072)
        (xy 108.425827 -81.867072)
        (xy 108.409254 -81.862538)
        (xy 108.15247 -81.79229)
        (xy 108.148522 -81.790606)
        (xy 107.891794 -81.681102)
        (xy 107.89179 -81.6811)
        (xy 107.887842 -81.679416)
        (xy 107.87375 -81.670982)
        (xy 107.644663 -81.533877)
        (xy 107.644659 -81.533874)
        (xy 107.640981 -81.531673)
        (xy 107.416455 -81.351794)
        (xy 107.413511 -81.348692)
        (xy 107.413507 -81.348688)
        (xy 107.340798 -81.272068)
        (xy 107.218418 -81.143107)
        (xy 107.050536 -80.909475)
        (xy 106.915915 -80.65522)
        (xy 106.817046 -80.385047)
        (xy 106.755758 -80.103955)
        (xy 106.733186 -79.817147)
        (xy 4.334303 -79.817147)
        (xy 6.605979 -82.088823)
        (xy 6.668291 -82.122849)
        (xy 6.739106 -82.117784)
        (xy 6.756736 -82.109609)
        (xy 6.793777 -82.088823)
        (xy 6.831883 -82.067439)
        (xy 6.834797 -82.066172)
        (xy 6.834801 -82.06617)
        (xy 6.871242 -82.050325)
        (xy 7.17659 -81.917556)
        (xy 7.334777 -81.867072)
        (xy 7.531634 -81.804246)
        (xy 7.531643 -81.804244)
        (xy 7.534678 -81.803275)
        (xy 7.616303 -81.786074)
        (xy 7.899363 -81.726424)
        (xy 7.899368 -81.726423)
        (xy 7.902482 -81.725767)
        (xy 8.276236 -81.685824)
        (xy 8.279423 -81.685807)
        (xy 8.279429 -81.685807)
        (xy 8.449944 -81.684915)
        (xy 8.652113 -81.683856)
        (xy 8.79374 -81.697493)
        (xy 9.0231 -81.719578)
        (xy 9.023105 -81.719579)
        (xy 9.026265 -81.719883)
        (xy 9.029385 -81.720506)
        (xy 9.029389 -81.720507)
        (xy 9.391732 -81.79291)
        (xy 9.391731 -81.79291)
        (xy 9.39486 -81.793535)
        (xy 9.397896 -81.794469)
        (xy 9.397904 -81.794471)
        (xy 9.751083 -81.903124)
        (xy 9.751086 -81.903125)
        (xy 9.754125 -81.90406)
        (xy 9.757053 -81.905297)
        (xy 9.757059 -81.905299)
        (xy 9.912706 -81.971047)
        (xy 10.100383 -82.050325)
        (xy 10.103186 -82.051859)
        (xy 10.103191 -82.051862)
        (xy 10.245068 -82.129538)
        (xy 10.430086 -82.230832)
        (xy 10.73986 -82.443734)
        (xy 10.742299 -82.445802)
        (xy 10.742305 -82.445807)
        (xy 11.024094 -82.684781)
        (xy 11.024102 -82.684789)
        (xy 11.026534 -82.686851)
        (xy 11.028742 -82.689146)
        (xy 11.02875 -82.689153)
        (xy 11.233071 -82.901475)
        (xy 11.287171 -82.957693)
        (xy 11.519104 -83.253488)
        (xy 11.580464 -83.350549)
        (xy 11.705514 -83.548359)
        (xy 11.719957 -83.571206)
        (xy 11.721377 -83.574055)
        (xy 11.721382 -83.574063)
        (xy 11.886255 -83.904748)
        (xy 11.887675 -83.907596)
        (xy 12.02054 -84.259213)
        (xy 12.030412 -84.296313)
        (xy 12.091727 -84.526755)
        (xy 12.117191 -84.622456)
        (xy 12.17664 -84.993607)
        (xy 12.198277 -85.368866)
        (xy 12.198368 -85.3948)
        (xy 12.198283 -85.396483)
        (xy 12.179512 -85.767028)
        (xy 12.179511 -85.767033)
        (xy 12.179351 -85.770201)
        (xy 12.1596 -85.899274)
        (xy 79.714 -85.899274)
        (xy 79.714 -83.802726)
        (xy 79.720775 -83.740359)
        (xy 79.723549 -83.73296)
        (xy 79.75061 -83.660776)
        (xy 79.772056 -83.603568)
        (xy 79.777438 -83.596386)
        (xy 79.777439 -83.596385)
        (xy 79.798335 -83.568503)
        (xy 79.859667 -83.486667)
        (xy 79.976568 -83.399056)
        (xy 79.984969 -83.395906)
        (xy 79.984972 -83.395905)
        (xy 80.066795 -83.365231)
        (xy 80.113359 -83.347775)
        (xy 80.121212 -83.346922)
        (xy 80.121216 -83.346921)
        (xy 80.172327 -83.341369)
        (xy 80.172331 -83.341369)
        (xy 80.175726 -83.341)
        (xy 84.272274 -83.341)
        (xy 84.275669 -83.341369)
        (xy 84.275673 -83.341369)
        (xy 84.326784 -83.346921)
        (xy 84.326788 -83.346922)
        (xy 84.334641 -83.347775)
        (xy 84.381205 -83.365231)
        (xy 84.463028 -83.395905)
        (xy 84.463031 -83.395906)
        (xy 84.471432 -83.399056)
        (xy 84.588333 -83.486667)
        (xy 84.649665 -83.568503)
        (xy 84.670561 -83.596385)
        (xy 84.670562 -83.596386)
        (xy 84.675944 -83.603568)
        (xy 84.697391 -83.660776)
        (xy 84.724451 -83.73296)
        (xy 84.727225 -83.740359)
        (xy 84.734 -83.802726)
        (xy 84.734 -84.252689)
        (xy 102.283505 -84.252689)
        (xy 102.309142 -83.98398)
        (xy 102.310227 -83.979546)
        (xy 102.310228 -83.97954)
        (xy 102.354326 -83.799327)
        (xy 102.3733 -83.721788)
        (xy 102.474636 -83.471603)
        (xy 102.611025 -83.238667)
        (xy 102.779612 -83.027859)
        (xy 102.976865 -82.843596)
        (xy 103.19865 -82.689738)
        (xy 103.208614 -82.684781)
        (xy 103.436237 -82.57154)
        (xy 103.43624 -82.571539)
        (xy 103.440324 -82.569507)
        (xy 103.696822 -82.485423)
        (xy 103.701313 -82.484643)
        (xy 103.701314 -82.484643)
        (xy 103.95899 -82.439902)
        (xy 103.958998 -82.439901)
        (xy 103.962771 -82.439246)
        (xy 103.966608 -82.439055)
        (xy 104.046493 -82.435078)
        (xy 104.046501 -82.435078)
        (xy 104.048064 -82.435)
        (xy 105.216566 -82.435)
        (xy 105.218834 -82.435165)
        (xy 105.218846 -82.435165)
        (xy 105.349435 -82.444641)
        (xy 105.417221 -82.449559)
        (xy 105.421676 -82.450543)
        (xy 105.421679 -82.450543)
        (xy 105.676342 -82.506767)
        (xy 105.676346 -82.506768)
        (xy 105.680802 -82.507752)
        (xy 105.849166 -82.57154)
        (xy 105.928951 -82.601768)
        (xy 105.928954 -82.601769)
        (xy 105.933221 -82.603386)
        (xy 105.937209 -82.605601)
        (xy 105.937213 -82.605603)
        (xy 106.165199 -82.732237)
        (xy 106.1652 -82.732238)
        (xy 106.169192 -82.734455)
        (xy 106.21966 -82.772971)
        (xy 106.380145 -82.895449)
        (xy 106.380149 -82.895453)
        (xy 106.38377 -82.898216)
        (xy 106.57246 -83.091238)
        (xy 106.605366 -83.136447)
        (xy 106.728625 -83.305785)
        (xy 106.728629 -83.305791)
        (xy 106.731311 -83.309476)
        (xy 106.751462 -83.347775)
        (xy 106.854868 -83.544318)
        (xy 106.854868 -83.544319)
        (xy 106.856994 -83.548359)
        (xy 106.946875 -83.802883)
        (xy 106.981694 -83.97954)
        (xy 106.998193 -84.063245)
        (xy 106.998194 -84.063251)
        (xy 106.999074 -84.067717)
        (xy 107.008056 -84.248133)
        (xy 107.012268 -84.332742)
        (xy 107.012268 -84.332748)
        (xy 107.012495 -84.337311)
        (xy 106.986858 -84.60602)
        (xy 106.982837 -84.622456)
        (xy 106.923786 -84.863774)
        (xy 106.9227 -84.868212)
        (xy 106.821364 -85.118397)
        (xy 106.684975 -85.351333)
        (xy 106.516388 -85.562141)
        (xy 106.319135 -85.746404)
        (xy 106.09735 -85.900262)
        (xy 105.920523 -85.988232)
        (xy 105.859763 -86.01846)
        (xy 105.85976 -86.018461)
        (xy 105.855676 -86.020493)
        (xy 105.599178 -86.104577)
        (xy 105.5932 -86.105615)
        (xy 105.33701 -86.150098)
        (xy 105.337002 -86.150099)
        (xy 105.333229 -86.150754)
        (xy 105.32204 -86.151311)
        (xy 105.249507 -86.154922)
        (xy 105.249499 -86.154922)
        (xy 105.247936 -86.155)
        (xy 104.079434 -86.155)
        (xy 104.077166 -86.154835)
        (xy 104.077154 -86.154835)
        (xy 103.946565 -86.145359)
        (xy 103.878779 -86.140441)
        (xy 103.874324 -86.139457)
        (xy 103.874321 -86.139457)
        (xy 103.619658 -86.083233)
        (xy 103.619654 -86.083232)
        (xy 103.615198 -86.082248)
        (xy 103.488988 -86.034431)
        (xy 103.367049 -85.988232)
        (xy 103.367046 -85.988231)
        (xy 103.362779 -85.986614)
        (xy 103.358791 -85.984399)
        (xy 103.358787 -85.984397)
        (xy 103.202632 -85.897661)
        (xy 103.126808 -85.855545)
        (xy 103.123176 -85.852773)
        (xy 102.915855 -85.694551)
        (xy 102.915851 -85.694547)
        (xy 102.91223 -85.691784)
        (xy 102.72354 -85.498762)
        (xy 102.720855 -85.495073)
        (xy 102.567375 -85.284215)
        (xy 102.567371 -85.284209)
        (xy 102.564689 -85.280524)
        (xy 102.439006 -85.041641)
        (xy 102.437488 -85.037342)
        (xy 102.437487 -85.03734)
        (xy 102.420931 -84.990456)
        (xy 102.349125 -84.787117)
        (xy 102.322613 -84.652605)
        (xy 102.313431 -84.60602)
        (xy 102.296926 -84.522283)
        (xy 102.296699 -84.51773)
        (xy 102.296699 -84.517727)
        (xy 102.28383 -84.259213)
        (xy 102.283505 -84.252689)
        (xy 84.734 -84.252689)
        (xy 84.734 -85.899274)
        (xy 84.727225 -85.961641)
        (xy 84.682618 -86.08063)
        (xy 84.679095 -86.090028)
        (xy 84.679094 -86.090031)
        (xy 84.675944 -86.098432)
        (xy 84.643474 -86.141758)
        (xy 84.593714 -86.208153)
        (xy 84.588333 -86.215333)
        (xy 84.471432 -86.302944)
        (xy 84.463031 -86.306094)
        (xy 84.463028 -86.306095)
        (xy 84.381205 -86.336769)
        (xy 84.334641 -86.354225)
        (xy 84.326788 -86.355078)
        (xy 84.326784 -86.355079)
        (xy 84.275673 -86.360631)
        (xy 84.275669 -86.360631)
        (xy 84.272274 -86.361)
        (xy 80.175726 -86.361)
        (xy 80.172331 -86.360631)
        (xy 80.172327 -86.360631)
        (xy 80.121216 -86.355079)
        (xy 80.121212 -86.355078)
        (xy 80.113359 -86.354225)
        (xy 80.066795 -86.336769)
        (xy 79.984972 -86.306095)
        (xy 79.984969 -86.306094)
        (xy 79.976568 -86.302944)
        (xy 79.859667 -86.215333)
        (xy 79.854286 -86.208153)
        (xy 79.804527 -86.141758)
        (xy 79.772056 -86.098432)
        (xy 79.768906 -86.090031)
        (xy 79.768905 -86.090028)
        (xy 79.765382 -86.08063)
        (xy 79.720775 -85.961641)
        (xy 79.714 -85.899274)
        (xy 12.1596 -85.899274)
        (xy 12.150057 -85.961641)
        (xy 12.122979 -86.138598)
        (xy 12.122977 -86.138607)
        (xy 12.122495 -86.141758)
        (xy 12.028381 -86.505667)
        (xy 12.023807 -86.518034)
        (xy 11.899081 -86.855213)
        (xy 11.897975 -86.858203)
        (xy 11.732609 -87.195755)
        (xy 11.533979 -87.514868)
        (xy 11.304117 -87.812275)
        (xy 11.045377 -88.08493)
        (xy 10.760408 -88.330043)
        (xy 10.584598 -88.452689)
        (xy 96.783505 -88.452689)
        (xy 96.809142 -88.18398)
        (xy 96.8733 -87.921788)
        (xy 96.974636 -87.671603)
        (xy 97.111025 -87.438667)
        (xy 97.279612 -87.227859)
        (xy 97.476865 -87.043596)
        (xy 97.69865 -86.889738)
        (xy 97.732355 -86.87297)
        (xy 97.936237 -86.77154)
        (xy 97.93624 -86.771539)
        (xy 97.940324 -86.769507)
        (xy 98.196822 -86.685423)
        (xy 98.201313 -86.684643)
        (xy 98.201314 -86.684643)
        (xy 98.45899 -86.639902)
        (xy 98.458998 -86.639901)
        (xy 98.462771 -86.639246)
        (xy 98.466608 -86.639055)
        (xy 98.546493 -86.635078)
        (xy 98.546501 -86.635078)
        (xy 98.548064 -86.635)
        (xy 99.716566 -86.635)
        (xy 99.718834 -86.635165)
        (xy 99.718846 -86.635165)
        (xy 99.849435 -86.644641)
        (xy 99.917221 -86.649559)
        (xy 99.921676 -86.650543)
        (xy 99.921679 -86.650543)
        (xy 100.176342 -86.706767)
        (xy 100.176346 -86.706768)
        (xy 100.180802 -86.707752)
        (xy 100.349166 -86.77154)
        (xy 100.428951 -86.801768)
        (xy 100.428954 -86.801769)
        (xy 100.433221 -86.803386)
        (xy 100.437209 -86.805601)
        (xy 100.437213 -86.805603)
        (xy 100.665199 -86.932237)
        (xy 100.6652 -86.932238)
        (xy 100.669192 -86.934455)
        (xy 100.71966 -86.972971)
        (xy 100.880145 -87.095449)
        (xy 100.880149 -87.095453)
        (xy 100.88377 -87.098216)
        (xy 101.07246 -87.291238)
        (xy 101.105366 -87.336447)
        (xy 101.228625 -87.505785)
        (xy 101.228629 -87.505791)
        (xy 101.231311 -87.509476)
        (xy 101.238319 -87.522795)
        (xy 101.354868 -87.744318)
        (xy 101.354868 -87.744319)
        (xy 101.356994 -87.748359)
        (xy 101.446875 -88.002883)
        (xy 101.481694 -88.17954)
        (xy 101.498193 -88.263245)
        (xy 101.498194 -88.263251)
        (xy 101.499074 -88.267717)
        (xy 101.502177 -88.330043)
        (xy 101.508283 -88.452689)
        (xy 102.283505 -88.452689)
        (xy 102.309142 -88.18398)
        (xy 102.3733 -87.921788)
        (xy 102.474636 -87.671603)
        (xy 102.611025 -87.438667)
        (xy 102.779612 -87.227859)
        (xy 102.976865 -87.043596)
        (xy 103.19865 -86.889738)
        (xy 103.232355 -86.87297)
        (xy 103.436237 -86.77154)
        (xy 103.43624 -86.771539)
        (xy 103.440324 -86.769507)
        (xy 103.696822 -86.685423)
        (xy 103.701313 -86.684643)
        (xy 103.701314 -86.684643)
        (xy 103.95899 -86.639902)
        (xy 103.958998 -86.639901)
        (xy 103.962771 -86.639246)
        (xy 103.966608 -86.639055)
        (xy 104.046493 -86.635078)
        (xy 104.046501 -86.635078)
        (xy 104.048064 -86.635)
        (xy 105.216566 -86.635)
        (xy 105.218834 -86.635165)
        (xy 105.218846 -86.635165)
        (xy 105.349435 -86.644641)
        (xy 105.417221 -86.649559)
        (xy 105.421676 -86.650543)
        (xy 105.421679 -86.650543)
        (xy 105.676342 -86.706767)
        (xy 105.676346 -86.706768)
        (xy 105.680802 -86.707752)
        (xy 105.849166 -86.77154)
        (xy 105.928951 -86.801768)
        (xy 105.928954 -86.801769)
        (xy 105.933221 -86.803386)
        (xy 105.937209 -86.805601)
        (xy 105.937213 -86.805603)
        (xy 106.165199 -86.932237)
        (xy 106.1652 -86.932238)
        (xy 106.169192 -86.934455)
        (xy 106.21966 -86.972971)
        (xy 106.380145 -87.095449)
        (xy 106.380149 -87.095453)
        (xy 106.38377 -87.098216)
        (xy 106.57246 -87.291238)
        (xy 106.605366 -87.336447)
        (xy 106.728625 -87.505785)
        (xy 106.728629 -87.505791)
        (xy 106.731311 -87.509476)
        (xy 106.738319 -87.522795)
        (xy 106.854868 -87.744318)
        (xy 106.854868 -87.744319)
        (xy 106.856994 -87.748359)
        (xy 106.946875 -88.002883)
        (xy 106.981694 -88.17954)
        (xy 106.998193 -88.263245)
        (xy 106.998194 -88.263251)
        (xy 106.999074 -88.267717)
        (xy 107.002177 -88.330043)
        (xy 107.012268 -88.532742)
        (xy 107.012268 -88.532748)
        (xy 107.012495 -88.537311)
        (xy 106.986858 -88.80602)
        (xy 106.940983 -88.993498)
        (xy 106.923786 -89.063774)
        (xy 106.9227 -89.068212)
        (xy 106.821364 -89.318397)
        (xy 106.684975 -89.551333)
        (xy 106.516388 -89.762141)
        (xy 106.319135 -89.946404)
        (xy 106.09735 -90.100262)
        (xy 105.920523 -90.188232)
        (xy 105.859763 -90.21846)
        (xy 105.85976 -90.218461)
        (xy 105.855676 -90.220493)
        (xy 105.599178 -90.304577)
        (xy 105.594686 -90.305357)
        (xy 105.33701 -90.350098)
        (xy 105.337002 -90.350099)
        (xy 105.333229 -90.350754)
        (xy 105.325348 -90.351146)
        (xy 105.249507 -90.354922)
        (xy 105.249499 -90.354922)
        (xy 105.247936 -90.355)
        (xy 104.079434 -90.355)
        (xy 104.077166 -90.354835)
        (xy 104.077154 -90.354835)
        (xy 103.946565 -90.345359)
        (xy 103.878779 -90.340441)
        (xy 103.874324 -90.339457)
        (xy 103.874321 -90.339457)
        (xy 103.619658 -90.283233)
        (xy 103.619654 -90.283232)
        (xy 103.615198 -90.282248)
        (xy 103.488989 -90.234431)
        (xy 103.367049 -90.188232)
        (xy 103.367046 -90.188231)
        (xy 103.362779 -90.186614)
        (xy 103.358791 -90.184399)
        (xy 103.358787 -90.184397)
        (xy 103.202632 -90.097661)
        (xy 103.126808 -90.055545)
        (xy 103.07634 -90.017029)
        (xy 102.915855 -89.894551)
        (xy 102.915851 -89.894547)
        (xy 102.91223 -89.891784)
        (xy 102.72354 -89.698762)
        (xy 102.720855 -89.695073)
        (xy 102.567375 -89.484215)
        (xy 102.567371 -89.484209)
        (xy 102.564689 -89.480524)
        (xy 102.439006 -89.241641)
        (xy 102.349125 -88.987117)
        (xy 102.327339 -88.876585)
        (xy 102.298283 -88.729167)
        (xy 102.296926 -88.722283)
        (xy 102.296699 -88.71773)
        (xy 102.296699 -88.717727)
        (xy 102.287718 -88.537311)
        (xy 102.283505 -88.452689)
        (xy 101.508283 -88.452689)
        (xy 101.512268 -88.532742)
        (xy 101.512268 -88.532748)
        (xy 101.512495 -88.537311)
        (xy 101.486858 -88.80602)
        (xy 101.440983 -88.993498)
        (xy 101.423786 -89.063774)
        (xy 101.4227 -89.068212)
        (xy 101.321364 -89.318397)
        (xy 101.184975 -89.551333)
        (xy 101.016388 -89.762141)
        (xy 100.819135 -89.946404)
        (xy 100.59735 -90.100262)
        (xy 100.420523 -90.188232)
        (xy 100.359763 -90.21846)
        (xy 100.35976 -90.218461)
        (xy 100.355676 -90.220493)
        (xy 100.099178 -90.304577)
        (xy 100.094686 -90.305357)
        (xy 99.83701 -90.350098)
        (xy 99.837002 -90.350099)
        (xy 99.833229 -90.350754)
        (xy 99.825348 -90.351146)
        (xy 99.749507 -90.354922)
        (xy 99.749499 -90.354922)
        (xy 99.747936 -90.355)
        (xy 98.579434 -90.355)
        (xy 98.577166 -90.354835)
        (xy 98.577154 -90.354835)
        (xy 98.446565 -90.345359)
        (xy 98.378779 -90.340441)
        (xy 98.374324 -90.339457)
        (xy 98.374321 -90.339457)
        (xy 98.119658 -90.283233)
        (xy 98.119654 -90.283232)
        (xy 98.115198 -90.282248)
        (xy 97.988989 -90.234431)
        (xy 97.867049 -90.188232)
        (xy 97.867046 -90.188231)
        (xy 97.862779 -90.186614)
        (xy 97.858791 -90.184399)
        (xy 97.858787 -90.184397)
        (xy 97.702632 -90.097661)
        (xy 97.626808 -90.055545)
        (xy 97.57634 -90.017029)
        (xy 97.415855 -89.894551)
        (xy 97.415851 -89.894547)
        (xy 97.41223 -89.891784)
        (xy 97.22354 -89.698762)
        (xy 97.220855 -89.695073)
        (xy 97.067375 -89.484215)
        (xy 97.067371 -89.484209)
        (xy 97.064689 -89.480524)
        (xy 96.939006 -89.241641)
        (xy 96.849125 -88.987117)
        (xy 96.827339 -88.876585)
        (xy 96.798283 -88.729167)
        (xy 96.796926 -88.722283)
        (xy 96.796699 -88.71773)
        (xy 96.796699 -88.717727)
        (xy 96.787718 -88.537311)
        (xy 96.783505 -88.452689)
        (xy 10.584598 -88.452689)
        (xy 10.452127 -88.545102)
        (xy 10.141982 -88.717727)
        (xy 10.126477 -88.726357)
        (xy 10.126475 -88.726358)
        (xy 10.123692 -88.727907)
        (xy 10.120772 -88.729165)
        (xy 10.120767 -88.729167)
        (xy 9.781398 -88.875322)
        (xy 9.781388 -88.875326)
        (xy 9.778464 -88.876585)
        (xy 9.419979 -88.989615)
        (xy 9.061879 -89.063774)
        (xy 9.055032 -89.065192)
        (xy 9.055029 -89.065192)
        (xy 9.051907 -89.065839)
        (xy 8.764334 -89.095557)
        (xy 8.681172 -89.104151)
        (xy 8.681169 -89.104151)
        (xy 8.678016 -89.104477)
        (xy 8.67485 -89.104483)
        (xy 8.674841 -89.104483)
        (xy 8.489371 -89.104806)
        (xy 8.302134 -89.105133)
        (xy 8.153197 -89.090267)
        (xy 7.931284 -89.068118)
        (xy 7.931278 -89.068117)
        (xy 7.928111 -89.067801)
        (xy 7.812114 -89.044201)
        (xy 7.5629 -88.993498)
        (xy 7.562896 -88.993497)
        (xy 7.559775 -88.992862)
        (xy 7.556733 -88.991915)
        (xy 7.556727 -88.991913)
        (xy 7.380336 -88.936973)
        (xy 7.200897 -88.881084)
        (xy 6.855153 -88.733611)
        (xy 6.526081 -88.551954)
        (xy 6.217052 -88.337972)
        (xy 6.214624 -88.335898)
        (xy 6.214621 -88.335896)
        (xy 6.031552 -88.17954)
        (xy 5.931229 -88.093856)
        (xy 5.929029 -88.091554)
        (xy 5.929026 -88.091551)
        (xy 5.848562 -88.00735)
        (xy 5.671539 -87.822106)
        (xy 5.669582 -87.819592)
        (xy 5.669581 -87.819591)
        (xy 5.554376 -87.671603)
        (xy 5.44064 -87.525503)
        (xy 5.438944 -87.522799)
        (xy 5.438941 -87.522795)
        (xy 5.386168 -87.438667)
        (xy 5.240897 -87.207085)
        (xy 5.074354 -86.870112)
        (xy 5.07324 -86.867131)
        (xy 5.073239 -86.86713)
        (xy 5.050235 -86.805603)
        (xy 4.942718 -86.518034)
        (xy 4.847335 -86.154455)
        (xy 4.839302 -86.103156)
        (xy 4.800093 -85.852773)
        (xy 4.789182 -85.783099)
        (xy 4.789011 -85.779937)
        (xy 4.78901 -85.77993)
        (xy 4.773783 -85.498762)
        (xy 4.768855 -85.407767)
        (xy 4.769005 -85.404586)
        (xy 4.786121 -85.041641)
        (xy 4.786561 -85.032302)
        (xy 4.84212 -84.660549)
        (xy 4.842906 -84.657467)
        (xy 4.842906 -84.657465)
        (xy 4.854888 -84.61046)
        (xy 4.934963 -84.296313)
        (xy 5.064138 -83.943325)
        (xy 5.201337 -83.660774)
        (xy 5.2131 -83.590761)
        (xy 5.185141 -83.525501)
        (xy 5.177088 -83.516644)
        (xy 3.277826 -81.617381)
        (xy 1.739095 -80.07865)
        (xy 1.676783 -80.044624)
        (xy 1.605968 -80.049689)
        (xy 1.549132 -80.092236)
        (xy 1.524321 -80.158756)
        (xy 1.524 -80.167745)
        (xy 1.524 -80.896254)
        (xy 1.544002 -80.964375)
        (xy 1.555559 -80.979662)
        (xy 1.566012 -80.991498)
        (xy 1.571956 -80.998228)
        (xy 1.58451 -81.024966)
        (xy 1.592824 -81.039935)
        (xy 1.608893 -81.064727)
        (xy 1.616239 -81.089291)
        (xy 1.622901 -81.106736)
        (xy 1.629983 -81.121821)
        (xy 1.633799 -81.129948)
        (xy 1.638343 -81.15913)
        (xy 1.642126 -81.175849)
        (xy 1.648014 -81.195536)
        (xy 1.648015 -81.195539)
        (xy 1.650587 -81.204141)
        (xy 1.650797 -81.238556)
        (xy 1.65083 -81.239328)
        (xy 1.651 -81.240423)
        (xy 1.651 -81.271298)
        (xy 1.651002 -81.272068)
        (xy 1.651452 -81.345716)
        (xy 1.651452 -81.345717)
        (xy 1.651476 -81.349652)
        (xy 1.651092 -81.350996)
        (xy 1.651 -81.352341)
        (xy 1.651 -89.526298)
        (xy 1.651002 -89.527068)
        (xy 1.651421 -89.595678)
        (xy 1.651476 -89.604652)
        (xy 1.64901 -89.613281)
        (xy 1.649009 -89.613286)
        (xy 1.643361 -89.633048)
        (xy 1.639783 -89.649809)
        (xy 1.63687 -89.670152)
        (xy 1.636867 -89.670162)
        (xy 1.635595 -89.679045)
        (xy 1.624979 -89.702395)
        (xy 1.618536 -89.719907)
        (xy 1.613954 -89.735937)
        (xy 1.611488 -89.744565)
        (xy 1.595726 -89.769548)
        (xy 1.587596 -89.784614)
        (xy 1.575367 -89.81151)
        (xy 1.558626 -89.830939)
        (xy 1.54752 -89.845949)
        (xy 1.543435 -89.852424)
        (xy 1.524 -89.919654)
        (xy 1.524 -94.202)
        (xy 56.014169 -94.202)
        (xy 56.014859 -94.195435)
        (xy 56.023689 -94.111427)
        (xy 56.033439 -94.018657)
        (xy 56.090407 -93.843327)
        (xy 56.09371 -93.837606)
        (xy 56.093711 -93.837604)
        (xy 56.179282 -93.689391)
        (xy 56.179285 -93.689386)
        (xy 56.182584 -93.683673)
        (xy 56.30594 -93.546672)
        (xy 56.401283 -93.477401)
        (xy 56.435994 -93.452182)
        (xy 56.455084 -93.438312)
        (xy 56.461112 -93.435628)
        (xy 56.461114 -93.435627)
        (xy 56.617468 -93.366014)
        (xy 56.623499 -93.363329)
        (xy 56.72026 -93.342762)
        (xy 56.788551 -93.328246)
        (xy 56.851024 -93.294517)
        (xy 56.885346 -93.232368)
        (xy 56.880618 -93.161529)
        (xy 56.838342 -93.104491)
        (xy 56.771941 -93.079364)
        (xy 56.762365 -93.078999)
        (xy 56.201108 -93.078999)
        (xy 56.195041 -93.077792)
        (xy 56.195038 -93.077792)
        (xy 56.154355 -93.0697)
        (xy 56.154354 -93.0697)
        (xy 56.142184 -93.067279)
        (xy 56.131868 -93.060386)
        (xy 56.131865 -93.060385)
        (xy 56.090121 -93.032493)
        (xy 56.075366 -93.022634)
        (xy 56.030721 -92.955816)
        (xy 56.0283 -92.943646)
        (xy 56.028299 -92.943643)
        (xy 56.028268 -92.943486)
        (xy 56.019 -92.896893)
        (xy 56.019001 -91.507108)
        (xy 56.030721 -91.448184)
        (xy 56.037614 -91.437868)
        (xy 56.037615 -91.437865)
        (xy 56.041542 -91.431988)
        (xy 56.075366 -91.381366)
        (xy 56.142184 -91.336721)
        (xy 56.154354 -91.3343)
        (xy 56.154357 -91.334299)
        (xy 56.186252 -91.327955)
        (xy 56.201107 -91.325)
        (xy 56.895943 -91.325)
        (xy 57.590892 -91.325001)
        (xy 57.596959 -91.326208)
        (xy 57.596962 -91.326208)
        (xy 57.637645 -91.3343)
        (xy 57.637646 -91.3343)
        (xy 57.649816 -91.336721)
        (xy 57.660132 -91.343614)
        (xy 57.660135 -91.343615)
        (xy 57.706316 -91.374472)
        (xy 57.716634 -91.381366)
        (xy 57.750458 -91.431988)
        (xy 57.754383 -91.437863)
        (xy 57.754384 -91.437865)
        (xy 57.761279 -91.448184)
        (xy 57.7637 -91.460355)
        (xy 57.763701 -91.460357)
        (xy 57.771793 -91.50104)
        (xy 57.773 -91.507107)
        (xy 57.773 -92.092347)
        (xy 57.793002 -92.160468)
        (xy 57.839911 -92.201115)
        (xy 57.951857 -92.201115)
        (xy 57.981512 -92.187571)
        (xy 58.019896 -92.127845)
        (xy 58.024309 -92.10552)
        (xy 58.033439 -92.018657)
        (xy 58.090407 -91.843327)
        (xy 58.09371 -91.837606)
        (xy 58.093711 -91.837604)
        (xy 58.179282 -91.689391)
        (xy 58.179285 -91.689386)
        (xy 58.182584 -91.683673)
        (xy 58.30594 -91.546672)
        (xy 58.401283 -91.477401)
        (xy 58.424745 -91.460355)
        (xy 58.455084 -91.438312)
        (xy 58.461112 -91.435628)
        (xy 58.461114 -91.435627)
        (xy 58.559812 -91.391684)
        (xy 58.623499 -91.363329)
        (xy 58.713661 -91.344165)
        (xy 58.797367 -91.326372)
        (xy 58.797371 -91.326372)
        (xy 58.803824 -91.325)
        (xy 58.988176 -91.325)
        (xy 58.994629 -91.326372)
        (xy 58.994633 -91.326372)
        (xy 59.078339 -91.344165)
        (xy 59.168501 -91.363329)
        (xy 59.232188 -91.391684)
        (xy 59.330886 -91.435627)
        (xy 59.330888 -91.435628)
        (xy 59.336916 -91.438312)
        (xy 59.367256 -91.460355)
        (xy 59.390717 -91.477401)
        (xy 59.48606 -91.546672)
        (xy 59.609416 -91.683673)
        (xy 59.612715 -91.689386)
        (xy 59.612718 -91.689391)
        (xy 59.698289 -91.837604)
        (xy 59.69829 -91.837606)
        (xy 59.701593 -91.843327)
        (xy 59.758561 -92.018657)
        (xy 59.77069 -92.134057)
        (xy 59.797703 -92.199713)
        (xy 59.800607 -92.201739)
        (xy 59.990645 -92.201739)
        (xy 60.019944 -92.143553)
        (xy 60.02131 -92.134057)
        (xy 60.033439 -92.018657)
        (xy 60.090407 -91.843327)
        (xy 60.09371 -91.837606)
        (xy 60.093711 -91.837604)
        (xy 60.179282 -91.689391)
        (xy 60.179285 -91.689386)
        (xy 60.182584 -91.683673)
        (xy 60.30594 -91.546672)
        (xy 60.401283 -91.477401)
        (xy 60.424745 -91.460355)
        (xy 60.455084 -91.438312)
        (xy 60.461112 -91.435628)
        (xy 60.461114 -91.435627)
        (xy 60.559812 -91.391684)
        (xy 60.623499 -91.363329)
        (xy 60.713661 -91.344165)
        (xy 60.797367 -91.326372)
        (xy 60.797371 -91.326372)
        (xy 60.803824 -91.325)
        (xy 60.988176 -91.325)
        (xy 60.994629 -91.326372)
        (xy 60.994633 -91.326372)
        (xy 61.078339 -91.344165)
        (xy 61.168501 -91.363329)
        (xy 61.232188 -91.391684)
        (xy 61.330886 -91.435627)
        (xy 61.330888 -91.435628)
        (xy 61.336916 -91.438312)
        (xy 61.367256 -91.460355)
        (xy 61.390717 -91.477401)
        (xy 61.48606 -91.546672)
        (xy 61.609416 -91.683673)
        (xy 61.612715 -91.689386)
        (xy 61.612718 -91.689391)
        (xy 61.698289 -91.837604)
        (xy 61.69829 -91.837606)
        (xy 61.701593 -91.843327)
        (xy 61.758561 -92.018657)
        (xy 61.77069 -92.134057)
        (xy 61.797703 -92.199713)
        (xy 61.800607 -92.201739)
        (xy 61.990645 -92.201739)
        (xy 62.019944 -92.143553)
        (xy 62.02131 -92.134057)
        (xy 62.033439 -92.018657)
        (xy 62.090407 -91.843327)
        (xy 62.09371 -91.837606)
        (xy 62.093711 -91.837604)
        (xy 62.179282 -91.689391)
        (xy 62.179285 -91.689386)
        (xy 62.182584 -91.683673)
        (xy 62.30594 -91.546672)
        (xy 62.401283 -91.477401)
        (xy 62.424745 -91.460355)
        (xy 62.455084 -91.438312)
        (xy 62.461112 -91.435628)
        (xy 62.461114 -91.435627)
        (xy 62.559812 -91.391684)
        (xy 62.623499 -91.363329)
        (xy 62.713661 -91.344165)
        (xy 62.797367 -91.326372)
        (xy 62.797371 -91.326372)
        (xy 62.803824 -91.325)
        (xy 62.988176 -91.325)
        (xy 62.994629 -91.326372)
        (xy 62.994633 -91.326372)
        (xy 63.078339 -91.344165)
        (xy 63.168501 -91.363329)
        (xy 63.232188 -91.391684)
        (xy 63.330886 -91.435627)
        (xy 63.330888 -91.435628)
        (xy 63.336916 -91.438312)
        (xy 63.367256 -91.460355)
        (xy 63.390717 -91.477401)
        (xy 63.48606 -91.546672)
        (xy 63.609416 -91.683673)
        (xy 63.612715 -91.689386)
        (xy 63.612718 -91.689391)
        (xy 63.698289 -91.837604)
        (xy 63.69829 -91.837606)
        (xy 63.701593 -91.843327)
        (xy 63.758561 -92.018657)
        (xy 63.77069 -92.134057)
        (xy 63.797703 -92.199713)
        (xy 63.800607 -92.201739)
        (xy 63.990645 -92.201739)
        (xy 64.019944 -92.143553)
        (xy 64.02131 -92.134057)
        (xy 64.033439 -92.018657)
        (xy 64.090407 -91.843327)
        (xy 64.09371 -91.837606)
        (xy 64.093711 -91.837604)
        (xy 64.179282 -91.689391)
        (xy 64.179285 -91.689386)
        (xy 64.182584 -91.683673)
        (xy 64.30594 -91.546672)
        (xy 64.401283 -91.477401)
        (xy 64.424745 -91.460355)
        (xy 64.455084 -91.438312)
        (xy 64.461112 -91.435628)
        (xy 64.461114 -91.435627)
        (xy 64.559812 -91.391684)
        (xy 64.623499 -91.363329)
        (xy 64.713661 -91.344165)
        (xy 64.797367 -91.326372)
        (xy 64.797371 -91.326372)
        (xy 64.803824 -91.325)
        (xy 64.988176 -91.325)
        (xy 64.994629 -91.326372)
        (xy 64.994633 -91.326372)
        (xy 65.078339 -91.344165)
        (xy 65.168501 -91.363329)
        (xy 65.232188 -91.391684)
        (xy 65.330886 -91.435627)
        (xy 65.330888 -91.435628)
        (xy 65.336916 -91.438312)
        (xy 65.367256 -91.460355)
        (xy 65.390717 -91.477401)
        (xy 65.48606 -91.546672)
        (xy 65.609416 -91.683673)
        (xy 65.612715 -91.689386)
        (xy 65.612718 -91.689391)
        (xy 65.698289 -91.837604)
        (xy 65.69829 -91.837606)
        (xy 65.701593 -91.843327)
        (xy 65.758561 -92.018657)
        (xy 65.777831 -92.202)
        (xy 65.758561 -92.385343)
        (xy 65.701593 -92.560673)
        (xy 65.698289 -92.566396)
        (xy 65.612718 -92.714609)
        (xy 65.612715 -92.714614)
        (xy 65.609416 -92.720327)
        (xy 65.48606 -92.857328)
        (xy 65.367258 -92.943643)
        (xy 65.342258 -92.961807)
        (xy 65.342257 -92.961808)
        (xy 65.336916 -92.965688)
        (xy 65.330888 -92.968372)
        (xy 65.330886 -92.968373)
        (xy 65.174532 -93.037986)
        (xy 65.174531 -93.037986)
        (xy 65.168501 -93.040671)
        (xy 64.989338 -93.078753)
        (xy 64.926865 -93.112481)
        (xy 64.895452 -93.169363)
        (xy 64.894729 -93.15853)
        (xy 64.852453 -93.101492)
        (xy 64.802662 -93.078753)
        (xy 64.623499 -93.040671)
        (xy 64.61747 -93.037986)
        (xy 64.617468 -93.037986)
        (xy 64.461115 -92.968373)
        (xy 64.461113 -92.968372)
        (xy 64.455085 -92.965688)
        (xy 64.449744 -92.961808)
        (xy 64.449743 -92.961807)
        (xy 64.311284 -92.861211)
        (xy 64.311282 -92.861209)
        (xy 64.30594 -92.857328)
        (xy 64.182584 -92.720327)
        (xy 64.179285 -92.714614)
        (xy 64.179282 -92.714609)
        (xy 64.093711 -92.566396)
        (xy 64.090407 -92.560673)
        (xy 64.033439 -92.385343)
        (xy 64.032749 -92.37878)
        (xy 64.032749 -92.378779)
        (xy 64.02131 -92.269943)
        (xy 63.994297 -92.204287)
        (xy 63.990645 -92.201739)
        (xy 63.800607 -92.201739)
        (xy 63.801355 -92.202261)
        (xy 63.772056 -92.260447)
        (xy 63.77069 -92.269943)
        (xy 63.759251 -92.378779)
        (xy 63.759251 -92.37878)
        (xy 63.758561 -92.385343)
        (xy 63.701593 -92.560673)
        (xy 63.698289 -92.566396)
        (xy 63.612718 -92.714609)
        (xy 63.612715 -92.714614)
        (xy 63.609416 -92.720327)
        (xy 63.48606 -92.857328)
        (xy 63.367258 -92.943643)
        (xy 63.342258 -92.961807)
        (xy 63.342257 -92.961808)
        (xy 63.336916 -92.965688)
        (xy 63.330888 -92.968372)
        (xy 63.330886 -92.968373)
        (xy 63.174532 -93.037986)
        (xy 63.174531 -93.037986)
        (xy 63.168501 -93.040671)
        (xy 62.989338 -93.078753)
        (xy 62.926865 -93.112481)
        (xy 62.895452 -93.169363)
        (xy 62.894729 -93.15853)
        (xy 62.852453 -93.101492)
        (xy 62.802662 -93.078753)
        (xy 62.623499 -93.040671)
        (xy 62.61747 -93.037986)
        (xy 62.617468 -93.037986)
        (xy 62.461115 -92.968373)
        (xy 62.461113 -92.968372)
        (xy 62.455085 -92.965688)
        (xy 62.449744 -92.961808)
        (xy 62.449743 -92.961807)
        (xy 62.311284 -92.861211)
        (xy 62.311282 -92.861209)
        (xy 62.30594 -92.857328)
        (xy 62.182584 -92.720327)
        (xy 62.179285 -92.714614)
        (xy 62.179282 -92.714609)
        (xy 62.093711 -92.566396)
        (xy 62.090407 -92.560673)
        (xy 62.033439 -92.385343)
        (xy 62.032749 -92.37878)
        (xy 62.032749 -92.378779)
        (xy 62.02131 -92.269943)
        (xy 61.994297 -92.204287)
        (xy 61.990645 -92.201739)
        (xy 61.800607 -92.201739)
        (xy 61.801355 -92.202261)
        (xy 61.772056 -92.260447)
        (xy 61.77069 -92.269943)
        (xy 61.759251 -92.378779)
        (xy 61.759251 -92.37878)
        (xy 61.758561 -92.385343)
        (xy 61.701593 -92.560673)
        (xy 61.698289 -92.566396)
        (xy 61.612718 -92.714609)
        (xy 61.612715 -92.714614)
        (xy 61.609416 -92.720327)
        (xy 61.48606 -92.857328)
        (xy 61.367258 -92.943643)
        (xy 61.342258 -92.961807)
        (xy 61.342257 -92.961808)
        (xy 61.336916 -92.965688)
        (xy 61.330888 -92.968372)
        (xy 61.330886 -92.968373)
        (xy 61.174532 -93.037986)
        (xy 61.174531 -93.037986)
        (xy 61.168501 -93.040671)
        (xy 60.989338 -93.078753)
        (xy 60.926865 -93.112481)
        (xy 60.895452 -93.169363)
        (xy 60.894729 -93.15853)
        (xy 60.852453 -93.101492)
        (xy 60.802662 -93.078753)
        (xy 60.623499 -93.040671)
        (xy 60.61747 -93.037986)
        (xy 60.617468 -93.037986)
        (xy 60.461115 -92.968373)
        (xy 60.461113 -92.968372)
        (xy 60.455085 -92.965688)
        (xy 60.449744 -92.961808)
        (xy 60.449743 -92.961807)
        (xy 60.311284 -92.861211)
        (xy 60.311282 -92.861209)
        (xy 60.30594 -92.857328)
        (xy 60.182584 -92.720327)
        (xy 60.179285 -92.714614)
        (xy 60.179282 -92.714609)
        (xy 60.093711 -92.566396)
        (xy 60.090407 -92.560673)
        (xy 60.033439 -92.385343)
        (xy 60.032749 -92.37878)
        (xy 60.032749 -92.378779)
        (xy 60.02131 -92.269943)
        (xy 59.994297 -92.204287)
        (xy 59.990645 -92.201739)
        (xy 59.800607 -92.201739)
        (xy 59.801355 -92.202261)
        (xy 59.772056 -92.260447)
        (xy 59.77069 -92.269943)
        (xy 59.759251 -92.378779)
        (xy 59.759251 -92.37878)
        (xy 59.758561 -92.385343)
        (xy 59.701593 -92.560673)
        (xy 59.698289 -92.566396)
        (xy 59.612718 -92.714609)
        (xy 59.612715 -92.714614)
        (xy 59.609416 -92.720327)
        (xy 59.48606 -92.857328)
        (xy 59.367258 -92.943643)
        (xy 59.342258 -92.961807)
        (xy 59.342257 -92.961808)
        (xy 59.336916 -92.965688)
        (xy 59.330888 -92.968372)
        (xy 59.330886 -92.968373)
        (xy 59.174532 -93.037986)
        (xy 59.174531 -93.037986)
        (xy 59.168501 -93.040671)
        (xy 58.989338 -93.078753)
        (xy 58.926865 -93.112481)
        (xy 58.895452 -93.169363)
        (xy 58.894729 -93.15853)
        (xy 58.852453 -93.101492)
        (xy 58.802662 -93.078753)
        (xy 58.623499 -93.040671)
        (xy 58.61747 -93.037986)
        (xy 58.617468 -93.037986)
        (xy 58.461115 -92.968373)
        (xy 58.461113 -92.968372)
        (xy 58.455085 -92.965688)
        (xy 58.449744 -92.961808)
        (xy 58.449743 -92.961807)
        (xy 58.311284 -92.861211)
        (xy 58.311282 -92.861209)
        (xy 58.30594 -92.857328)
        (xy 58.182584 -92.720327)
        (xy 58.179285 -92.714614)
        (xy 58.179282 -92.714609)
        (xy 58.093711 -92.566396)
        (xy 58.090407 -92.560673)
        (xy 58.033439 -92.385343)
        (xy 58.032749 -92.37878)
        (xy 58.032749 -92.378779)
        (xy 58.024309 -92.29848)
        (xy 57.997295 -92.232823)
        (xy 57.951857 -92.201115)
        (xy 57.839911 -92.201115)
        (xy 57.843363 -92.204106)
        (xy 57.806985 -92.225573)
        (xy 57.775054 -92.288985)
        (xy 57.772999 -92.311651)
        (xy 57.772999 -92.896892)
        (xy 57.763732 -92.943486)
        (xy 57.7637 -92.943645)
        (xy 57.7637 -92.943646)
        (xy 57.761279 -92.955816)
        (xy 57.754386 -92.966132)
        (xy 57.754385 -92.966135)
        (xy 57.723528 -93.012316)
        (xy 57.716634 -93.022634)
        (xy 57.649816 -93.067279)
        (xy 57.637646 -93.0697)
        (xy 57.637643 -93.069701)
        (xy 57.605748 -93.076045)
        (xy 57.590893 -93.079)
        (xy 57.029647 -93.079)
        (xy 56.961526 -93.099002)
        (xy 56.915033 -93.152658)
        (xy 56.904929 -93.222932)
        (xy 56.934423 -93.287512)
        (xy 56.994149 -93.325896)
        (xy 57.003446 -93.328246)
        (xy 57.068293 -93.342029)
        (xy 57.162043 -93.361956)
        (xy 57.162046 -93.361957)
        (xy 57.168501 -93.363329)
        (xy 57.174532 -93.366014)
        (xy 57.330886 -93.435627)
        (xy 57.330888 -93.435628)
        (xy 57.336916 -93.438312)
        (xy 57.356007 -93.452182)
        (xy 57.390717 -93.477401)
        (xy 57.48606 -93.546672)
        (xy 57.609416 -93.683673)
        (xy 57.612715 -93.689386)
        (xy 57.612718 -93.689391)
        (xy 57.698289 -93.837604)
        (xy 57.69829 -93.837606)
        (xy 57.701593 -93.843327)
        (xy 57.758561 -94.018657)
        (xy 57.768312 -94.111427)
        (xy 57.77069 -94.134057)
        (xy 57.797703 -94.199713)
        (xy 57.800607 -94.201739)
        (xy 57.990645 -94.201739)
        (xy 58.019944 -94.143553)
        (xy 58.02131 -94.134057)
        (xy 58.023689 -94.111427)
        (xy 58.033439 -94.018657)
        (xy 58.090407 -93.843327)
        (xy 58.09371 -93.837606)
        (xy 58.093711 -93.837604)
        (xy 58.179282 -93.689391)
        (xy 58.179285 -93.689386)
        (xy 58.182584 -93.683673)
        (xy 58.30594 -93.546672)
        (xy 58.401283 -93.477401)
        (xy 58.435994 -93.452182)
        (xy 58.455084 -93.438312)
        (xy 58.461112 -93.435628)
        (xy 58.461114 -93.435627)
        (xy 58.617468 -93.366014)
        (xy 58.623499 -93.363329)
        (xy 58.788548 -93.328247)
        (xy 58.802662 -93.325247)
        (xy 58.865135 -93.291519)
        (xy 58.896548 -93.234637)
        (xy 58.897271 -93.24547)
        (xy 58.939547 -93.302508)
        (xy 58.989338 -93.325247)
        (xy 59.003452 -93.328247)
        (xy 59.168501 -93.363329)
        (xy 59.174532 -93.366014)
        (xy 59.330886 -93.435627)
        (xy 59.330888 -93.435628)
        (xy 59.336916 -93.438312)
        (xy 59.356007 -93.452182)
        (xy 59.390717 -93.477401)
        (xy 59.48606 -93.546672)
        (xy 59.609416 -93.683673)
        (xy 59.612715 -93.689386)
        (xy 59.612718 -93.689391)
        (xy 59.698289 -93.837604)
        (xy 59.69829 -93.837606)
        (xy 59.701593 -93.843327)
        (xy 59.758561 -94.018657)
        (xy 59.768312 -94.111427)
        (xy 59.77069 -94.134057)
        (xy 59.797703 -94.199713)
        (xy 59.800607 -94.201739)
        (xy 59.990645 -94.201739)
        (xy 60.019944 -94.143553)
        (xy 60.02131 -94.134057)
        (xy 60.023689 -94.111427)
        (xy 60.033439 -94.018657)
        (xy 60.090407 -93.843327)
        (xy 60.09371 -93.837606)
        (xy 60.093711 -93.837604)
        (xy 60.179282 -93.689391)
        (xy 60.179285 -93.689386)
        (xy 60.182584 -93.683673)
        (xy 60.30594 -93.546672)
        (xy 60.401283 -93.477401)
        (xy 60.435994 -93.452182)
        (xy 60.455084 -93.438312)
        (xy 60.461112 -93.435628)
        (xy 60.461114 -93.435627)
        (xy 60.617468 -93.366014)
        (xy 60.623499 -93.363329)
        (xy 60.788548 -93.328247)
        (xy 60.802662 -93.325247)
        (xy 60.865135 -93.291519)
        (xy 60.896548 -93.234637)
        (xy 60.897271 -93.24547)
        (xy 60.939547 -93.302508)
        (xy 60.989338 -93.325247)
        (xy 61.003452 -93.328247)
        (xy 61.168501 -93.363329)
        (xy 61.174532 -93.366014)
        (xy 61.330886 -93.435627)
        (xy 61.330888 -93.435628)
        (xy 61.336916 -93.438312)
        (xy 61.356007 -93.452182)
        (xy 61.390717 -93.477401)
        (xy 61.48606 -93.546672)
        (xy 61.609416 -93.683673)
        (xy 61.612715 -93.689386)
        (xy 61.612718 -93.689391)
        (xy 61.698289 -93.837604)
        (xy 61.69829 -93.837606)
        (xy 61.701593 -93.843327)
        (xy 61.758561 -94.018657)
        (xy 61.768312 -94.111427)
        (xy 61.77069 -94.134057)
        (xy 61.797703 -94.199713)
        (xy 61.800607 -94.201739)
        (xy 61.990645 -94.201739)
        (xy 62.019944 -94.143553)
        (xy 62.02131 -94.134057)
        (xy 62.023689 -94.111427)
        (xy 62.033439 -94.018657)
        (xy 62.090407 -93.843327)
        (xy 62.09371 -93.837606)
        (xy 62.093711 -93.837604)
        (xy 62.179282 -93.689391)
        (xy 62.179285 -93.689386)
        (xy 62.182584 -93.683673)
        (xy 62.30594 -93.546672)
        (xy 62.401283 -93.477401)
        (xy 62.435994 -93.452182)
        (xy 62.455084 -93.438312)
        (xy 62.461112 -93.435628)
        (xy 62.461114 -93.435627)
        (xy 62.617468 -93.366014)
        (xy 62.623499 -93.363329)
        (xy 62.788548 -93.328247)
        (xy 62.802662 -93.325247)
        (xy 62.865135 -93.291519)
        (xy 62.896548 -93.234637)
        (xy 62.897271 -93.24547)
        (xy 62.939547 -93.302508)
        (xy 62.989338 -93.325247)
        (xy 63.003452 -93.328247)
        (xy 63.168501 -93.363329)
        (xy 63.174532 -93.366014)
        (xy 63.330886 -93.435627)
        (xy 63.330888 -93.435628)
        (xy 63.336916 -93.438312)
        (xy 63.356007 -93.452182)
        (xy 63.390717 -93.477401)
        (xy 63.48606 -93.546672)
        (xy 63.609416 -93.683673)
        (xy 63.612715 -93.689386)
        (xy 63.612718 -93.689391)
        (xy 63.698289 -93.837604)
        (xy 63.69829 -93.837606)
        (xy 63.701593 -93.843327)
        (xy 63.758561 -94.018657)
        (xy 63.768312 -94.111427)
        (xy 63.77069 -94.134057)
        (xy 63.797703 -94.199713)
        (xy 63.800607 -94.201739)
        (xy 63.990645 -94.201739)
        (xy 64.019944 -94.143553)
        (xy 64.02131 -94.134057)
        (xy 64.023689 -94.111427)
        (xy 64.033439 -94.018657)
        (xy 64.090407 -93.843327)
        (xy 64.09371 -93.837606)
        (xy 64.093711 -93.837604)
        (xy 64.179282 -93.689391)
        (xy 64.179285 -93.689386)
        (xy 64.182584 -93.683673)
        (xy 64.30594 -93.546672)
        (xy 64.401283 -93.477401)
        (xy 64.435994 -93.452182)
        (xy 64.455084 -93.438312)
        (xy 64.461112 -93.435628)
        (xy 64.461114 -93.435627)
        (xy 64.617468 -93.366014)
        (xy 64.623499 -93.363329)
        (xy 64.788548 -93.328247)
        (xy 64.802662 -93.325247)
        (xy 64.865135 -93.291519)
        (xy 64.896548 -93.234637)
        (xy 64.897271 -93.24547)
        (xy 64.939547 -93.302508)
        (xy 64.989338 -93.325247)
        (xy 65.003452 -93.328247)
        (xy 65.168501 -93.363329)
        (xy 65.174532 -93.366014)
        (xy 65.330886 -93.435627)
        (xy 65.330888 -93.435628)
        (xy 65.336916 -93.438312)
        (xy 65.356007 -93.452182)
        (xy 65.390717 -93.477401)
        (xy 65.48606 -93.546672)
        (xy 65.609416 -93.683673)
        (xy 65.612715 -93.689386)
        (xy 65.612718 -93.689391)
        (xy 65.698289 -93.837604)
        (xy 65.69829 -93.837606)
        (xy 65.701593 -93.843327)
        (xy 65.758561 -94.018657)
        (xy 65.768312 -94.111427)
        (xy 65.777141 -94.195435)
        (xy 65.777831 -94.202)
        (xy 65.758561 -94.385343)
        (xy 65.71111 -94.531382)
        (xy 90.23 -94.531382)
        (xy 90.23 -93.682618)
        (xy 90.230146 -93.680482)
        (xy 90.230146 -93.680471)
        (xy 90.231477 -93.660947)
        (xy 90.232933 -93.639596)
        (xy 90.278137 -93.458293)
        (xy 90.281169 -93.452186)
        (xy 90.28117 -93.452182)
        (xy 90.343859 -93.325896)
        (xy 90.361219 -93.290925)
        (xy 90.478302 -93.145302)
        (xy 90.483614 -93.141031)
        (xy 90.572333 -93.0697)
        (xy 90.623925 -93.028219)
        (xy 90.630031 -93.025188)
        (xy 90.630034 -93.025186)
        (xy 90.785182 -92.94817)
        (xy 90.785186 -92.948169)
        (xy 90.791293 -92.945137)
        (xy 90.972596 -92.899933)
        (xy 90.993947 -92.898477)
        (xy 91.013471 -92.897146)
        (xy 91.013482 -92.897146)
        (xy 91.015618 -92.897)
        (xy 91.864382 -92.897)
        (xy 91.866518 -92.897146)
        (xy 91.866529 -92.897146)
        (xy 91.886053 -92.898477)
        (xy 91.907404 -92.899933)
        (xy 92.088707 -92.945137)
        (xy 92.094814 -92.948169)
        (xy 92.094818 -92.94817)
        (xy 92.249966 -93.025186)
        (xy 92.249969 -93.025188)
        (xy 92.256075 -93.028219)
        (xy 92.307668 -93.0697)
        (xy 92.396386 -93.141031)
        (xy 92.401698 -93.145302)
        (xy 92.518781 -93.290925)
        (xy 92.536141 -93.325896)
        (xy 92.59883 -93.452182)
        (xy 92.598831 -93.452186)
        (xy 92.601863 -93.458293)
        (xy 92.647067 -93.639596)
        (xy 92.648523 -93.660947)
        (xy 92.649854 -93.680471)
        (xy 92.649854 -93.680482)
        (xy 92.65 -93.682618)
        (xy 92.65 -93.845498)
        (xy 102.288 -93.845498)
        (xy 102.288 -91.544502)
        (xy 102.288337 -91.541256)
        (xy 102.288337 -91.541252)
        (xy 102.297994 -91.448184)
        (xy 102.298996 -91.438526)
        (xy 102.301177 -91.43199)
        (xy 102.301177 -91.431988)
        (xy 102.336871 -91.325)
        (xy 102.355082 -91.270414)
        (xy 102.448338 -91.119714)
        (xy 102.573762 -90.99451)
        (xy 102.724624 -90.901517)
        (xy 102.731572 -90.899212)
        (xy 102.731573 -90.899212)
        (xy 102.886302 -90.84789)
        (xy 102.886304 -90.84789)
        (xy 102.892833 -90.845724)
        (xy 102.997502 -90.835)
        (xy 106.298498 -90.835)
        (xy 106.301744 -90.835337)
        (xy 106.301748 -90.835337)
        (xy 106.397616 -90.845284)
        (xy 106.39762 -90.845285)
        (xy 106.404474 -90.845996)
        (xy 106.41101 -90.848177)
        (xy 106.411012 -90.848177)
        (xy 106.565638 -90.899764)
        (xy 106.572586 -90.902082)
        (xy 106.723286 -90.995338)
        (xy 106.84849 -91.120762)
        (xy 106.941483 -91.271624)
        (xy 106.965362 -91.343617)
        (xy 106.99511 -91.433302)
        (xy 106.99511 -91.433304)
        (xy 106.997276 -91.439833)
        (xy 107.008 -91.544502)
        (xy 107.008 -93.845498)
        (xy 106.997004 -93.951474)
        (xy 106.940918 -94.119586)
        (xy 106.847662 -94.270286)
        (xy 106.722238 -94.39549)
        (xy 106.571376 -94.488483)
        (xy 106.442041 -94.531382)
        (xy 106.409698 -94.54211)
        (xy 106.409696 -94.54211)
        (xy 106.403167 -94.544276)
        (xy 106.298498 -94.555)
        (xy 103.103244 -94.555)
        (xy 103.035123 -94.575002)
        (xy 103.025871 -94.58568)
        (xy 102.978294 -94.555104)
        (xy 102.9558 -94.550673)
        (xy 102.898384 -94.544716)
        (xy 102.89838 -94.544715)
        (xy 102.891526 -94.544004)
        (xy 102.88499 -94.541823)
        (xy 102.884988 -94.541823)
        (xy 102.847254 -94.529234)
        (xy 102.723414 -94.487918)
        (xy 102.572714 -94.394662)
        (xy 102.44751 -94.269238)
        (xy 102.354517 -94.118376)
        (xy 102.352212 -94.111428)
        (xy 102.352212 -94.111427)
        (xy 102.319359 -94.012378)
        (xy 102.298724 -93.950167)
        (xy 102.288 -93.845498)
        (xy 92.65 -93.845498)
        (xy 92.65 -94.531382)
        (xy 92.647067 -94.574404)
        (xy 92.601863 -94.755707)
        (xy 92.598831 -94.761814)
        (xy 92.59883 -94.761818)
        (xy 92.521814 -94.916966)
        (xy 92.521812 -94.916969)
        (xy 92.518781 -94.923075)
        (xy 92.401698 -95.068698)
        (xy 92.325012 -95.130355)
        (xy 92.261391 -95.181507)
        (xy 92.26139 -95.181508)
        (xy 92.256075 -95.185781)
        (xy 92.249969 -95.188812)
        (xy 92.249966 -95.188814)
        (xy 92.094818 -95.26583)
        (xy 92.094814 -95.265831)
        (xy 92.088707 -95.268863)
        (xy 91.907404 -95.314067)
        (xy 91.886053 -95.315523)
        (xy 91.866529 -95.316854)
        (xy 91.866518 -95.316854)
        (xy 91.864382 -95.317)
        (xy 91.015618 -95.317)
        (xy 91.013482 -95.316854)
        (xy 91.013471 -95.316854)
        (xy 90.993947 -95.315523)
        (xy 90.972596 -95.314067)
        (xy 90.791293 -95.268863)
        (xy 90.785186 -95.265831)
        (xy 90.785182 -95.26583)
        (xy 90.630034 -95.188814)
        (xy 90.630031 -95.188812)
        (xy 90.623925 -95.185781)
        (xy 90.61861 -95.181508)
        (xy 90.618609 -95.181507)
        (xy 90.554988 -95.130355)
        (xy 90.478302 -95.068698)
        (xy 90.361219 -94.923075)
        (xy 90.358188 -94.916969)
        (xy 90.358186 -94.916966)
        (xy 90.28117 -94.761818)
        (xy 90.281169 -94.761814)
        (xy 90.278137 -94.755707)
        (xy 90.232933 -94.574404)
        (xy 90.23 -94.531382)
        (xy 65.71111 -94.531382)
        (xy 65.701593 -94.560673)
        (xy 65.696869 -94.568855)
        (xy 65.612718 -94.714609)
        (xy 65.612715 -94.714614)
        (xy 65.609416 -94.720327)
        (xy 65.572058 -94.761818)
        (xy 65.490482 -94.852417)
        (xy 65.490481 -94.852418)
        (xy 65.48606 -94.857328)
        (xy 65.336916 -94.965688)
        (xy 65.330888 -94.968372)
        (xy 65.330886 -94.968373)
        (xy 65.174532 -95.037986)
        (xy 65.174531 -95.037986)
        (xy 65.168501 -95.040671)
        (xy 65.061635 -95.063386)
        (xy 64.994633 -95.077628)
        (xy 64.994629 -95.077628)
        (xy 64.988176 -95.079)
        (xy 64.803824 -95.079)
        (xy 64.797371 -95.077628)
        (xy 64.797367 -95.077628)
        (xy 64.730365 -95.063386)
        (xy 64.623499 -95.040671)
        (xy 64.61747 -95.037986)
        (xy 64.617468 -95.037986)
        (xy 64.461115 -94.968373)
        (xy 64.461113 -94.968372)
        (xy 64.455085 -94.965688)
        (xy 64.449744 -94.961808)
        (xy 64.449743 -94.961807)
        (xy 64.311284 -94.861211)
        (xy 64.311282 -94.861209)
        (xy 64.30594 -94.857328)
        (xy 64.301519 -94.852418)
        (xy 64.301518 -94.852417)
        (xy 64.219943 -94.761818)
        (xy 64.182584 -94.720327)
        (xy 64.179285 -94.714614)
        (xy 64.179282 -94.714609)
        (xy 64.095131 -94.568855)
        (xy 64.090407 -94.560673)
        (xy 64.033439 -94.385343)
        (xy 64.032749 -94.37878)
        (xy 64.032749 -94.378779)
        (xy 64.02131 -94.269943)
        (xy 63.994297 -94.204287)
        (xy 63.990645 -94.201739)
        (xy 63.800607 -94.201739)
        (xy 63.801355 -94.202261)
        (xy 63.772056 -94.260447)
        (xy 63.77069 -94.269943)
        (xy 63.759251 -94.378779)
        (xy 63.759251 -94.37878)
        (xy 63.758561 -94.385343)
        (xy 63.701593 -94.560673)
        (xy 63.696869 -94.568855)
        (xy 63.612718 -94.714609)
        (xy 63.612715 -94.714614)
        (xy 63.609416 -94.720327)
        (xy 63.572058 -94.761818)
        (xy 63.490482 -94.852417)
        (xy 63.490481 -94.852418)
        (xy 63.48606 -94.857328)
        (xy 63.336916 -94.965688)
        (xy 63.330888 -94.968372)
        (xy 63.330886 -94.968373)
        (xy 63.174532 -95.037986)
        (xy 63.174531 -95.037986)
        (xy 63.168501 -95.040671)
        (xy 63.061635 -95.063386)
        (xy 62.994633 -95.077628)
        (xy 62.994629 -95.077628)
        (xy 62.988176 -95.079)
        (xy 62.803824 -95.079)
        (xy 62.797371 -95.077628)
        (xy 62.797367 -95.077628)
        (xy 62.730365 -95.063386)
        (xy 62.623499 -95.040671)
        (xy 62.61747 -95.037986)
        (xy 62.617468 -95.037986)
        (xy 62.461115 -94.968373)
        (xy 62.461113 -94.968372)
        (xy 62.455085 -94.965688)
        (xy 62.449744 -94.961808)
        (xy 62.449743 -94.961807)
        (xy 62.311284 -94.861211)
        (xy 62.311282 -94.861209)
        (xy 62.30594 -94.857328)
        (xy 62.301519 -94.852418)
        (xy 62.301518 -94.852417)
        (xy 62.219943 -94.761818)
        (xy 62.182584 -94.720327)
        (xy 62.179285 -94.714614)
        (xy 62.179282 -94.714609)
        (xy 62.095131 -94.568855)
        (xy 62.090407 -94.560673)
        (xy 62.033439 -94.385343)
        (xy 62.032749 -94.37878)
        (xy 62.032749 -94.378779)
        (xy 62.02131 -94.269943)
        (xy 61.994297 -94.204287)
        (xy 61.990645 -94.201739)
        (xy 61.800607 -94.201739)
        (xy 61.801355 -94.202261)
        (xy 61.772056 -94.260447)
        (xy 61.77069 -94.269943)
        (xy 61.759251 -94.378779)
        (xy 61.759251 -94.37878)
        (xy 61.758561 -94.385343)
        (xy 61.701593 -94.560673)
        (xy 61.696869 -94.568855)
        (xy 61.612718 -94.714609)
        (xy 61.612715 -94.714614)
        (xy 61.609416 -94.720327)
        (xy 61.572058 -94.761818)
        (xy 61.490482 -94.852417)
        (xy 61.490481 -94.852418)
        (xy 61.48606 -94.857328)
        (xy 61.336916 -94.965688)
        (xy 61.330888 -94.968372)
        (xy 61.330886 -94.968373)
        (xy 61.174532 -95.037986)
        (xy 61.174531 -95.037986)
        (xy 61.168501 -95.040671)
        (xy 61.061635 -95.063386)
        (xy 60.994633 -95.077628)
        (xy 60.994629 -95.077628)
        (xy 60.988176 -95.079)
        (xy 60.803824 -95.079)
        (xy 60.797371 -95.077628)
        (xy 60.797367 -95.077628)
        (xy 60.730365 -95.063386)
        (xy 60.623499 -95.040671)
        (xy 60.61747 -95.037986)
        (xy 60.617468 -95.037986)
        (xy 60.461115 -94.968373)
        (xy 60.461113 -94.968372)
        (xy 60.455085 -94.965688)
        (xy 60.449744 -94.961808)
        (xy 60.449743 -94.961807)
        (xy 60.311284 -94.861211)
        (xy 60.311282 -94.861209)
        (xy 60.30594 -94.857328)
        (xy 60.301519 -94.852418)
        (xy 60.301518 -94.852417)
        (xy 60.219943 -94.761818)
        (xy 60.182584 -94.720327)
        (xy 60.179285 -94.714614)
        (xy 60.179282 -94.714609)
        (xy 60.095131 -94.568855)
        (xy 60.090407 -94.560673)
        (xy 60.033439 -94.385343)
        (xy 60.032749 -94.37878)
        (xy 60.032749 -94.378779)
        (xy 60.02131 -94.269943)
        (xy 59.994297 -94.204287)
        (xy 59.990645 -94.201739)
        (xy 59.800607 -94.201739)
        (xy 59.801355 -94.202261)
        (xy 59.772056 -94.260447)
        (xy 59.77069 -94.269943)
        (xy 59.759251 -94.378779)
        (xy 59.759251 -94.37878)
        (xy 59.758561 -94.385343)
        (xy 59.701593 -94.560673)
        (xy 59.696869 -94.568855)
        (xy 59.612718 -94.714609)
        (xy 59.612715 -94.714614)
        (xy 59.609416 -94.720327)
        (xy 59.572058 -94.761818)
        (xy 59.490482 -94.852417)
        (xy 59.490481 -94.852418)
        (xy 59.48606 -94.857328)
        (xy 59.336916 -94.965688)
        (xy 59.330888 -94.968372)
        (xy 59.330886 -94.968373)
        (xy 59.174532 -95.037986)
        (xy 59.174531 -95.037986)
        (xy 59.168501 -95.040671)
        (xy 59.061635 -95.063386)
        (xy 58.994633 -95.077628)
        (xy 58.994629 -95.077628)
        (xy 58.988176 -95.079)
        (xy 58.803824 -95.079)
        (xy 58.797371 -95.077628)
        (xy 58.797367 -95.077628)
        (xy 58.730365 -95.063386)
        (xy 58.623499 -95.040671)
        (xy 58.61747 -95.037986)
        (xy 58.617468 -95.037986)
        (xy 58.461115 -94.968373)
        (xy 58.461113 -94.968372)
        (xy 58.455085 -94.965688)
        (xy 58.449744 -94.961808)
        (xy 58.449743 -94.961807)
        (xy 58.311284 -94.861211)
        (xy 58.311282 -94.861209)
        (xy 58.30594 -94.857328)
        (xy 58.301519 -94.852418)
        (xy 58.301518 -94.852417)
        (xy 58.219943 -94.761818)
        (xy 58.182584 -94.720327)
        (xy 58.179285 -94.714614)
        (xy 58.179282 -94.714609)
        (xy 58.095131 -94.568855)
        (xy 58.090407 -94.560673)
        (xy 58.033439 -94.385343)
        (xy 58.032749 -94.37878)
        (xy 58.032749 -94.378779)
        (xy 58.02131 -94.269943)
        (xy 57.994297 -94.204287)
        (xy 57.990645 -94.201739)
        (xy 57.800607 -94.201739)
        (xy 57.801355 -94.202261)
        (xy 57.772056 -94.260447)
        (xy 57.77069 -94.269943)
        (xy 57.759251 -94.378779)
        (xy 57.759251 -94.37878)
        (xy 57.758561 -94.385343)
        (xy 57.701593 -94.560673)
        (xy 57.696869 -94.568855)
        (xy 57.612718 -94.714609)
        (xy 57.612715 -94.714614)
        (xy 57.609416 -94.720327)
        (xy 57.572058 -94.761818)
        (xy 57.490482 -94.852417)
        (xy 57.490481 -94.852418)
        (xy 57.48606 -94.857328)
        (xy 57.336916 -94.965688)
        (xy 57.330888 -94.968372)
        (xy 57.330886 -94.968373)
        (xy 57.174532 -95.037986)
        (xy 57.174531 -95.037986)
        (xy 57.168501 -95.040671)
        (xy 57.061635 -95.063386)
        (xy 56.994633 -95.077628)
        (xy 56.994629 -95.077628)
        (xy 56.988176 -95.079)
        (xy 56.803824 -95.079)
        (xy 56.797371 -95.077628)
        (xy 56.797367 -95.077628)
        (xy 56.730365 -95.063386)
        (xy 56.623499 -95.040671)
        (xy 56.61747 -95.037986)
        (xy 56.617468 -95.037986)
        (xy 56.461115 -94.968373)
        (xy 56.461113 -94.968372)
        (xy 56.455085 -94.965688)
        (xy 56.449744 -94.961808)
        (xy 56.449743 -94.961807)
        (xy 56.311284 -94.861211)
        (xy 56.311282 -94.861209)
        (xy 56.30594 -94.857328)
        (xy 56.301519 -94.852418)
        (xy 56.301518 -94.852417)
        (xy 56.219943 -94.761818)
        (xy 56.182584 -94.720327)
        (xy 56.179285 -94.714614)
        (xy 56.179282 -94.714609)
        (xy 56.095131 -94.568855)
        (xy 56.090407 -94.560673)
        (xy 56.033439 -94.385343)
        (xy 56.014169 -94.202)
        (xy 1.524 -94.202)
        (xy 1.524 -96.436382)
        (xy 101.406 -96.436382)
        (xy 101.406 -95.587618)
        (xy 101.408933 -95.544596)
        (xy 101.454137 -95.363293)
        (xy 101.457169 -95.357186)
        (xy 101.45717 -95.357182)
        (xy 101.52949 -95.211494)
        (xy 101.537219 -95.195925)
        (xy 101.541492 -95.19061)
        (xy 101.541493 -95.190609)
        (xy 101.636077 -95.072969)
        (xy 101.654302 -95.050302)
        (xy 101.799925 -94.933219)
        (xy 101.806031 -94.930188)
        (xy 101.806034 -94.930186)
        (xy 101.961182 -94.85317)
        (xy 101.961186 -94.853169)
        (xy 101.967293 -94.850137)
        (xy 102.148596 -94.804933)
        (xy 102.169947 -94.803477)
        (xy 102.189471 -94.802146)
        (xy 102.189482 -94.802146)
        (xy 102.191618 -94.802)
        (xy 102.942796 -94.802)
        (xy 103.010917 -94.781998)
        (xy 103.020169 -94.77132)
        (xy 103.067746 -94.801896)
        (xy 103.080338 -94.804724)
        (xy 103.083404 -94.804933)
        (xy 103.088798 -94.806278)
        (xy 103.088799 -94.806278)
        (xy 103.258085 -94.848486)
        (xy 103.264707 -94.850137)
        (xy 103.270814 -94.853169)
        (xy 103.270818 -94.85317)
        (xy 103.425966 -94.930186)
        (xy 103.425969 -94.930188)
        (xy 103.432075 -94.933219)
        (xy 103.577698 -95.050302)
        (xy 103.595923 -95.072969)
        (xy 103.690507 -95.190609)
        (xy 103.690508 -95.19061)
        (xy 103.694781 -95.195925)
        (xy 103.70251 -95.211494)
        (xy 103.77483 -95.357182)
        (xy 103.774831 -95.357186)
        (xy 103.777863 -95.363293)
        (xy 103.823067 -95.544596)
        (xy 103.826 -95.587618)
        (xy 103.826 -96.436382)
        (xy 103.823067 -96.479404)
        (xy 103.777863 -96.660707)
        (xy 103.774831 -96.666814)
        (xy 103.77483 -96.666818)
        (xy 103.697814 -96.821966)
        (xy 103.697812 -96.821969)
        (xy 103.694781 -96.828075)
        (xy 103.577698 -96.973698)
        (xy 103.432075 -97.090781)
        (xy 103.425969 -97.093812)
        (xy 103.425966 -97.093814)
        (xy 103.270818 -97.17083)
        (xy 103.270814 -97.170831)
        (xy 103.264707 -97.173863)
        (xy 103.091105 -97.217147)
        (xy 106.733186 -97.217147)
        (xy 106.749747 -96.929929)
        (xy 106.750572 -96.925724)
        (xy 106.750573 -96.925716)
        (xy 106.768687 -96.833391)
        (xy 106.805134 -96.647616)
        (xy 106.806521 -96.643566)
        (xy 106.806522 -96.643561)
        (xy 106.827011 -96.583718)
        (xy 106.898324 -96.375432)
        (xy 107.027591 -96.118413)
        (xy 107.190543 -95.881316)
        (xy 107.384166 -95.668528)
        (xy 107.387455 -95.665778)
        (xy 107.601583 -95.486738)
        (xy 107.601588 -95.486734)
        (xy 107.604875 -95.483986)
        (xy 107.726731 -95.407546)
        (xy 107.844948 -95.333388)
        (xy 107.844952 -95.333386)
        (xy 107.848588 -95.331105)
        (xy 107.852498 -95.32934)
        (xy 107.852499 -95.329339)
        (xy 108.106883 -95.21448)
        (xy 108.106887 -95.214478)
        (xy 108.110795 -95.212714)
        (xy 108.180123 -95.192178)
        (xy 108.382529 -95.132222)
        (xy 108.382534 -95.132221)
        (xy 108.386642 -95.131004)
        (xy 108.390876 -95.130356)
        (xy 108.390881 -95.130355)
        (xy 108.64045 -95.092166)
        (xy 108.671027 -95.087487)
        (xy 108.817483 -95.085186)
        (xy 108.954396 -95.083035)
        (xy 108.954402 -95.083035)
        (xy 108.958687 -95.082968)
        (xy 109.244298 -95.117531)
        (xy 109.522577 -95.190536)
        (xy 109.526537 -95.192176)
        (xy 109.526542 -95.192178)
        (xy 109.66967 -95.251464)
        (xy 109.788372 -95.300632)
        (xy 109.811364 -95.314067)
        (xy 110.033069 -95.443621)
        (xy 110.03307 -95.443621)
        (xy 110.036767 -95.445782)
        (xy 110.082579 -95.481703)
        (xy 110.259792 -95.620657)
        (xy 110.263164 -95.623301)
        (xy 110.463375 -95.829902)
        (xy 110.633694 -96.061764)
        (xy 110.635737 -96.065527)
        (xy 110.635741 -96.065533)
        (xy 110.768921 -96.31082)
        (xy 110.770971 -96.314595)
        (xy 110.872664 -96.583718)
        (xy 110.936892 -96.864152)
        (xy 110.957118 -97.090781)
        (xy 110.962246 -97.14824)
        (xy 110.962246 -97.148247)
        (xy 110.962466 -97.150708)
        (xy 110.96293 -97.195)
        (xy 110.961289 -97.219067)
        (xy 110.943654 -97.477752)
        (xy 110.943653 -97.477758)
        (xy 110.943362 -97.482029)
        (xy 110.937955 -97.508141)
        (xy 110.885892 -97.759541)
        (xy 110.885021 -97.763747)
        (xy 110.877479 -97.785047)
        (xy 110.790418 -98.030899)
        (xy 110.788987 -98.03494)
        (xy 110.657036 -98.290591)
        (xy 110.641312 -98.312964)
        (xy 110.494073 -98.522464)
        (xy 110.494068 -98.52247)
        (xy 110.491609 -98.525969)
        (xy 110.295769 -98.736718)
        (xy 110.073139 -98.918938)
        (xy 109.827839 -99.069259)
        (xy 109.564407 -99.184897)
        (xy 109.560279 -99.186073)
        (xy 109.560276 -99.186074)
        (xy 109.291847 -99.262538)
        (xy 109.291848 -99.262538)
        (xy 109.287719 -99.263714)
        (xy 109.075268 -99.29395)
        (xy 109.007146 -99.303645)
        (xy 109.007144 -99.303645)
        (xy 109.002894 -99.30425)
        (xy 108.998605 -99.304272)
        (xy 108.998598 -99.304273)
        (xy 108.719488 -99.305735)
        (xy 108.719481 -99.305735)
        (xy 108.715202 -99.305757)
        (xy 108.710958 -99.305198)
        (xy 108.710954 -99.305198)
        (xy 108.585208 -99.288643)
        (xy 108.429969 -99.268205)
        (xy 108.425829 -99.267072)
        (xy 108.425827 -99.267072)
        (xy 108.409254 -99.262538)
        (xy 108.15247 -99.19229)
        (xy 108.148522 -99.190606)
        (xy 107.891794 -99.081102)
        (xy 107.89179 -99.0811)
        (xy 107.887842 -99.079416)
        (xy 107.87375 -99.070982)
        (xy 107.644663 -98.933877)
        (xy 107.644659 -98.933874)
        (xy 107.640981 -98.931673)
        (xy 107.416455 -98.751794)
        (xy 107.413511 -98.748692)
        (xy 107.413507 -98.748688)
        (xy 107.326182 -98.656666)
        (xy 107.218418 -98.543107)
        (xy 107.050536 -98.309475)
        (xy 106.915915 -98.05522)
        (xy 106.817046 -97.785047)
        (xy 106.755758 -97.503955)
        (xy 106.733186 -97.217147)
        (xy 103.091105 -97.217147)
        (xy 103.083404 -97.219067)
        (xy 103.062053 -97.220523)
        (xy 103.042529 -97.221854)
        (xy 103.042518 -97.221854)
        (xy 103.040382 -97.222)
        (xy 102.191618 -97.222)
        (xy 102.189482 -97.221854)
        (xy 102.189471 -97.221854)
        (xy 102.169947 -97.220523)
        (xy 102.148596 -97.219067)
        (xy 101.967293 -97.173863)
        (xy 101.961186 -97.170831)
        (xy 101.961182 -97.17083)
        (xy 101.806034 -97.093814)
        (xy 101.806031 -97.093812)
        (xy 101.799925 -97.090781)
        (xy 101.654302 -96.973698)
        (xy 101.537219 -96.828075)
        (xy 101.534188 -96.821969)
        (xy 101.534186 -96.821966)
        (xy 101.45717 -96.666818)
        (xy 101.457169 -96.666814)
        (xy 101.454137 -96.660707)
        (xy 101.408933 -96.479404)
        (xy 101.406 -96.436382)
        (xy 1.524 -96.436382)
        (xy 1.524 -100.966)
        (xy 1.544002 -101.034121)
        (xy 1.597658 -101.080614)
        (xy 1.65 -101.092)
        (xy 118.746 -101.092)
      )
    )
  )
)
