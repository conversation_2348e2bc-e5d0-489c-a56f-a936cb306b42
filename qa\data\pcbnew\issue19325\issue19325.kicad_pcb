(kicad_pcb
	(version 20240108)
	(generator "pcbnew")
	(generator_version "8.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(31 "B.Cu" signal)
		(32 "B.Adhes" user "B.Adhesive")
		(33 "F<PERSON>hes" user "F.Adhesive")
		(34 "B.Paste" user)
		(35 "F.Paste" user)
		(36 "B.SilkS" user "B.Silkscreen")
		(37 "F.<PERSON>" user "F.Silkscreen")
		(38 "B.Mask" user)
		(39 "F.Mask" user)
		(40 "Dwgs.User" user "User.Drawings")
		(41 "Cmts.User" user "User.Comments")
		(42 "Eco1.User" user "User.Eco1")
		(43 "Eco2.User" user "User.Eco2")
		(44 "Edge.Cuts" user)
		(45 "Margin" user)
		(46 "B.CrtYd" user "B.Courtyard")
		(47 "F.CrtYd" user "F.Courtyard")
		(48 "B.Fab" user)
		(49 "F.Fab" user)
		(50 "User.1" user)
		(51 "User.2" user)
		(52 "User.3" user)
		(53 "User.4" user)
		(54 "User.5" user)
		(55 "User.6" user)
		(56 "User.7" user)
		(57 "User.8" user)
		(58 "User.9" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(pcbplotparams
			(layerselection 0x00010fc_ffffffff)
			(plot_on_all_layers_selection 0x0000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(viasonmask no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotreference yes)
			(plotvalue yes)
			(plotfptext yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(footprint "DAQ_Eurocard"
		(layer "F.Cu")
		(uuid "8bfb7590-9453-4d0d-a1da-ba41dcc3ad9c")
		(at 59.375 146.4)
		(property "Reference" "REF**"
			(at 6 1.5 0)
			(unlocked yes)
			(layer "F.SilkS")
			(uuid "cb185c70-e40b-485c-a2ec-4a71034a0cef")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "DAQ_Eurocard"
			(at 6 3 0)
			(unlocked yes)
			(layer "F.Fab")
			(uuid "1691d842-b22a-4e81-8291-216caf19eefb")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Footprint" ""
			(at 6 2 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "5b2fe82d-d756-4f2d-8201-0851d83e87b9")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 6 2 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "2ab16ad5-c042-4ea0-b724-f7092e363cfa")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 6 2 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "1d685e05-a110-43ed-b4d1-daf3c603594e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(attr through_hole)
		(fp_text user "${REFERENCE}"
			(at 6 5 0)
			(layer "F.Fab")
			(uuid "b8e14abe-00bd-45ca-b550-155b6e33795d")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "MH" thru_hole circle
			(at -4.57 -3.6)
			(size 0.34 0.34)
			(drill 0.3)
			(layers "*.Cu")
			(remove_unused_layers yes)
			(keep_end_layers yes)
			(zone_layer_connections)
			(uuid "4ccbf1d2-81ec-4e07-b4d2-142e278be757")
		)
		(pad "MH" thru_hole circle
			(at -3.57 -1.55)
			(size 0.34 0.34)
			(drill 0.3)
			(property pad_prop_castellated)
			(layers "*.Cu")
			(remove_unused_layers yes)
			(keep_end_layers yes)
			(zone_layer_connections)
			(uuid "906962f6-d763-4538-b561-5883603cce0e")
		)
		(pad "MH" thru_hole circle
			(at -2.32 -2.55)
			(size 0.34 0.34)
			(drill 0.3)
			(layers "*.Cu")
			(remove_unused_layers yes)
			(keep_end_layers yes)
			(zone_layer_connections)
			(uuid "ed4cb1e9-44af-402e-941f-f8a8751f5d69")
		)
		(pad "MH" thru_hole circle
			(at -0.739814 -1.170149)
			(size 0.34 0.34)
			(drill 0.3)
			(layers "*.Cu")
			(remove_unused_layers yes)
			(keep_end_layers yes)
			(zone_layer_connections)
			(uuid "0e564731-c5c1-4cd7-8927-07c00eab1591")
		)
		(pad "MH" thru_hole custom
			(at 0 0)
			(size 4.7 4.7)
			(drill 2.7)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(options
				(clearance outline)
				(anchor circle)
			)
			(primitives
				(gr_poly
					(pts
						(xy 3.380001 1.599999) (xy 3.380001 -2.100001) (xy 4.380001 -3.100001) (xy -3.569999 -3.100001)
						(xy -3.569999 2.599999) (xy 2.380001 2.599999)
					)
					(width 0.1)
					(fill yes)
				)
			)
			(uuid "3e4f0059-2d3a-432a-a90d-790c645de9b1")
		)
		(model "${KIPRJMOD}/GATE_KiCad_Lib/3D/EuroCard_FrontPanel_3U_6HP_noPanel.stp"
			(offset
				(xyz -2.55 -11.2 3.5)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 90 90 0)
			)
		)
		(model "${KIPRJMOD}/GATE_KiCad_Lib/3D/Harting_09031967921.stp"
			(offset
				(xyz 161 50 3.9)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz -90 0 -90)
			)
		)
	)
	(gr_line
		(start 47.25 139.625)
		(end 58.45 145.125)
		(stroke
			(width 0.1)
			(type default)
		)
		(layer "Cmts.User")
		(uuid "55a46a51-a7fe-4088-83de-514f56fb27a1")
	)
	(gr_line
		(start 47.25 139.625)
		(end 54.5 142.625)
		(stroke
			(width 0.1)
			(type default)
		)
		(layer "Cmts.User")
		(uuid "ae58718a-51fe-4a34-b12e-701e2c8e0c90")
	)
	(gr_line
		(start 47.25 139.625)
		(end 59.4 146.325)
		(stroke
			(width 0.1)
			(type default)
		)
		(layer "Cmts.User")
		(uuid "bfff4060-009c-4666-a8c5-ba038f7af93d")
	)
	(gr_line
		(start 47.25 139.625)
		(end 55.6 144.7)
		(stroke
			(width 0.1)
			(type default)
		)
		(layer "Cmts.User")
		(uuid "c394c9fc-0242-4881-aab4-58e171251c3c")
	)
	(gr_line
		(start 55.95 134.65)
		(end 57.05 143.55)
		(stroke
			(width 0.1)
			(type default)
		)
		(layer "Cmts.User")
		(uuid "dca0e6db-1de6-49d0-9325-e84ab52fb2b7")
	)
	(gr_rect
		(start 51.025 137.675)
		(end 70.55 154.725)
		(stroke
			(width 0.05)
			(type default)
		)
		(fill none)
		(layer "Edge.Cuts")
		(uuid "484f17a3-b864-4513-9996-66399888f9dd")
	)
	(gr_text "There should be 4 violations of annular ring"
		(at 32.325 139.075 0)
		(layer "Cmts.User")
		(uuid "28288875-072d-4009-abcf-c2a3b03b5c2b")
		(effects
			(font
				(size 0.5 0.5)
				(thickness 0.15)
			)
			(justify left bottom)
		)
	)
	(gr_text "This hole should not have\nan error b/c its annular ring\nshould be from the bigger pad"
		(at 51.875 134.275 0)
		(layer "Cmts.User")
		(uuid "c842f852-45a6-47f1-878c-1af45ff0fad5")
		(effects
			(font
				(size 0.5 0.5)
				(thickness 0.15)
			)
			(justify left bottom)
		)
	)
)
