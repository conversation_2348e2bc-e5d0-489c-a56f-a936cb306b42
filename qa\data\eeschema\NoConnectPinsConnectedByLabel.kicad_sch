(kicad_sch (version 20210621) (generator eeschema)

  (uuid 0f9e1d66-b8ed-495e-9f18-8de0c9aeaa30)

  (paper "A4")

  (lib_symbols
    (symbol "Connector:TestPoint" (pin_numbers hide) (pin_names (offset 0.762) hide) (in_bom yes) (on_board yes)
      (property "Reference" "TP" (id 0) (at 0 6.858 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "TestPoint" (id 1) (at 0 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 5.08 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 5.08 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "test point tp" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "test point" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Pin* Test*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "TestPoint_0_1"
        (circle (center 0 3.302) (radius 0.762) (stroke (width 0)) (fill (type none)))
      )
      (symbol "TestPoint_1_1"
        (pin passive line (at 0 0 90) (length 2.54)
          (name "1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )



  (no_connect (at 29.21 29.21) (uuid cdfb9b52-55cd-430a-8376-4fa8cc3718b3))

  (wire (pts (xy 29.21 22.86) (xy 29.21 29.21))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid 852bbc08-6c3b-44ae-9ce3-13eb108cee14)
  )
  (wire (pts (xy 44.45 22.86) (xy 44.45 27.94))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid b2d86fc4-7977-4adb-91d8-c1a79f5281a6)
  )

  (label "test" (at 29.21 27.94 90)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 9967f29b-1fdf-48dd-8c6a-0d6ad57e40c1)
  )
  (label "test" (at 44.45 27.94 90)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid f029ede5-c93d-4f9a-9006-c37df43817b6)
  )

  (symbol (lib_id "Connector:TestPoint") (at 29.21 22.86 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 819e043f-ce2f-49c7-bc14-3fd537e0c7e5)
    (property "Reference" "TP1" (id 0) (at 31.75 19.7484 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "TestPoint" (id 1) (at 31.75 22.2884 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 34.29 22.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 34.29 22.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid abfb681a-3003-4344-9d0b-b80b02b873da))
  )

  (symbol (lib_id "Connector:TestPoint") (at 44.45 22.86 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid a43439ba-66cf-4a38-92d1-0cd9bfd44db4)
    (property "Reference" "TP2" (id 0) (at 46.99 19.7484 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "TestPoint" (id 1) (at 46.99 22.2884 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 49.53 22.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 49.53 22.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 48f406e8-2d99-4dde-9e9a-0fa75fb3df24))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/819e043f-ce2f-49c7-bc14-3fd537e0c7e5"
      (reference "TP1") (unit 1) (value "TestPoint") (footprint "")
    )
    (path "/a43439ba-66cf-4a38-92d1-0cd9bfd44db4"
      (reference "TP2") (unit 1) (value "TestPoint") (footprint "")
    )
  )
)
