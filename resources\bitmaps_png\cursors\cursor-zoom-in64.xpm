/* XPM */
static char const * cursor_zoom_in64_xpm[] = {
"64 64 3 1",
" 	c None",
".	c #FFFFFF",
"+	c #000000",
"                                                                ",
"        ........                                                ",
"      ...++++++...                                              ",
"    ...+++    +++...                                            ",
"   ..+++        +++..                                           ",
"  ..++            ++..                                          ",
"  .++              ++.                                          ",
" ..+       ..       +..                                         ",
" .++      .++.      ++.                                         ",
"..+       .++.       +..                                        ",
".++       .++.       ++.                                        ",
".+     ....++....     +.                                        ",
".+    .++++++++++.    +.                                        ",
".+    .++++++++++.    +.                                        ",
".+     ....++....     +.                                        ",
".++       .++.       ++.                                        ",
"..+       .++.       +..                                        ",
" .++      .++.      ++.                                         ",
" ..+       ..       +..                                         ",
"  .++              ++...                                        ",
"  ..++            ++..++...                                     ",
"   ..+++        +++..+++.++.                                    ",
"    ...+++    +++...+++.++++.                                   ",
"      ...++++++... .++.++  ++.                                  ",
"        ........    ..++    ++.                                 ",
"                    .++      ++.                                ",
"                    .++       ++.                               ",
"                     .++       ++.                              ",
"                      .++       ++.                             ",
"                       .++       ++.                            ",
"                        .++       ++.                           ",
"                         .++       ++.                          ",
"                          .++       ++.                         ",
"                           .++       ++.                        ",
"                            .++      ++.                        ",
"                             .++    ++.                         ",
"                              .++  ++.                          ",
"                               .++++.                           ",
"                                .++.                            ",
"                                 ..                             ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                "};
