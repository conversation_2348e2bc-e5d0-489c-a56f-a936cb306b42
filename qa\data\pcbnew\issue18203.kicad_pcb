(kicad_pcb
	(version 20240108)
	(generator "pcbnew")
	(generator_version "8.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(31 "B.Cu" signal)
		(32 "B.Adhes" user "B.Adhesive")
		(33 "F<PERSON>hes" user "F.Adhesive")
		(34 "B.Paste" user)
		(35 "F.Paste" user)
		(36 "B.SilkS" user "B.Silkscreen")
		(37 "F.<PERSON>" user "F.Silkscreen")
		(38 "B.Mask" user)
		(39 "F.Mask" user)
		(40 "Dwgs.User" user "User.Drawings")
		(41 "Cmts.User" user "User.Comments")
		(42 "Eco1.User" user "User.Eco1")
		(43 "Eco2.User" user "User.Eco2")
		(44 "Edge.Cuts" user)
		(45 "Margin" user)
		(46 "B.CrtYd" user "B.Courtyard")
		(47 "F.CrtYd" user "F.Courtyard")
		(48 "B.Fab" user)
		(49 "F.Fab" user)
		(50 "User.1" user)
		(51 "User.2" user)
		(52 "User.3" user)
		(53 "User.4" user)
		(54 "User.5" user)
		(55 "User.6" user)
		(56 "User.7" user)
		(57 "User.8" user)
		(58 "User.9" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(pcbplotparams
			(layerselection 0x00010fc_ffffffff)
			(plot_on_all_layers_selection 0x0000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(viasonmask no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotreference yes)
			(plotvalue yes)
			(plotfptext yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(footprint "MountingHole:MountingHole_3.2mm_M3_ISO14580_Pad"
		(layer "F.Cu")
		(uuid "7726c890-db15-4ad7-814e-dd8447b43ed2")
		(at 70.3 153.4)
		(descr "Mounting Hole 3.2mm, M3, ISO14580")
		(tags "mounting hole 3.2mm m3 iso14580")
		(property "Reference" "H205"
			(at 0 -3.75 0)
			(layer "F.SilkS")
			(hide yes)
			(uuid "69cccef0-a8a2-48a2-be02-3b276d7072c3")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "M3"
			(at 0 3.75 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "1b7e40fa-418f-45d5-b0d9-c3f086a93cf6")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Footprint" "MountingHole:MountingHole_3.2mm_M3_ISO14580_Pad"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "cda40eb5-55d9-4984-922a-3cd45a7fcb03")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "9612decc-23e6-4abe-a20f-28237de64d36")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" "Mounting Hole with connection"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "33191b5e-4a3b-4d2f-bdd1-90692f6686a8")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(attr exclude_from_pos_files exclude_from_bom)
		(fp_circle
			(center 0 0)
			(end 2.75 0)
			(stroke
				(width 0.15)
				(type solid)
			)
			(fill none)
			(layer "Cmts.User")
			(uuid "49f8db1f-3f32-4c10-83f5-25b68933e968")
		)
		(fp_circle
			(center 0 0)
			(end 3 0)
			(stroke
				(width 0.05)
				(type solid)
			)
			(fill none)
			(layer "F.CrtYd")
			(uuid "618b7e68-8d12-46da-9dff-96876e14c0ad")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "29fb7ad8-c6ed-480b-ade2-449708344f36")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 5 5.5)
			(drill 3.2)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(pinfunction "1")
			(pintype "input+no_connect")
			(uuid "d7f69fe4-85ef-4985-a12a-043b32cbe18d")
		)
	)
	(gr_line
		(start 67.3 153.4)
		(end 67.3 140.7)
		(stroke
			(width 0.1)
			(type default)
		)
		(layer "Edge.Cuts")
		(uuid "97b5451b-be12-48cb-97bb-046b12351275")
	)
	(gr_line
		(start 67.3 140.7)
		(end 80.1 140.7)
		(stroke
			(width 0.1)
			(type default)
		)
		(layer "Edge.Cuts")
		(uuid "a45d033f-eded-4908-8b8b-a4856e094f4d")
	)
	(gr_arc
		(start 70.3 156.4)
		(mid 68.178686 155.521314)
		(end 67.3 153.4)
		(stroke
			(width 0.1)
			(type default)
		)
		(layer "Edge.Cuts")
		(uuid "aa375595-1206-4739-8744-4e01967a5e45")
	)
	(gr_line
		(start 80.1 156.4)
		(end 70.3 156.4)
		(stroke
			(width 0.1)
			(type default)
		)
		(layer "Edge.Cuts")
		(uuid "d1e9169a-9d96-4c1d-9c99-bceeb7cce997")
	)
	(gr_line
		(start 80.1 140.7)
		(end 80.1 156.4)
		(stroke
			(width 0.1)
			(type default)
		)
		(layer "Edge.Cuts")
		(uuid "f6a2d3af-1670-4475-9393-9f14f5ab578f")
	)
)
