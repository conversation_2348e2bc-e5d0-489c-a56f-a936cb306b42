(kicad_pcb (version 20230620) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.Adhes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
    (50 "User.1" user)
    (51 "User.2" user)
    (52 "User.3" user)
    (53 "User.4" user)
    (54 "User.5" user)
    (55 "User.6" user)
    (56 "User.7" user)
    (57 "User.8" user)
    (58 "User.9" user)
  )

  (setup
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 4)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (pdf_front_fp_property_popups true)
      (pdf_back_fp_property_popups true)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "Test1")
  (net 2 "Test2")
  (net 3 "Test3")

  (footprint "Connector_Wire:SolderWirePad_1x01_SMD_1x2mm" (layer "B.Cu")
    (tstamp ca5993ae-dd71-48b4-90b5-80f8f75da987)
    (at 153.75 83.55 180)
    (descr "Wire Pad, Square, SMD Pad,  5mm x 10mm,")
    (tags "MesurementPoint Square SMDPad 5mmx10mm ")
    (property "Reference" "REF**" (at 0 2.54 0) (layer "B.SilkS") (tstamp 6399a8cf-5382-4bce-ba64-4dfde3ade47a)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Value" "SolderWirePad_1x01_SMD_1x2mm" (at 0 -2.54 0) (layer "B.Fab") (tstamp 430c8d54-ab51-43c0-92b3-5d6fbb078310)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Footprint" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp a19340b1-0d66-4f29-8b99-fe06f136a1a5)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Datasheet" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp a3c8bd05-8193-4c4e-9f08-751159a1f438)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Description" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp a0c09c7e-297d-47c3-842d-749ea0b5e9aa)
      (effects (font (size 1.5 1.5) (thickness 0.3)))
    )
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_line (start 0.63 1.27) (end -0.63 1.27)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp ee1db011-af60-43d7-8b53-6f3509e93f29))
    (fp_line (start 0.63 -1.27) (end 0.63 1.27)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp d66c1cf8-e4ab-4443-a7d8-edb1da6d4316))
    (fp_line (start -0.63 1.27) (end -0.63 -1.27)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 310f9ca4-b0af-47f2-a88a-b7ef1cf6fb3a))
    (fp_line (start -0.63 -1.27) (end 0.63 -1.27)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 6ca357af-ad11-43da-b598-c0750bdfde27))
    (fp_line (start 0.63 1.27) (end 0.63 -1.27)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 5e2f4003-df05-4479-b514-2002705ba3c5))
    (fp_line (start 0.63 -1.27) (end -0.63 -1.27)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp b2b534d0-5721-4e67-bcf2-5e13a3b4a76b))
    (fp_line (start -0.63 1.27) (end 0.63 1.27)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp c7131b36-fc5f-41c4-aa20-7fd3da3905b2))
    (fp_line (start -0.63 -1.27) (end -0.63 1.27)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp ff006fc0-ed05-4222-8abb-c96ac6671151))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "B.Fab") (tstamp a917afff-2f63-452d-b185-91ecbe43eead)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (pad "1" smd roundrect (at 0 0 180) (size 1 2) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 3 "Test3")
      (tstamp 5b7a22ae-aba8-4d28-b413-f13f01e1b3b7)
    )
  )

  (footprint "NetTie:NetTie-2_SMD_Pad0.5mm" (layer "B.Cu")
    (tstamp e79c5996-ed5b-4e02-88ac-11f9036c04c1)
    (at 155.35 85.75 -90)
    (descr "Net tie, 2 pin, 0.5mm square SMD pads")
    (tags "net tie")
    (property "Reference" "NT2" (at 0 1.2 90) (layer "B.SilkS") hide (tstamp 146ccf02-58cd-48fa-91d5-369ef2b2fc43)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Value" "Net-Tie_2" (at 0 -1.2 90) (layer "B.Fab") (tstamp 8ecd81aa-f7ad-48a3-ad05-c7ce3d88a782)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Footprint" "" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 7be8ff44-eb1d-4b09-a149-179939c55183)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Datasheet" "" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp cfa60fd9-e837-4978-92fd-93fbb3b5cd65)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Description" "Net tie, 2 pins" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 852dc496-0cb6-44c0-8a70-71a964205c42)
      (effects (font (size 1.5 1.5) (thickness 0.3)))
    )
    (property "exclude_from_bom" "" (at 0 0 0) (layer "B.Fab") hide (tstamp e5bf200c-3c4e-4bf7-be10-2fcf4e1c5179)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (attr exclude_from_pos_files exclude_from_bom)
    (net_tie_pad_groups "1, 2")
    (fp_poly
      (pts
        (xy -0.5 0.25)
        (xy 0.5 0.25)
        (xy 0.5 -0.25)
        (xy -0.5 -0.25)
      )
      (stroke (width 0) (type solid)) (fill solid) (layer "B.Cu") (tstamp 4e729321-142d-4160-a36b-2f04a38006f8))
    (pad "1" smd circle (at -0.5 0 270) (size 0.5 0.5) (layers "B.Cu")
      (net 1 "Test1") (pinfunction "1") (pintype "passive")
      (tstamp 1032c1e0-1464-4daf-9a92-755e91fe89e6)
    )
    (pad "2" smd circle (at 0.5 0 270) (size 0.5 0.5) (layers "B.Cu")
      (net 2 "Test2") (pinfunction "2") (pintype "passive")
      (tstamp f61d1578-8818-4727-8655-15416c8bd70b)
    )
  )

  (gr_rect (start 139.573 79.375) (end 163.957 93.091)
    (stroke (width 0.05) (type default)) (fill none) (layer "Edge.Cuts") (tstamp 2146930a-368d-424c-864c-4d2ba41cbcae))
  (gr_text "Otherwise" (at 155.194 89.281 0) (layer "B.Cu" knockout) (tstamp 6c4f9b12-5489-4923-9fc0-4f8130f1cb8b)
    (effects (font (size 1.5 1.5) (thickness 0.3) bold) (justify left bottom mirror))
  )
  (gr_text "What" (at 156.718 84.455 0) (layer "B.Cu") (tstamp 7cdcf930-f446-408b-862f-6846f278bce8)
    (effects (font (size 1 1) (thickness 0.15)) (justify left bottom))
  )

  (zone (net 2) (net_name "Test2") (layer "B.Cu") (tstamp a78787ef-dd0d-4073-9547-f9e19d46d566) (hatch edge 0.5)
    (priority 2)
    (connect_pads (clearance 0.5))
    (min_thickness 0.25) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.5) (thermal_bridge_width 0.5) (island_removal_mode 1) (island_area_min 10))
    (polygon
      (pts
        (xy 143.637 84.582)
        (xy 147.955 84.582)
        (xy 147.955 91.694)
        (xy 143.637 91.694)
      )
    )
    (filled_polygon
      (layer "B.Cu")
      (pts
        (xy 147.898039 84.601685)
        (xy 147.943794 84.654489)
        (xy 147.955 84.706)
        (xy 147.955 87.14587)
        (xy 147.935315 87.212909)
        (xy 147.882511 87.258664)
        (xy 147.831 87.26987)
        (xy 144.184952 87.26987)
        (xy 144.184952 89.393786)
        (xy 147.831 89.393786)
        (xy 147.898039 89.413471)
        (xy 147.943794 89.466275)
        (xy 147.955 89.517786)
        (xy 147.955 91.57)
        (xy 147.935315 91.637039)
        (xy 147.882511 91.682794)
        (xy 147.831 91.694)
        (xy 143.761 91.694)
        (xy 143.693961 91.674315)
        (xy 143.648206 91.621511)
        (xy 143.637 91.57)
        (xy 143.637 84.706)
        (xy 143.656685 84.638961)
        (xy 143.709489 84.593206)
        (xy 143.761 84.582)
        (xy 147.831 84.582)
      )
    )
  )
  (zone (net 3) (net_name "Test3") (layer "B.Cu") (tstamp d8faf083-2cf6-4659-b189-791fef968e17) (hatch edge 0.5)
    (connect_pads (clearance 0.127))
    (min_thickness 0.2) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.5) (thermal_bridge_width 0.5))
    (polygon
      (pts
        (xy 157.4 82.05)
        (xy 157.4 88.1)
        (xy 152.7 88.1)
        (xy 152.7 82.05)
      )
    )
    (filled_polygon
      (layer "B.Cu")
      (pts
        (xy 157.359191 82.068907)
        (xy 157.395155 82.118407)
        (xy 157.4 82.149)
        (xy 157.4 88.001)
        (xy 157.381093 88.059191)
        (xy 157.331593 88.095155)
        (xy 157.301 88.1)
        (xy 155.116894 88.1)
        (xy 155.058703 88.081093)
        (xy 155.022739 88.031593)
        (xy 155.017894 88.001)
        (xy 155.017894 87.269871)
        (xy 155.017894 87.26987)
        (xy 152.799 87.26987)
        (xy 152.740809 87.250963)
        (xy 152.704845 87.201463)
        (xy 152.7 87.17087)
        (xy 152.7 86.25)
        (xy 154.967794 86.25)
        (xy 154.9865 86.368107)
        (xy 154.986501 86.368111)
        (xy 155.040787 86.474652)
        (xy 155.040789 86.474655)
        (xy 155.125345 86.559211)
        (xy 155.125347 86.559212)
        (xy 155.125346 86.559212)
        (xy 155.231888 86.613498)
        (xy 155.231892 86.6135)
        (xy 155.33385 86.629648)
        (xy 155.349999 86.632206)
        (xy 155.35 86.632206)
        (xy 155.350001 86.632206)
        (xy 155.364206 86.629955)
        (xy 155.468108 86.6135)
        (xy 155.574655 86.559211)
        (xy 155.659211 86.474655)
        (xy 155.7135 86.368108)
        (xy 155.732206 86.25)
        (xy 155.7135 86.131892)
        (xy 155.659211 86.025345)
        (xy 155.574655 85.940789)
        (xy 155.574652 85.940787)
        (xy 155.574653 85.940787)
        (xy 155.468111 85.886501)
        (xy 155.468108 85.8865)
        (xy 155.409054 85.877147)
        (xy 155.350001 85.867794)
        (xy 155.349999 85.867794)
        (xy 155.231892 85.8865)
        (xy 155.231888 85.886501)
        (xy 155.125347 85.940787)
        (xy 155.040787 86.025347)
        (xy 154.986501 86.131888)
        (xy 154.9865 86.131892)
        (xy 154.967794 86.249999)
        (xy 154.967794 86.25)
        (xy 152.7 86.25)
        (xy 152.7 85.25)
        (xy 154.967794 85.25)
        (xy 154.9865 85.368107)
        (xy 154.986501 85.368111)
        (xy 155.040787 85.474652)
        (xy 155.040789 85.474655)
        (xy 155.125345 85.559211)
        (xy 155.125347 85.559212)
        (xy 155.125346 85.559212)
        (xy 155.231888 85.613498)
        (xy 155.231892 85.6135)
        (xy 155.33385 85.629648)
        (xy 155.349999 85.632206)
        (xy 155.35 85.632206)
        (xy 155.350001 85.632206)
        (xy 155.364206 85.629955)
        (xy 155.468108 85.6135)
        (xy 155.574655 85.559211)
        (xy 155.659211 85.474655)
        (xy 155.7135 85.368108)
        (xy 155.732206 85.25)
        (xy 155.7135 85.131892)
        (xy 155.659211 85.025345)
        (xy 155.574655 84.940789)
        (xy 155.574652 84.940787)
        (xy 155.574653 84.940787)
        (xy 155.468111 84.886501)
        (xy 155.468108 84.8865)
        (xy 155.409054 84.877147)
        (xy 155.350001 84.867794)
        (xy 155.349999 84.867794)
        (xy 155.231892 84.8865)
        (xy 155.231888 84.886501)
        (xy 155.125347 84.940787)
        (xy 155.040787 85.025347)
        (xy 154.986501 85.131888)
        (xy 154.9865 85.131892)
        (xy 154.967794 85.249999)
        (xy 154.967794 85.25)
        (xy 152.7 85.25)
        (xy 152.7 84.780722)
        (xy 152.718907 84.722531)
        (xy 152.768407 84.686567)
        (xy 152.829593 84.686567)
        (xy 152.879093 84.722531)
        (xy 152.883261 84.72875)
        (xy 152.90768 84.76834)
        (xy 153.031659 84.892319)
        (xy 153.180875 84.984356)
        (xy 153.347306 85.039506)
        (xy 153.450013 85.049999)
        (xy 153.5 85.049998)
        (xy 154 85.049998)
        (xy 154.000001 85.049999)
        (xy 154.049986 85.049999)
        (xy 154.152687 85.039507)
        (xy 154.152699 85.039504)
        (xy 154.319124 84.984356)
        (xy 154.46834 84.892319)
        (xy 154.592319 84.76834)
        (xy 154.684356 84.619124)
        (xy 154.739506 84.452693)
        (xy 154.75 84.349987)
        (xy 154.75 83.8)
        (xy 154.000001 83.8)
        (xy 154 83.800001)
        (xy 154 85.049998)
        (xy 153.5 85.049998)
        (xy 153.5 83.399)
        (xy 153.518907 83.340809)
        (xy 153.568407 83.304845)
        (xy 153.599 83.3)
        (xy 154.749998 83.3)
        (xy 154.749999 83.299999)
        (xy 154.749999 82.750013)
        (xy 154.739507 82.647312)
        (xy 154.739504 82.6473)
        (xy 154.684356 82.480875)
        (xy 154.592319 82.331659)
        (xy 154.479664 82.219004)
        (xy 154.451887 82.164487)
        (xy 154.461458 82.104055)
        (xy 154.504723 82.06079)
        (xy 154.549668 82.05)
        (xy 157.301 82.05)
      )
    )
  )
  (zone (net 3) (net_name "Test3") (layer "B.Cu") (tstamp e7fb42af-8060-4e89-b56c-5293c80f1b68) (hatch edge 0.5)
    (priority 1)
    (connect_pads (clearance 0.5))
    (min_thickness 0.25) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.5) (thermal_bridge_width 0.5) (island_removal_mode 1) (island_area_min 10))
    (polygon
      (pts
        (xy 159.385 89.154)
        (xy 150.241 89.154)
        (xy 150.241 91.313)
        (xy 159.385 91.313)
      )
    )
    (filled_polygon
      (layer "B.Cu")
      (pts
        (xy 159.328039 89.173685)
        (xy 159.373794 89.226489)
        (xy 159.385 89.278)
        (xy 159.385 91.189)
        (xy 159.365315 91.256039)
        (xy 159.312511 91.301794)
        (xy 159.261 91.313)
        (xy 150.365 91.313)
        (xy 150.297961 91.293315)
        (xy 150.252206 91.240511)
        (xy 150.241 91.189)
        (xy 150.241 89.517786)
        (xy 150.260685 89.450747)
        (xy 150.313489 89.404992)
        (xy 150.365 89.393786)
        (xy 155.017894 89.393786)
        (xy 155.017894 89.278)
        (xy 155.037579 89.210961)
        (xy 155.090383 89.165206)
        (xy 155.141894 89.154)
        (xy 159.261 89.154)
      )
    )
  )
)
