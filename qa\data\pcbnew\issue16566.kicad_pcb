(kicad_pcb
	(version 20231231)
	(generator "pcbnew")
	(generator_version "7.99")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(31 "B.Cu" signal)
		(32 "B.Adhes" user "B.Adhesive")
		(33 "F<PERSON>hes" user "F.Adhesive")
		(34 "B.Paste" user)
		(35 "F.Paste" user)
		(36 "B.SilkS" user "B.Silkscreen")
		(37 "F.<PERSON>" user "F.Silkscreen")
		(38 "B.Mask" user)
		(39 "F.Mask" user)
		(40 "Dwgs.User" user "User.Drawings")
		(41 "Cmts.User" user "User.Comments")
		(42 "Eco1.User" user "User.Eco1")
		(43 "Eco2.User" user "User.Eco2")
		(44 "Edge.Cuts" user)
		(45 "Margin" user)
		(46 "B.CrtYd" user "B.Courtyard")
		(47 "F.CrtYd" user "F.Courtyard")
		(48 "B.Fab" user)
		(49 "F.Fab" user)
		(50 "User.1" user)
		(51 "User.2" user)
		(52 "User.3" user)
		(53 "User.4" user)
		(54 "User.5" user)
		(55 "User.6" user)
		(56 "User.7" user)
		(57 "User.8" user)
		(58 "User.9" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(pcbplotparams
			(layerselection 0x00010fc_ffffffff)
			(plot_on_all_layers_selection 0x0000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(viasonmask no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotreference yes)
			(plotvalue yes)
			(plotfptext yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(net 1 "VCC")
	(net 2 "GND")
	(footprint "Resistor_SMD:R_1206_3216Metric"
		(layer "F.Cu")
		(uuid "bec61144-695e-45df-a68b-2fb7d976fdfa")
		(at 151.5725 89.535)
		(descr "Resistor SMD 1206 (3216 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R1"
			(at 0 -1.82 0)
			(layer "F.SilkS")
			(uuid "7f64c4bc-304e-4df6-8b8e-f74d21d27ce8")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "R"
			(at 0 1.82 0)
			(layer "F.Fab")
			(uuid "d55fab90-b467-4428-8b3a-02bb4fb10172")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Footprint" "Resistor_SMD:R_1206_3216Metric"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "12f3df93-256c-4a8a-bbb9-a02f2375918f")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "ef32ab47-8b9c-4257-9a36-4ca59d5c64a9")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Description" "Resistor"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "460509b2-a2cc-4e4c-b02e-31160aa69868")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(path "/dada3a60-98e6-45a2-a8c7-a90f3284a597")
		(attr smd)
		(fp_line
			(start -0.727064 -0.91)
			(end 0.727064 -0.91)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0e796235-1c35-404e-8ff9-3ef674e7d2f8")
		)
		(fp_line
			(start -0.727064 0.91)
			(end 0.727064 0.91)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "29093ac2-672f-41d2-95d3-83241eb8f68d")
		)
		(fp_line
			(start -2.28 -1.12)
			(end 2.28 -1.12)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "*************-4440-83cb-6de80274c1b4")
		)
		(fp_line
			(start -2.28 1.12)
			(end -2.28 -1.12)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b37cbb6d-8fb4-468b-9c18-cbd3c49ad8f1")
		)
		(fp_line
			(start 2.28 -1.12)
			(end 2.28 1.12)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "99aa4b3c-b698-4f9a-bac9-250fcefc3178")
		)
		(fp_line
			(start 2.28 1.12)
			(end -2.28 1.12)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b536385a-216e-4f13-9a0f-147befcafb26")
		)
		(fp_line
			(start -1.6 -0.8)
			(end 1.6 -0.8)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f068cf92-9aea-4ed9-8a82-65d03c00816c")
		)
		(fp_line
			(start -1.6 0.8)
			(end -1.6 -0.8)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "31d10202-643a-465c-9f18-600a1e3a1655")
		)
		(fp_line
			(start 1.6 -0.8)
			(end 1.6 0.8)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f12ef3cf-3b26-4519-b7fd-ada65c25a945")
		)
		(fp_line
			(start 1.6 0.8)
			(end -1.6 0.8)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "228ce64c-d99a-4acb-ba85-2414dd150988")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "4d18fa15-75c0-471d-a995-fc1f9393efdb")
			(effects
				(font
					(size 0.8 0.8)
					(thickness 0.12)
				)
			)
		)
		(pad "1" smd roundrect
			(at -1.4625 0)
			(size 1.125 1.75)
			(layers "F.Cu" "F.Paste" "F.Mask")
			(roundrect_rratio 0.222222)
			(net 1 "VCC")
			(pintype "passive")
			(uuid "f413018c-f4a3-4350-9444-46ee005605d8")
		)
		(pad "2" smd oval
			(at 1.4625 0)
			(size 1.125 1.75)
			(layers "F.Cu" "F.Paste" "F.Mask")
			(net 2 "GND")
			(pintype "passive")
			(uuid "f864d905-4e66-4c6a-9dbb-10ad926c266a")
		)
		(model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_1206_3216Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(gr_rect
		(start 92.71 62.865)
		(end 167.64 117.475)
		(stroke
			(width 0.1)
			(type default)
		)
		(fill none)
		(layer "Edge.Cuts")
		(uuid "47f4f7de-3e86-451e-afa2-b9c5401bb925")
	)
	(segment
		(start 153.9225 90.9843)
		(end 153.9225 86.8187)
		(width 0.25)
		(layer "F.Cu")
		(net 0)
		(uuid "11ed0c56-f17a-440e-9f40-7eb9be0013ba")
	)
	(segment
		(start 153.9225 86.8187)
		(end 154.2796 86.4616)
		(width 0.25)
		(layer "F.Cu")
		(net 0)
		(uuid "666f9f32-3952-4934-8df5-9ad24f041214")
	)
	(segment
		(start 153.0858 91.821)
		(end 153.9225 90.9843)
		(width 0.25)
		(layer "F.Cu")
		(net 0)
		(uuid "96063ec2-be50-433f-9564-7ca25d99ba52")
	)
)