(kicad_sch (version 20200828) (generator eeschema)

  (page 3 4)

  (paper "A4")

  (lib_symbols
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254)) (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )


  (bus_entry (at 151.13 93.98) (size 2.54 2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 151.13 104.14) (size 2.54 2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )

  (wire (pts (xy 153.67 96.52) (xy 168.91 96.52))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 153.67 106.68) (xy 168.91 106.68))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 168.91 96.52) (xy 168.91 97.79))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 168.91 105.41) (xy 168.91 106.68))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 132.08 91.44) (xy 151.13 91.44))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 151.13 91.44) (xy 151.13 93.98))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 151.13 93.98) (xy 151.13 104.14))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 151.13 104.14) (xy 151.13 111.76))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )

  (label "B1" (at 156.21 96.52 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "B2" (at 156.21 106.68 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )

  (hierarchical_label "B[20..0]" (shape input) (at 132.08 91.44 180)
    (effects (font (size 1.27 1.27)) (justify right))
  )

  (symbol (lib_id "Device:R") (at 168.91 101.6 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid "2099c994-a92c-409d-a12d-82f6c93cabc6")
    (property "Reference" "R2" (id 0) (at 170.688 100.457 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 170.6881 102.7493 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 167.132 101.6 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 168.91 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )
)
