(kicad_pcb
	(version 20240108)
	(generator "pcbnew")
	(generator_version "8.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(31 "B.Cu" signal)
		(32 "B.Adhes" user "B.Adhesive")
		(33 "F<PERSON>hes" user "F.Adhesive")
		(34 "B.Paste" user)
		(35 "F.Paste" user)
		(36 "B.SilkS" user "B.Silkscreen")
		(37 "F.<PERSON>" user "F.Silkscreen")
		(38 "B.Mask" user)
		(39 "F.Mask" user)
		(40 "Dwgs.User" user "User.Drawings")
		(41 "Cmts.User" user "User.Comments")
		(42 "Eco1.User" user "User.Eco1")
		(43 "Eco2.User" user "User.Eco2")
		(44 "Edge.Cuts" user)
		(45 "Margin" user)
		(46 "B.CrtYd" user "B.Courtyard")
		(47 "F.CrtYd" user "F.Courtyard")
		(48 "B.Fab" user)
		(49 "F.Fab" user)
		(50 "User.1" user)
		(51 "User.2" user)
		(52 "User.3" user)
		(53 "User.4" user)
		(54 "User.5" user)
		(55 "User.6" user)
		(56 "User.7" user)
		(57 "User.8" user)
		(58 "User.9" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(pcbplotparams
			(layerselection 0x00010fc_ffffffff)
			(plot_on_all_layers_selection 0x0000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(viasonmask no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotreference yes)
			(plotvalue yes)
			(plotfptext yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(net 1 "unconnected-(TP1-Pad1)")
	(net 2 "unconnected-(TP1-Pad1)_0")
	(footprint "TestPoint:TestPoint_Bridge_Pitch2.0mm_Drill0.7mm"
		(layer "F.Cu")
		(uuid "5680b68d-7137-4930-9dd5-5f4fafc5da8a")
		(at 138.030698 95.032002)
		(descr "wire loop as test point, pitch 2.0mm, hole diameter 0.7mm, wire diameter 0.5mm")
		(tags "test point wire loop")
		(property "Reference" "TP1"
			(at 1.1 1.7 0)
			(layer "F.SilkS")
			(uuid "11d7e369-d660-47fd-b0a6-a75a951bd9c1")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "TestPoint"
			(at 1 -1.7 0)
			(layer "F.Fab")
			(uuid "2eb9ba42-d796-4602-a8f0-fde3acb50d6e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Footprint" "TestPoint:TestPoint_Bridge_Pitch2.0mm_Drill0.7mm"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "6c96debe-73f8-4b61-be48-a4e8041cff18")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "184f7e85-ad7b-4b59-937b-3d4d83be1bf6")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Description" "test point"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "d0d1b28c-4db5-4f05-827c-fd0be8adb39e")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property ki_fp_filters "Pin* Test*")
		(path "/b198df71-7d13-4632-9ab4-7f217c917f8e")
		(sheetname "Root")
		(sheetfile "unconnected-netnames.kicad_sch")
		(attr through_hole)
		(fp_line
			(start -0.9 -0.9)
			(end -0.9 0.9)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2fe3d8a9-dc64-48f4-aaf4-fe3103931d68")
		)
		(fp_line
			(start -0.9 0.9)
			(end 2.9 0.9)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "138aaf38-c051-46e9-ad38-138884916c01")
		)
		(fp_line
			(start 2.9 -0.9)
			(end -0.9 -0.9)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e06fafc6-4b7a-4ed3-8a80-e4feea1bfd93")
		)
		(fp_line
			(start 2.9 0.9)
			(end 2.9 -0.9)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4b1513ef-3a66-48c5-a1aa-1c7c8ba3bbd4")
		)
		(fp_line
			(start -1.2 1.2)
			(end -1.2 -1.2)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "0059a420-bbf7-4e65-9f77-86799f6138e6")
		)
		(fp_line
			(start -1.2 1.2)
			(end 3.2 1.2)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "33a7f956-1108-4fde-9cfb-0ef4b812e0ef")
		)
		(fp_line
			(start 3.2 -1.2)
			(end -1.2 -1.2)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5dfad35d-845b-4737-a5c9-97b1dc8ff81c")
		)
		(fp_line
			(start 3.2 -1.2)
			(end 3.2 1.2)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "97e9c191-1b39-4899-801d-c217ef903a17")
		)
		(fp_line
			(start 2 0)
			(end 0 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2f4282e9-3e81-4d51-abb4-e88b82550105")
		)
		(fp_text user "${REFERENCE}"
			(at 1.1 1.7 0)
			(layer "F.Fab")
			(uuid "32926f2f-12bb-47bd-8d01-164977f77338")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole circle
			(at 0 0)
			(size 1.4 1.4)
			(drill 0.7)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 2 "unconnected-(TP1-Pad1)_0")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "891be13e-e841-49df-9fe9-aa0803a01c48")
		)
		(pad "1" thru_hole circle
			(at 2 0)
			(size 1.4 1.4)
			(drill 0.7)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(net 1 "unconnected-(TP1-Pad1)")
			(pinfunction "1")
			(pintype "passive+no_connect")
			(uuid "7f73b514-d3e4-4e9b-aa32-6a472854c04b")
		)
		(model "${KICAD6_3DMODEL_DIR}/TestPoint.3dshapes/TestPoint_Bridge_Pitch2.0mm_Drill0.7mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(gr_rect
		(start 134.659755 91.720974)
		(end 144.39064 98.204018)
		(stroke
			(width 0.05)
			(type default)
		)
		(fill none)
		(layer "Edge.Cuts")
		(uuid "d39fdf5e-1d58-48ca-93a7-ec96883ae32a")
	)
	(gr_text "There should be no schematic parity errors\nhere because NC pads share a common \nprefix with the test point"
		(at 128.318133 89.631558 0)
		(layer "F.Fab")
		(uuid "93b20610-b7a1-4193-abe4-582a88122aa9")
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
			(justify left bottom)
		)
	)
)
