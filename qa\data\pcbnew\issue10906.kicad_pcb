(kicad_pcb (version 20220211) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.Adhes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
    (50 "User.1" user)
    (51 "User.2" user)
    (52 "User.3" user)
    (53 "User.4" user)
    (54 "User.5" user)
    (55 "User.6" user)
    (56 "User.7" user)
    (57 "User.8" user)
    (58 "User.9" user)
  )

  (setup
    (pad_to_mask_clearance 0)
    (solder_mask_min_width 0.5)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 4)
      (excludeedgelayer true)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")

  (footprint "Connector_Wire:SolderWirePad_1x01_SMD_1x2mm" (layer "F.Cu")
    (tedit 620FF1C8) (tstamp 2e642b3e-a476-4c54-9a52-dcea955640cd)
    (at 136.84 58.35)
    (descr "Wire Pad, Square, SMD Pad,  5mm x 10mm,")
    (tags "MeasurementPoint Square SMDPad 5mmx10mm ")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "REF**" (at 0 -2.54) (layer "F.SilkS") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b1086f75-01ba-4188-8d36-75a9e2828ca9)
    )
    (fp_text value "" (at 0 2.54) (layer "F.Fab") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 716e31c5-485f-40b5-88e3-a75900da9811)
    )
    (fp_rect (start -0.762 -0.508) (end 1.016 0.762)
      (stroke (width 0) (type solid)) (fill solid) (layer "F.Cu") (tstamp e87a6f80-914f-4f62-9c9f-9ba62a88ee3d))
    (fp_rect (start -0.762 -0.508) (end 1.016 0.762)
      (stroke (width 0) (type solid)) (fill solid) (layer "F.Mask") (tstamp df3dc9a2-ba40-4c3a-87fe-61cc8e23d71b))
  )

  (gr_rect (start 135.25 57.21) (end 138.55 60.51)
    (stroke (width 0.05) (type default)) (fill none) (layer "Edge.Cuts") (tstamp 2f0570b6-86da-47a8-9e56-ce60c431c534))

)
