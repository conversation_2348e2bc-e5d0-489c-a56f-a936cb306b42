(version 1)
(rule Rule1
	(condition "<PERSON><PERSON>('AREA1') && A.Type == 'Footprint'")
	(constraint assertion "A.Component_Class == 'CLASS1'")
)

(rule Rule2
	(condition "A<PERSON>intersects<PERSON>('AREA3') && A.Type == 'Footprint'")
	(constraint assertion "A.Component_Class != 'CLASS1'")
)

(rule Rule3
	(condition "A<PERSON>intersects<PERSON>('AREA2') && A.Type == 'Footprint'")
	(constraint assertion "A.Component_Class == 'CLASS2,CLASS3'")
)

(rule Rule4
	(condition "A.intersects<PERSON>('AREA4') && A.Type == 'Footprint'")
	(constraint assertion "A.hasComponentClass('CLASS3')")
)

(rule Rule5
	(condition "A.intersects<PERSON>('AREA4') && A.Type == 'Footprint'")
	(constraint assertion "A.hasComponentClass('CLASS4')")
)
