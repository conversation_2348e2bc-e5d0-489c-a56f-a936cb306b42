(kicad_pcb (version 20220914) (generator pcbnew)

  (general
    (thickness 1.026)
  )

  (paper "A3")
  (layers
    (0 "F.Cu" signal)
    (1 "In1.Cu" signal)
    (2 "In2.Cu" signal)
    (31 "B.Cu" signal)
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen") (color "White"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (color "Green") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "prepreg") (thickness 0.3) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In1.Cu" (type "copper") (thickness 0.018))
      (layer "dielectric 2" (type "core") (thickness 0.3) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In2.Cu" (type "copper") (thickness 0.018))
      (layer "dielectric 3" (type "prepreg") (thickness 0.3) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (color "Green") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen") (color "White"))
      (copper_finish "ENIG")
      (dielectric_constraints no)
    )
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (plot_on_all_layers_selection 0x0001000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 6)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 4)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "production/")
    )
  )

  (net 0 "")

  (gr_rect (start 9.35 -61.4) (end 180.15 57)
    (stroke (width 0.1) (type default)) (fill none) (layer "Edge.Cuts") (tstamp 911ca37d-8865-4202-a99f-e7891c2d16ae))
  (gr_text "simple text 2" (at 16 -13) (layer "F.Cu") (tstamp 97394bf4-d910-49e0-bda6-baaca7842e99)
    (effects (font (size 1.5 1.5) (thickness 0.25)) (justify left))
  )
  (gr_text "simple text 2" (at 89 -13) (layer "F.Cu") (tstamp ff95eb0d-ff5f-4a8d-a925-d60a05d1fff2)
    (effects (font (size 1.5 1.5) (thickness 0.25)) (justify left mirror))
  )
  (gr_text "simple text 2" (at 71 31) (layer "B.Cu") (tstamp 18ca425a-3b7e-4cc7-8440-480d3042a171)
    (effects (font (size 1.5 1.5) (thickness 0.25)) (justify left))
  )
  (gr_text "simple text 2" (at 32 29) (layer "B.Cu") (tstamp 790ffc06-45d7-4ed4-b045-d89e829d3135)
    (effects (font (size 1.5 1.5) (thickness 0.25)) (justify left mirror))
  )
  (gr_text "simple text 1" (at 32 26) (layer "B.SilkS") (tstamp 7e3a2863-5a81-4d4a-8295-ed17226136f0)
    (effects (font (size 1.5 1.5) (thickness 0.25)) (justify left mirror))
  )
  (gr_text "simple text 1" (at 71 28) (layer "B.SilkS") (tstamp 8cb8f7df-4caa-483a-b7a1-d40d95e631c7)
    (effects (font (size 1.5 1.5) (thickness 0.25)) (justify left))
  )
  (gr_text "incorrect / false" (at 74 23) (layer "F.SilkS") (tstamp 3a0442d8-8f69-4fb3-85b5-b3ffb208ee62)
    (effects (font (size 2 2) (thickness 0.3)) (justify left))
  )
  (gr_text "incorrect / false" (at 77 -22) (layer "F.SilkS") (tstamp 5527bae0-eadf-41e4-9a4e-e82e31eb71f8)
    (effects (font (size 2 2) (thickness 0.3)) (justify left))
  )
  (gr_text "top layer (F.silkscreen / F.Cu)" (at 15 -30) (layer "F.SilkS") (tstamp 80b69f34-19f5-4535-aecf-df8f3e4851e1)
    (effects (font (size 3 3) (thickness 0.3)) (justify left))
  )
  (gr_text "testboard for DRC:\n- mirrored text on top-layers\n- non-mirrored text on bottom layers" (at 15 -49) (layer "F.SilkS") (tstamp 8d9ea4cf-1047-42af-bf72-13258f22d6ad)
    (effects (font (size 3 3) (thickness 0.3)) (justify left))
  )
  (gr_text "correct" (at 30 -22) (layer "F.SilkS") (tstamp a0f029bd-d330-447d-9dd4-6c9ea6600de6)
    (effects (font (size 2 2) (thickness 0.3)) (justify left))
  )
  (gr_text "bottom layer (B.silkscreen / B.Cu)" (at 16 17) (layer "F.SilkS") (tstamp b506ef8d-4126-4569-a30b-5d5c63934176)
    (effects (font (size 3 3) (thickness 0.3)) (justify left))
  )
  (gr_text "correct" (at 29 22) (layer "F.SilkS") (tstamp b620752b-4aab-48d8-8370-8770b56820e3)
    (effects (font (size 2 2) (thickness 0.3)) (justify left))
  )
  (gr_text "simple text 1" (at 16 -16) (layer "F.SilkS") (tstamp dd26746f-af1a-449d-ae5e-a68117c99a23)
    (effects (font (size 1.5 1.5) (thickness 0.25)) (justify left))
  )
  (gr_text "simple text 1" (at 89 -16) (layer "F.SilkS") (tstamp ec347ae7-892c-45b9-a6fe-fe03f0d9c93a)
    (effects (font (size 1.5 1.5) (thickness 0.25)) (justify left mirror))
  )
  (gr_text_box "textbox 2\nmultiline"
    (start 35 -10) (end 47 -4) (layer "F.Cu") (tstamp b37cbeb3-cc18-45c3-a18b-e035ed193e4b)
      (effects (font (size 1 1) (thickness 0.25) bold) (justify left top))
    (stroke (width 0.15) (type solid))  )
  (gr_text_box "textbox 2\nmultiline"
    (start 92 -10) (end 104 -4) (layer "F.Cu") (tstamp d129d2f4-9499-48b6-a0e8-0edf3c1f7c7f)
      (effects (font (size 1 1) (thickness 0.25) bold) (justify left top mirror))
    (stroke (width 0.15) (type solid))  )
  (gr_text_box "textbox 2\nmultiline"
    (start 35 32) (end 47 38) (layer "B.Cu") (tstamp 831c76a2-3beb-4d6b-b7c1-3175cb76a7e0)
      (effects (font (size 1 1) (thickness 0.25) bold) (justify left top mirror))
    (stroke (width 0.15) (type solid))  )
  (gr_text_box "textbox 2\nmultiline"
    (start 90 33) (end 102 39) (layer "B.Cu") (tstamp df9e4d96-b3a4-4674-9dba-7893ca08029d)
      (effects (font (size 1 1) (thickness 0.25) bold) (justify left top))
    (stroke (width 0.15) (type solid))  )
  (gr_text_box "textbox 1"
    (start 35 25) (end 47 31) (layer "B.SilkS") (tstamp a818a586-9ac1-47f6-963e-02014d41be0e)
      (effects (font (size 1 1) (thickness 0.25) bold) (justify left top mirror))
    (stroke (width 0.15) (type solid))  )
  (gr_text_box "textbox 1"
    (start 90 26) (end 102 32) (layer "B.SilkS") (tstamp fc05ddb3-8177-4e58-b8c6-b0abc62afc22)
      (effects (font (size 1 1) (thickness 0.25) bold) (justify left top))
    (stroke (width 0.15) (type solid))  )
  (gr_text_box "textbox 1"
    (start 92 -17) (end 104 -11) (layer "F.SilkS") (tstamp 2d1d2f60-3886-4d23-962a-ce37b181b02f)
      (effects (font (size 1 1) (thickness 0.25) bold) (justify left top mirror))
    (stroke (width 0.15) (type solid))  )
  (gr_text_box "textbox 1"
    (start 35 -17) (end 47 -11) (layer "F.SilkS") (tstamp 876b3d96-2106-4766-b75c-d8c618194c89)
      (effects (font (size 1 1) (thickness 0.25) bold) (justify left top))
    (stroke (width 0.15) (type solid))  )

)
