(kicad_pcb (version 20200829) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user)
    (33 "<PERSON><PERSON>Adhes" user)
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user)
    (37 "F.SilkS" user)
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user)
    (41 "Cmts.User" user)
    (42 "Eco1.User" user)
    (43 "Eco2.User" user)
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user)
    (47 "F.CrtYd" user)
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (pcbplotparams
      (layerselection 0x010fc_ffffffff)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (svguseinch false)
      (svgprecision 6)
      (excludeedgelayer true)
      (linewidth 0.150000)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )


  (net 0 "")

  (module "Jumper:SolderJumper-2_P1.3mm_Open_TrianglePad1.0x1.5mm" (layer "F.Cu") (tedit 5A64794F) (tstamp 45f71005-ae4b-4884-b6a3-eb67eece4a66)
    (at 55 55)
    (descr "SMD Solder Jumper, 1x1.5mm Triangular Pads, 0.3mm gap, open")
    (tags "solder jumper open")
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_text reference "SB1" (at 0 -1.8) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 920d4adc-c967-4af0-8187-11eef43919bd)
    )
    (fp_text value "SolderJumper-2_P1.3mm_Open_TrianglePad1.0x1.5mm" (at 0 1.9) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 9cbcb47a-cb17-49df-98a5-2ee0e09b9db2)
    )
    (fp_line (start -1.4 1) (end -1.4 -1) (layer "F.SilkS") (width 0.12) (tstamp 421b6989-a0a8-4ca9-aba2-096742614893))
    (fp_line (start 1.4 -1) (end 1.4 1) (layer "F.SilkS") (width 0.12) (tstamp 9265368b-fb29-4426-9ff3-5e450c586e6f))
    (fp_line (start 1.4 1) (end -1.4 1) (layer "F.SilkS") (width 0.12) (tstamp abada1f4-9fef-41eb-a371-594b0c5ff159))
    (fp_line (start -1.4 -1) (end 1.4 -1) (layer "F.SilkS") (width 0.12) (tstamp fba79c0f-cbb9-4f82-9dc8-56afbd8e27fc))
    (fp_line (start 1.65 1.25) (end -1.65 1.25) (layer "F.CrtYd") (width 0.05) (tstamp 267383d2-2581-47eb-8bca-88b2ba619d19))
    (fp_line (start -1.65 -1.25) (end 1.65 -1.25) (layer "F.CrtYd") (width 0.05) (tstamp 5102f76a-c878-44bc-9d75-bfd6582ac9e4))
    (fp_line (start -1.65 -1.25) (end -1.65 1.25) (layer "F.CrtYd") (width 0.05) (tstamp 711987ed-ea86-4296-af4a-9fbd6d608e10))
    (fp_line (start 1.65 1.25) (end 1.65 -1.25) (layer "F.CrtYd") (width 0.05) (tstamp a9960b84-0016-42a2-9d36-98c678386c44))
    (pad "1" smd custom (at -0.725 0) (size 0.3 0.3) (layers "F.Cu" "F.Mask")
      (zone_connect 2)
      (options (clearance outline) (anchor rect))
      (primitives
        (gr_poly (pts
          (xy -0.5 -0.75)
          (xy 0.5 -0.75)
          (xy 1 0)
          (xy 0.5 0.75)
          (xy -0.5 0.75)
) (width 0))
      ) (tstamp e59d970d-034f-45b4-8ede-1d68d925f15c))
    (pad "2" smd custom (at 0.725 0) (size 0.3 0.3) (layers "F.Cu" "F.Mask")
      (zone_connect 2)
      (options (clearance outline) (anchor rect))
      (primitives
        (gr_poly (pts
          (xy -0.65 -0.75)
          (xy 0.5 -0.75)
          (xy 0.5 0.75)
          (xy -0.65 0.75)
          (xy -0.15 0)
) (width 0))
      ) (tstamp 9ac25e5d-8bf8-408a-8326-0eace0c875f1))
  )

  (gr_rect (start 50 50) (end 100 100) (layer "Edge.Cuts") (width 0.05) (tstamp 140d541f-fd45-4b34-9b91-43b10f43e3f9))

)
