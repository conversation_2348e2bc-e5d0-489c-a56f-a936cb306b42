/* XPM */
static char const * voltage_probe64_xpm[] = {
"64 64 3 1",
" 	c None",
".	c #FFFFFF",
"+	c #000000",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                             ....               ",
"                                          ...++++...            ",
"                                         .++++++++++.           ",
"                                        .++++++++++++.          ",
"                                       .+++++....+++++.         ",
"                                      .+++++.    .+++++.        ",
"                                     .+++++.      .+++++.       ",
"                                  ...+++++.        .++++.       ",
"                                 .+++++++.          .+++.       ",
"                              ...+++++++.           .+++.       ",
"                             .+++++++++.             .++.       ",
"                            .+++++++++.              .+++.      ",
"                           .++++++++++.              .++++.     ",
"                          .+++++++++++.              .+++++.    ",
"                         .++++++++++++.               .+++++.   ",
"                        .+++++++++++++.                .+++++.  ",
"                       .+++++++++++++.                  .+++++. ",
"                      .+++++++++++++.                    .++++. ",
"                     .+++++++++++++.                      ....  ",
"                    .+++++++++++++.                             ",
"                   .+++++++++++++.                              ",
"                  .+++++++++++++.                               ",
"                 .+++++++++++++.                                ",
"                .+++++++++++++.                                 ",
"               .+++++++++++++.                                  ",
"              .+++++++++++++.                                   ",
"             .+++++++++++++.                                    ",
"            .+++++++++++++.                                     ",
"           .+++++++++++++.                                      ",
"          .+++++++++++++.                                       ",
"         .+++++++++++++.                                        ",
"        .+++++++++++++.                                         ",
"       .+++++++++++++.                                          ",
"       .++++++++++++.                                           ",
"       .+++++++++++.                                            ",
"       .++++++++++.                                             ",
"       .+++++++++.                                              ",
"      .+++++++++.                                               ",
"     .+++++.....                                                ",
"    .+++++.                                                     ",
"   .+++++.                                                      ",
"  .+++++.                                                       ",
" .+++++.                                                        ",
" .++++.                                                         ",
" .+++.                                                          ",
" .+..                                                           ",
"                                                                ",
"                                                                "};
