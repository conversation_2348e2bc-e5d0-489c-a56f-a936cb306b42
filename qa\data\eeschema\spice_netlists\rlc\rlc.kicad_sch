(kicad_sch (version 20221004) (generator eeschema)

  (uuid b0197b4d-36ea-4821-991f-20bf73bcc80c)

  (paper "A4")

  (lib_symbols
    (symbol "Device:C" (pin_numbers hide) (pin_names (offset 0.254)) (in_bom yes) (on_board yes)
      (property "Reference" "C" (at 0.635 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "C" (at 0.635 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0.9652 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "cap capacitor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Unpolarized capacitor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "C_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "C_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy 2.032 -0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
      )
      (symbol "C_1_1"
        (pin passive line (at 0 3.81 270) (length 2.794)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 2.794)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:L" (pin_numbers hide) (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "L" (at -1.27 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "L" (at 1.905 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "inductor choke coil reactor magnetic" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Inductor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Choke_* *Coil* Inductor_* L_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "L_0_1"
        (arc (start 0 -2.54) (mid 0.6323 -1.905) (end 0 -1.27)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 -1.27) (mid 0.6323 -0.635) (end 0 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0) (mid 0.6323 0.635) (end 0 1.27)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 1.27) (mid 0.6323 1.905) (end 0 2.54)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "L_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:IPULSE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "IPULSE" (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "pulse(0 1 2n 2n 2n 50n 100n)" (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, pulse" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "IPULSE_0_0"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy -1.397 -0.762)
            (xy -1.143 0.762)
            (xy -0.127 0.762)
            (xy 0.127 -0.762)
            (xy 1.143 -0.762)
            (xy 1.397 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "IPULSE_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "IPULSE_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VPULSE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VPULSE" (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "pulse(0 1 2n 2n 2n 50n 100n)" (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, pulse" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VPULSE_0_0"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy -1.397 -0.762)
            (xy -1.143 0.762)
            (xy -0.127 0.762)
            (xy 0.127 -0.762)
            (xy 1.143 -0.762)
            (xy 1.397 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VPULSE_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VPULSE_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 203.2 88.9) (diameter 0) (color 0 0 0 0)
    (uuid 2c5620de-a49d-4307-bbbd-1d9cb5903343)
  )
  (junction (at 190.5 88.9) (diameter 0) (color 0 0 0 0)
    (uuid 92fadc25-2694-4945-bcc8-2af79507fa8a)
  )

  (wire (pts (xy 101.6 114.3) (xy 101.6 100.33))
    (stroke (width 0) (type default))
    (uuid 09b94725-f2ad-4821-b8d5-1e53bce69e8b)
  )
  (wire (pts (xy 101.6 90.17) (xy 101.6 76.2))
    (stroke (width 0) (type default))
    (uuid 130d812d-a881-4206-85bf-8d55e92ee0df)
  )
  (wire (pts (xy 215.9 99.06) (xy 215.9 101.6))
    (stroke (width 0) (type default))
    (uuid 154482f9-3c75-4510-a9cc-ac475d8c8309)
  )
  (wire (pts (xy 203.2 91.44) (xy 203.2 88.9))
    (stroke (width 0) (type default))
    (uuid 16202aab-2740-446e-b6fd-7d24f99d82c0)
  )
  (wire (pts (xy 127 86.36) (xy 127 91.44))
    (stroke (width 0) (type default))
    (uuid 2cf191a2-0e2d-4c04-884d-dd659ed454db)
  )
  (wire (pts (xy 165.1 90.17) (xy 165.1 88.9))
    (stroke (width 0) (type default))
    (uuid 32faffb7-3bf1-4090-a6fd-9d1336f30989)
  )
  (wire (pts (xy 127 99.06) (xy 127 104.14))
    (stroke (width 0) (type default))
    (uuid 60ec2611-e35b-4027-a794-364d57b5ed3c)
  )
  (wire (pts (xy 203.2 99.06) (xy 203.2 101.6))
    (stroke (width 0) (type default))
    (uuid 62a8c09d-fe91-4e6c-88f6-d8c8d1c765c9)
  )
  (wire (pts (xy 215.9 88.9) (xy 215.9 91.44))
    (stroke (width 0) (type default))
    (uuid 6591664b-0777-4b84-a4c1-3001f22033f4)
  )
  (wire (pts (xy 127 76.2) (xy 127 78.74))
    (stroke (width 0) (type default))
    (uuid 7b47a9c2-befb-443c-8726-bb3aa1d69401)
  )
  (wire (pts (xy 190.5 88.9) (xy 203.2 88.9))
    (stroke (width 0) (type default))
    (uuid 8dba7d30-a51e-4d65-8750-e7d67cf586ec)
  )
  (wire (pts (xy 190.5 88.9) (xy 190.5 91.44))
    (stroke (width 0) (type default))
    (uuid 9775fd6d-48d4-4f1a-89a3-cc1fd8779516)
  )
  (wire (pts (xy 190.5 99.06) (xy 190.5 101.6))
    (stroke (width 0) (type default))
    (uuid a9eb063d-7d02-447a-9817-d1c5a7d1ef0f)
  )
  (wire (pts (xy 127 111.76) (xy 127 114.3))
    (stroke (width 0) (type default))
    (uuid ac0c1535-8ab4-4548-89bf-0bbc0c1ab837)
  )
  (wire (pts (xy 101.6 76.2) (xy 127 76.2))
    (stroke (width 0) (type default))
    (uuid c5a497db-6bba-4366-9887-d5be59bc6e15)
  )
  (wire (pts (xy 165.1 101.6) (xy 165.1 100.33))
    (stroke (width 0) (type default))
    (uuid cc5222c0-ed7d-43c2-b53e-ea04c24f4b4b)
  )
  (wire (pts (xy 165.1 88.9) (xy 190.5 88.9))
    (stroke (width 0) (type default))
    (uuid dca73c8b-5e4a-4de9-9609-102ffe41c63a)
  )
  (wire (pts (xy 203.2 88.9) (xy 215.9 88.9))
    (stroke (width 0) (type default))
    (uuid e3d31500-d429-4fee-8369-************)
  )

  (text ".tran 1u 10m" (at 101.6 127 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 5609af8f-6a32-46ea-8e1e-d83776811610)
  )

  (label "Vs" (at 127 76.2 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 174e2d29-d800-43fd-9b4f-0a1a68f787a9)
  )
  (label "Vp" (at 215.9 88.9 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 8b8b1d00-3917-4ec9-a65d-fa5fe9d5bcbe)
  )

  (symbol (lib_id "Device:C") (at 127 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 134b0945-6312-494f-b15e-86844b1af56b)
    (property "Reference" "Cs1" (at 130.81 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100u" (at 130.81 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 127.9652 111.76 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 127 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "C" (at 127 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 903935c6-35d3-4916-ada6-6df4770de2aa))
    (pin "2" (uuid 345dcb8d-fa56-42b9-8459-76c190d65d47))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "Cs1") (unit 1) (value "100u") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 190.5 95.25 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 21bd30c7-8dd9-4b17-b22c-5b93a2f6ec2b)
    (property "Reference" "Rp1" (at 193.04 94.615 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "1K" (at 193.04 97.155 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 188.722 95.25 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 190.5 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 190.5 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7eaeaa13-0708-49b3-b75d-a26c16d10ee7))
    (pin "2" (uuid 638515be-9c03-48d2-b9fe-a631fb6915e0))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "Rp1") (unit 1) (value "1K") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:L") (at 203.2 95.25 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 23a20b7d-be56-4b4b-b6ae-be89f2c401db)
    (property "Reference" "Lp1" (at 204.47 94.615 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100u" (at 204.47 97.155 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 203.2 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 203.2 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "L" (at 203.2 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 4ebb4d0f-b627-4dc9-9847-2a94f54f22db))
    (pin "2" (uuid 3b2f749c-1e7c-4201-80ac-4298b93d47b7))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "Lp1") (unit 1) (value "100u") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 127 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 32a99710-4056-4181-85bb-64e2153c9cb4)
    (property "Reference" "Rs1" (at 129.54 81.915 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "1m" (at 129.54 84.455 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 125.222 82.55 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 127 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 127 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 074f07c5-e6cf-47ec-af8c-7b1f0375a3bf))
    (pin "2" (uuid 0cfc39f9-53c2-4a24-b203-fdb49a1dc8b2))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "Rs1") (unit 1) (value "1m") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 127 114.3 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 34aa6f33-c05e-4723-9e74-afc1625c9501)
    (property "Reference" "#PWR0101" (at 127 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 127 119.38 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 127 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 127 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 9be53029-4c95-425f-a7d0-c2ccc29969b7))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "#PWR0101") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 190.5 101.6 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 3ec19414-af4c-48f9-b3c1-0967946aad57)
    (property "Reference" "#PWR0104" (at 190.5 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 190.5 106.68 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 190.5 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 190.5 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid a4a4fb8e-4422-4b92-b806-cf49086e38ed))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "#PWR0104") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 215.9 101.6 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 40f72e93-2e7b-4041-9908-4ceb8cc5968e)
    (property "Reference" "#PWR0106" (at 215.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 215.9 106.68 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 215.9 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 215.9 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8bb89dcf-2cf1-4186-97fc-66be0f608afe))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "#PWR0106") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 165.1 101.6 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 487d173e-6d23-410c-bf91-75aa1396dec9)
    (property "Reference" "#PWR0105" (at 165.1 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 165.1 106.68 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 165.1 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 165.1 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5fd81701-e0a4-4345-a8e1-a760208a4cc8))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "#PWR0105") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 215.9 95.25 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 4dbe17b3-8286-49ce-adaf-3c794ad8274c)
    (property "Reference" "Cp1" (at 219.71 94.615 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100u" (at 219.71 97.155 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 216.8652 99.06 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 215.9 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "C" (at 215.9 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 37d61ed5-e320-4d57-bff7-ec42aeca9f21))
    (pin "2" (uuid 07323bac-5c2c-4732-830c-f3c57201154d))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "Cp1") (unit 1) (value "100u") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:IPULSE") (at 165.1 95.25 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 6f3941ed-3978-4d28-b217-551f8904977b)
    (property "Reference" "I1" (at 168.91 93.345 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "${Sim.Device} ${Sim.Type}" (at 168.91 95.885 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 165.1 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 165.1 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "I" (at 165.1 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Type" "PULSE" (at 165.1 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "y2=1 tw=1u" (at 165.1 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8a80b586-d30c-44d1-b165-e9c7e3047e62))
    (pin "2" (uuid 45a2089b-d1a0-426a-994b-107d5560e7f0))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "I1") (unit 1) (value "${Sim.Device} ${Sim.Type}") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VPULSE") (at 101.6 95.25 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 7ba34897-97ee-4909-8ec6-27586fb15817)
    (property "Reference" "V1" (at 105.41 93.345 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "${Sim.Device} ${Sim.Type}" (at 105.41 95.885 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 101.6 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 101.6 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 101.6 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Type" "PULSE" (at 101.6 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "y2=1 tw=1u" (at 101.6 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e8818239-f91c-47e3-ac1e-178af6b74e90))
    (pin "2" (uuid 024d1f91-a924-4436-bab9-338ed09010fe))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "V1") (unit 1) (value "${Sim.Device} ${Sim.Type}") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:L") (at 127 95.25 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 86f0bff3-c028-492d-8a21-44716128e6e8)
    (property "Reference" "Ls1" (at 128.27 94.615 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100u" (at 128.27 97.155 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 127 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 127 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "L" (at 127 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8e8c16ca-b9d0-42ae-8073-59118828361f))
    (pin "2" (uuid a1d46bf7-ab6a-4497-adbf-0ad4bd09d044))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "Ls1") (unit 1) (value "100u") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 203.2 101.6 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 916e7603-28f1-49c3-8465-04b0add34ac6)
    (property "Reference" "#PWR0103" (at 203.2 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 203.2 106.68 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 203.2 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 203.2 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 9fec72ed-c4f8-4007-9bec-eb7883b5f9b5))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "#PWR0103") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 101.6 114.3 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid e7e2ccf3-0a39-44c0-9068-a7e83432702d)
    (property "Reference" "#PWR0102" (at 101.6 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 101.6 119.38 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 101.6 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 101.6 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8178158b-a483-4990-b982-4cba4f29f25c))
    (instances
      (project "rlc"
        (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c"
          (reference "#PWR0102") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (sheet_instances
    (path "/b0197b4d-36ea-4821-991f-20bf73bcc80c" (page "1"))
  )
)
