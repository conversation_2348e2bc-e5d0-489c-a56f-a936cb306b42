Cmp-Mod V01 Genere par PcbNew le 26/5/2003-7:55:8

BeginCmp
TimeStamp = 3EC23ABA;
Reference = C1;
ValeurCmp = 100nF;
IdModule  = SM1206;
EndCmp

BeginCmp
TimeStamp = 3EC23B98;
Reference = C2;
ValeurCmp = 10uF;
IdModule  = CP6;
EndCmp

BeginCmp
TimeStamp = 3EC23DCD;
Reference = C3;
ValeurCmp = 10uF;
IdModule  = CP6;
EndCmp

BeginCmp
TimeStamp = 3EC242AD;
Reference = C4;
ValeurCmp = 22pF;
IdModule  = SM1206;
EndCmp

BeginCmp
TimeStamp = 3EC242AD;
Reference = C5;
ValeurCmp = 22pF;
IdModule  = SM1206;
EndCmp

BeginCmp
TimeStamp = 3EC243D6;
Reference = C6;
ValeurCmp = 47uF;
IdModule  = CP6;
EndCmp

BeginCmp
TimeStamp = 3EC4C7DB;
Reference = C7;
ValeurCmp = 100nF;
IdModule  = SM1206;
EndCmp

BeginCmp
TimeStamp = 3EC4C7DB;
Reference = C8;
ValeurCmp = 100nF;
IdModule  = SM1206;
EndCmp

BeginCmp
TimeStamp = 3EC4C7DB;
Reference = C10;
ValeurCmp = 100nF;
IdModule  = SM1206;
EndCmp

BeginCmp
TimeStamp = 3EC4C7DB;
Reference = C11;
ValeurCmp = 100nF;
IdModule  = SM1206;
EndCmp

BeginCmp
TimeStamp = 3EC4C7DB;
Reference = C12;
ValeurCmp = 100nF;
IdModule  = SM1206;
EndCmp

BeginCmp
TimeStamp = 3ECDDCAD;
Reference = C13;
ValeurCmp = 10uF;
IdModule  = CP6;
EndCmp

BeginCmp
TimeStamp = 3ECDDACB;
Reference = C14;
ValeurCmp = 10uF;
IdModule  = CP6;
EndCmp

BeginCmp
TimeStamp = 3EC243C8;
Reference = D1;
ValeurCmp = 1N4004;
IdModule  = D5;
EndCmp

BeginCmp
TimeStamp = 3EC4D08F;
Reference = D2;
ValeurCmp = LED;
IdModule  = LEDV;
EndCmp

BeginCmp
TimeStamp = 3EC4D08F;
Reference = D3;
ValeurCmp = LED;
IdModule  = LEDV;
EndCmp

BeginCmp
TimeStamp = 3ECDDCDD;
Reference = D4;
ValeurCmp = 1N4004;
IdModule  = D5;
EndCmp

BeginCmp
TimeStamp = 3EC4C60F;
Reference = J1;
ValeurCmp = DB25;
IdModule  = DB25FC;
EndCmp

BeginCmp
TimeStamp = 3ECDEBEA;
Reference = J2;
ValeurCmp = DB9FEM;
IdModule  = DB9F_CI_INVERT;
EndCmp

BeginCmp
TimeStamp = 3EC4C49F;
Reference = K1;
ValeurCmp = CONN_3;
IdModule  = SIL-3;
EndCmp

BeginCmp
TimeStamp = 3EC243AC;
Reference = P2;
ValeurCmp = CONN_3;
IdModule  = bornier3;
EndCmp

BeginCmp
TimeStamp = 3EC2446D;
Reference = P3;
ValeurCmp = CONN_2;
IdModule  = bornier2;
EndCmp

BeginCmp
TimeStamp = 3ECB3F5E;
Reference = P4;
ValeurCmp = CONN_2X2;
IdModule  = head_2x2;
EndCmp

BeginCmp
TimeStamp = 3EC242C1;
Reference = R1;
ValeurCmp = 100K;
IdModule  = R4;
EndCmp

BeginCmp
TimeStamp = 3EC4D082;
Reference = R2;
ValeurCmp = 330;
IdModule  = R4;
EndCmp

BeginCmp
TimeStamp = 3EC4D082;
Reference = R3;
ValeurCmp = 330;
IdModule  = R4;
EndCmp

BeginCmp
TimeStamp = 3EC8B970;
Reference = R4;
ValeurCmp = 4,7K;
IdModule  = R4;
EndCmp

BeginCmp
TimeStamp = 3EC8B978;
Reference = R5;
ValeurCmp = 4,7K;
IdModule  = R4;
EndCmp

BeginCmp
TimeStamp = 3ECDDB57;
Reference = R6;
ValeurCmp = 1,1k;
IdModule  = R4;
EndCmp

BeginCmp
TimeStamp = 3ECDDB60;
Reference = R7;
ValeurCmp = 1,1K;
IdModule  = R4;
EndCmp

BeginCmp
TimeStamp = 3ECDDB43;
Reference = R8;
ValeurCmp = 2,2K;
IdModule  = R4;
EndCmp

BeginCmp
TimeStamp = 3ECDDB4B;
Reference = R9;
ValeurCmp = 2,2K;
IdModule  = R4;
EndCmp

BeginCmp
TimeStamp = 3ECDDB6D;
Reference = R10;
ValeurCmp = 75;
IdModule  = R4;
EndCmp

BeginCmp
TimeStamp = 3EC8B876;
Reference = SW1;
ValeurCmp = SW_PUSH;
IdModule  = SW_PUSH;
EndCmp

BeginCmp
TimeStamp = 3EC8B882;
Reference = SW2;
ValeurCmp = SW_PUSH;
IdModule  = SW_PUSH;
EndCmp

BeginCmp
TimeStamp = 3EC238E5;
Reference = U1;
ValeurCmp = TDA8702;
IdModule  = 16DIP-ELL300;
EndCmp

BeginCmp
TimeStamp = 3EC230AD;
Reference = U2;
ValeurCmp = XC95108PC84;
IdModule  = PLCC84;
EndCmp

BeginCmp
TimeStamp = 3EC2436D;
Reference = U3;
ValeurCmp = LM7805;
IdModule  = LM78XX;
EndCmp

BeginCmp
TimeStamp = 3EC4C318;
Reference = U4;
ValeurCmp = 74HC04;
IdModule  = 14DIP-ELL300;
EndCmp

BeginCmp
TimeStamp = 3EC4C463;
Reference = U5;
ValeurCmp = RAM_32KO;
IdModule  = 28DIP-ELL300-600;
EndCmp

BeginCmp
TimeStamp = 3ECDDB8B;
Reference = U6;
ValeurCmp = LM318N;
IdModule  = 8DIP-ELL300;
EndCmp

BeginCmp
TimeStamp = 3EC2428D;
Reference = X1;
ValeurCmp = 10MHz;
IdModule  = HC-18UH;
EndCmp

EndListe
