(kicad_sch (version 20211123) (generator eeschema)

  (uuid 95209d8c-a917-4d20-aa13-b6e7ee41be98)

  (paper "A4")

  (lib_symbols
    (symbol "Connector:Conn_01x03_Female" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "J" (id 0) (at 0 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Conn_01x03_Female" (id 1) (at 0 -5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "connector" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Generic connector, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Connector*:*_1x??_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Conn_01x03_Female_1_1"
        (arc (start 0 -2.032) (mid -0.508 -2.54) (end 0 -3.048)
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.27 -2.54)
            (xy -0.508 -2.54)
          )
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.27 0)
            (xy -0.508 0)
          )
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.27 2.54)
            (xy -0.508 2.54)
          )
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (arc (start 0 0.508) (mid -0.508 0) (end 0 -0.508)
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (arc (start 0 3.048) (mid -0.508 2.54) (end 0 2.032)
          (stroke (width 0.1524) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (pin passive line (at -5.08 2.54 0) (length 3.81)
          (name "Pin_1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 0 0) (length 3.81)
          (name "Pin_2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 -2.54 0) (length 3.81)
          (name "Pin_3" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VDC" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VDC" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "dc(1)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, DC" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VDC_0_0"
        (polyline
          (pts
            (xy -1.27 0.254)
            (xy 1.27 0.254)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.254)
            (xy -1.27 -0.254)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.254 -0.254)
            (xy -0.254 -0.254)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.254)
            (xy 0.762 -0.254)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VDC_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type background))
        )
      )
      (symbol "VDC_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Spice_power_electronics:UC3525" (in_bom yes) (on_board yes)
      (property "Reference" "U" (id 0) (at 6.35 19.05 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "UC3525" (id 1) (at 11.43 16.51 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "Package_DIP:DIP-16_W7.62mm" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "https://www.ti.com/lit/ds/symlink/uc3525a.pdf" (id 3) (at 38.1 6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Primitive" "X" (id 4) (at 3.81 21.59 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "SG3525" (id 5) (at 11.43 19.05 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 6) (at 3.81 19.05 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Lib_File" "$(KICAD_SIMULATION_LIB_DIR)/ngspice-lib/pwm_generator/IC_3525.lib" (id 7) (at 15.24 10.16 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "ki_keywords" "SMPS PWM Controller" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Regulating Pulse Width Modulators, NOR Logic, PDIP-16/SOIC-16" (id 9) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "SOIC*16*7.5x10.3mm*P1.27mm* DIP*16*W7.62mm*" (id 10) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "UC3525_0_1"
        (rectangle (start -10.16 -15.24) (end 10.16 15.24)
          (stroke (width 0.254) (type default) (color 0 0 0 0))
          (fill (type background))
        )
      )
      (symbol "UC3525_1_1"
        (pin input line (at -12.7 10.16 0) (length 2.54)
          (name "INV" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -12.7 2.54 0) (length 2.54)
          (name "SD" (effects (font (size 1.27 1.27))))
          (number "10" (effects (font (size 1.27 1.27))))
        )
        (pin output line (at 12.7 2.54 180) (length 2.54)
          (name "OUTA" (effects (font (size 1.27 1.27))))
          (number "11" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 0 -17.78 90) (length 2.54)
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "12" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 2.54 17.78 270) (length 2.54)
          (name "VC" (effects (font (size 1.27 1.27))))
          (number "13" (effects (font (size 1.27 1.27))))
        )
        (pin output line (at 12.7 -2.54 180) (length 2.54)
          (name "OUTB" (effects (font (size 1.27 1.27))))
          (number "14" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 0 17.78 270) (length 2.54)
          (name "VIN" (effects (font (size 1.27 1.27))))
          (number "15" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -12.7 5.08 0) (length 2.54)
          (name "REF" (effects (font (size 1.27 1.27))))
          (number "16" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -12.7 12.7 0) (length 2.54)
          (name "NI" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -12.7 0 0) (length 2.54)
          (name "SYNC" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin output line (at -12.7 -2.54 0) (length 2.54)
          (name "OSC" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -12.7 -7.62 0) (length 2.54)
          (name "CT" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -12.7 -5.08 0) (length 2.54)
          (name "RT" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
        (pin open_collector line (at -12.7 -10.16 0) (length 2.54)
          (name "DISCH" (effects (font (size 1.27 1.27))))
          (number "7" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -12.7 -12.7 0) (length 2.54)
          (name "SS" (effects (font (size 1.27 1.27))))
          (number "8" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -12.7 7.62 0) (length 2.54)
          (name "COMP" (effects (font (size 1.27 1.27))))
          (number "9" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:+15V" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (id 0) (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "+15V" (id 1) (at 0 3.556 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"+15V\"" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "+15V_0_1"
        (polyline
          (pts
            (xy -0.762 1.27)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 0)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 2.54)
            (xy 0.762 1.27)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
      )
      (symbol "+15V_1_1"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "+15V" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:-15V" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (id 0) (at 0 2.54 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "-15V" (id 1) (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"-15V\"" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "-15V_0_0"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "-15V" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "-15V_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
            (xy 0.762 1.27)
            (xy 0 2.54)
            (xy -0.762 1.27)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type outline))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (id 0) (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (id 1) (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "pspice:CAP" (pin_names (offset 0.254)) (in_bom yes) (on_board yes)
      (property "Reference" "C" (id 0) (at 2.54 3.81 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "CAP" (id 1) (at 2.54 -3.81 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "simulation" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Capacitor symbol for simulation only" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "CAP_0_1"
        (polyline
          (pts
            (xy -3.81 -1.27)
            (xy 3.81 -1.27)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -3.81 1.27)
            (xy 3.81 1.27)
          )
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
      )
      (symbol "CAP_1_1"
        (pin passive line (at 0 6.35 270) (length 5.08)
          (name "~" (effects (font (size 1.016 1.016))))
          (number "1" (effects (font (size 1.016 1.016))))
        )
        (pin passive line (at 0 -6.35 90) (length 5.08)
          (name "~" (effects (font (size 1.016 1.016))))
          (number "2" (effects (font (size 1.016 1.016))))
        )
      )
    )
    (symbol "pspice:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "resistor simulation" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor symbol for simulation only" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 3.81) (end 1.016 -3.81)
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 6.35 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -6.35 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 77.47 59.69) (diameter 0) (color 0 0 0 0)
    (uuid 180c34f1-b9e1-4075-9378-6c728dd4c940)
  )
  (junction (at 86.36 161.29) (diameter 0) (color 0 0 0 0)
    (uuid 1ec7b424-9384-49d4-ac36-c2a94dae312c)
  )
  (junction (at 86.36 163.83) (diameter 0) (color 0 0 0 0)
    (uuid 39a40aaf-47f1-49e9-98e8-4e1bb2ed418d)
  )
  (junction (at 106.68 30.48) (diameter 0) (color 0 0 0 0)
    (uuid 4259481a-64cd-4311-88a4-b1e78fdf8e06)
  )
  (junction (at 31.75 161.29) (diameter 0) (color 0 0 0 0)
    (uuid 88fae5bd-6a89-4eb6-914c-bf52dd095dfa)
  )
  (junction (at 86.36 158.75) (diameter 0) (color 0 0 0 0)
    (uuid 8a135673-1fd6-4389-a758-38d9874b695d)
  )
  (junction (at 92.71 31.75) (diameter 0) (color 0 0 0 0)
    (uuid bcd03d9a-4f83-4fb2-80c2-ed244306720e)
  )

  (no_connect (at 78.74 53.34) (uuid 25400d1d-2b89-4b3e-961d-1d1d5bed7f82))
  (no_connect (at 104.14 53.34) (uuid a4cd0486-afe4-499e-bc92-0aadb11f05fb))
  (no_connect (at 78.74 50.8) (uuid e19f5579-8f60-478e-a3c7-0f14252def9f))

  (wire (pts (xy 78.74 60.96) (xy 77.47 60.96))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 001dfe64-69da-4fff-908a-0bba6bd7f438)
  )
  (wire (pts (xy 93.98 31.75) (xy 92.71 31.75))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 03ed05d7-641c-42fc-9d0a-7ae53aa4e351)
  )
  (wire (pts (xy 71.12 45.72) (xy 78.74 45.72))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 08b2b507-6c4d-4707-bdf2-c13caaec5dc2)
  )
  (polyline (pts (xy 123.19 99.06) (xy 123.19 120.65))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 1463ed39-ce46-41c9-8126-b4d4bb74bbc4)
  )
  (polyline (pts (xy 82.55 120.65) (xy 60.96 120.65))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 15fa0b37-29b8-489c-bcf9-c04ea207d9b2)
  )

  (wire (pts (xy 77.47 43.18) (xy 77.47 40.64))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 16d4456b-c4be-48e7-82d3-0591d3ea3b72)
  )
  (polyline (pts (xy 74.93 176.53) (xy 106.68 176.53))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 1738c625-2a89-4f3a-b3e5-57a9d82802b1)
  )
  (polyline (pts (xy 123.19 120.65) (xy 91.44 120.65))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 1ad56a24-ccb0-4fd9-915a-8e32930c6562)
  )

  (wire (pts (xy 93.98 33.02) (xy 93.98 31.75))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 229ada47-4812-454c-b6ed-6e36eed5ba2a)
  )
  (wire (pts (xy 74.93 158.75) (xy 86.36 158.75))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 24ede8fb-d354-4cb4-8adb-bbe83898458f)
  )
  (wire (pts (xy 77.47 59.69) (xy 77.47 58.42))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 276c3ea6-3b3a-4429-99e2-2121728a9256)
  )
  (polyline (pts (xy 60.96 97.79) (xy 82.55 97.79))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 34e06bb9-8ba5-47f3-8903-18681b3b6463)
  )

  (wire (pts (xy 86.36 161.29) (xy 104.14 161.29))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 37d7e5d7-c596-4f81-8164-c434988e6603)
  )
  (polyline (pts (xy 82.55 97.79) (xy 82.55 120.65))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 3f17807d-9438-40dd-9c1e-7dae2033a444)
  )

  (wire (pts (xy 86.36 158.75) (xy 97.79 158.75))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 440c954d-ef7a-4cbc-839b-60c8d4606d10)
  )
  (wire (pts (xy 91.44 31.75) (xy 92.71 31.75))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 443272c0-a012-4526-a027-e4ed496422f5)
  )
  (wire (pts (xy 71.12 48.26) (xy 78.74 48.26))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 46a17320-b4e1-4102-8db3-e322ea049e46)
  )
  (wire (pts (xy 91.44 72.39) (xy 91.44 68.58))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 4aa4f4e3-207a-402e-90b0-50a767bf6420)
  )
  (wire (pts (xy 86.36 163.83) (xy 97.79 163.83))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 4d840f36-c33c-42ff-8ee9-596b5920572a)
  )
  (wire (pts (xy 77.47 58.42) (xy 78.74 58.42))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 4f11ef72-1699-498c-be04-90f7321c2387)
  )
  (wire (pts (xy 97.79 158.75) (xy 97.79 153.67))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 5126df69-c9d9-4643-8540-201789acca22)
  )
  (wire (pts (xy 76.2 63.5) (xy 78.74 63.5))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 5d11c0e3-d722-46a4-b01d-0a0cfa06cc22)
  )
  (wire (pts (xy 77.47 43.18) (xy 78.74 43.18))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 5d9ba425-3997-470f-9b76-4a7d78c4aca5)
  )
  (wire (pts (xy 118.11 48.26) (xy 104.14 48.26))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 5eb77423-66bd-40e2-89cd-07911e11126d)
  )
  (wire (pts (xy 97.79 170.18) (xy 97.79 163.83))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 6249084a-25fa-40e9-b3a7-6fd2d1077cf2)
  )
  (wire (pts (xy 71.12 100.33) (xy 71.12 101.6))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 6611a813-c322-458f-8cd4-77e33ac36bd4)
  )
  (wire (pts (xy 71.12 48.26) (xy 71.12 52.07))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 676bc776-8f40-4eab-a9d9-9b105977f7f2)
  )
  (polyline (pts (xy 106.68 146.05) (xy 106.68 176.53))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 6f4add23-54c4-4b8d-b341-f5dd40a49d38)
  )

  (wire (pts (xy 109.22 101.6) (xy 109.22 104.14))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 746b070d-9799-42f3-a86b-7536851d3819)
  )
  (wire (pts (xy 74.93 163.83) (xy 86.36 163.83))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 7c1e4463-e365-49db-bf2c-10201d40000b)
  )
  (wire (pts (xy 74.93 165.1) (xy 74.93 163.83))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 85ffb061-99bd-4663-9bba-0bbfe3c8e2f6)
  )
  (wire (pts (xy 31.75 161.29) (xy 86.36 161.29))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 8643b424-5ff4-406f-936e-b0fe80083a72)
  )
  (wire (pts (xy 43.18 157.48) (xy 74.93 157.48))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 8b690000-b321-4235-b7b6-bce480bdcb5b)
  )
  (wire (pts (xy 76.2 59.69) (xy 77.47 59.69))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 8b723995-6b4c-48df-9ec4-9c6ec8f0532a)
  )
  (wire (pts (xy 97.79 101.6) (xy 97.79 104.14))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 9b2d23c5-44d0-4858-9fdb-24f4bf0dad85)
  )
  (wire (pts (xy 104.14 162.56) (xy 104.14 161.29))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 9d66bb61-88b0-4b9f-8325-87ecfd287397)
  )
  (wire (pts (xy 43.18 151.13) (xy 31.75 151.13))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid 9fbed3ba-ce4a-4cac-b7af-cbbf459e7c2d)
  )
  (polyline (pts (xy 74.93 146.05) (xy 106.68 146.05))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid a0d671e7-b112-4c9d-bebc-66cb7822e8cf)
  )

  (wire (pts (xy 43.18 165.1) (xy 74.93 165.1))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid a89f9baa-efad-4d0d-b542-3a904d24e1f6)
  )
  (wire (pts (xy 43.18 165.1) (xy 43.18 171.45))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid a974b376-d1e7-4179-a017-b0a030eb122e)
  )
  (wire (pts (xy 77.47 60.96) (xy 77.47 59.69))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid abd22443-7694-4247-bf69-b1bf8a6b7b9c)
  )
  (wire (pts (xy 76.2 55.88) (xy 78.74 55.88))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid af97a913-1df9-4ddd-977b-5e0501246a6c)
  )
  (polyline (pts (xy 74.93 176.53) (xy 74.93 146.05))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid b457fdef-5d04-4925-9a9e-8c42836b4e14)
  )

  (wire (pts (xy 74.93 157.48) (xy 74.93 158.75))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid b74a9094-c986-49ae-8219-b877abfb4713)
  )
  (polyline (pts (xy 60.96 120.65) (xy 60.96 97.79))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid c7e6812b-fe9b-475e-a15d-2d7c4309efc6)
  )
  (polyline (pts (xy 91.44 99.06) (xy 91.44 120.65))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid c9c3cfd1-5400-4a41-8054-ce93b217babc)
  )

  (wire (pts (xy 92.71 30.48) (xy 106.68 30.48))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid cd575216-1ca5-41c1-854e-e58b3a11d507)
  )
  (wire (pts (xy 92.71 30.48) (xy 92.71 31.75))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid dce96d3c-65a5-4116-8f01-0a54e119cb20)
  )
  (wire (pts (xy 91.44 33.02) (xy 91.44 31.75))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid e191c5cc-6210-4e63-a712-23d21e02f2ef)
  )
  (wire (pts (xy 41.91 38.1) (xy 78.74 38.1))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid e7532149-62e2-4fc0-b0c4-e3c77970e07f)
  )
  (wire (pts (xy 43.18 99.06) (xy 43.18 102.87))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid eb0c4646-5b1f-4b04-a32d-bcdc94aa84b7)
  )
  (wire (pts (xy 77.47 40.64) (xy 78.74 40.64))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid ebf45c15-250a-4753-bd82-5b69f0d0ff14)
  )
  (wire (pts (xy 106.68 22.86) (xy 106.68 30.48))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid f0b8c226-fa02-4a4c-b37f-b1e126469d97)
  )
  (wire (pts (xy 43.18 171.45) (xy 31.75 171.45))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid f33330c3-2016-410e-b310-81f014647b29)
  )
  (wire (pts (xy 43.18 157.48) (xy 43.18 151.13))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid f97e344d-b832-4fce-ab1d-4cac34e6237e)
  )
  (polyline (pts (xy 91.44 99.06) (xy 123.19 99.06))
    (stroke (width 0) (type default) (color 0 0 0 0))
    (uuid fd668274-60a2-4bd7-a036-5f4d1e19f437)
  )

  (text "* Auxilary power supply input" (at 76.2 144.78 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 23ea6767-4b6d-4bf8-8f7c-b6ca35e99fc2)
  )
  (text ".subckt OPAMP INon IInv out Vp Vm g=100k\n  bGain gain gnd v={g*v(INon, IInv)}\n  bOut out gnd v={v(gain)> v(Vp) ? v(Vp) : v(gain) < v(Vm) ? v(Vm) : v(gain)}\n.ends OPAMP\n\n\n.subckt SG3525 1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16\n	* Voltage reference\n	.param Vref = 5.1V\n	bVref 16 12 v={Vref < v(15, 12) ? Vref : v(15) }\n\n	* Error amplifier (opamp)\n	xOpampError 2 1 9 15 12 OPAMP\n\n	* Measure Ct\n	aCtMeter 5 CtVal ctest\n	.model ctest cmeter(gain=1.0)\n\n	* Measure Rt\n	vRtMeasI 16 6 0\n	bRt 12 RtVal v={-Vref/i(vRtMeasI)}\n\n	* Triangular wave oscillator\n	* f = 1/(Ct*0.7*Rt)\n	bFreq freq 12 v={(1/(v(CtVal)*0.7*v(RtVal))) < 1 ? 1 : 1/(v(CtVal)*0.7*v(RtVal))}\n	aTriOsc freq carrier trigen\n	.model trigen triangle(cntl_array = [10 500e3]\n		+freq_array=[10 500e3] out_low = 0\n		+out_high = {Vref} duty_cycle = 0.999)\n\n	* Carrier debug (send it to OSC output)\n	Rtie carrier 4 0\n\n	* PWM\n	bOutA 11 12 v={min(v(9), v(8)) > v(carrier)+v(12) ? v(doShutdown,12) < {Vref/2} ? v(13) : v(12) : v(12) }\n	bOutB 14 12 v={min(v(9), v(8)) < v(carrier)+v(12) ? v(doShutdown,12) < {Vref/2} ? v(13) : v(12) : v(12) }\n\n	* Soft Start\n	.param IssVal = 50u\n	Iss 12 8 {IssVal}\n	a1 12 8 vclamp\n	.model vclamp zener(v_breakdown={Vref} i_breakdown={IssVal})\n	*.model vclamp sidiode(Vrev={Vref})\n\n	* Shutdown\n	bShutdown doShutdown 12 v={v(10,12) > 0.8 ? {Vref} : v(12) }; Threshold on pin 10 is around 0.8V\n	sShutdownSwitch 8 12 doShutdown 12 sswitch ON\n	.model sswitch SW(RON=3k VT={Vref/2})\n\n.ends SG3525\n\n"
    (at 167.64 123.19 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 3727230b-8c74-4697-b114-69ede059545e)
  )
  (text ".tran 10us 20ms uic\n.option INTERP" (at 184.15 175.26 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 616da666-c04f-4a81-b4bd-413937f9cc9a)
  )
  (text "* RC oscillator for PWM carrier\n* f = 1/(0.7*Rt*Ct)"
    (at 91.44 97.79 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid b95129da-268e-495d-875c-24b959da9990)
  )
  (text "* Soft Start ramp\n* Tstart = Css*5.1/50µA" (at 60.96 96.52 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid bf89960e-9f28-42a6-9260-c8e68f3a7d33)
  )

  (label "pwm_to_driver" (at 118.11 48.26 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 07072e08-28bf-466d-ac79-4b46ffac8a92)
  )
  (label "rt" (at 76.2 55.88 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid 214ea642-b164-44cd-8889-885a740d960d)
  )
  (label "ct" (at 76.2 59.69 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid 4732f422-3323-4835-be98-33a90b22f608)
  )
  (label "ct" (at 109.22 101.6 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 5c02e94e-fb75-4de5-83e5-d43f3442e2e7)
  )
  (label "vref" (at 71.12 45.72 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid 5e7ccbef-1202-4950-b0cf-9da440dcdc54)
  )
  (label "opamp_buff_fb" (at 77.47 41.91 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid 7d8d9634-5f64-41dd-96c9-590841817ae4)
  )
  (label "rt" (at 97.79 101.6 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 95744d34-5b95-4f9c-b741-661a221976e4)
  )
  (label "soft_start" (at 71.12 100.33 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid b8331225-faa0-4ab0-9987-5366ca198f71)
  )
  (label "vref" (at 43.18 99.06 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid c659cd6f-35d3-4a3f-88fa-33c05a4c75e6)
  )
  (label "soft_start" (at 76.2 63.5 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid cbb180fa-99e4-4a77-a048-2f13d78fc722)
  )

  (symbol (lib_id "power:GND") (at 71.12 114.3 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 019ba37f-40c4-41fd-87c8-4532d4ac1fc8)
    (property "Reference" "#PWR03" (id 0) (at 71.12 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 71.12 119.38 0))
    (property "Footprint" "" (id 2) (at 71.12 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 71.12 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 032ba992-86b5-4da3-b4b0-22c9db9a60d8))
  )

  (symbol (lib_id "pspice:CAP") (at 43.18 109.22 0) (mirror y) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 02faac99-3fd7-465a-8f62-7941ceb685fd)
    (property "Reference" "C1" (id 0) (at 48.26 107.9499 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "100nF" (id 1) (at 48.26 110.4899 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "Capacitor_THT:C_Disc_D7.0mm_W2.5mm_P5.00mm" (id 2) (at 43.18 109.22 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 43.18 109.22 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 796986bf-adf1-47dd-90c3-94c0820e25aa))
    (pin "2" (uuid 23f90421-06fb-4560-97fe-f58dfd3fa7b6))
  )

  (symbol (lib_id "pspice:CAP") (at 106.68 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 0a1da370-f23b-4ae8-8734-a18676f37bcd)
    (property "Reference" "C3" (id 0) (at 111.76 35.5599 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100nF" (id 1) (at 111.76 38.0999 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_THT:C_Disc_D7.0mm_W2.5mm_P5.00mm" (id 2) (at 106.68 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 106.68 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1b68288d-63bd-4dab-b2d4-deb973b69640))
    (pin "2" (uuid efca32c9-5dae-4fd1-84f9-5eb59eec8dad))
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 31.75 156.21 0) (unit 1)
    (in_bom no) (on_board no) (fields_autoplaced)
    (uuid 0b0f76dc-a27b-45c6-8fb5-8ad35189772a)
    (property "Reference" "V1" (id 0) (at 35.56 153.4801 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VDC" (id 1) (at 35.56 156.0201 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 31.75 156.21 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 31.75 156.21 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 31.75 156.21 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 31.75 156.21 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "dc 15" (id 6) (at 35.56 158.5601 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (pin "1" (uuid 04551189-4a2e-4382-ac03-950b873af2fd))
    (pin "2" (uuid 3088ee83-ba69-4ab6-be72-13eca2c477ef))
  )

  (symbol (lib_id "Connector:Conn_01x03_Female") (at 91.44 161.29 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 189a7cd7-ef2d-41b6-b7d5-56a162f660b0)
    (property "Reference" "J1" (id 0) (at 82.55 160.02 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "Conn_01x03_Female" (id 1) (at 77.47 156.21 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "TerminalBlock_Phoenix:TerminalBlock_Phoenix_MKDS-1,5-3-5.08_1x03_P5.08mm_Horizontal" (id 2) (at 91.44 161.29 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 91.44 161.29 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "N" (id 4) (at 91.44 161.29 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8607f456-e0a7-4034-91b4-1e471e6dc0c0))
    (pin "2" (uuid f404367f-e46f-44dc-96af-8787a3c982c3))
    (pin "3" (uuid 8d56f002-6543-4ef0-acf7-f0bba05eda7d))
  )

  (symbol (lib_id "power:GND") (at 91.44 72.39 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 1ee1be6b-f089-41f0-a8fc-3b89aa669f61)
    (property "Reference" "#PWR04" (id 0) (at 91.44 78.74 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 91.44 77.47 0))
    (property "Footprint" "" (id 2) (at 91.44 72.39 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 91.44 72.39 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 50a53bf9-53e0-4e9d-b9a9-e7fd52791818))
  )

  (symbol (lib_id "pspice:CAP") (at 71.12 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 43e7145f-51f2-4587-a41a-7f9d115c8db7)
    (property "Reference" "C2" (id 0) (at 76.2 106.6799 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100nF" (id 1) (at 76.2 109.2199 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_THT:C_Disc_D7.0mm_W2.5mm_P5.00mm" (id 2) (at 71.12 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 71.12 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid cf91262d-1754-41f9-8730-92a14c6bcd2f))
    (pin "2" (uuid 6ed60e08-f03c-4d44-bb3d-d1fca0611b6b))
  )

  (symbol (lib_id "power:+15V") (at 97.79 153.67 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 563f5b91-280f-4d4a-aba4-6aca35f844c4)
    (property "Reference" "#PWR06" (id 0) (at 97.79 157.48 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+15V" (id 1) (at 97.79 148.59 0))
    (property "Footprint" "" (id 2) (at 97.79 153.67 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 97.79 153.67 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid ea00e847-3316-4b46-b348-86360b15c9a5))
  )

  (symbol (lib_id "power:GND") (at 71.12 52.07 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 7042be8d-f6da-4e40-832f-8c34e3d6b035)
    (property "Reference" "#PWR02" (id 0) (at 71.12 58.42 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 71.12 55.88 0))
    (property "Footprint" "" (id 2) (at 71.12 52.07 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 71.12 52.07 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e7d5fccb-bb2b-49d0-98b1-f7bce622f261))
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 31.75 166.37 0) (unit 1)
    (in_bom no) (on_board no) (fields_autoplaced)
    (uuid 7fb5187a-dc8c-42f4-bdac-bb6ce5c97775)
    (property "Reference" "V2" (id 0) (at 35.56 163.6401 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VDC" (id 1) (at 35.56 166.1801 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 31.75 166.37 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 31.75 166.37 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 31.75 166.37 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 31.75 166.37 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "dc 15" (id 6) (at 35.56 168.7201 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (pin "1" (uuid fce4f0aa-50fd-4ff4-88f8-628f952c8847))
    (pin "2" (uuid da301795-d22f-490d-a4bc-3a22c24711f1))
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 41.91 43.18 0) (unit 1)
    (in_bom no) (on_board no) (fields_autoplaced)
    (uuid 800e2ec8-c724-4087-ba37-51e6e3e11b78)
    (property "Reference" "V3" (id 0) (at 45.72 40.4501 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VDC" (id 1) (at 45.72 42.9901 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 41.91 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 41.91 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 41.91 43.18 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 41.91 43.18 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "dc 4.5" (id 6) (at 45.72 45.5301 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (pin "1" (uuid b00f6e80-3db0-4b73-bbab-430d78bf2510))
    (pin "2" (uuid fb1aaa83-e024-4c53-8c60-f2d2d5c35c2f))
  )

  (symbol (lib_id "pspice:R") (at 97.79 110.49 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 936e9914-499c-453e-afff-3055dc279783)
    (property "Reference" "R1" (id 0) (at 100.33 109.2199 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "3.22k" (id 1) (at 100.33 111.7599 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal" (id 2) (at 97.79 110.49 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 97.79 110.49 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b605eb21-2be9-4b74-a36d-867e2a5ea0a6))
    (pin "2" (uuid 46bf7a29-4e1d-48cc-9e6a-a6061dcf7fc7))
  )

  (symbol (lib_id "power:GND") (at 104.14 162.56 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 9bace587-2add-43ab-884c-1331c48920d3)
    (property "Reference" "#PWR08" (id 0) (at 104.14 168.91 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 104.14 167.64 0))
    (property "Footprint" "" (id 2) (at 104.14 162.56 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 104.14 162.56 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 4751fc22-5f98-4f20-9a0a-17b4d1a641cb))
  )

  (symbol (lib_id "power:GND") (at 97.79 116.84 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid aa6f1321-c9d0-4b06-9ea0-82e735bbedf3)
    (property "Reference" "#PWR05" (id 0) (at 97.79 123.19 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 93.98 118.11 0))
    (property "Footprint" "" (id 2) (at 97.79 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 97.79 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 445b0058-9963-4270-8f0a-7f1ec8bb9e03))
  )

  (symbol (lib_id "pspice:CAP") (at 109.22 110.49 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid aba7622b-df07-48ef-88cf-49fbccff82bf)
    (property "Reference" "C4" (id 0) (at 114.3 109.2199 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "150nF" (id 1) (at 114.3 111.7599 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Capacitor_THT:C_Disc_D7.0mm_W2.5mm_P5.00mm" (id 2) (at 109.22 110.49 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 109.22 110.49 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 99a88976-d3e1-4815-8ca0-2f4989da61b4))
    (pin "2" (uuid 9200730c-0ab4-430d-ae58-7e71d5e47010))
  )

  (symbol (lib_id "power:GND") (at 106.68 43.18 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid c4bef934-3f6e-46f3-a46e-8686e94beccd)
    (property "Reference" "#PWR010" (id 0) (at 106.68 49.53 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 110.49 44.45 0))
    (property "Footprint" "" (id 2) (at 106.68 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 106.68 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid efddf087-5675-4741-82df-b0d37370e689))
  )

  (symbol (lib_id "Spice_power_electronics:UC3525") (at 91.44 50.8 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid ce184558-d5f3-4fac-b695-2d9f807fb58a)
    (property "Reference" "U1" (id 0) (at 93.4594 68.58 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "UC3525" (id 1) (at 93.4594 71.12 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Package_DIP:DIP-16_W7.62mm" (id 2) (at 91.44 50.8 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "https://www.ti.com/lit/ds/symlink/uc3525a.pdf" (id 3) (at 129.54 44.45 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Primitive" "X" (id 4) (at 95.25 29.21 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "SG3525" (id 5) (at 102.87 31.75 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 6) (at 95.25 31.75 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (pin "1" (uuid 2ee05f7e-8551-4151-8f0c-ae64dc3999ab))
    (pin "10" (uuid 24b9f771-f870-4f55-a1fd-0197332dbea6))
    (pin "11" (uuid 95d890a4-0aab-40fa-b90e-40942f798003))
    (pin "12" (uuid d9223473-11de-401c-ab71-4b96dedcbcad))
    (pin "13" (uuid 06f4256b-e5b4-4789-9029-c285bf23fe67))
    (pin "14" (uuid 1002653f-3d1c-4b1d-a558-e744fc6e3dae))
    (pin "15" (uuid d768a71d-6d0e-479e-9496-a1ba6b49eddf))
    (pin "16" (uuid 9a5bc1f1-3d20-4da5-bef9-a881472d6d75))
    (pin "2" (uuid 79f9995d-82d3-4353-ae6e-f3bc6eee8810))
    (pin "3" (uuid 9d6ff076-3ff3-418c-a7e8-9c39435e957e))
    (pin "4" (uuid 5e342575-46de-4310-ab86-5abbbee8e461))
    (pin "5" (uuid 1b52ef56-0d7f-44eb-870c-5e2a81054c23))
    (pin "6" (uuid 93cb273e-7b5e-4d15-ae03-08ee83123ac0))
    (pin "7" (uuid d1c4f841-1c67-4994-9815-918af3aaabf6))
    (pin "8" (uuid a339a130-ea62-4548-a1b5-666f0073bdbd))
    (pin "9" (uuid 7436b478-5107-4910-a4a7-e08bd67f0a90))
  )

  (symbol (lib_id "power:GND") (at 41.91 48.26 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid d724acc2-171e-49c1-b919-649c13b5b3ae)
    (property "Reference" "#PWR012" (id 0) (at 41.91 54.61 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 41.91 53.34 0))
    (property "Footprint" "" (id 2) (at 41.91 48.26 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 41.91 48.26 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 07d7b14f-11c4-4251-9ef4-e0a4956a24c6))
  )

  (symbol (lib_id "power:-15V") (at 97.79 170.18 180) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid da553073-9f25-47e3-a631-2bec39e9dbd5)
    (property "Reference" "#PWR07" (id 0) (at 97.79 172.72 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "-15V" (id 1) (at 97.79 175.26 0))
    (property "Footprint" "" (id 2) (at 97.79 170.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 97.79 170.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f46d02e5-8b30-4e3a-a727-329d68f36b1f))
  )

  (symbol (lib_id "power:GND") (at 43.18 115.57 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid dc44e8c7-1fb4-4aa1-8f7f-693f4f492f15)
    (property "Reference" "#PWR01" (id 0) (at 43.18 121.92 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 43.18 120.65 0))
    (property "Footprint" "" (id 2) (at 43.18 115.57 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 43.18 115.57 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 03a0038f-2fa5-4a40-a5f5-5844997f0c85))
  )

  (symbol (lib_id "power:+15V") (at 106.68 22.86 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid f0a461b5-90a6-495f-a03d-62e6c8a5d9a3)
    (property "Reference" "#PWR09" (id 0) (at 106.68 26.67 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+15V" (id 1) (at 106.68 17.78 0))
    (property "Footprint" "" (id 2) (at 106.68 22.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 106.68 22.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1b1bf75f-fa3f-4b63-85fd-d1059d659d0f))
  )

  (symbol (lib_id "power:GND") (at 109.22 116.84 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid fa5a3fd3-2676-45bf-91f9-099426ccd199)
    (property "Reference" "#PWR011" (id 0) (at 109.22 123.19 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 105.41 118.11 0))
    (property "Footprint" "" (id 2) (at 109.22 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 109.22 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3994e8aa-4ad6-417a-8e8c-4599e1dbc433))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/dc44e8c7-1fb4-4aa1-8f7f-693f4f492f15"
      (reference "#PWR01") (unit 1) (value "GND") (footprint "")
    )
    (path "/7042be8d-f6da-4e40-832f-8c34e3d6b035"
      (reference "#PWR02") (unit 1) (value "GND") (footprint "")
    )
    (path "/019ba37f-40c4-41fd-87c8-4532d4ac1fc8"
      (reference "#PWR03") (unit 1) (value "GND") (footprint "")
    )
    (path "/1ee1be6b-f089-41f0-a8fc-3b89aa669f61"
      (reference "#PWR04") (unit 1) (value "GND") (footprint "")
    )
    (path "/aa6f1321-c9d0-4b06-9ea0-82e735bbedf3"
      (reference "#PWR05") (unit 1) (value "GND") (footprint "")
    )
    (path "/563f5b91-280f-4d4a-aba4-6aca35f844c4"
      (reference "#PWR06") (unit 1) (value "+15V") (footprint "")
    )
    (path "/da553073-9f25-47e3-a631-2bec39e9dbd5"
      (reference "#PWR07") (unit 1) (value "-15V") (footprint "")
    )
    (path "/9bace587-2add-43ab-884c-1331c48920d3"
      (reference "#PWR08") (unit 1) (value "GND") (footprint "")
    )
    (path "/f0a461b5-90a6-495f-a03d-62e6c8a5d9a3"
      (reference "#PWR09") (unit 1) (value "+15V") (footprint "")
    )
    (path "/c4bef934-3f6e-46f3-a46e-8686e94beccd"
      (reference "#PWR010") (unit 1) (value "GND") (footprint "")
    )
    (path "/fa5a3fd3-2676-45bf-91f9-099426ccd199"
      (reference "#PWR011") (unit 1) (value "GND") (footprint "")
    )
    (path "/d724acc2-171e-49c1-b919-649c13b5b3ae"
      (reference "#PWR012") (unit 1) (value "GND") (footprint "")
    )
    (path "/02faac99-3fd7-465a-8f62-7941ceb685fd"
      (reference "C1") (unit 1) (value "100nF") (footprint "Capacitor_THT:C_Disc_D7.0mm_W2.5mm_P5.00mm")
    )
    (path "/43e7145f-51f2-4587-a41a-7f9d115c8db7"
      (reference "C2") (unit 1) (value "100nF") (footprint "Capacitor_THT:C_Disc_D7.0mm_W2.5mm_P5.00mm")
    )
    (path "/0a1da370-f23b-4ae8-8734-a18676f37bcd"
      (reference "C3") (unit 1) (value "100nF") (footprint "Capacitor_THT:C_Disc_D7.0mm_W2.5mm_P5.00mm")
    )
    (path "/aba7622b-df07-48ef-88cf-49fbccff82bf"
      (reference "C4") (unit 1) (value "150nF") (footprint "Capacitor_THT:C_Disc_D7.0mm_W2.5mm_P5.00mm")
    )
    (path "/189a7cd7-ef2d-41b6-b7d5-56a162f660b0"
      (reference "J1") (unit 1) (value "Conn_01x03_Female") (footprint "TerminalBlock_Phoenix:TerminalBlock_Phoenix_MKDS-1,5-3-5.08_1x03_P5.08mm_Horizontal")
    )
    (path "/936e9914-499c-453e-afff-3055dc279783"
      (reference "R1") (unit 1) (value "3.22k") (footprint "Resistor_THT:R_Axial_DIN0207_L6.3mm_D2.5mm_P7.62mm_Horizontal")
    )
    (path "/ce184558-d5f3-4fac-b695-2d9f807fb58a"
      (reference "U1") (unit 1) (value "UC3525") (footprint "Package_DIP:DIP-16_W7.62mm")
    )
    (path "/0b0f76dc-a27b-45c6-8fb5-8ad35189772a"
      (reference "V1") (unit 1) (value "VDC") (footprint "")
    )
    (path "/7fb5187a-dc8c-42f4-bdac-bb6ce5c97775"
      (reference "V2") (unit 1) (value "VDC") (footprint "")
    )
    (path "/800e2ec8-c724-4087-ba37-51e6e3e11b78"
      (reference "V3") (unit 1) (value "VDC") (footprint "")
    )
  )
)
