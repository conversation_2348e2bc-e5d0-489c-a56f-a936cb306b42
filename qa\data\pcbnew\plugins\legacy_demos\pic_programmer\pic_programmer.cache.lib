EESchema-LIBRARY Version  30/4/2008-18:02:35
#
#
# 24C16
#
DEF 24C16 U 0 30 Y Y 1 F N
F0 "U" 150 350 60 H V C C
F1 "24C16" 200 -350 60 H V C C
ALIAS 24C512
DRAW
S -400 -300 400 300 0 1 0 N
X VCC 8 0 300 0 D 60 60 0 0 W N
X SDA 5 700 -200 300 L 60 60 1 1 B
X SCL 6 700 -100 300 L 60 60 1 1 I
X WP 7 700 100 300 L 60 60 1 1 I
X A2 3 -700 0 300 R 60 60 1 1 I
X A1 2 -700 100 300 R 60 60 1 1 I
X A0 1 -700 200 300 R 60 60 1 1 I
X GND 4 0 -300 0 U 60 60 0 0 W N
ENDDRAW
ENDDEF
#
# 74LS125
#
DEF 74LS125 U 0 30 Y Y 4 F N
F0 "U" 0 100 50 H V L B
F1 "74LS125" 50 -150 40 H V L T
DRAW
P 4 0 1 0  -150 150  -150 -150  150 0  -150 150 N
X VCC 14 -150 150 0 D 50 30 0 0 W N
X O 3 450 0 300 L 50 30 1 1 T
X O 11 450 0 300 L 50 30 4 1 T
X O 8 450 0 300 L 50 30 3 1 T
X O 6 450 0 300 L 50 30 2 1 T
X D 2 -450 0 300 R 50 30 1 1 I
X D 5 -450 0 300 R 50 30 2 1 I
X D 9 -450 0 300 R 50 30 3 1 I
X D 12 -450 0 300 R 50 30 4 1 I
X GND 7 -150 -150 0 U 50 30 0 0 W N
X E 1 0 -300 220 U 50 30 1 0 I I
X E 13 0 -300 220 U 50 30 4 0 I I
X E 10 0 -300 220 U 50 30 3 0 I I
X E 4 0 -300 220 U 50 30 2 0 I I
ENDDRAW
ENDDEF
#
# 7805
#
DEF 7805 U 0 30 N Y 1 F N
F0 "U" 150 -196 60 H V C C
F1 "7805" 0 200 60 H V C C
ALIAS LM7805 LM7812 78L05
DRAW
S -200 -150 200 150 0 1 0 N
X VO VO 400 50 200 L 40 40 1 1 w
X VI VI -400 50 200 R 40 40 1 1 I
X GND GND 0 -250 100 U 40 40 1 1 I
ENDDRAW
ENDDEF
#
# C
#
DEF C C 0 10 N Y 1 F N
F0 "C" 50 100 50 H V L C
F1 "C" 50 -100 50 H V L C
$FPLIST
 SM*
 C?
 C1-1
$ENDFPLIST
DRAW
P 2 0 1 8  -100 -30  100 -30 N
P 2 0 1 8  -100 30  100 30 N
X ~ 1 0 200 170 D 40 40 1 1 P
X ~ 2 0 -200 170 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# CONN_2
#
DEF CONN_2 P 0 40 Y N 1 F N
F0 "P" -50 0 40 V V C C
F1 "CONN_2" 50 0 40 V V C C
DRAW
S -100 150 100 -150 0 1 0 N
X PM 2 -350 -100 250 R 60 60 1 1 P I
X P1 1 -350 100 250 R 60 60 1 1 P I
ENDDRAW
ENDDEF
#
# CP
#
DEF CP C 0 10 N N 1 F N
F0 "C" 50 100 50 H V L C
F1 "CP" 50 -100 50 H V L C
ALIAS CAPAPOL
$FPLIST
 CP*
 SM*
$ENDFPLIST
DRAW
P 4 0 1 0  -50 50  -50 -20  50 -20  50 50 F
P 4 0 1 8  -100 50  -100 -50  100 -50  100 50 N
X ~ 1 0 200 150 D 40 40 1 1 P
X ~ 2 0 -200 150 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# DB9
#
DEF DB9 J 0 40 Y N 1 F N
F0 "J" 0 550 70 H V C C
F1 "DB9" 0 -550 70 H V C C
$FPLIST
 DB9*
$ENDFPLIST
DRAW
C -70 -200 30 0 1 0 N
C 50 -100 30 0 1 0 N
C -70 200 30 0 1 0 N
P 2 0 1 0  -150 -200  -100 -200 N
C -70 -400 30 0 1 0 N
P 2 0 1 8  140 -409  -50 -490 N
P 2 0 1 0  -150 -100  20 -100 N
C 50 100 30 0 1 0 N
P 2 0 1 8  -140 470  -100 490 N
C 50 300 30 0 1 0 N
P 2 0 1 0  -150 -300  20 -300 N
P 2 0 1 8  129 390  -70 490 N
P 2 0 1 0  -150 -400  -100 -400 N
P 2 0 1 8  -150 -459  -140 -470 N
P 2 0 1 0  -150 0  -100 0 N
P 2 0 1 8  150 -390  140 -409 N
P 2 0 1 0  -150 200  -100 200 N
P 2 0 1 8  -110 -490  -50 -490 N
P 2 0 1 8  -140 470  -150 460 N
P 2 0 1 8  129 390  150 370 N
C 50 -300 30 0 1 0 N
C -70 0 30 0 1 0 N
C -70 400 30 0 1 0 N
P 2 0 1 8  -140 -470  -110 -490 N
P 2 0 1 8  -100 490  -70 490 N
P 2 0 1 8  150 370  150 -390 N
P 2 0 1 0  -150 100  20 100 N
P 2 0 1 8  -150 -460  -150 460 N
P 2 0 1 0  -150 400  -100 400 N
P 2 0 1 0  -150 300  20 300 N
X 1 1 -450 -400 300 R 60 60 1 1 P
X P6 6 -450 -300 300 R 60 60 1 1 P
X 2 2 -450 -200 300 R 60 60 1 1 P
X P7 7 -450 -100 300 R 60 60 1 1 P
X 3 3 -450 0 300 R 60 60 1 1 P
X P8 8 -450 100 300 R 60 60 1 1 P
X 4 4 -450 200 300 R 60 60 1 1 P
X P9 9 -450 300 300 R 60 60 1 1 P
X 5 5 -450 400 300 R 60 60 1 1 P
ENDDRAW
ENDDEF
#
# DIODE
#
DEF DIODE D 0 40 N N 1 F N
F0 "D" 0 100 40 H V C C
F1 "DIODE" 0 -100 40 H V C C
$FPLIST
 D?
 S*
$ENDFPLIST
DRAW
P 3 0 1 0  -50 50  50 0  -50 -50 F
P 2 0 1 6  50 50  50 -50 N
X K 2 200 0 150 L 40 40 1 1 P
X A 1 -200 0 150 R 40 40 1 1 P
ENDDRAW
ENDDEF
#
# DIODESCH
#
DEF DIODESCH D 0 40 N N 1 F N
F0 "D" 0 100 40 H V C C
F1 "DIODESCH" 0 -100 40 H V C C
$FPLIST
 D?
 S*
$ENDFPLIST
DRAW
P 3 0 1 0  -50 50  50 0  -50 -50 F
P 6 0 1 8  75 25  75 50  50 50  50 -50  25 -50  25 -25 N
X K 2 200 0 150 L 40 40 1 1 P
X A 1 -200 0 150 R 40 40 1 1 P
ENDDRAW
ENDDEF
#
# GND
#
DEF ~GND #PWR 0 0 Y Y 1 F P
F0 "#PWR" 0 0 30 H I C C
F1 "GND" 0 -70 30 H I C C
DRAW
P 4 0 1 4  -50 0  0 -50  50 0  -50 0 N
X GND 1 0 0 0 U 30 30 1 1 W N
ENDDRAW
ENDDEF
#
# INDUCTOR
#
DEF INDUCTOR L 0 40 N N 0 F N
F0 "L" -50 0 40 V V C C
F1 "INDUCTOR" 100 0 40 V V C C
DRAW
A 0 148 48 -889 889 0 1 0 N 1 101 1 196
A 0 51 51 -889 889 0 1 0 N 1 1 1 102
A 0 -49 51 -889 889 0 1 0 N 1 -99 1 2
A 0 -150 50 -889 889 0 1 0 N 1 -199 1 -100
X 1 1 0 300 100 D 70 70 1 1 P
X 2 2 0 -300 100 U 70 70 1 1 P
ENDDRAW
ENDDEF
#
# JUMPER
#
DEF JUMPER JP 0 30 Y N 1 F N
F0 "JP" 0 150 60 H V C C
F1 "JUMPER" 0 -80 40 H V C C
DRAW
A 0 -26 125 1426 373 0 1 0 N -98 50 99 50
X 2 2 300 0 200 L 60 60 0 1 P I
X 1 1 -300 0 200 R 60 60 0 1 P I
ENDDRAW
ENDDEF
#
# LED
#
DEF LED D 0 40 Y N 1 F N
F0 "D" 0 100 50 H V C C
F1 "LED" 0 -100 50 H V C C
DRAW
P 3 0 1 0  -50 50  50 0  -50 -50 F
P 3 0 1 0  65 -40  110 -80  105 -55 N
P 3 0 1 0  80 -25  125 -65  120 -40 N
P 2 0 1 0  50 50  50 -50 N
X K 2 200 0 150 L 40 40 1 1 P
X A 1 -200 0 150 R 40 40 1 1 P
ENDDRAW
ENDDEF
#
# LT1372
#
DEF LT1372 U 0 30 Y Y 1 F N
F0 "U" 600 500 60 H V C C
F1 "LT1372" -500 500 60 H V C C
ALIAS LT1373
DRAW
S -700 -400 700 400 0 1 0 N
X Vin 5 0 700 300 D 60 60 1 1 W
X FB+ 2 1000 -250 300 L 60 60 1 1 I
X Vsw 8 1000 250 300 L 60 60 1 1 I
X S/S 4 -1000 -250 300 R 60 60 1 1 P
X FB- 3 -1000 250 300 R 60 60 1 1 P
X GND 7 -300 -700 300 U 60 60 1 1 I
X GND_S 6 -150 -700 300 U 60 60 1 1 I
X Vc 1 250 -700 300 U 60 60 1 1 I
ENDDRAW
ENDDEF
#
# NPN
#
DEF NPN Q 0 0 Y Y 0 F N
F0 "Q" 150 0 50 H V C C
F1 "NPN" -98 150 50 H V C C
DRAW
P 5 0 1 0  50 -100  100 -100  100 -50  50 -100  50 -100 N
P 2 0 1 0  0 0  75 -75 N
P 2 0 1 0  0 0  100 100 N
P 2 0 1 8  0 100  0 -100 N
X C 3 100 200 100 D 40 40 1 1 P
X B 2 -200 0 200 R 40 40 1 1 I
X E 1 100 -200 100 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# PIC12C508A
#
DEF PIC12C508A U 0 40 Y Y 1 F N
F0 "U" 0 700 60 H V C C
F1 "PIC12C508A" 0 -650 60 H V C C
ALIAS PIC12C509A
DRAW
S 400 -600 -450 650 0 1 0 N
X GP2 5 700 -500 300 L 50 50 1 1 I
X GP2 5 700 -500 300 L 50 50 1 2 I
X GP1 6 700 -200 300 L 50 50 1 2 I
X GP1 6 700 -200 300 L 50 50 1 1 I
X GP0 7 700 200 300 L 50 50 1 2 I
X GP0 7 700 200 300 L 50 50 1 1 I
X VSS 8 700 500 300 L 50 50 1 1 W
X VSS 8 700 500 300 L 50 50 1 2 W
X GP3/MCLR 4 -750 -500 300 R 50 50 1 2 I
X GP3/MCLR 4 -750 -500 300 R 50 50 1 1 I
X GP4/OSC2 3 -750 -200 300 R 50 50 1 1 I
X GP4/OSC2 3 -750 -200 300 R 50 50 1 2 I
X GP5/OSC1 2 -750 200 300 R 50 50 1 2 I
X GP5/OSC1 2 -750 200 300 R 50 50 1 1 I
X VDD 1 -750 500 300 R 50 50 1 2 W
X VDD 1 -750 500 300 R 50 50 1 1 W
ENDDRAW
ENDDEF
#
# PIC16F54
#
DEF PIC16F54 U? 0 40 Y Y 1 F N
F0 "U?" 0 -750 60 H V C C
F1 "PIC16F54" 0 800 60 H V C C
DRAW
S -500 700 450 -700 0 1 0 N
X RB4 10 750 -600 300 L 50 50 1 2 B
X RB4 10 750 -600 300 L 50 50 1 1 B
X RB5 11 750 -450 300 L 50 50 1 1 B
X RB5 11 750 -450 300 L 50 50 1 2 B
X ICSPC/RB6 12 750 -300 300 L 50 50 1 1 B
X ICSPC/RB6 12 750 -300 300 L 50 50 1 2 B
X ICSPD/RB7 13 750 -150 300 L 50 50 1 1 B
X ICSPD/RB7 13 750 -150 300 L 50 50 1 2 B
X VDD 14 750 0 300 L 50 50 1 2 W
X VDD 14 750 0 300 L 50 50 1 1 W
X OSC2/CLKO 15 750 150 300 L 50 50 1 2 O
X OSC2/CLKO 15 750 150 300 L 50 50 1 1 O
X OSC1/CLKI 16 750 300 300 L 50 50 1 2 I
X OSC1/CLKI 16 750 300 300 L 50 50 1 1 I
X RA0 17 750 450 300 L 50 50 1 2 B
X RA0 17 750 450 300 L 50 50 1 1 B
X RA1 18 750 600 300 L 50 50 1 2 B
X RA1 18 750 600 300 L 50 50 1 1 B
X RB3 9 -800 -600 300 R 50 50 1 1 B
X RB3 9 -800 -600 300 R 50 50 1 2 B
X RB2 8 -800 -450 300 R 50 50 1 2 B
X RB2 8 -800 -450 300 R 50 50 1 1 B
X RB1 7 -800 -300 300 R 50 50 1 2 B
X RB1 7 -800 -300 300 R 50 50 1 1 B
X RB0 6 -800 -150 300 R 50 50 1 1 B
X RB0 6 -800 -150 300 R 50 50 1 2 B
X VSS 5 -800 0 300 R 50 50 1 2 W
X VSS 5 -800 0 300 R 50 50 1 1 W
X MCLR 4 -800 150 300 R 50 50 1 1 I
X MCLR 4 -800 150 300 R 50 50 1 2 I
X T0ckl 3 -800 300 300 R 50 50 1 2 O
X T0ckl 3 -800 300 300 R 50 50 1 1 O
X RA3 2 -800 450 300 R 50 50 1 2 B
X RA3 2 -800 450 300 R 50 50 1 1 B
X RA2 1 -800 600 300 R 50 50 1 2 B
X RA2 1 -800 600 300 R 50 50 1 1 B
ENDDRAW
ENDDEF
#
# PNP
#
DEF PNP Q 0 0 Y Y 0 F N
F0 "Q" 150 0 60 H V C C
F1 "PNP" -96 150 60 H V C C
DRAW
P 5 0 1 0  60 -60  120 -80  80 -120  60 -60  60 -60 N
P 2 0 1 0  0 0  60 -60 N
P 2 0 1 0  0 0  100 100 N
P 2 0 1 8  0 100  0 -100 N
X C 3 100 200 100 D 40 40 1 1 P
X B 2 -200 0 200 R 40 40 1 1 I
X E 1 100 -200 100 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# POT
#
DEF POT RV 0 40 Y N 0 F N
F0 "RV" 0 -100 50 H V C C
F1 "POT" 0 0 50 H V C C
DRAW
P 3 0 1 0  0 50  -20 70  20 70 F
S -150 50 150 -50 0 1 0 N
X 2 2 0 150 80 D 40 40 1 1 P
X 3 3 250 0 100 L 40 40 1 1 P
X 1 1 -250 0 100 R 40 40 1 1 P
ENDDRAW
ENDDEF
#
# PWR_FLAG
#
DEF PWR_FLAG #FLG 0 0 N N 1 F P
F0 "#FLG" 0 270 30 H I C C
F1 "PWR_FLAG" 0 230 30 H V C C
DRAW
P 3 0 1 0  0 0  0 100  0 100 N
P 5 0 1 0  0 100  -100 150  0 200  100 150  0 100 N
X pwr 1 0 0 0 U 20 20 0 0 w
ENDDRAW
ENDDEF
#
# R
#
DEF R R 0 0 N Y 1 F N
F0 "R" 80 0 50 V V C C
F1 "R" 0 0 50 V V C C
$FPLIST
 R?
 SM0603
 SM0805
$ENDFPLIST
DRAW
S -40 150 40 -150 0 1 8 N
X ~ 1 0 250 100 D 60 60 1 1 P
X ~ 2 0 -250 100 U 60 60 1 1 P
ENDDRAW
ENDDEF
#
# SUPP28
#
DEF SUPP28 J 0 40 Y Y 1 F N
F0 "J" 0 100 70 H V C C
F1 "SUPP28" 0 -100 70 H V C C
DRAW
S -300 -750 300 750 0 1 0 N
X 15 15 600 -650 300 L 60 60 1 1 P
X 16 16 600 -550 300 L 60 60 1 1 P
X 17 17 600 -450 300 L 60 60 1 1 P
X 18 18 600 -350 300 L 60 60 1 1 P
X 19 19 600 -250 300 L 60 60 1 1 P
X 20 20 600 -150 300 L 60 60 1 1 P
X 21 21 600 -50 300 L 60 60 1 1 P
X 22 22 600 50 300 L 60 60 1 1 P
X 23 23 600 150 300 L 60 60 1 1 P
X 24 24 600 250 300 L 60 60 1 1 P
X 25 25 600 350 300 L 60 60 1 1 P
X 26 26 600 450 300 L 60 60 1 1 P
X 27 27 600 550 300 L 60 60 1 1 P
X 28 28 600 650 300 L 60 60 1 1 P
X 14 14 -600 -650 300 R 60 60 1 1 P
X 13 13 -600 -550 300 R 60 60 1 1 P
X 12 12 -600 -450 300 R 60 60 1 1 P
X 11 11 -600 -350 300 R 60 60 1 1 P
X 10 10 -600 -250 300 R 60 60 1 1 P
X 9 9 -600 -150 300 R 60 60 1 1 P
X 8 8 -600 -50 300 R 60 60 1 1 P
X 7 7 -600 50 300 R 60 60 1 1 P
X 6 6 -600 150 300 R 60 60 1 1 P
X 5 5 -600 250 300 R 60 60 1 1 P
X 4 4 -600 350 300 R 60 60 1 1 P
X 3 3 -600 450 300 R 60 60 1 1 P
X 2 2 -600 550 300 R 60 60 1 1 P
X 1 1 -600 650 300 R 60 60 1 1 P
ENDDRAW
ENDDEF
#
# SUPP40
#
DEF SUPP40 P 0 40 Y Y 1 F N
F0 "P" 0 1100 70 H V C C
F1 "SUPP40" 0 -1100 70 H V C C
DRAW
S -300 -1050 300 1050 0 1 0 N
X 21 21 600 -950 300 L 60 60 1 1 P
X 22 22 600 -850 300 L 60 60 1 1 P
X 23 23 600 -750 300 L 60 60 1 1 P
X 24 24 600 -650 300 L 60 60 1 1 P
X 25 25 600 -550 300 L 60 60 1 1 P
X 26 26 600 -450 300 L 60 60 1 1 P
X 27 27 600 -350 300 L 60 60 1 1 P
X 28 28 600 -250 300 L 60 60 1 1 P
X 29 29 600 -150 300 L 60 60 1 1 P
X 30 30 600 -50 300 L 60 60 1 1 P
X 31 31 600 50 300 L 60 60 1 1 P
X 32 32 600 150 300 L 60 60 1 1 P
X 33 33 600 250 300 L 60 60 1 1 P
X 34 34 600 350 300 L 60 60 1 1 P
X 35 35 600 450 300 L 60 60 1 1 P
X 36 36 600 550 300 L 60 60 1 1 P
X 37 37 600 650 300 L 60 60 1 1 P
X 38 38 600 750 300 L 60 60 1 1 P
X 39 39 600 850 300 L 60 60 1 1 P
X 40 40 600 950 300 L 60 60 1 1 P
X 20 20 -600 -950 300 R 60 60 1 1 P
X 19 19 -600 -850 300 R 60 60 1 1 P
X 18 18 -600 -750 300 R 60 60 1 1 P
X 17 17 -600 -650 300 R 60 60 1 1 P
X 16 16 -600 -550 300 R 60 60 1 1 P
X 15 15 -600 -450 300 R 60 60 1 1 P
X 14 14 -600 -350 300 R 60 60 1 1 P
X 13 13 -600 -250 300 R 60 60 1 1 P
X 12 12 -600 -150 300 R 60 60 1 1 P
X 11 11 -600 -50 300 R 60 60 1 1 P
X 10 10 -600 50 300 R 60 60 1 1 P
X 9 9 -600 150 300 R 60 60 1 1 P
X 8 8 -600 250 300 R 60 60 1 1 P
X 7 7 -600 350 300 R 60 60 1 1 P
X 6 6 -600 450 300 R 60 60 1 1 P
X 5 5 -600 550 300 R 60 60 1 1 P
X 4 4 -600 650 300 R 60 60 1 1 P
X 3 3 -600 750 300 R 60 60 1 1 P
X 2 2 -600 850 300 R 60 60 1 1 P
X 1 1 -600 950 300 R 60 60 1 1 P
ENDDRAW
ENDDEF
#
# VCC
#
DEF VCC #PWR 0 0 Y Y 1 F P
F0 "#PWR" 0 100 30 H I C C
F1 "VCC" 0 100 30 H V C C
DRAW
P 3 0 1 4  0 0  0 30  0 30 N
C 0 50 20 0 1 4 N
X VCC 1 0 0 0 U 20 20 0 0 W N
ENDDRAW
ENDDEF
#
# VPP
#
DEF VPP #PWR 0 0 Y Y 1 F N
F0 "#PWR" 0 200 40 H I C C
F1 "VPP" 0 150 40 H V C C
DRAW
P 2 0 1 0  0 60  0 0 N
C 0 80 20 0 1 0 N
X VPP 1 0 0 0 U 40 40 0 0 W N
ENDDRAW
ENDDEF
#
#EndLibrary
