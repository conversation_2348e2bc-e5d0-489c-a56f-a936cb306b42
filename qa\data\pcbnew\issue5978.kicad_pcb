(kicad_pcb (version 20201002) (generator pcbnew)

  (general
    (thickness 0.6)
  )

  (paper "A4" portrait)
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B<PERSON>SilkS" user "B.Silkscreen")
    (37 "F.<PERSON>S" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (grid_origin 118.35 87.8)
    (pcbplotparams
      (layerselection 0x0020000_7ffffffe)
      (disableapertmacros false)
      (usegerberextensions true)
      (usegerberattributes false)
      (usegerberadvancedattributes false)
      (creategerberjobfile false)
      (svguseinch false)
      (svgprecision 6)
      (excludeedgelayer false)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue false)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "output/")
    )
  )


  (net 0 "")

  (module "Library:QFN-20_EPthermal_4x4_Pitch0.5mm" (layer "F.Cu") (tedit 5A577FBB) (tstamp 45b7cde5-bc80-41b4-aa8e-41957f86bdb8)
    (at 111.85 167.8)
    (solder_mask_margin 0.05)
    (attr smd)
    (fp_text reference "U1" (at 3.1212 2.10262) (layer "F.SilkS")
      (effects (font (size 0.6 0.6) (thickness 0.125)))
      (tstamp 8b4262ff-30a3-4738-9376-1c809710ad89)
    )
    (fp_text value "DRV8662" (at -0.95 1.55) (layer "F.Fab")
      (effects (font (size 0.2 0.2) (thickness 0.04)))
      (tstamp 5f9a7236-4a12-4d4e-8a5e-887c58ad8dec)
    )
    (fp_line (start 1.5 2.15) (end 2.15 2.15) (layer "F.SilkS") (width 0.15) (tstamp 327d2c64-87cf-402c-afdd-ed5a14beeb7e))
    (fp_line (start -1.5 2.15) (end -2.15 2.15) (layer "F.SilkS") (width 0.15) (tstamp 6a1ed244-ec32-45a6-b784-c506ab3c221b))
    (fp_line (start -1.5 -2.15) (end -2.45 -2.15) (layer "F.SilkS") (width 0.15) (tstamp 75225048-9669-4639-ac49-c0a544a1eafa))
    (fp_line (start 1.5 -2.15) (end 2.15 -2.15) (layer "F.SilkS") (width 0.15) (tstamp 7944d492-4921-47c1-a264-5b48b2ae90a5))
    (fp_line (start 2.15 2.15) (end 2.15 1.5) (layer "F.SilkS") (width 0.15) (tstamp 7c24d088-1c24-4609-910e-7f97e05d7552))
    (fp_line (start 2.15 -2.15) (end 2.15 -1.5) (layer "F.SilkS") (width 0.15) (tstamp c3ec1833-b076-414f-805b-967aa57886cd))
    (fp_line (start -2.15 2.15) (end -2.15 1.5) (layer "F.SilkS") (width 0.15) (tstamp d2f67dea-2553-4e93-891f-77c862864ea6))
    (fp_line (start -2.7 -2.7) (end 2.7 -2.7) (layer "F.CrtYd") (width 0.05) (tstamp 36f09194-9ad6-4b52-b405-d3bd5ea8e9e6))
    (fp_line (start 2.7 2.7) (end -2.7 2.7) (layer "F.CrtYd") (width 0.05) (tstamp 6e9c91e8-ae74-4418-8f8a-46261d4f8e63))
    (fp_line (start -2.7 2.7) (end -2.7 -2.7) (layer "F.CrtYd") (width 0.05) (tstamp deb91194-cc4f-4bb7-bf04-6785f76afef6))
    (fp_line (start 2.7 -2.7) (end 2.7 2.7) (layer "F.CrtYd") (width 0.05) (tstamp e00ef1b1-d5b2-4815-8652-d067f42c7c52))
    (fp_line (start 2 -2) (end -1 -2) (layer "F.Fab") (width 0.15) (tstamp 3a9112de-af1c-4faf-b4ab-9a961b86ff75))
    (fp_line (start -2 -1) (end -2 2) (layer "F.Fab") (width 0.15) (tstamp 4e9f2468-ca53-4094-a969-dad35490bef5))
    (fp_line (start -2 2) (end 2 2) (layer "F.Fab") (width 0.15) (tstamp 7d98b9b4-7fa5-4ec2-b289-94599c56eb28))
    (fp_line (start 2 2) (end 2 -2) (layer "F.Fab") (width 0.15) (tstamp a8e95b8b-1ba3-4b98-a49a-a1379bbfc6a4))
    (fp_line (start -1 -2) (end -2 -1) (layer "F.Fab") (width 0.15) (tstamp bb4d5b22-c955-448b-ba2c-e21d20421eb0))
    (pad "" smd rect (at 0.7 -0.45 180) (size 1.15 0.5) (layers "F.Paste")
      (solder_paste_margin -0.001) (solder_paste_margin_ratio -0.00001) (tstamp 53be17fd-90aa-45c2-8960-2aac1f09b2b7))
    (pad "" smd rect (at 0.45 0 270) (size 2.5 0.5) (layers "F.Paste")
      (solder_paste_margin -0.001) (solder_paste_margin_ratio -0.00001) (tstamp 7b955d4c-951f-47ab-9cb9-d3535edabc4d))
    (pad "" smd rect (at -0.7 -0.45 180) (size 1.15 0.5) (layers "F.Paste")
      (solder_paste_margin -0.001) (solder_paste_margin_ratio -0.00001) (tstamp 7f5d1a78-4bf0-4a63-a6a7-e9f734b54bca))
    (pad "" smd rect (at -0.7 0.45 180) (size 1.15 0.5) (layers "F.Paste")
      (solder_paste_margin -0.001) (solder_paste_margin_ratio -0.00001) (tstamp 8b4516fb-bc3a-404c-a00d-38569b343da5))
    (pad "" smd rect (at 0.7 0.45 180) (size 1.15 0.5) (layers "F.Paste")
      (solder_paste_margin -0.001) (solder_paste_margin_ratio -0.00001) (tstamp c8fba975-8e7c-403e-ae9c-49154d2b6189))
    (pad "" smd rect (at -0.45 0 270) (size 2.5 0.5) (layers "F.Paste")
      (solder_paste_margin -0.001) (solder_paste_margin_ratio -0.00001) (tstamp ef12e846-e57f-4e28-9f2e-3f00e11c197e))
    (pad "1" smd oval (at -2 -1 90) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 768a7468-1753-4223-902f-76af41e9fac5))
    (pad "2" smd oval (at -2 -0.5 90) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 6b1c48fe-8c58-4c07-84bd-19466c67d61e))
    (pad "3" smd oval (at -2 0 90) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp b4e991e0-48e3-4f1e-8958-eb74f1605ff3))
    (pad "4" smd oval (at -2 0.5 90) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp c469c387-729d-49e0-ba07-d3274e6750f2))
    (pad "5" smd oval (at -2 1 90) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 91136658-66ff-4f6e-a83a-eb428d0a82ec))
    (pad "6" smd oval (at -1 2) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp e6e435c1-f3b5-432d-baed-6a3b0d037e6f))
    (pad "7" smd oval (at -0.5 2) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 648b167b-9735-4366-8574-8291918326c0))
    (pad "8" smd oval (at 0 2) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp d4fc124e-fd92-44fe-bfff-dbde2578cede))
    (pad "9" smd oval (at 0.5 2) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 5a9cc9c0-cb48-4734-8d45-42b2d71efaa8))
    (pad "10" smd oval (at 1 2) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 5f46267a-8bbd-46df-b186-11bf6c0aeae2))
    (pad "11" smd oval (at 2 1 90) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 6e62e90e-49b3-4580-80df-b98eb8e6bdd3))
    (pad "12" smd oval (at 2 0.5 90) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 688d58dc-5cb4-4847-94fb-89376c16c1b1))
    (pad "13" smd oval (at 2 0 90) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 4e747f0e-c64a-47de-bbfc-9a80d6053f48))
    (pad "14" smd oval (at 2 -0.5 90) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp a86fee9b-e895-4625-af7b-1bbbcaa60a1b))
    (pad "15" smd oval (at 2 -1 90) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 3a07e109-54f7-47e0-b916-a4bf49106377))
    (pad "16" smd oval (at 1 -2) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp b79e63c3-00af-402c-9f78-8748b728dabb))
    (pad "17" smd oval (at 0.5 -2) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp c49d8a2a-516e-4c7c-8a70-3d52e15a470e))
    (pad "18" smd oval (at 0 -2) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 73f5fd23-6004-41c3-93d4-82d07b0b753f))
    (pad "19" smd oval (at -0.5 -2) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp 8c8282eb-b554-4fc9-a715-20220d73947d))
    (pad "20" smd oval (at -1 -2) (size 0.28 0.9) (layers "F.Cu" "F.Paste" "F.Mask") (tstamp b05e47c7-918e-499c-a5da-c457afef2e5c))
    (pad "21" smd rect (at 0 0) (size 2.69 2.69) (layers "F.Cu" "F.Mask")
      (solder_paste_margin -0.1) (solder_paste_margin_ratio -0.5) (tstamp 0ee5314f-6106-43e6-98d8-f37806cc06dc))
    (pad "21" thru_hole circle (at 0 0) (size 0.45 0.45) (drill 0.3) (layers *.Cu)
      (solder_paste_margin_ratio -0.5) (tstamp 2dc81eab-8a72-457b-8a91-39614ced35b3))
    (pad "21" thru_hole circle (at 0.9 -0.9) (size 0.45 0.45) (drill 0.3) (layers *.Cu)
      (solder_paste_margin_ratio -0.5) (tstamp 52012ffa-7aa6-4d85-84f0-9f2f9f9df1c4))
    (pad "21" thru_hole circle (at 0 0.9) (size 0.45 0.45) (drill 0.3) (layers *.Cu)
      (solder_paste_margin_ratio -0.5) (tstamp 5225ad7d-bb06-4401-bdb8-110ab2b0aade))
    (pad "21" thru_hole circle (at 0.9 0.9) (size 0.45 0.45) (drill 0.3) (layers *.Cu)
      (solder_paste_margin_ratio -0.5) (tstamp 6ea7d2d3-6d72-47b6-87b1-f7adc48bf14a))
    (pad "21" thru_hole circle (at -0.9 -0.9) (size 0.45 0.45) (drill 0.3) (layers *.Cu)
      (solder_paste_margin_ratio -0.5) (tstamp 70d30e2f-895f-491c-9c54-437a2ccac849))
    (pad "21" thru_hole circle (at 0 -0.9) (size 0.45 0.45) (drill 0.3) (layers *.Cu)
      (solder_paste_margin_ratio -0.5) (tstamp 77d71e37-7032-46a2-8537-6d07e9ba2472))
    (pad "21" smd rect (at 0 0) (size 2.69 2.69) (layers "B.Cu")
      (solder_paste_margin -0.1) (solder_paste_margin_ratio -0.5) (tstamp 8450d738-e08f-4bb2-8086-d58a63ba384c))
    (pad "21" thru_hole circle (at -0.9 0) (size 0.45 0.45) (drill 0.3) (layers *.Cu)
      (solder_paste_margin_ratio -0.5) (tstamp 8ff0f344-6a82-4ed0-96d9-64833fc41a86))
    (pad "21" thru_hole circle (at 0.9 0) (size 0.45 0.45) (drill 0.3) (layers *.Cu)
      (solder_paste_margin_ratio -0.5) (tstamp bfcd3d32-8e9a-4760-ae2b-b32e0778299a))
    (pad "21" thru_hole circle (at -0.9 0.9) (size 0.45 0.45) (drill 0.3) (layers *.Cu)
      (solder_paste_margin_ratio -0.5) (tstamp dba7fae7-a726-4eff-bcfe-fa5080a06b44))
  )

)
