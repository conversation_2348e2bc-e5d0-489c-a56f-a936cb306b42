(kicad_pcb
	(version 20231231)
	(generator "pcbnew")
	(generator_version "7.99")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(31 "B.Cu" signal)
		(32 "B.Adhes" user "B.Adhesive")
		(33 "F<PERSON>hes" user "F.Adhesive")
		(34 "B.Paste" user)
		(35 "F.Paste" user)
		(36 "B.SilkS" user "B.Silkscreen")
		(37 "F.<PERSON>" user "F.Silkscreen")
		(38 "B.Mask" user)
		(39 "F.Mask" user)
		(40 "Dwgs.User" user "User.Drawings")
		(41 "Cmts.User" user "User.Comments")
		(42 "Eco1.User" user "User.Eco1")
		(43 "Eco2.User" user "User.Eco2")
		(44 "Edge.Cuts" user)
		(45 "Margin" user)
		(46 "B.CrtYd" user "B.Courtyard")
		(47 "F.CrtYd" user "F.Courtyard")
		(48 "B.Fab" user)
		(49 "F.Fab" user)
		(50 "User.1" user)
		(51 "User.2" user)
		(52 "User.3" user)
		(53 "User.4" user)
		(54 "User.5" user)
		(55 "User.6" user)
		(56 "User.7" user)
		(57 "User.8" user)
		(58 "User.9" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(pcbplotparams
			(layerselection 0x00010fc_ffffffff)
			(plot_on_all_layers_selection 0x0000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(viasonmask no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotreference yes)
			(plotvalue yes)
			(plotfptext yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(gr_rect
		(start 137.54 30.055)
		(end 143.61 50.985)
		(locked yes)
		(stroke
			(width 0.2)
			(type default)
		)
		(fill none)
		(layer "F.Cu")
		(uuid "31c575c4-a510-466f-a17b-69828f152bcb")
	)
	(gr_rect
		(start 123.79 32.895)
		(end 134.57 47.885)
		(locked yes)
		(stroke
			(width 0.2)
			(type default)
		)
		(fill none)
		(layer "F.Cu")
		(uuid "7b225496-2d63-45ad-a1bb-01d0f7578455")
	)
	(gr_text "Simple group of two items"
		(at 119.52 25.15 0)
		(layer "Cmts.User")
		(uuid "2dd7f66b-e965-4c09-bb3f-acef10a77fbe")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3)
				(bold yes)
			)
			(justify left bottom)
		)
	)
	(group "GroupName"
		(uuid "a78cc65c-451e-451e-9147-4460cc669685")
		(locked yes)
		(members "31c575c4-a510-466f-a17b-69828f152bcb" "7b225496-2d63-45ad-a1bb-01d0f7578455")
	)
)