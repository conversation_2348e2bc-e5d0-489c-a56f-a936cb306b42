(kicad_pcb
	(version 20240108)
	(generator "pcbnew")
	(generator_version "8.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(1 "In1.Cu" signal)
		(2 "In2.Cu" signal)
		(31 "B.Cu" signal)
		(32 "B<PERSON>" user "B.Adhesive")
		(33 "<PERSON><PERSON>" user "F.Adhesive")
		(34 "B.Paste" user)
		(35 "F.Paste" user)
		(36 "B.SilkS" user "B.Silkscreen")
		(37 "F.SilkS" user "F.Silkscreen")
		(38 "B.Mask" user)
		(39 "F.Mask" user)
		(40 "Dwgs.User" user "User.Drawings")
		(41 "Cmts.User" user "User.Comments")
		(42 "Eco1.User" user "User.Eco1")
		(43 "Eco2.User" user "User.Eco2")
		(44 "Edge.Cuts" user)
		(45 "Margin" user)
		(46 "B.CrtYd" user "B.Courtyard")
		(47 "F.CrtYd" user "F.Courtyard")
		(48 "B.Fab" user)
		(49 "F.Fab" user)
		(50 "User.1" user)
		(51 "User.2" user)
		(52 "User.3" user)
		(53 "User.4" user)
		(54 "User.5" user)
		(55 "User.6" user)
		(56 "User.7" user)
		(57 "User.8" user)
		(58 "User.9" user)
	)
	(setup
		(stackup
			(layer "F.SilkS"
				(type "Top Silk Screen")
			)
			(layer "F.Paste"
				(type "Top Solder Paste")
			)
			(layer "F.Mask"
				(type "Top Solder Mask")
				(thickness 0.01)
			)
			(layer "F.Cu"
				(type "copper")
				(thickness 0.035)
			)
			(layer "dielectric 1"
				(type "prepreg")
				(thickness 0.1)
				(material "FR4")
				(epsilon_r 4.5)
				(loss_tangent 0.02)
			)
			(layer "In1.Cu"
				(type "copper")
				(thickness 0.035)
			)
			(layer "dielectric 2"
				(type "core")
				(thickness 1.24)
				(material "FR4")
				(epsilon_r 4.5)
				(loss_tangent 0.02)
			)
			(layer "In2.Cu"
				(type "copper")
				(thickness 0.035)
			)
			(layer "dielectric 3"
				(type "prepreg")
				(thickness 0.1)
				(material "FR4")
				(epsilon_r 4.5)
				(loss_tangent 0.02)
			)
			(layer "B.Cu"
				(type "copper")
				(thickness 0.035)
			)
			(layer "B.Mask"
				(type "Bottom Solder Mask")
				(thickness 0.01)
			)
			(layer "B.Paste"
				(type "Bottom Solder Paste")
			)
			(layer "B.SilkS"
				(type "Bottom Silk Screen")
			)
			(copper_finish "None")
			(dielectric_constraints no)
		)
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(pcbplotparams
			(layerselection 0x00010fc_ffffffff)
			(plot_on_all_layers_selection 0x0000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(viasonmask no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotreference yes)
			(plotvalue yes)
			(plotfptext yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 0)
			(scaleselection 1)
			(outputdirectory "Gerbers_Example")
		)
	)
	(net 0 "")
	(net 1 "Net-(JP1-C)")
	(net 2 "Net-(JP1-B)")
	(net 3 "GND")
	(footprint "Resistor_SMD:R_0402_1005Metric"
		(layer "F.Cu")
		(uuid "576be02a-0f1a-46c0-998b-48af4d29d29b")
		(at 23.38 22.22 180)
		(descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R1"
			(at 0 -1.17 0)
			(layer "F.SilkS")
			(uuid "6b8036dc-13bc-4c13-9091-0a90e0997cdb")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "R"
			(at 0 1.17 0)
			(layer "F.Fab")
			(uuid "ee544c61-af0b-4f4c-b4e7-778f83302b9b")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0402_1005Metric"
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "389f4a3b-52d9-43dc-9ce7-8e096cd07fd7")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "27e55486-d8dd-49ea-9d06-96bfa001bf79")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" "Resistor"
			(at 0 0 180)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "7981daa4-61b6-41e2-a9c8-f3ec304889b1")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_*")
		(path "/f8307808-625b-4f30-9502-e2faf630c5b7")
		(sheetname "Root")
		(sheetfile "test.kicad_sch")
		(attr smd)
		(fp_line
			(start -0.153641 0.38)
			(end 0.153641 0.38)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "720543e2-bd9f-45cb-9fa9-e8e6f4be35c0")
		)
		(fp_line
			(start -0.153641 -0.38)
			(end 0.153641 -0.38)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "09d557d9-1357-4ec5-b079-1f789a7fb98b")
		)
		(fp_line
			(start 0.93 0.47)
			(end -0.93 0.47)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "255fd722-72a4-4763-93c1-da0d6ebcbb51")
		)
		(fp_line
			(start 0.93 -0.47)
			(end 0.93 0.47)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2115ff22-31cc-443e-9be8-cdaf715aa1cc")
		)
		(fp_line
			(start -0.93 0.47)
			(end -0.93 -0.47)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "cafaa2a2-c446-4329-933e-36279cc8dc28")
		)
		(fp_line
			(start -0.93 -0.47)
			(end 0.93 -0.47)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "38b2fb09-6134-4f6f-827f-32a082ce1c98")
		)
		(fp_line
			(start 0.525 0.27)
			(end -0.525 0.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "cf0f1b53-9d81-4837-a978-c07f2bed9d2f")
		)
		(fp_line
			(start 0.525 -0.27)
			(end 0.525 0.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4e8cfbc9-c744-4641-9a71-1218bff927f0")
		)
		(fp_line
			(start -0.525 0.27)
			(end -0.525 -0.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7cdf46ca-0c25-4469-a7f2-8c2deac09b74")
		)
		(fp_line
			(start -0.525 -0.27)
			(end 0.525 -0.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ca9d53ac-ee83-4546-b0ac-bc20fc22970e")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "69674a79-53b4-491c-9bca-b88b16713375")
			(effects
				(font
					(size 0.26 0.26)
					(thickness 0.04)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.51 0 180)
			(size 0.54 0.64)
			(layers "F.Cu" "F.Paste" "F.Mask")
			(roundrect_rratio 0.25)
			(net 2 "Net-(JP1-B)")
			(pintype "passive")
			(uuid "a93afa47-4389-4710-9da5-cf103eade4e4")
		)
		(pad "2" smd roundrect
			(at 0.51 0 180)
			(size 0.54 0.64)
			(layers "F.Cu" "F.Paste" "F.Mask")
			(roundrect_rratio 0.25)
			(net 1 "Net-(JP1-C)")
			(pintype "passive")
			(uuid "454c47db-7d4c-4665-abdf-e18876244a60")
		)
		(model "${KICAD8_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Jumper:SolderJumper-3_P1.3mm_Bridged12_RoundedPad1.0x1.5mm"
		(layer "F.Cu")
		(uuid "7aa18b00-180c-4c3e-b14e-a52be2c7a03a")
		(at 22.79 19.98)
		(descr "SMD Solder 3-pad Jumper, 1x1.5mm rounded Pads, 0.3mm gap, pads 1-2 bridged with 1 copper strip")
		(tags "net tie solder jumper bridged")
		(property "Reference" "JP1"
			(at 0 -1.8 0)
			(layer "F.SilkS")
			(uuid "09ada9ef-087c-4231-b5e0-5436c837bc66")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "Jumper_3_Bridged12"
			(at 0 1.9 0)
			(layer "F.Fab")
			(uuid "c12a3f68-5c7a-41f7-bd1d-a6dad481d7b4")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Footprint" "Jumper:SolderJumper-3_P1.3mm_Bridged12_RoundedPad1.0x1.5mm"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "4d07ea9a-bfbf-4ea3-86cc-21a0d5c63616")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "d3edb7d4-4771-4a48-b6bb-f5543d92de6c")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" "Jumper, 3-pole, pins 1+2 closed/bridged"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "969dd546-9655-4b72-a975-05b2e21723be")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "Jumper* TestPoint*3Pads* TestPoint*Bridge*")
		(path "/0633b9a1-8769-4ac6-aece-41e16f8d375c")
		(sheetname "Root")
		(sheetfile "test.kicad_sch")
		(zone_connect 1)
		(attr exclude_from_pos_files exclude_from_bom allow_soldermask_bridges)
		(net_tie_pad_groups "1,2")
		(fp_poly
			(pts
				(xy -0.9 -0.3) (xy -0.4 -0.3) (xy -0.4 0.3) (xy -0.9 0.3)
			)
			(stroke
				(width 0)
				(type solid)
			)
			(fill solid)
			(layer "F.Cu")
			(uuid "cedfca7f-046b-47d2-91c7-3103a5524306")
		)
		(fp_line
			(start -2.05 0.3)
			(end -2.05 -0.3)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "682f0bca-e0c5-48db-9541-6c359aad8f6b")
		)
		(fp_line
			(start -1.5 1.5)
			(end -0.9 1.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "afb81f66-3dfb-417f-a0c5-a85f424e7cc3")
		)
		(fp_line
			(start -1.4 -1)
			(end 1.4 -1)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4f3f9760-6878-499c-9d98-831cc8e0f671")
		)
		(fp_line
			(start -1.2 1.2)
			(end -1.5 1.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0c0c0eb6-99d1-40da-a997-8d6b8b3e985f")
		)
		(fp_line
			(start -1.2 1.2)
			(end -0.9 1.5)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dcfb749f-b31b-4b83-8c98-75651b8c3c20")
		)
		(fp_line
			(start 1.4 1)
			(end -1.4 1)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d5db8b86-47fa-4cf0-81ff-7cf504a1327c")
		)
		(fp_line
			(start 2.05 -0.3)
			(end 2.05 0.3)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9a2f4d51-40d5-43e2-89f1-67ba59124a20")
		)
		(fp_arc
			(start -2.05 -0.3)
			(mid -1.844975 -0.794975)
			(end -1.35 -1)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e21a499a-9973-4db2-98a4-17e183194031")
		)
		(fp_arc
			(start -1.35 1)
			(mid -1.844975 0.794975)
			(end -2.05 0.3)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c5c59397-811a-4f88-9b00-615bb79d049a")
		)
		(fp_arc
			(start 1.35 -1)
			(mid 1.844975 -0.794975)
			(end 2.05 -0.3)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c8b4ebe5-5ab9-4007-8140-f1f0323a3c80")
		)
		(fp_arc
			(start 2.05 0.3)
			(mid 1.844975 0.794975)
			(end 1.35 1)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a420450a-3d0d-43c7-be53-7b6f5285569b")
		)
		(fp_rect
			(start -0.75 -0.75)
			(end 0.75 0.75)
			(stroke
				(width 0)
				(type default)
			)
			(fill solid)
			(layer "F.Mask")
			(uuid "85df5ee1-8714-473b-b7de-ec6b0f0768ae")
		)
		(fp_line
			(start -2.3 -1.25)
			(end -2.3 1.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b9d22d91-8d12-49fd-8876-483c6e3c7cb1")
		)
		(fp_line
			(start -2.3 -1.25)
			(end 2.3 -1.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b5f0e04f-ee74-4135-9782-4d642b60095f")
		)
		(fp_line
			(start 2.3 1.25)
			(end -2.3 1.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "347d37c5-9de1-49d3-8694-11b84c8661e0")
		)
		(fp_line
			(start 2.3 1.25)
			(end 2.3 -1.25)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "fc832f1d-ae85-47fb-907d-6a6609bbab4e")
		)
		(pad "1" smd custom
			(at -1.3 0)
			(size 1 0.5)
			(layers "F.Cu" "F.Mask")
			(net 3 "GND")
			(pinfunction "A")
			(pintype "passive")
			(zone_connect 2)
			(thermal_bridge_angle 45)
			(options
				(clearance outline)
				(anchor rect)
			)
			(primitives
				(gr_circle
					(center 0 0.25)
					(end 0.5 0.25)
					(width 0)
					(fill yes)
				)
				(gr_circle
					(center 0 -0.25)
					(end 0.5 -0.25)
					(width 0)
					(fill yes)
				)
				(gr_poly
					(pts
						(xy 0.55 0.75) (xy 0 0.75) (xy 0 -0.75) (xy 0.55 -0.75)
					)
					(width 0)
					(fill yes)
				)
			)
			(uuid "fcbde01d-3988-4e04-b354-4fa75e322cd7")
		)
		(pad "2" smd rect
			(at 0 0)
			(size 1 1.5)
			(layers "F.Cu" "F.Mask")
			(net 1 "Net-(JP1-C)")
			(pinfunction "C")
			(pintype "passive")
			(uuid "09c520f1-639a-40ed-832d-e050a794feec")
		)
		(pad "3" smd custom
			(at 1.3 0)
			(size 1 0.5)
			(layers "F.Cu" "F.Mask")
			(net 2 "Net-(JP1-B)")
			(pinfunction "B")
			(pintype "passive")
			(zone_connect 2)
			(thermal_bridge_angle 45)
			(options
				(clearance outline)
				(anchor rect)
			)
			(primitives
				(gr_circle
					(center 0 0.25)
					(end 0.5 0.25)
					(width 0)
					(fill yes)
				)
				(gr_circle
					(center 0 -0.25)
					(end 0.5 -0.25)
					(width 0)
					(fill yes)
				)
				(gr_poly
					(pts
						(xy 0 0.75) (xy -0.55 0.75) (xy -0.55 -0.75) (xy 0 -0.75)
					)
					(width 0)
					(fill yes)
				)
			)
			(uuid "b5969ac2-e653-4bff-8ecd-d85d05187762")
		)
	)
	(gr_rect
		(start 8.15 7.41)
		(end 36.67 35.93)
		(stroke
			(width 0.1)
			(type default)
		)
		(fill none)
		(layer "Edge.Cuts")
		(uuid "1e3a21a5-75df-4f87-bacd-8bcbb81b52ba")
	)
	(segment
		(start 23.39 20.959718)
		(end 22.79 20.359718)
		(width 0.127)
		(layer "F.Cu")
		(net 1)
		(uuid "1a2991c1-a214-42f5-8a37-8592b383b9db")
	)
	(segment
		(start 22.79 20.359718)
		(end 22.79 19.98)
		(width 0.127)
		(layer "F.Cu")
		(net 1)
		(uuid "2f924df7-5556-4851-b389-309598a8050c")
	)
	(segment
		(start 23.39 21.7)
		(end 23.39 20.959718)
		(width 0.127)
		(layer "F.Cu")
		(net 1)
		(uuid "2fb26524-0e45-4d4d-9c3e-19299ba7ff23")
	)
	(segment
		(start 22.87 22.22)
		(end 23.39 21.7)
		(width 0.127)
		(layer "F.Cu")
		(net 1)
		(uuid "71a69050-b92d-41cc-a917-f0dc9b2bb25c")
	)
	(segment
		(start 23.89 22.22)
		(end 23.89 20.18)
		(width 0.25)
		(layer "F.Cu")
		(net 2)
		(uuid "02dc1621-604f-4e72-91b9-7c163a4567d7")
	)
	(segment
		(start 23.89 20.18)
		(end 24.09 19.98)
		(width 0.25)
		(layer "F.Cu")
		(net 2)
		(uuid "2dd79d0f-1eda-40bb-947f-068ff63c6510")
	)
	(zone
		(net 3)
		(net_name "GND")
		(layer "F.Cu")
		(uuid "de2e8c00-a3eb-4c4d-b94a-2ed3288e2cd1")
		(hatch edge 0.5)
		(connect_pads yes
			(clearance 0.127)
		)
		(min_thickness 0.25)
		(filled_areas_thickness no)
		(fill yes
			(thermal_gap 0.5)
			(thermal_bridge_width 0.5)
		)
		(polygon
			(pts
				(xy 5.98 5.1) (xy 6.13 38.93) (xy 37.67 38.62) (xy 38.05 5.63)
			)
		)
		(filled_polygon
			(layer "F.Cu")
			(pts
				(xy 36.358539 7.684185) (xy 36.404294 7.736989) (xy 36.4155 7.7885) (xy 36.4155 35.5515) (xy 36.395815 35.618539)
				(xy 36.343011 35.664294) (xy 36.2915 35.6755) (xy 8.5285 35.6755) (xy 8.461461 35.655815) (xy 8.415706 35.603011)
				(xy 8.4045 35.5515) (xy 8.4045 19.210247) (xy 22.0895 19.210247) (xy 22.0895 20.749752) (xy 22.101131 20.808229)
				(xy 22.101132 20.80823) (xy 22.145447 20.874552) (xy 22.211769 20.918867) (xy 22.21177 20.918868)
				(xy 22.270247 20.930499) (xy 22.27025 20.9305) (xy 22.270252 20.9305) (xy 22.936068 20.9305) (xy 23.003107 20.950185)
				(xy 23.023749 20.966819) (xy 23.089681 21.032751) (xy 23.123166 21.094074) (xy 23.126 21.120432)
				(xy 23.126 21.539286) (xy 23.106315 21.606325) (xy 23.089681 21.626967) (xy 23.053467 21.663181)
				(xy 22.992144 21.696666) (xy 22.965786 21.6995) (xy 22.695683 21.6995) (xy 22.646828 21.705931)
				(xy 22.539595 21.755935) (xy 22.455935 21.839595) (xy 22.405931 21.946828) (xy 22.3995 21.995683)
				(xy 22.3995 22.444316) (xy 22.405931 22.493171) (xy 22.450684 22.589143) (xy 22.455935 22.600404)
				(xy 22.539596 22.684065) (xy 22.646827 22.734068) (xy 22.695683 22.7405) (xy 22.695684 22.7405)
				(xy 23.044317 22.7405) (xy 23.060601 22.738356) (xy 23.093173 22.734068) (xy 23.200404 22.684065)
				(xy 23.284065 22.600404) (xy 23.284065 22.600403) (xy 23.291736 22.592733) (xy 23.294564 22.595561)
				(xy 23.332977 22.564843) (xy 23.402474 22.557635) (xy 23.464835 22.589143) (xy 23.468093 22.592903)
				(xy 23.468264 22.592733) (xy 23.475935 22.600404) (xy 23.559596 22.684065) (xy 23.666827 22.734068)
				(xy 23.715683 22.7405) (xy 23.715684 22.7405) (xy 24.064317 22.7405) (xy 24.080601 22.738356) (xy 24.113173 22.734068)
				(xy 24.220404 22.684065) (xy 24.304065 22.600404) (xy 24.354068 22.493173) (xy 24.3605 22.444316)
				(xy 24.3605 21.995684) (xy 24.354068 21.946827) (xy 24.304065 21.839596) (xy 24.251818 21.787349)
				(xy 24.218334 21.726025) (xy 24.2155 21.699668) (xy 24.2155 21.021259) (xy 24.235185 20.95422) (xy 24.287989 20.908465)
				(xy 24.304563 20.902282) (xy 24.35774 20.886669) (xy 24.410945 20.862371) (xy 24.531899 20.784639)
				(xy 24.576105 20.746334) (xy 24.645202 20.66659) (xy 24.67025 20.637684) (xy 24.670252 20.637681)
				(xy 24.670259 20.637673) (xy 24.701881 20.588468) (xy 24.701884 20.58846) (xy 24.701887 20.588456)
				(xy 24.761606 20.457689) (xy 24.761609 20.457683) (xy 24.778088 20.401561) (xy 24.79855 20.259246)
				(xy 24.79855 20.200755) (xy 24.796761 20.188318) (xy 24.7955 20.170673) (xy 24.7955 19.789327) (xy 24.796762 19.77168)
				(xy 24.79855 19.759245) (xy 24.79855 19.700754) (xy 24.778088 19.558439) (xy 24.761609 19.502317)
				(xy 24.761606 19.50231) (xy 24.701887 19.371543) (xy 24.701878 19.371527) (xy 24.693275 19.358141)
				(xy 24.670259 19.322327) (xy 24.670255 19.322322) (xy 24.67025 19.322315) (xy 24.604362 19.246276)
				(xy 24.576105 19.213666) (xy 24.531899 19.175361) (xy 24.531898 19.17536) (xy 24.41095 19.097631)
				(xy 24.410946 19.097629) (xy 24.410945 19.097629) (xy 24.35774 19.073331) (xy 24.271211 19.047924)
				(xy 24.219789 19.032825) (xy 24.219777 19.032822) (xy 24.16189 19.0245) (xy 24.161889 19.0245) (xy 23.54 19.0245)
				(xy 23.539995 19.0245) (xy 23.494271 19.029652) (xy 23.473166 19.039816) (xy 23.404224 19.051168)
				(xy 23.37191 19.042657) (xy 23.368227 19.041131) (xy 23.309752 19.0295) (xy 23.309748 19.0295) (xy 22.270252 19.0295)
				(xy 22.270247 19.0295) (xy 22.21177 19.041131) (xy 22.211769 19.041132) (xy 22.145447 19.085447)
				(xy 22.101132 19.151769) (xy 22.101131 19.15177) (xy 22.0895 19.210247) (xy 8.4045 19.210247) (xy 8.4045 7.7885)
				(xy 8.424185 7.721461) (xy 8.476989 7.675706) (xy 8.5285 7.6645) (xy 36.2915 7.6645)
			)
		)
	)
)
