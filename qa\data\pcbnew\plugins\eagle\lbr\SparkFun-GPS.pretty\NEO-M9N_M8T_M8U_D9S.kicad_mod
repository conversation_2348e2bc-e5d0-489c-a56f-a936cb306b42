(footprint "NEO-M9N_M8T_M8U_D9S" (version 20231007) (generator pcbnew)
  (layer "F.Cu")
  (descr "u-blox NEO-M9N/M8T/M8U/D9S Module  Mechanical Specifications\n\n• Overall: 12.2mm x 16.0mm\n• Pad Pitch: 1.1mm\n• Pad Width: .8mm\n• Pad Length: .9mm\n• Number of Pins: 24")
  (property "Reference" "REF**" (at -6.1 -8.5 0 unlocked) (layer "F.SilkS") (tstamp d793e9d1-634d-4f2b-a46b-df4164a04e1c)
    (effects (font (size 0.46736 0.46736) (thickness 0.04064)) (justify left bottom))
  )
  (property "Value" "NEO-M9N_M8T_M8U_D9S" (at -6.1 9 0 unlocked) (layer "F.Fab") (tstamp 1e63a92f-c780-4611-a65d-63e8b4c5fead)
    (effects (font (size 0.46736 0.46736) (thickness 0.04064)) (justify left bottom))
  )
  (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp b4829b67-e0f6-4310-be25-13e7f698df49)
    (effects (font (size 1.27 1.27)))
  )
  (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 69c25a73-c971-4588-a772-e6a30e8ca131)
    (effects (font (size 1.27 1.27)))
  )
  (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 17de464d-65fa-4ba2-97a4-c05c76d2d290)
    (effects (font (size 1.27 1.27)))
  )
  (fp_poly
    (pts
      (xy -6.1 -7.4)
      (xy -7.3 -7.4)
      (xy -7.3 -6.6)
      (xy -6.1 -6.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp bce7f8c4-cf14-493c-9799-2ca976dfa71a))
  (fp_poly
    (pts
      (xy -6.1 -6.3)
      (xy -7.3 -6.3)
      (xy -7.3 -5.5)
      (xy -6.1 -5.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp e4665653-469b-4f71-9b3e-2243bbff4236))
  (fp_poly
    (pts
      (xy -6.1 -5.2)
      (xy -7.3 -5.2)
      (xy -7.3 -4.4)
      (xy -6.1 -4.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 76b6a542-a8af-4dec-9478-a0e0dd425dcd))
  (fp_poly
    (pts
      (xy -6.1 -4.1)
      (xy -7.3 -4.1)
      (xy -7.3 -3.3)
      (xy -6.1 -3.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp afd87c73-ba04-4c2d-8ff6-3ba6f8078dfe))
  (fp_poly
    (pts
      (xy -6.1 -3)
      (xy -7.3 -3)
      (xy -7.3 -2.2)
      (xy -6.1 -2.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp f7b4c816-15c8-46a3-85c3-c565060cd4cc))
  (fp_poly
    (pts
      (xy -6.1 0)
      (xy -7.3 0)
      (xy -7.3 0.8)
      (xy -6.1 0.8)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp dcffd602-ee8f-4197-8eec-92e434df6c4c))
  (fp_poly
    (pts
      (xy -6.1 1.1)
      (xy -7.3 1.1)
      (xy -7.3 1.9)
      (xy -6.1 1.9)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 2b66a769-6ef2-4183-920a-05d4488650f1))
  (fp_poly
    (pts
      (xy -6.1 2.2)
      (xy -7.3 2.2)
      (xy -7.3 3)
      (xy -6.1 3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7649b3b9-c74c-41dd-8ee8-69c5f7fb7d69))
  (fp_poly
    (pts
      (xy -6.1 3.3)
      (xy -7.3 3.3)
      (xy -7.3 4.1)
      (xy -6.1 4.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp b33c00ea-09fd-4d25-9a13-89af387c9223))
  (fp_poly
    (pts
      (xy -6.1 4.4)
      (xy -7.3 4.4)
      (xy -7.3 5.2)
      (xy -6.1 5.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 5a2674b6-77c7-4374-a0f0-13cf2511bf3c))
  (fp_poly
    (pts
      (xy -6.1 5.5)
      (xy -7.3 5.5)
      (xy -7.3 6.3)
      (xy -6.1 6.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 4764a22f-1f3d-4c64-9a03-b4e3e24755e3))
  (fp_poly
    (pts
      (xy -6.1 6.6)
      (xy -7.3 6.6)
      (xy -7.3 7.4)
      (xy -6.1 7.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 9e6460a3-23cd-47aa-b773-3f2669b621c7))
  (fp_poly
    (pts
      (xy -5.2 -7.3)
      (xy -6.1 -7.3)
      (xy -6.1 -6.7)
      (xy -5.2 -6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 4cba49d8-de06-49ca-8df3-e736a95222a6))
  (fp_poly
    (pts
      (xy -5.2 -6.2)
      (xy -6.1 -6.2)
      (xy -6.1 -5.6)
      (xy -5.2 -5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 9be9a387-2e2e-4d20-a17d-fb4b6890d476))
  (fp_poly
    (pts
      (xy -5.2 -5.1)
      (xy -6.1 -5.1)
      (xy -6.1 -4.5)
      (xy -5.2 -4.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp c93f0241-3c36-4ae6-8e13-57ee8cd6991d))
  (fp_poly
    (pts
      (xy -5.2 -4)
      (xy -6.1 -4)
      (xy -6.1 -3.4)
      (xy -5.2 -3.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 2ec37641-02eb-4c1b-aed6-5fe7f5bd9950))
  (fp_poly
    (pts
      (xy -5.2 -2.9)
      (xy -6.1 -2.9)
      (xy -6.1 -2.3)
      (xy -5.2 -2.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp e1f0739e-69e5-4122-91f2-145f7b15a1b5))
  (fp_poly
    (pts
      (xy -5.2 0.1)
      (xy -6.1 0.1)
      (xy -6.1 0.7)
      (xy -5.2 0.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 92e62be6-7e77-45cd-aa01-2076841adabe))
  (fp_poly
    (pts
      (xy -5.2 1.2)
      (xy -6.1 1.2)
      (xy -6.1 1.8)
      (xy -5.2 1.8)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 62faaf4e-d7d8-4b59-84cb-dad49385c74a))
  (fp_poly
    (pts
      (xy -5.2 2.3)
      (xy -6.1 2.3)
      (xy -6.1 2.9)
      (xy -5.2 2.9)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 3ce9bed8-9b47-4e73-9a8f-b78f59dbf5fd))
  (fp_poly
    (pts
      (xy -5.2 3.4)
      (xy -6.1 3.4)
      (xy -6.1 4)
      (xy -5.2 4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7bb1d85f-c015-49d9-8520-56c964f97574))
  (fp_poly
    (pts
      (xy -5.2 4.5)
      (xy -6.1 4.5)
      (xy -6.1 5.1)
      (xy -5.2 5.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp aed80105-4b19-48ae-8c84-a7d8435d1710))
  (fp_poly
    (pts
      (xy -5.2 5.6)
      (xy -6.1 5.6)
      (xy -6.1 6.2)
      (xy -5.2 6.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp f294a23c-0ac6-418f-b0c5-5a73b31492ea))
  (fp_poly
    (pts
      (xy -5.2 6.7)
      (xy -6.1 6.7)
      (xy -6.1 7.3)
      (xy -5.2 7.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp d383c0c7-5044-4895-b163-c8d8f68bd951))
  (fp_poly
    (pts
      (xy 5.2 -6.7)
      (xy 6.1 -6.7)
      (xy 6.1 -7.3)
      (xy 5.2 -7.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 552f791c-e08b-46d3-987b-4af567a0da9c))
  (fp_poly
    (pts
      (xy 5.2 -5.6)
      (xy 6.1 -5.6)
      (xy 6.1 -6.2)
      (xy 5.2 -6.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp e6f9b51b-9e1f-49d5-be95-4ff981f5b849))
  (fp_poly
    (pts
      (xy 5.2 -4.5)
      (xy 6.1 -4.5)
      (xy 6.1 -5.1)
      (xy 5.2 -5.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 74144cee-81d4-4ea6-b867-93c89fb3ab68))
  (fp_poly
    (pts
      (xy 5.2 -3.4)
      (xy 6.1 -3.4)
      (xy 6.1 -4)
      (xy 5.2 -4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 8ec66a8f-8ee7-4788-b1d9-e5b2ec68dbd7))
  (fp_poly
    (pts
      (xy 5.2 -2.3)
      (xy 6.1 -2.3)
      (xy 6.1 -2.9)
      (xy 5.2 -2.9)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 4b3a3327-d4c4-4bc9-aa8c-661a7c63bb7b))
  (fp_poly
    (pts
      (xy 5.2 0.7)
      (xy 6.1 0.7)
      (xy 6.1 0.1)
      (xy 5.2 0.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 697df5c6-af84-450b-8a0e-eff6dce8d6bb))
  (fp_poly
    (pts
      (xy 5.2 1.8)
      (xy 6.1 1.8)
      (xy 6.1 1.2)
      (xy 5.2 1.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 2ee00938-cfcb-4af2-ae61-dbb974cf0d54))
  (fp_poly
    (pts
      (xy 5.2 2.9)
      (xy 6.1 2.9)
      (xy 6.1 2.3)
      (xy 5.2 2.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 34ad188e-356d-4434-a455-a1e6d4260b04))
  (fp_poly
    (pts
      (xy 5.2 4)
      (xy 6.1 4)
      (xy 6.1 3.4)
      (xy 5.2 3.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp a6b1912d-f008-4e17-b908-c3bfe0c7f555))
  (fp_poly
    (pts
      (xy 5.2 5.1)
      (xy 6.1 5.1)
      (xy 6.1 4.5)
      (xy 5.2 4.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 212a8447-93d9-493d-b6da-ba4d7c9c3ff0))
  (fp_poly
    (pts
      (xy 5.2 6.2)
      (xy 6.1 6.2)
      (xy 6.1 5.6)
      (xy 5.2 5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 6bbae98b-4d10-45d4-a51b-7c154ed4a437))
  (fp_poly
    (pts
      (xy 5.2 7.3)
      (xy 6.1 7.3)
      (xy 6.1 6.7)
      (xy 5.2 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 0eb87dfa-e12d-4a59-9621-c8de3468473a))
  (fp_poly
    (pts
      (xy 6.1 -6.6)
      (xy 7.3 -6.6)
      (xy 7.3 -7.4)
      (xy 6.1 -7.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 60f74017-8f4c-45e3-abba-25f9aa71cc9e))
  (fp_poly
    (pts
      (xy 6.1 -5.5)
      (xy 7.3 -5.5)
      (xy 7.3 -6.3)
      (xy 6.1 -6.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 49de5311-bed7-4524-9ee2-e3516d8d5def))
  (fp_poly
    (pts
      (xy 6.1 -4.4)
      (xy 7.3 -4.4)
      (xy 7.3 -5.2)
      (xy 6.1 -5.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp fa445cda-a2f0-4a1f-89f2-00ae8e29ba69))
  (fp_poly
    (pts
      (xy 6.1 -3.3)
      (xy 7.3 -3.3)
      (xy 7.3 -4.1)
      (xy 6.1 -4.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 499fe8ea-82aa-48ee-829d-f23fca252e80))
  (fp_poly
    (pts
      (xy 6.1 -2.2)
      (xy 7.3 -2.2)
      (xy 7.3 -3)
      (xy 6.1 -3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 56671b2a-a538-4099-839b-15d84ae5e917))
  (fp_poly
    (pts
      (xy 6.1 0.8)
      (xy 7.3 0.8)
      (xy 7.3 0)
      (xy 6.1 0)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 38c787f3-135e-4df3-9521-d2f0c03d0967))
  (fp_poly
    (pts
      (xy 6.1 1.9)
      (xy 7.3 1.9)
      (xy 7.3 1.1)
      (xy 6.1 1.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 4e3515ce-6f58-4026-a5c7-7bf3deef8f22))
  (fp_poly
    (pts
      (xy 6.1 3)
      (xy 7.3 3)
      (xy 7.3 2.2)
      (xy 6.1 2.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 052cc2ba-4f0b-44d8-b926-a768c56ffc23))
  (fp_poly
    (pts
      (xy 6.1 4.1)
      (xy 7.3 4.1)
      (xy 7.3 3.3)
      (xy 6.1 3.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7a141027-6d8d-4c4e-b658-63c4f2482c79))
  (fp_poly
    (pts
      (xy 6.1 5.2)
      (xy 7.3 5.2)
      (xy 7.3 4.4)
      (xy 6.1 4.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp b7b5cdad-2519-449b-a810-123fbc72c980))
  (fp_poly
    (pts
      (xy 6.1 6.3)
      (xy 7.3 6.3)
      (xy 7.3 5.5)
      (xy 6.1 5.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 44881e2c-a9a9-4207-94cf-29ae1748b92f))
  (fp_poly
    (pts
      (xy 6.1 7.4)
      (xy 7.3 7.4)
      (xy 7.3 6.6)
      (xy 6.1 6.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp e615ca8a-a33f-49a0-b94c-8c3340e13d6f))
  (fp_line (start -6.35 -8.25) (end -4.88 -8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 0496487b-a3a1-4cde-8b6f-f0798e778427))
  (fp_line (start -6.35 -6.4) (end -6.35 -8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 5b34fc4b-94ea-4838-975b-18bb980526e0))
  (fp_line (start -6.35 8.25) (end -6.35 6.4)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp f1f1c8a4-f6e4-4887-94c1-afba35a71e25))
  (fp_line (start -4.88 8.25) (end -6.35 8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 0a959ed1-bf77-4de9-9411-fc8f67e296c5))
  (fp_line (start 4.88 -8.25) (end 6.35 -8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 65bc3c08-5c03-42f0-916d-623b77ffb127))
  (fp_line (start 6.35 -8.25) (end 6.35 -6.4)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 63c7f681-bbf9-4c07-b26e-3c286d1b0f45))
  (fp_line (start 6.35 6.4) (end 6.35 8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp effbcf9f-e067-48d8-967d-e113f78d5aa4))
  (fp_line (start 6.35 8.25) (end 4.88 8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp c8fdf07c-54dc-4aa0-bbd5-720f3e322867))
  (fp_circle (center 6.858 8.763) (end 7.0612 8.763)
    (stroke (width 0.4064) (type solid)) (fill solid) (layer "F.SilkS") (tstamp 0a3b6366-38ba-45dc-ad90-f8a63d0a76f7))
  (fp_line (start -6.1 -8) (end 6.1 -8)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp a4430819-7e08-4947-b6be-e3040b34fac6))
  (fp_line (start -6.1 8) (end -6.1 -8)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 46186d40-0551-445a-8ffd-1f3a86b19e21))
  (fp_line (start 6.1 -8) (end 6.1 8)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 7013b3df-4ce8-48e3-b2c1-f9c00e4e4a33))
  (fp_line (start 6.1 8) (end -6.1 8)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp f72c088a-70b1-439c-988d-02c4c0ec1b8b))
  (fp_poly
    (pts
      (xy -6.1 -6.6)
      (xy -5.2 -6.6)
      (xy -5.2 -7.4)
      (xy -6.1 -7.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 6f94ffdd-c712-4693-b26b-744c50fc6479))
  (fp_poly
    (pts
      (xy -6.1 -5.5)
      (xy -5.2 -5.5)
      (xy -5.2 -6.3)
      (xy -6.1 -6.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 8bdd26ad-2463-481c-8ceb-2dcdc91f7c3c))
  (fp_poly
    (pts
      (xy -6.1 -4.4)
      (xy -5.2 -4.4)
      (xy -5.2 -5.2)
      (xy -6.1 -5.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 4a408f48-1a53-4e1e-baae-fbdc10a6ae4c))
  (fp_poly
    (pts
      (xy -6.1 -3.3)
      (xy -5.2 -3.3)
      (xy -5.2 -4.1)
      (xy -6.1 -4.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 4fc89164-ccc9-4f88-8148-90dca4383f8e))
  (fp_poly
    (pts
      (xy -6.1 -2.2)
      (xy -5.2 -2.2)
      (xy -5.2 -3)
      (xy -6.1 -3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp c348a2f9-8557-41fe-aafb-499f85a1c1f3))
  (fp_poly
    (pts
      (xy -6.1 0.8)
      (xy -5.2 0.8)
      (xy -5.2 0)
      (xy -6.1 0)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 86d5c530-ab1e-49cd-9bc2-d196d17a25da))
  (fp_poly
    (pts
      (xy -6.1 1.9)
      (xy -5.2 1.9)
      (xy -5.2 1.1)
      (xy -6.1 1.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 8d13d77f-a638-41ed-8e60-5582338cd6b6))
  (fp_poly
    (pts
      (xy -6.1 3)
      (xy -5.2 3)
      (xy -5.2 2.2)
      (xy -6.1 2.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 4159771b-c5c8-4d47-b829-160968be7a6c))
  (fp_poly
    (pts
      (xy -6.1 4.1)
      (xy -5.2 4.1)
      (xy -5.2 3.3)
      (xy -6.1 3.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp f6d37375-f4b1-49f5-b870-e4ba2b52fe51))
  (fp_poly
    (pts
      (xy -6.1 5.2)
      (xy -5.2 5.2)
      (xy -5.2 4.4)
      (xy -6.1 4.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 4da2377b-5da4-4740-8c0a-63a9e8243c5c))
  (fp_poly
    (pts
      (xy -6.1 6.3)
      (xy -5.2 6.3)
      (xy -5.2 5.5)
      (xy -6.1 5.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 66e965c8-46e0-438c-9612-b3a93a3caf32))
  (fp_poly
    (pts
      (xy -6.1 7.4)
      (xy -5.2 7.4)
      (xy -5.2 6.6)
      (xy -6.1 6.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp f5699d22-79c6-4d47-9df5-f90d424afd02))
  (fp_poly
    (pts
      (xy 5.2 -6.6)
      (xy 6.1 -6.6)
      (xy 6.1 -7.4)
      (xy 5.2 -7.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 90e49219-9780-4228-ac37-8d2aaa6aa000))
  (fp_poly
    (pts
      (xy 5.2 -5.5)
      (xy 6.1 -5.5)
      (xy 6.1 -6.3)
      (xy 5.2 -6.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp f6e5e146-0c64-43fe-ab02-d6f5b87fdfc5))
  (fp_poly
    (pts
      (xy 5.2 -4.4)
      (xy 6.1 -4.4)
      (xy 6.1 -5.2)
      (xy 5.2 -5.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 120e3d06-bd72-49f7-8a05-3a93c7d9c8e4))
  (fp_poly
    (pts
      (xy 5.2 -3.3)
      (xy 6.1 -3.3)
      (xy 6.1 -4.1)
      (xy 5.2 -4.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp fc73a1e4-7104-426c-9e27-a98f3a43d002))
  (fp_poly
    (pts
      (xy 5.2 -2.2)
      (xy 6.1 -2.2)
      (xy 6.1 -3)
      (xy 5.2 -3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 45bff25d-0b4b-41d1-b755-c94bac35ae04))
  (fp_poly
    (pts
      (xy 5.2 0.8)
      (xy 6.1 0.8)
      (xy 6.1 0)
      (xy 5.2 0)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp f4edc4bb-e57a-45dd-bd74-1856242eb396))
  (fp_poly
    (pts
      (xy 5.2 1.9)
      (xy 6.1 1.9)
      (xy 6.1 1.1)
      (xy 5.2 1.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp e5eea5de-e68c-4fa1-9602-469d7c587718))
  (fp_poly
    (pts
      (xy 5.2 3)
      (xy 6.1 3)
      (xy 6.1 2.2)
      (xy 5.2 2.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 5ec61a6c-9ff9-45ca-ba16-741e3b55390e))
  (fp_poly
    (pts
      (xy 5.2 4.1)
      (xy 6.1 4.1)
      (xy 6.1 3.3)
      (xy 5.2 3.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 558513ab-e90f-42f2-9711-756e7ec70ec4))
  (fp_poly
    (pts
      (xy 5.2 5.2)
      (xy 6.1 5.2)
      (xy 6.1 4.4)
      (xy 5.2 4.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 2e382c6e-00ee-4189-9a2d-38a453cf0c67))
  (fp_poly
    (pts
      (xy 5.2 6.3)
      (xy 6.1 6.3)
      (xy 6.1 5.5)
      (xy 5.2 5.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 60800be6-ef5a-4d30-9ab6-11b548ead4d3))
  (fp_poly
    (pts
      (xy 5.2 7.4)
      (xy 6.1 7.4)
      (xy 6.1 6.6)
      (xy 5.2 6.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp f25a89a3-ddcd-4e14-b12d-5ada438f3f92))
  (pad "1" smd rect (at 6 7) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 23d270e3-2d29-4ed9-a791-0cd72bfb8ff4)
  )
  (pad "2" smd rect (at 6 5.9) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 56357746-56a5-449b-895b-0d29d42b3e91)
  )
  (pad "3" smd rect (at 6 4.8) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp de235913-9fd3-41cb-a7b5-db8b1adc90d8)
  )
  (pad "4" smd rect (at 6 3.7) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 44363a16-0938-42a9-9652-db273ee2759c)
  )
  (pad "5" smd rect (at 6 2.6) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 0792c0b5-dbdd-46bc-b657-2fea3dd9c31e)
  )
  (pad "6" smd rect (at 6 1.5) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 05fc6a84-efb3-4c37-a1a0-42c6ae89116b)
  )
  (pad "7" smd rect (at 6 0.4) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 2d8e6afc-6897-4cde-913f-5d976401226d)
  )
  (pad "8" smd rect (at 6 -2.6) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b6d3ccc5-ae9e-4b0f-ab4a-147185bc794c)
  )
  (pad "9" smd rect (at 6 -3.7) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 91573284-164f-44be-9d06-92790e2aade8)
  )
  (pad "10" smd rect (at 6 -4.8) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 3e2f440c-ef4e-4765-b451-e321ff001e4b)
  )
  (pad "11" smd rect (at 6 -5.9) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 487f0772-cbd8-4180-aa66-9bbd4f8b7df5)
  )
  (pad "12" smd rect (at 6 -7) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 7af818dc-2427-4cbb-9027-c16740ec4a88)
  )
  (pad "13" smd rect (at -6 -7 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 1fb5fd9f-d8ed-43f8-986b-b29e6fe187f2)
  )
  (pad "14" smd rect (at -6 -5.9 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 3632ab49-34c6-4ac2-b0ae-f89f8038d145)
  )
  (pad "15" smd rect (at -6 -4.8 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 8696af94-f93b-4fbf-b93b-3263a70dff1c)
  )
  (pad "16" smd rect (at -6 -3.7 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 65b55b03-a44f-4a8c-847f-a6c73bd0be5b)
  )
  (pad "17" smd rect (at -6 -2.6 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 6c9b6a57-0de4-4962-823b-a0cf500eb03e)
  )
  (pad "18" smd rect (at -6 0.4 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp a49a5f21-f9c4-4ccc-a365-23308399b74d)
  )
  (pad "19" smd rect (at -6 1.5 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 12be515a-f092-45ca-b261-45af1e353d36)
  )
  (pad "20" smd rect (at -6 2.6 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 93ea667f-805b-405c-a3f9-f664d8389f5b)
  )
  (pad "21" smd rect (at -6 3.7 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 12833f08-1509-4ba5-9bb8-8083ad9204a2)
  )
  (pad "22" smd rect (at -6 4.8 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 70d14ce8-da52-4a51-8cbe-80b4e69b418e)
  )
  (pad "23" smd rect (at -6 5.9 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 55c8087d-1726-4323-8a27-bc67b0d666be)
  )
  (pad "24" smd rect (at -6 7 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 84b01437-53b3-4b82-8211-f7cc1b8b534a)
  )
)
