(kicad_sch (version 20230819) (generator eeschema)

  (uuid 3c62b2d9-e85b-41ac-b240-e9fba27dce75)

  (paper "A5")

  (lib_symbols
    (symbol ".Project_Library:REF${#}" (power) (pin_names (offset 0)) (exclude_from_sim no) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "REF${#}" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Description" "Power symbol creates a local label (not global) with name \"REF${#}\", where the text variable \"#\" is the current sheet number " (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "local power" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "REF${#}_1_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.905)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.905)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "REF${#}" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (exclude_from_sim no) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Description" "Resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "PCM_4ms_Power-symbol:PWR_FLAG" (power) (pin_numbers hide) (pin_names (offset 0) hide) (exclude_from_sim no) (in_bom yes) (on_board yes)
      (property "Reference" "#FLG" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "PWR_FLAG" (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Description" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "PWR_FLAG_0_0"
        (pin power_out line (at 0 0 90) (length 0)
          (name "pwr" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "PWR_FLAG_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
            (xy -1.016 1.905)
            (xy 0 2.54)
            (xy 1.016 1.905)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
    )
  )

  (junction (at 52.07 56.515) (diameter 0) (color 0 0 0 0)
    (uuid 4d7ab109-3052-4d81-8202-b1562ac838f1)
  )
  (junction (at 76.2 56.515) (diameter 0) (color 0 0 0 0)
    (uuid bc9460a4-99cc-4bdb-8e43-a384bbfc72aa)
  )

  (wire (pts (xy 52.07 56.515) (xy 57.15 56.515))
    (stroke (width 0) (type default))
    (uuid 79105409-01a7-4863-becb-bdee5c98b930)
  )
  (wire (pts (xy 52.07 72.39) (xy 46.355 72.39))
    (stroke (width 0) (type default))
    (uuid 82b61808-c9ab-4a47-afc9-7d70785a53e5)
  )
  (wire (pts (xy 46.355 56.515) (xy 52.07 56.515))
    (stroke (width 0) (type default))
    (uuid 886d34f4-cb4c-488b-9285-8343e265f56a)
  )
  (wire (pts (xy 64.77 56.515) (xy 76.2 56.515))
    (stroke (width 0) (type default))
    (uuid bd37f502-7ffa-41ad-92d6-c62fe54d9619)
  )
  (wire (pts (xy 76.2 56.515) (xy 76.2 62.23))
    (stroke (width 0) (type default))
    (uuid d4af9bf7-4447-4477-936e-5ddb4503a8ff)
  )
  (wire (pts (xy 52.07 56.515) (xy 52.07 72.39))
    (stroke (width 0) (type default))
    (uuid e3d7f375-c20f-4a01-8a2b-b6365bb37095)
  )
  (wire (pts (xy 76.2 51.435) (xy 76.2 56.515))
    (stroke (width 0) (type default))
    (uuid ef9b8d67-23ec-4755-83d2-bd1af884a0c6)
  )

  (hierarchical_label "${param}" (shape input) (at 46.355 72.39 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 5b7361e7-1bbc-4e69-aa47-4c25593681d8)
  )
  (hierarchical_label "REF_NODE" (shape input) (at 46.355 56.515 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 6af255fa-cae8-4410-a1a0-a9da5295758d)
  )

  (symbol (lib_id "PCM_4ms_Power-symbol:PWR_FLAG") (at 76.2 51.435 0) (unit 1)
    (exclude_from_sim no) (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 6059cea7-e82b-44fb-ad00-7e6582d903eb)
    (property "Reference" "#FLG0201" (at 76.2 49.53 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "PWR_FLAG" (at 76.2 47.3019 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 76.2 51.435 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 76.2 51.435 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "" (at 76.2 51.435 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c87469b5-b57a-439d-af20-56a874e1fe4b))
    (instances
      (project "ERC_dynamic_power_symbol_test"
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d/c7b6fd2e-05cd-4dd4-8428-8dccf678354a"
          (reference "#FLG0201") (unit 1)
        )
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d/088a9fe0-ec9a-43a7-9e7e-edfb4cee57b2"
          (reference "#FLG0301") (unit 1)
        )
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d/356ec2aa-e896-4e5b-96d5-f383f5c93ff2"
          (reference "#FLG0401") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id ".Project_Library:REF${#}") (at 76.2 62.23 0) (unit 1)
    (exclude_from_sim no) (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid b2b6c513-6b49-4516-9750-e44a26481ea2)
    (property "Reference" "#PWR0201" (at 76.2 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "REF${#}" (at 76.2 66.3631 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 76.2 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 76.2 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "Power symbol creates a local label (not global) with name \"REF${#}\", where the text variable \"#\" is the current sheet number " (at 76.2 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e2bc9777-d2f7-41f5-b26c-c2ca3f254bcf))
    (instances
      (project "ERC_dynamic_power_symbol_test"
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d/c7b6fd2e-05cd-4dd4-8428-8dccf678354a"
          (reference "#PWR0201") (unit 1)
        )
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d/088a9fe0-ec9a-43a7-9e7e-edfb4cee57b2"
          (reference "#PWR0301") (unit 1)
        )
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d/356ec2aa-e896-4e5b-96d5-f383f5c93ff2"
          (reference "#PWR0401") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 60.96 56.515 90) (unit 1)
    (exclude_from_sim no) (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid fbe14eb8-65ef-4b4b-9471-e98e2dbc2edc)
    (property "Reference" "R201" (at 60.96 51.3545 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "0R" (at 60.96 53.7788 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 60.96 58.293 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 60.96 56.515 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "Resistor" (at 60.96 56.515 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 0130a35f-faa1-40c8-bb2e-a1a7fcd4327a))
    (pin "2" (uuid 467ae783-b011-4c07-85c0-2f988a5d1d1f))
    (instances
      (project "ERC_dynamic_power_symbol_test"
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d/c7b6fd2e-05cd-4dd4-8428-8dccf678354a"
          (reference "R201") (unit 1)
        )
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d/088a9fe0-ec9a-43a7-9e7e-edfb4cee57b2"
          (reference "R301") (unit 1)
        )
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d/356ec2aa-e896-4e5b-96d5-f383f5c93ff2"
          (reference "R401") (unit 1)
        )
      )
    )
  )
)
