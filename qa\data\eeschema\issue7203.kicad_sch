(kicad_sch (version 20220904) (generator eeschema)

  (uuid 04c96d79-48d5-4885-af8e-d7f9a9a29a18)

  (paper "A4")

  (lib_symbols
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)        (effects (font (size 1.27 1.27)))
      
)
      (property "Value" "R" (id 1) (at 0 0 90)        (effects (font (size 1.27 1.27)))
      
)
      (property "Footprint" "" (id 2) (at -1.778 0 90)        (effects (font (size 1.27 1.27)) hide)
      
)
      (property "Datasheet" "~" (id 3) (at 0 0 0)        (effects (font (size 1.27 1.27)) hide)
      
)
      (property "ki_keywords" "R res resistor" (id 4) (at 0 0 0)        (effects (font (size 1.27 1.27)) hide)
      
)
      (property "ki_description" "Resistor" (id 5) (at 0 0 0)        (effects (font (size 1.27 1.27)) hide)
      
)
      (property "ki_fp_filters" "R_*" (id 6) (at 0 0 0)        (effects (font (size 1.27 1.27)) hide)
      
)
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )



  (no_connect (at 90.17 88.9) (uuid 6ec41ff2-1713-41c8-b474-cc3abd059206))
  (no_connect (at 144.78 88.9) (uuid 76c08e27-3db5-46ac-9f08-6fb09b48acca))
  (no_connect (at 116.84 88.9) (uuid 9b226482-45cb-4b73-a977-f0428d1628a1))

  (wire (pts (xy 73.66 80.01) (xy 73.66 88.9))
    (stroke (width 0) (type default))
    (uuid 00c8ee41-02ce-4618-b70d-30c1634ab5fc)
  )
  (wire (pts (xy 100.33 88.9) (xy 116.84 88.9))
    (stroke (width 0) (type default))
    (uuid 0cf69055-7db4-479a-87cb-551226f8a0e1)
  )
  (wire (pts (xy 73.66 88.9) (xy 90.17 88.9))
    (stroke (width 0) (type default))
    (uuid 198342f2-efea-403f-9043-804cfa9262a0)
  )
  (wire (pts (xy 100.33 80.01) (xy 100.33 88.9))
    (stroke (width 0) (type default))
    (uuid 35caae75-61ed-4745-a5d7-97a6ace7b59e)
  )
  (wire (pts (xy 128.27 64.77) (xy 128.27 72.39))
    (stroke (width 0) (type default))
    (uuid 61b14430-4ca4-434d-b30f-654d55c6a3bc)
  )
  (wire (pts (xy 128.27 80.01) (xy 128.27 88.9))
    (stroke (width 0) (type default))
    (uuid 6eff6f57-a353-4b8a-81a9-c7210595fb71)
  )
  (wire (pts (xy 100.33 64.77) (xy 100.33 72.39))
    (stroke (width 0) (type default))
    (uuid 7da5e747-fa49-4980-b8e9-ba0870b0bd00)
  )
  (wire (pts (xy 128.27 88.9) (xy 144.78 88.9))
    (stroke (width 0) (type default))
    (uuid 7e19c5d8-157a-4955-a4b7-14783802a8b1)
  )
  (wire (pts (xy 73.66 64.77) (xy 73.66 72.39))
    (stroke (width 0) (type default))
    (uuid b124d93d-79dd-491b-b451-beb59748c0d6)
  )

  (text "This test should result in 3 ERC errors\ncentered on the labels"
    (at 74.93 48.26 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid d05c5d4d-805f-4bc1-85d4-4536a8642b9a)
  )

  (label "test1" (at 73.66 64.77 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 4318cfac-09aa-479c-886b-29110f48468d)
  )

  (global_label "test2" (shape input) (at 100.33 64.77 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid 7df1ad95-c091-4bad-9d4b-1fa200f7e372)
    (property "Intersheetrefs" "${INTERSHEET_REFS}" (id 0) (at 107.8546 64.77 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
  )

  (hierarchical_label "test3" (shape input) (at 128.27 64.77 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid 0b095bd8-cc42-4ec2-b2b6-647d97f49853)
  )

  (symbol (lib_id "Device:R") (at 73.66 76.2 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 0a2cc769-fb59-4476-8c45-9d19637150dc)
    (default_instance (reference "R1") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R1" (id 0) (at 76.2 75.565 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 76.2 78.105 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 71.882 76.2 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 73.66 76.2 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6a3c8010-df12-4417-b2b4-0d31c61a340a))
    (pin "2" (uuid 03c6a327-1a26-48ff-89e0-9f7507022052))
  )

  (symbol (lib_id "Device:R") (at 128.27 76.2 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 5500cea6-55d2-4685-8e6f-119da9acb56f)
    (default_instance (reference "R1") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R1" (id 0) (at 130.81 75.565 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 130.81 78.105 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 126.492 76.2 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 128.27 76.2 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 476de613-4f8d-4993-b247-2762e739fc91))
    (pin "2" (uuid 2e54cfa4-b4ed-4614-99a8-30189d5d9307))
  )

  (symbol (lib_id "Device:R") (at 100.33 76.2 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 641fc5f7-d00e-4a53-9aa3-57b6273a605e)
    (default_instance (reference "R1") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R1" (id 0) (at 102.87 75.565 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 102.87 78.105 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 98.552 76.2 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 100.33 76.2 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid dfbb0613-a535-4327-96c6-d6347ed6e447))
    (pin "2" (uuid 734940da-e524-4392-a646-ee035ed7b825))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/641fc5f7-d00e-4a53-9aa3-57b6273a605e"
      (reference "R1") (unit 1) (value "R") (footprint "")
    )
    (path "/0a2cc769-fb59-4476-8c45-9d19637150dc"
      (reference "R2") (unit 1) (value "R") (footprint "")
    )
    (path "/5500cea6-55d2-4685-8e6f-119da9acb56f"
      (reference "R3") (unit 1) (value "R") (footprint "")
    )
  )
)
