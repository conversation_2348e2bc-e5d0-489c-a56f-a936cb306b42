{"board": {"design_settings": {"defaults": {"board_outline_line_width": 0.2032, "copper_line_width": 0.30479999999999996, "copper_text_italic": false, "copper_text_size_h": 1.524, "copper_text_size_v": 1.524, "copper_text_thickness": 0.2032, "copper_text_upright": false, "courtyard_line_width": 0.049999999999999996, "dimension_precision": 1, "dimension_units": 0, "fab_line_width": 0.09999999999999999, "fab_text_italic": false, "fab_text_size_h": 1.0, "fab_text_size_v": 1.0, "fab_text_thickness": 0.15, "fab_text_upright": false, "other_line_width": 0.09999999999999999, "other_text_italic": false, "other_text_size_h": 1.0, "other_text_size_v": 1.0, "other_text_thickness": 0.15, "other_text_upright": false, "pads": {"drill": 1.0, "height": 2.032, "width": 1.524}, "silk_line_width": 0.30479999999999996, "silk_text_italic": false, "silk_text_size_h": 1.27, "silk_text_size_v": 1.27, "silk_text_thickness": 0.2032, "silk_text_upright": false, "zones": {"45_degree_only": false, "min_clearance": 0.39999999999999997}}, "diff_pair_dimensions": [{"gap": 0.25, "via_gap": 0.25, "width": 0.2}], "drc_exclusions": [], "meta": {"version": 0}, "rule_severities": {"clearance": "error", "copper_edge_clearance": "error", "courtyards_overlap": "error", "drill_too_small": "error", "duplicate_footprints": "warning", "extra_footprint": "warning", "hole_near_hole": "error", "invalid_outline": "error", "item_on_disabled_layer": "error", "items_not_allowed": "error", "keepout": "error", "malformed_courtyard": "error", "microvia_drill_too_small": "error", "microvia_too_small": "error", "missing_courtyard": "ignore", "missing_footprint": "warning", "npth_inside_courtyard": "ignore", "padstack": "error", "pth_inside_courtyard": "ignore", "shorting_items": "error", "track_dangling": "warning", "track_width": "error", "tracks_crossing": "error", "unconnected_items": "error", "unresolved_variable": "error", "via_annulus": "error", "via_dangling": "warning", "via_hole_larger_than_pad": "error", "via_too_small": "error", "zone_has_empty_net": "error", "zones_intersect": "error"}, "rules": {"allow_blind_buried_vias": false, "allow_microvias": false, "max_error": 0.005, "min_clearance": 0.0, "min_copper_edge_clearance": 0.01, "min_hole_to_hole": 0.25, "min_microvia_diameter": 0.508, "min_microvia_drill": 0.127, "min_through_hole_diameter": 0.508, "min_track_width": 0.2032, "min_via_annulus": 0.049999999999999996, "min_via_diameter": 0.889, "solder_mask_clearance": 0.254, "solder_mask_min_width": 0.0, "solder_paste_clearance": 0.0, "solder_paste_margin_ratio": 0.0}, "track_widths": [0.2032], "via_dimensions": [{"diameter": 0.889, "drill": 0.635}], "zones_use_no_outline": false}, "layer_presets": []}, "boards": [], "cvpcb": {"equivalence_files": []}, "libraries": {"pinned_footprint_libs": [], "pinned_symbol_libs": []}, "meta": {"filename": "board.kicad_pro", "version": 1}, "net_settings": {"classes": [{"bus_width": 6.0, "clearance": 0.2032, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.508, "microvia_drill": 0.127, "name": "<PERSON><PERSON><PERSON>", "track_width": 0.2032, "via_diameter": 0.889, "via_drill": 0.635, "wire_width": 6.0}, {"bus_width": 6.0, "clearance": 0.2286, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.508, "microvia_drill": 0.127, "name": "pwr", "nets": [], "track_width": 0.2286, "via_diameter": 0.889, "via_drill": 0.635, "wire_width": 6.0}], "meta": {"version": 0}, "net_colors": null}, "pcbnew": {"last_paths": {"gencad": "", "idf": "", "netlist": "./", "specctra_dsn": "", "step": "", "vmrl": ""}, "page_layout_descr_file": ""}, "schematic": {"legacy_lib_dir": "", "legacy_lib_list": []}, "sheets": [], "text_variables": {}}