[IBIS Ver]      2.1  |Let's test a comment      
[Comment char]  #_char 
[File name]     ibis_v2_1.pkg
[File Rev]      1.0  #Let's test a comment  
[Date]          26/08/2021 
[Source]        This is the
                source for the files
[Notes]         We can have some
                Notes 
[Copyright] /*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright (C) 2017-2021 KiCad Developers, see AUTHORS.txt for contributors.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, you may find one here:
 * http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
 * or you may search the http://www.gnu.org website for the version 2 license,
 * or you may write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA
 */
[Disclaimer] This is NOT a valid component.
[Component]     Virtual
[Manufacturer]  KiCad    
[Package]      
R_pkg           1m              0.8m            2m 
L_pkg           1m              0.8m            2m
C_pkg           1m              0.8m            2m

[Pin]   signal_name     model_name      R_pin   L_pin   C_pin             
  1     VCC             POWER           1m      0.8m    2m            
  2     GND             GND             1m      0.8m    2m               
  3     X               Input           1m      0.8m    2m
  4     Y               Output           1m      0.8m    2m
  5     YN              Output           1m      0.8m    2m
  6     Y               Output           1m      0.8m    2m
  7     YN              Output           1m      0.8m    2m

[Package Model]     QS-SMT-cer-8-pin-pkgs
[Pin Mapping]  pulldown_ref     pullup_ref   gnd_clamp_ref  power_clamp_ref
1              GNDBUS           NC
2              NC               PWRBUS 
3              GNDBUS           PWRBUS
4              GNDBUS           PWRBUS       GNDBUS           PWRBUS
5              GNDBUS           PWRBUS       GNDBUS           PWRBUS
6              GNDBUS           PWRBUS       GNDBUS           PWRBUS
7              GNDBUS           PWRBUS       GNDBUS           PWRBUS

[Diff Pin]  inv_pin  vdiff  tdelay_typ tdelay_min tdelay_max
#
 4           5       150mV    -1ns       0ns      -2ns 
 6           7       150mV    -1ns       0ns      -2ns 
                                 

[Model]         Input
Model_type      Input
Polarity        Non-Inverting
Enable          Active-High
Vinl = 0.8V                            
Vinh = 2.0V          
Vmeas= 1.5V  
Cref =50pF   
Rref=500   
Vref = 0         

C_comp          10.0pF          8.0pF          15.0pF 

[Voltage range]         5.0V            4.5V            5.5V 
[Temperature Range]     27.0            -50             130.0
[Pullup Reference]      5.0V            4.5V            5.5V
[Pulldown Reference]    0V              0V              0V
[POWER Clamp Reference] 5.0V            4.5V            5.5V
[GND Clamp Reference]   0V              0V              0V

[Pulldown]
#  Voltage   I(typ)    I(min)    I(max)
# 
   -5.0V    -50.0m    -40.0m    -60.0m 
   0.0V      0         0         0
   5.0V      50.0m     40.0m     60.0m 
[Pullup]
# 
#  Voltage   I(typ)    I(min)    I(max)
# 
   -5.0V     50.0m     40.0m     60.0m 
   0.0V      0         0         0
   5.0V     -50.0m    -40.0m    -60.0m 
[GND_clamp]
# 
#  Voltage   I(typ)    I(min)    I(max)
# 
   -5.0V    -50.0m     NA        NA
   0.0V      0         NA        NA
   5.0V      0         NA        NA
[POWER_clamp]
# 
#  Voltage   I(typ)    I(min)    I(max)
# 
   -5.0V     50.0m     NA        NA
   0.0V      0         NA        NA
   5.0V      0         NA        NA

[Rgnd]          1500hm          100Ohm          3600
[Rpower]        150Ohm          100Ohm          NA
[Rac]           30Ohm           NA              NA
[Cac]           50pF            NA              NA

[Model]         Output
Model_type      Output
Polarity        Non-Inverting
Enable          Active-High
C_comp          10.0pF          8.0pF          15.0pF 

[Voltage range]         5.0V            4.5V            5.5V 
[Pulldown]
#  Voltage   I(typ)    I(min)    I(max)
# 
   -5.0V    -50.0m    -40.0m    -60.0m 
   0.0V      0         0         0
   5.0V      50.0m     40.0m     60.0m 
[Pullup]
# 
#  Voltage   I(typ)    I(min)    I(max)
# 
   -5.0V     50.0m     40.0m     60.0m 
   0.0V      0         0         0
   5.0V     -50.0m    -40.0m    -60.0m 
[GND_clamp]
# 
#  Voltage   I(typ)    I(min)    I(max)
# 
   -5.0V    -50.0m     NA        NA
   0.0V      0         NA        NA
   5.0V      0         NA        NA
[POWER_clamp]
# 
#  Voltage   I(typ)    I(min)    I(max)
# 
   -5.0V     50.0m     NA        NA
   0.0V      0         NA        NA
   5.0V      0         NA        NA


[Ramp]
# variable      typ             min             max 
dV/dt_r         3.0/2n          2.8/3n          3.2/1n 
dV/dt_f         3.0/2n          2.8/3n          3.2/1n 

[Rising Waveform]
R_fixture = 500
V_fixture = 5.0
C_fixture = 10p
L_fixture = 2n
C_dut = 7p
R_dut = 1m
L_dut = 1n
#Time     V(typ)     V(min)    V(max)
 0.0ns     0         0         0
 1.0ns     1         0.5       1.5
 2.0ns     2         1         3
#
[Falling Waveform]
R_fixture = 50
V_fixture = 0
#Time     V(typ)     V(min)    V(max)
 0.0ns     2         1         NA
 1.0ns     1         0.5       NA
 2.0ns     0         0         NA

 
[END]