(kicad_sch (version 20230819) (generator eeschema)

  (uuid 6872e4bc-16cd-41ca-a040-4bec79014681)

  (paper "A4")

  (lib_symbols
    (symbol "74xx:74LS138" (pin_names (offset 1.016)) (exclude_from_sim no) (in_bom yes) (on_board yes)
      (property "Reference" "U" (at -7.62 11.43 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "74LS138" (at -7.62 -13.97 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "http://www.ti.com/lit/gpn/sn74LS138" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Description" "Decoder 3 to 8 active low outputs" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_locked" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_keywords" "TTL DECOD DECOD8" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "DIP?16*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "74LS138_1_0"
        (pin input line (at -12.7 7.62 0) (length 5.08)
          (name "A0" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin output output_low (at 12.7 -5.08 180) (length 5.08)
          (name "O5" (effects (font (size 1.27 1.27))))
          (number "10" (effects (font (size 1.27 1.27))))
        )
        (pin output output_low (at 12.7 -2.54 180) (length 5.08)
          (name "O4" (effects (font (size 1.27 1.27))))
          (number "11" (effects (font (size 1.27 1.27))))
        )
        (pin output output_low (at 12.7 0 180) (length 5.08)
          (name "O3" (effects (font (size 1.27 1.27))))
          (number "12" (effects (font (size 1.27 1.27))))
        )
        (pin output output_low (at 12.7 2.54 180) (length 5.08)
          (name "O2" (effects (font (size 1.27 1.27))))
          (number "13" (effects (font (size 1.27 1.27))))
        )
        (pin output output_low (at 12.7 5.08 180) (length 5.08)
          (name "O1" (effects (font (size 1.27 1.27))))
          (number "14" (effects (font (size 1.27 1.27))))
        )
        (pin output output_low (at 12.7 7.62 180) (length 5.08)
          (name "O0" (effects (font (size 1.27 1.27))))
          (number "15" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 0 15.24 270) (length 5.08)
          (name "VCC" (effects (font (size 1.27 1.27))))
          (number "16" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -12.7 5.08 0) (length 5.08)
          (name "A1" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -12.7 2.54 0) (length 5.08)
          (name "A2" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin input input_low (at -12.7 -10.16 0) (length 5.08)
          (name "E1" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin input input_low (at -12.7 -7.62 0) (length 5.08)
          (name "E2" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -12.7 -5.08 0) (length 5.08)
          (name "E3" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
        (pin output output_low (at 12.7 -10.16 180) (length 5.08)
          (name "O7" (effects (font (size 1.27 1.27))))
          (number "7" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at 0 -17.78 90) (length 5.08)
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "8" (effects (font (size 1.27 1.27))))
        )
        (pin output output_low (at 12.7 -7.62 180) (length 5.08)
          (name "O6" (effects (font (size 1.27 1.27))))
          (number "9" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "74LS138_1_1"
        (rectangle (start -7.62 10.16) (end 7.62 -12.7)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (exclude_from_sim no) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "global power" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:PWR_FLAG" (power) (pin_numbers hide) (pin_names (offset 0) hide) (exclude_from_sim no) (in_bom yes) (on_board yes)
      (property "Reference" "#FLG" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "PWR_FLAG" (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Description" "Special symbol for telling ERC where power comes from" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "flag power" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "PWR_FLAG_0_0"
        (pin power_out line (at 0 0 90) (length 0)
          (name "pwr" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "PWR_FLAG_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
            (xy -1.016 1.905)
            (xy 0 2.54)
            (xy 1.016 1.905)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
    )
    (symbol "power:VCC" (power) (pin_names (offset 0)) (exclude_from_sim no) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "VCC" (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Description" "Power symbol creates a global label with name \"VCC\"" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "global power" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VCC_0_1"
        (polyline
          (pts
            (xy -0.762 1.27)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 0)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 2.54)
            (xy 0.762 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "VCC_1_1"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "VCC" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 133.35 106.68) (diameter 0) (color 0 0 0 0)
    (uuid 591f0034-1c08-4385-8710-c0d768959d8f)
  )
  (junction (at 146.05 116.84) (diameter 0) (color 0 0 0 0)
    (uuid b64e93fb-af17-48bf-b415-65b806c8699c)
  )
  (junction (at 133.35 109.22) (diameter 0) (color 0 0 0 0)
    (uuid ff76e434-337c-4c44-9216-1bb5329e9465)
  )

  (no_connect (at 158.75 106.68) (uuid 05604f50-7b37-42fd-be9c-c54f3786b1b2))
  (no_connect (at 158.75 109.22) (uuid 091322b6-8f18-4b59-a0bd-732e79ca7f42))
  (no_connect (at 158.75 99.06) (uuid 5f9e85f1-a98c-4b9c-96f6-00b8e2fd9e5b))
  (no_connect (at 158.75 96.52) (uuid 69b0189b-e87e-4cb8-b2e5-f324be88cd0f))
  (no_connect (at 158.75 101.6) (uuid 7caba47a-90e5-496e-994b-e363b369cb7d))
  (no_connect (at 158.75 93.98) (uuid aeb6352b-4803-468c-9577-3b87cd1b434c))
  (no_connect (at 158.75 91.44) (uuid dfcc0890-6c8b-4487-82bf-01970cfe62af))
  (no_connect (at 158.75 104.14) (uuid ec507f0b-d744-4433-a7a0-74a05a37ccb5))

  (bus_entry (at 116.84 88.9) (size 2.54 2.54)
    (stroke (width 0) (type default))
    (uuid 37fe642c-d525-4639-bf08-461c8e08b026)
  )
  (bus_entry (at 116.84 93.98) (size 2.54 2.54)
    (stroke (width 0) (type default))
    (uuid 4068cb17-b46e-43c0-b306-7df3a2c33b3b)
  )
  (bus_entry (at 116.84 91.44) (size 2.54 2.54)
    (stroke (width 0) (type default))
    (uuid 73cef5fb-4174-405d-a0de-634eee362434)
  )

  (wire (pts (xy 133.35 109.22) (xy 133.35 116.84))
    (stroke (width 0) (type default))
    (uuid 178fcdab-ba5c-454c-9247-e8c95bd6c15d)
  )
  (bus (pts (xy 86.36 64.77) (xy 116.84 64.77))
    (stroke (width 0) (type default))
    (uuid 20152e6a-1b2e-4042-8b8f-93125fff953c)
  )
  (bus (pts (xy 116.84 91.44) (xy 116.84 93.98))
    (stroke (width 0) (type default))
    (uuid 2d774613-d103-4f92-afc7-5ea7fea64e56)
  )
  (bus (pts (xy 116.84 88.9) (xy 116.84 91.44))
    (stroke (width 0) (type default))
    (uuid 3e187b0d-f042-41ae-943d-0084bde57665)
  )

  (wire (pts (xy 146.05 116.84) (xy 175.26 116.84))
    (stroke (width 0) (type default))
    (uuid 61094eaf-f91e-497e-8166-d02aa99c7b33)
  )
  (wire (pts (xy 175.26 116.84) (xy 175.26 115.57))
    (stroke (width 0) (type default))
    (uuid 72dd73d1-ce1f-4645-94dc-3c5e9de68043)
  )
  (wire (pts (xy 133.35 116.84) (xy 146.05 116.84))
    (stroke (width 0) (type default))
    (uuid 7cbe4a58-f6ae-4829-a24a-65da50e7e0c9)
  )
  (wire (pts (xy 119.38 93.98) (xy 133.35 93.98))
    (stroke (width 0) (type default))
    (uuid 82626ab4-c048-42a5-be32-a608bb5ecc5c)
  )
  (wire (pts (xy 133.35 106.68) (xy 133.35 109.22))
    (stroke (width 0) (type default))
    (uuid a0f66a67-2d4f-4796-96b9-5461f082e48e)
  )
  (bus (pts (xy 116.84 64.77) (xy 116.84 88.9))
    (stroke (width 0) (type default))
    (uuid b9bcf62b-e852-4d3e-a1a7-2c2e9c12de35)
  )

  (wire (pts (xy 119.38 91.44) (xy 133.35 91.44))
    (stroke (width 0) (type default))
    (uuid e8e1b2a0-a26b-489d-8d6e-ded650605f7e)
  )
  (wire (pts (xy 119.38 96.52) (xy 133.35 96.52))
    (stroke (width 0) (type default))
    (uuid fb38bfae-e032-4395-8985-97101621dc3a)
  )
  (wire (pts (xy 133.35 104.14) (xy 133.35 106.68))
    (stroke (width 0) (type default))
    (uuid fbe8cc01-1a76-41ca-8141-56f1641f6b96)
  )

  (label "A1" (at 133.35 93.98 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 282c151e-75dd-4c68-b308-00699e2a9d59)
  )
  (label "A[0..2]" (at 93.98 64.77 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 4f21bed6-6f5e-4394-844f-1128cfadd2b3)
  )
  (label "A2" (at 133.35 96.52 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid e3c46e1d-1eb0-4b43-b9e8-e8ed1cf471bd)
  )
  (label "A0" (at 133.35 91.44 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid e96f1529-2a0f-421f-814b-f555d9bb351d)
  )

  (hierarchical_label "A[0..2]" (shape input) (at 86.36 64.77 180) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify right))
    (uuid 1b9b23a3-d7af-4acb-86ce-d2c3b62d2ba8)
  )

  (symbol (lib_id "power:PWR_FLAG") (at 175.26 115.57 0) (unit 1)
    (exclude_from_sim no) (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 265c390b-dfa4-4ded-b3c0-a64d7e852a3b)
    (property "Reference" "#FLG02" (at 175.26 113.665 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "PWR_FLAG" (at 175.26 110.49 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 175.26 115.57 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 175.26 115.57 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "Special symbol for telling ERC where power comes from" (at 175.26 115.57 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid ff09da93-83e0-4294-841b-2c9fa9d5feb5))
    (instances
      (project "issue12814"
        (path "/9d9c2574-56a3-45db-8021-2a92a85311b1/fd81cd4d-ed06-4c80-bc4c-cba1d4bc410f"
          (reference "#FLG02") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:VCC") (at 146.05 83.82 0) (unit 1)
    (exclude_from_sim no) (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 2cfafb54-44b2-4d23-8777-6a5aea97f1be)
    (property "Reference" "#PWR05" (at 146.05 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "VCC" (at 146.05 78.74 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 146.05 83.82 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 146.05 83.82 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "Power symbol creates a global label with name \"VCC\"" (at 146.05 83.82 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 68f035a8-c984-46d6-9021-cb487b69e745))
    (instances
      (project "issue12814"
        (path "/9d9c2574-56a3-45db-8021-2a92a85311b1/fd81cd4d-ed06-4c80-bc4c-cba1d4bc410f"
          (reference "#PWR05") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 146.05 116.84 0) (unit 1)
    (exclude_from_sim no) (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 3cb53738-fc5e-418a-9319-ae679c48e3c2)
    (property "Reference" "#PWR06" (at 146.05 123.19 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 146.05 121.92 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 146.05 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 146.05 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "Power symbol creates a global label with name \"GND\" , ground" (at 146.05 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8748e088-cf03-4dee-a82c-181fb597d790))
    (instances
      (project "issue12814"
        (path "/9d9c2574-56a3-45db-8021-2a92a85311b1/fd81cd4d-ed06-4c80-bc4c-cba1d4bc410f"
          (reference "#PWR06") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "74xx:74LS138") (at 146.05 99.06 0) (unit 1)
    (exclude_from_sim no) (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid a47e62d0-6d36-402f-8a08-f4532bf659d8)
    (property "Reference" "U2" (at 148.2441 83.82 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "74LS138" (at 148.2441 86.36 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 146.05 99.06 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "http://www.ti.com/lit/gpn/sn74LS138" (at 146.05 99.06 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "Decoder 3 to 8 active low outputs" (at 146.05 99.06 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "4" (uuid 48d6ebc7-e962-4ba1-a428-80a38574ba6c))
    (pin "10" (uuid 0e047d96-e512-4e1d-a722-b9a2c061e462))
    (pin "13" (uuid f727509a-**************-92dfbc818384))
    (pin "12" (uuid de3e72c0-ba4e-4910-92ba-1ec6cbeee4c7))
    (pin "11" (uuid 9047155a-d66d-4c4e-af11-c8cf5205bcdd))
    (pin "5" (uuid 1510b2e7-912c-4179-8216-e5d6023291d1))
    (pin "3" (uuid ed3b1501-f406-4330-89ab-bcaa4444ad54))
    (pin "8" (uuid 65500f6e-4d2d-4c34-89e4-ab10b55782d5))
    (pin "2" (uuid b6752884-b0a3-42ea-8c61-6b4e68d40e0b))
    (pin "9" (uuid 124c892b-71b2-4a69-bccd-2316cdbfbd3e))
    (pin "6" (uuid 2122f164-dc30-4a5c-9972-464cb2315d66))
    (pin "15" (uuid 0f39f711-d217-4b46-ba25-0e67f9712b08))
    (pin "16" (uuid 86fc53bf-2703-40fe-806a-871398fe76aa))
    (pin "7" (uuid de424f87-d1b3-4ec6-a907-a4485d05d709))
    (pin "1" (uuid 418f8854-e299-411f-bc00-aa978e2976f0))
    (pin "14" (uuid fc3a9eaa-67fa-446e-83ef-548d1f452871))
    (instances
      (project "issue12814"
        (path "/9d9c2574-56a3-45db-8021-2a92a85311b1/fd81cd4d-ed06-4c80-bc4c-cba1d4bc410f"
          (reference "U2") (unit 1)
        )
      )
    )
  )
)
