(kicad_pcb (version 20230620) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.Adhes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
    (50 "User.1" user)
    (51 "User.2" user)
    (52 "User.3" user)
    (53 "User.4" user)
    (54 "User.5" user)
    (55 "User.6" user)
    (56 "User.7" user)
    (57 "User.8" user)
    (58 "User.9" user)
  )

  (setup
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 4)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (pdf_front_fp_property_popups true)
      (pdf_back_fp_property_popups true)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "A")
  (net 2 "B")

  (footprint "TestPoint:TestPoint_Pad_1.0x1.0mm" (layer "F.Cu")
    (tstamp 0662cfad-e2af-4a28-8e06-54487b2d060f)
    (at 126.383886 85.958221)
    (descr "SMD rectangular pad as test Point, square 1.0mm side length")
    (tags "test point SMD pad rectangle square")
    (property "Reference" "REF**" (at 0 -1.448 0 unlocked) (layer "F.SilkS") (tstamp a9c377d9-d9a8-4957-9ef2-653acbec4dad)
      (effects (font (size 1 1) (thickness 0.1)))
    )
    (property "Value" "TestPoint_Pad_1.0x1.0mm" (at 0 1.55 0 unlocked) (layer "F.Fab") (tstamp 8722d65f-d023-4ca1-9653-ede49a60bc5e)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "TestPoint:TestPoint_Pad_1.0x1.0mm" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 1f52001b-ae94-49e9-b2b5-6673a1b966af)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 2cc1d293-3904-4185-9d78-14af283b2a39)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 8d2540c1-292c-4170-86dd-0959865683a2)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_line (start -0.7 -0.7) (end 0.7 -0.7)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp de5cbed9-338f-4fba-aa6e-c8a10dd46598))
    (fp_line (start -0.7 0.7) (end -0.7 -0.7)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0004140c-4466-41de-a8de-f1da559892df))
    (fp_line (start 0.7 -0.7) (end 0.7 0.7)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6d11ed56-9675-4b43-9e47-1493d54e3ac3))
    (fp_line (start 0.7 0.7) (end -0.7 0.7)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 20542c66-768b-43a3-a7a6-10c5c62bb528))
    (fp_line (start -1 -1) (end -1 1)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 78bcf185-2b20-4fc4-9289-1b0fa982c740))
    (fp_line (start -1 -1) (end 1 -1)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 058a4358-6342-4f80-80a8-3dbc570a4ca7))
    (fp_line (start 1 1) (end -1 1)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 1b5c24fb-0c0f-45c4-a0b6-2789970f4a7a))
    (fp_line (start 1 1) (end 1 -1)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2a79ab99-848f-4e4a-9a6a-b6833477e773))
    (fp_text user "${REFERENCE}" (at 0 -1.45 0) (layer "F.Fab") (tstamp a858b269-27f4-4723-ba2d-fa8cdd0a9779)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad "1" smd rect (at 0 0) (size 1 1) (layers "F.Cu" "F.Mask")
      (net 1 "A")
      (tstamp ff356f6c-417c-45fd-8338-965175a521e8)
    )
  )

  (footprint "TestPoint:TestPoint_Pad_1.0x1.0mm" (layer "B.Cu")
    (tstamp 42ed20a1-6256-4072-835a-a4bd28a9f9a8)
    (at 128.622292 85.304594 180)
    (descr "SMD rectangular pad as test Point, square 1.0mm side length")
    (tags "test point SMD pad rectangle square")
    (property "Reference" "REF**" (at 0 1.448 0 unlocked) (layer "B.SilkS") (tstamp 3467d764-46ee-4a7d-803e-52e5934180a2)
      (effects (font (size 1 1) (thickness 0.1)) (justify mirror))
    )
    (property "Value" "TestPoint_Pad_1.0x1.0mm" (at 0 -1.55 0 unlocked) (layer "B.Fab") (tstamp a61e917b-111f-4d17-9565-6ae0b5832d32)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Footprint" "TestPoint:TestPoint_Pad_1.0x1.0mm" (at 0 0 0 unlocked) (layer "B.Fab") hide (tstamp dd971483-3b14-41dd-be84-dd65b929cd2b)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "B.Fab") hide (tstamp 093cf8e6-f50b-4ee6-900c-63d6c741c2dd)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Description" "" (at 0 0 0 unlocked) (layer "B.Fab") hide (tstamp ed3395ab-2567-4809-9ce8-0af0ac4a06cd)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_line (start 0.7 0.7) (end -0.7 0.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 477ea467-832b-4e6a-9280-640c8709aedd))
    (fp_line (start 0.7 -0.7) (end 0.7 0.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 6f8c6994-cad2-429a-a1c5-43413d1196e8))
    (fp_line (start -0.7 0.7) (end -0.7 -0.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 585c91fb-6706-4683-bcd7-6beb6cecefd8))
    (fp_line (start -0.7 -0.7) (end 0.7 -0.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 30b86a99-e8ac-45bd-b9f8-3f1f188d9521))
    (fp_line (start 1 1) (end 1 -1)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp fcf86568-6d87-4044-8c2f-405cf9649f0e))
    (fp_line (start 1 1) (end -1 1)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp bdcd4fd5-3c51-46b9-952d-edfef4ece6f5))
    (fp_line (start -1 -1) (end 1 -1)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp d8a94b9c-85fe-4c07-80d1-074ae82400dd))
    (fp_line (start -1 -1) (end -1 1)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 2bdb98a7-177c-4399-b91b-0e73fdfb39e3))
    (fp_text user "${REFERENCE}" (at 0 1.45 180) (layer "B.Fab") (tstamp 3361ae7f-450d-488d-bc3e-4c7d82969fd2)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (pad "1" smd rect (at 0 0 180) (size 1 1) (layers "B.Cu" "B.Mask")
      (net 2 "B")
      (tstamp 8285774c-eb9a-4524-aae8-d1d472cd8c36)
    )
  )

  (gr_rect (start 120.188564 80.549407) (end 134.184964 91.096551)
    (stroke (width 0.05) (type default)) (fill none) (layer "Edge.Cuts") (tstamp 24026c1a-e66c-43d9-874c-745b91ba08d9))
  (gr_text "Without refilling, \nthe inner zone \nis located too close" (at 122.336593 82.233874 0) (layer "Cmts.User") (tstamp 5bc9367f-80d0-476c-b99a-6c82dbf59fbc)
    (effects (font (size 0.5 0.5) (thickness 0.125)) (justify left bottom))
  )

  (via (at 131.802386 81.865308) (size 0.6) (drill 0.3) (layers "F.Cu" "B.Cu") (free) (net 2) (tstamp 2b81f30e-99a8-4345-9760-67513bc8c623))

  (zone (net 1) (net_name "A") (layer "F.Cu") (tstamp fd8091a8-712f-4b09-81f9-cc203a1012a5) (hatch edge 0.5)
    (priority 1)
    (connect_pads (clearance 0.5))
    (min_thickness 0.25) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.5) (thermal_bridge_width 0.5))
    (polygon
      (pts
        (xy 122.70769 82.894624)
        (xy 127.682194 82.784966)
        (xy 127.742008 89.07537)
        (xy 122.647877 88.736426)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 127.624117 82.805935)
        (xy 127.671025 82.857718)
        (xy 127.683387 82.910524)
        (xy 127.706143 85.303649)
        (xy 127.687097 85.370873)
        (xy 127.634731 85.417127)
        (xy 127.56567 85.427728)
        (xy 127.501841 85.399309)
        (xy 127.465967 85.348161)
        (xy 127.413944 85.20868)
        (xy 127.41394 85.208673)
        (xy 127.32778 85.093579)
        (xy 127.327777 85.093576)
        (xy 127.212683 85.007416)
        (xy 127.212676 85.007412)
        (xy 127.077969 84.95717)
        (xy 127.077962 84.957168)
        (xy 127.018434 84.950767)
        (xy 126.72059 84.950767)
        (xy 126.72059 86.950767)
        (xy 127.018418 86.950767)
        (xy 127.018434 86.950766)
        (xy 127.077962 86.944365)
        (xy 127.077969 86.944363)
        (xy 127.212676 86.894121)
        (xy 127.212683 86.894117)
        (xy 127.327777 86.807957)
        (xy 127.32778 86.807954)
        (xy 127.41394 86.69286)
        (xy 127.413944 86.692853)
        (xy 127.464186 86.558146)
        (xy 127.464188 86.558139)
        (xy 127.470358 86.500757)
        (xy 127.497096 86.436205)
        (xy 127.554488 86.396357)
        (xy 127.624313 86.393864)
        (xy 127.684402 86.429517)
        (xy 127.715677 86.491996)
        (xy 127.717641 86.512833)
        (xy 127.740735 88.941582)
        (xy 127.721689 89.008806)
        (xy 127.669323 89.05506)
        (xy 127.608509 89.066487)
        (xy 122.764839 88.744208)
        (xy 122.699254 88.720117)
        (xy 122.657106 88.664391)
        (xy 122.649077 88.619212)
        (xy 122.66616 86.950766)
        (xy 122.673839 86.200767)
        (xy 125.47059 86.200767)
        (xy 125.47059 86.498611)
        (xy 125.476991 86.558139)
        (xy 125.476993 86.558146)
        (xy 125.527235 86.692853)
        (xy 125.527239 86.69286)
        (xy 125.613399 86.807954)
        (xy 125.613402 86.807957)
        (xy 125.728496 86.894117)
        (xy 125.728503 86.894121)
        (xy 125.86321 86.944363)
        (xy 125.863217 86.944365)
        (xy 125.922745 86.950766)
        (xy 125.922762 86.950767)
        (xy 126.220589 86.950767)
        (xy 126.22059 86.200767)
        (xy 125.47059 86.200767)
        (xy 122.673839 86.200767)
        (xy 122.678958 85.700767)
        (xy 125.47059 85.700767)
        (xy 126.220589 85.700767)
        (xy 126.22059 84.950767)
        (xy 125.922745 84.950767)
        (xy 125.863217 84.957168)
        (xy 125.86321 84.95717)
        (xy 125.728503 85.007412)
        (xy 125.728496 85.007416)
        (xy 125.613402 85.093576)
        (xy 125.613399 85.093579)
        (xy 125.527239 85.208673)
        (xy 125.527235 85.20868)
        (xy 125.476993 85.343387)
        (xy 125.476991 85.343394)
        (xy 125.47059 85.402922)
        (xy 125.47059 85.700767)
        (xy 122.678958 85.700767)
        (xy 122.70646 83.014675)
        (xy 122.72683 82.947844)
        (xy 122.7801 82.902632)
        (xy 122.82772 82.891978)
        (xy 127.55666 82.787733)
      )
    )
  )
  (zone (net 2) (net_name "B") (layers "F&B.Cu") (tstamp cbf3ce30-83db-44ed-be70-44935fea7e57) (hatch edge 0.5)
    (connect_pads (clearance 0.5))
    (min_thickness 0.25) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.5) (thermal_bridge_width 0.5))
    (polygon
      (pts
        (xy 118.583563 79.871518)
        (xy 118.513781 92.023664)
        (xy 135.590585 92.063539)
        (xy 135.002417 80.190525)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 133.627503 81.069592)
        (xy 133.673258 81.122396)
        (xy 133.684464 81.173907)
        (xy 133.684464 90.472051)
        (xy 133.664779 90.53909)
        (xy 133.611975 90.584845)
        (xy 133.560464 90.596051)
        (xy 120.813064 90.596051)
        (xy 120.746025 90.576366)
        (xy 120.70027 90.523562)
        (xy 120.689064 90.472051)
        (xy 120.689064 88.6215)
        (xy 122.056899 88.6215)
        (xy 122.064671 88.715117)
        (xy 122.072702 88.760304)
        (xy 122.097652 88.850836)
        (xy 122.097653 88.850838)
        (xy 122.097654 88.850841)
        (xy 122.167233 88.976779)
        (xy 122.167235 88.976782)
        (xy 122.167237 88.976785)
        (xy 122.189915 89.006769)
        (xy 122.209381 89.032505)
        (xy 122.209388 89.032515)
        (xy 122.311632 89.133746)
        (xy 122.438254 89.202072)
        (xy 122.503839 89.226163)
        (xy 122.644575 89.256047)
        (xy 127.488245 89.578326)
        (xy 127.488246 89.578325)
        (xy 127.488248 89.578326)
        (xy 127.522856 89.576259)
        (xy 127.615155 89.570747)
        (xy 127.675969 89.55932)
        (xy 127.796972 89.520317)
        (xy 127.917267 89.441382)
        (xy 127.940871 89.420532)
        (xy 127.969625 89.395136)
        (xy 127.969636 89.395125)
        (xy 128.062819 89.285496)
        (xy 128.121342 89.154055)
        (xy 128.140388 89.086831)
        (xy 128.159508 88.94423)
        (xy 128.157329 88.715115)
        (xy 128.136313 86.504804)
        (xy 128.135208 86.483479)
        (xy 128.132242 86.452014)
        (xy 128.132241 86.452014)
        (xy 128.126441 86.409695)
        (xy 128.081003 86.273178)
        (xy 128.049728 86.210699)
        (xy 128.049724 86.210693)
        (xy 127.967674 86.092518)
        (xy 127.96767 86.092514)
        (xy 127.864369 86.009269)
        (xy 127.824521 85.951877)
        (xy 127.822027 85.882052)
        (xy 127.857679 85.821963)
        (xy 127.874151 85.809042)
        (xy 127.882675 85.803449)
        (xy 127.909751 85.779533)
        (xy 127.935033 85.757203)
        (xy 127.935044 85.757192)
        (xy 128.028227 85.647563)
        (xy 128.08675 85.516122)
        (xy 128.105796 85.448898)
        (xy 128.124916 85.306296)
        (xy 128.10216 82.913171)
        (xy 128.088876 82.802755)
        (xy 128.076514 82.749949)
        (xy 128.0394 82.645096)
        (xy 127.958963 82.5258)
        (xy 127.954746 82.521145)
        (xy 127.924494 82.487748)
        (xy 127.919398 82.4801)
        (xy 127.801268 82.382215)
        (xy 127.801267 82.382214)
        (xy 127.669103 82.325344)
        (xy 127.669102 82.325343)
        (xy 127.669096 82.325341)
        (xy 127.669094 82.32534)
        (xy 127.601645 82.307141)
        (xy 127.458816 82.28981)
        (xy 127.458815 82.28981)
        (xy 123.267027 82.382214)
        (xy 122.729875 82.394055)
        (xy 122.729861 82.394056)
        (xy 122.630671 82.406123)
        (xy 122.630635 82.406129)
        (xy 122.583048 82.416776)
        (xy 122.583034 82.416779)
        (xy 122.583029 82.416781)
        (xy 122.583022 82.416783)
        (xy 122.583023 82.416783)
        (xy 122.488125 82.44814)
        (xy 122.366292 82.524685)
        (xy 122.313028 82.569892)
        (xy 122.31301 82.569909)
        (xy 122.217699 82.677654)
        (xy 122.217693 82.677663)
        (xy 122.15659 82.807909)
        (xy 122.156587 82.807916)
        (xy 122.156588 82.807916)
        (xy 122.136218 82.874747)
        (xy 122.136216 82.874755)
        (xy 122.12896 82.921791)
        (xy 122.114282 83.016951)
        (xy 122.08678 85.703046)
        (xy 122.085536 85.824418)
        (xy 122.081661 86.203046)
        (xy 122.056899 88.621498)
        (xy 122.056899 88.6215)
        (xy 120.689064 88.6215)
        (xy 120.689064 81.173907)
        (xy 120.708749 81.106868)
        (xy 120.761553 81.061113)
        (xy 120.813064 81.049907)
        (xy 133.560464 81.049907)
      )
    )
    (filled_polygon
      (layer "B.Cu")
      (pts
        (xy 133.627503 81.069592)
        (xy 133.673258 81.122396)
        (xy 133.684464 81.173907)
        (xy 133.684464 90.472051)
        (xy 133.664779 90.53909)
        (xy 133.611975 90.584845)
        (xy 133.560464 90.596051)
        (xy 120.813064 90.596051)
        (xy 120.746025 90.576366)
        (xy 120.70027 90.523562)
        (xy 120.689064 90.472051)
        (xy 120.689064 85.554594)
        (xy 127.622292 85.554594)
        (xy 127.622292 85.852438)
        (xy 127.628693 85.911966)
        (xy 127.628695 85.911973)
        (xy 127.678937 86.04668)
        (xy 127.678941 86.046687)
        (xy 127.765101 86.161781)
        (xy 127.765104 86.161784)
        (xy 127.880198 86.247944)
        (xy 127.880205 86.247948)
        (xy 128.014912 86.29819)
        (xy 128.014919 86.298192)
        (xy 128.074447 86.304593)
        (xy 128.074464 86.304594)
        (xy 128.372292 86.304594)
        (xy 128.372292 85.554594)
        (xy 128.872292 85.554594)
        (xy 128.872292 86.304594)
        (xy 129.17012 86.304594)
        (xy 129.170136 86.304593)
        (xy 129.229664 86.298192)
        (xy 129.229671 86.29819)
        (xy 129.364378 86.247948)
        (xy 129.364385 86.247944)
        (xy 129.479479 86.161784)
        (xy 129.479482 86.161781)
        (xy 129.565642 86.046687)
        (xy 129.565646 86.04668)
        (xy 129.615888 85.911973)
        (xy 129.61589 85.911966)
        (xy 129.622291 85.852438)
        (xy 129.622292 85.852421)
        (xy 129.622292 85.554594)
        (xy 128.872292 85.554594)
        (xy 128.372292 85.554594)
        (xy 127.622292 85.554594)
        (xy 120.689064 85.554594)
        (xy 120.689064 85.054594)
        (xy 127.622292 85.054594)
        (xy 128.372292 85.054594)
        (xy 128.372292 84.304594)
        (xy 128.872292 84.304594)
        (xy 128.872292 85.054594)
        (xy 129.622292 85.054594)
        (xy 129.622292 84.756766)
        (xy 129.622291 84.756749)
        (xy 129.61589 84.697221)
        (xy 129.615888 84.697214)
        (xy 129.565646 84.562507)
        (xy 129.565642 84.5625)
        (xy 129.479482 84.447406)
        (xy 129.479479 84.447403)
        (xy 129.364385 84.361243)
        (xy 129.364378 84.361239)
        (xy 129.229671 84.310997)
        (xy 129.229664 84.310995)
        (xy 129.170136 84.304594)
        (xy 128.872292 84.304594)
        (xy 128.372292 84.304594)
        (xy 128.074447 84.304594)
        (xy 128.014919 84.310995)
        (xy 128.014912 84.310997)
        (xy 127.880205 84.361239)
        (xy 127.880198 84.361243)
        (xy 127.765104 84.447403)
        (xy 127.765101 84.447406)
        (xy 127.678941 84.5625)
        (xy 127.678937 84.562507)
        (xy 127.628695 84.697214)
        (xy 127.628693 84.697221)
        (xy 127.622292 84.756749)
        (xy 127.622292 85.054594)
        (xy 120.689064 85.054594)
        (xy 120.689064 81.173907)
        (xy 120.708749 81.106868)
        (xy 120.761553 81.061113)
        (xy 120.813064 81.049907)
        (xy 133.560464 81.049907)
      )
    )
  )
)
