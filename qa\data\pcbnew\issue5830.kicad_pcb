(kicad_pcb
	(version 20241228)
	(generator "pcbnew")
	(generator_version "9.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(title_block
		(title "I2C soil moisture sensor")
		(date "2020-09-29")
		(rev "2.7.9")
		(company "Catnip electronics")
		(comment 1 "<EMAIL>")
	)
	(layers
		(0 "F.Cu" signal)
		(4 "In1.Cu" signal)
		(6 "In2.Cu" signal)
		(2 "B.Cu" signal)
		(9 "F.Adhes" user "F.Adhesive")
		(11 "B.Adhes" user "B.Adhesive")
		(13 "F.Paste" user)
		(15 "B.Paste" user)
		(5 "F.SilkS" user "F.Silkscreen")
		(7 "B.SilkS" user "B.Silkscreen")
		(1 "F.Mask" user)
		(3 "B.Mask" user)
		(17 "Dwgs.User" user "User.Drawings")
		(19 "Cmts.User" user "User.Comments")
		(21 "Eco1.User" user "User.Eco1")
		(23 "Eco2.User" user "User.Eco2")
		(25 "Edge.Cuts" user)
		(27 "Margin" user)
		(31 "F.CrtYd" user "F.Courtyard")
		(29 "B.CrtYd" user "B.Courtyard")
		(35 "F.Fab" user)
		(33 "B.Fab" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(tenting front back)
		(aux_axis_origin 86 13)
		(grid_origin 96.3 30.2)
		(pcbplotparams
			(layerselection 0x55555555_5755f5ff)
			(plot_on_all_layers_selection 0x00000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes no)
			(usegerberadvancedattributes no)
			(creategerberjobfile no)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 6)
			(plotframeref no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(pdf_metadata yes)
			(pdf_single_document no)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(plotpadnumbers no)
			(hidednponfab no)
			(sketchdnponfab yes)
			(crossoutdnponfab yes)
			(subtractmaskfromsilk yes)
			(outputformat 1)
			(mirror no)
			(drillshape 0)
			(scaleselection 1)
			(outputdirectory "mfgset/")
		)
	)
	(net 0 "")
	(net 1 "Net-(C1-Pad1)")
	(net 2 "GND")
	(net 3 "Net-(C2-Pad1)")
	(net 4 "VCC")
	(net 5 "/SENSE_LOW")
	(net 6 "/SENSOR_TRACK")
	(net 7 "/THERMISTOR")
	(net 8 "/RESET")
	(net 9 "/EXCITATION")
	(net 10 "Net-(D3-Pad1)")
	(net 11 "/SCK/SCL")
	(net 12 "/MOSI/SDA")
	(net 13 "/MISO/SENSE_HIGH")
	(net 14 "Net-(IC1-Pad11)")
	(net 15 "Net-(IC1-Pad12)")
	(footprint "Crystals:Crystal_SMD_3225-4pin_3.2x2.5mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e0abe2")
		(at 91.25 26 90)
		(descr "SMD Crystal SERIES SMD3225/4 http://www.txccrystal.com/images/pdf/7m-accuracy.pdf, 3.2x2.5mm^2 package")
		(tags "SMD SMT crystal")
		(property "Reference" "X1"
			(at 0 -3.25 90)
			(layer "F.SilkS")
			(uuid "4249bb3b-44a3-4294-9235-c5d4102b5f6e")
			(effects
				(font
					(size 0.7 0.7)
					(thickness 0.11)
				)
			)
		)
		(property "Value" "16MHz"
			(at 0 -0.75 90)
			(layer "F.Fab")
			(uuid "630efe6c-1a8c-4929-a1b3-5fba61c65985")
			(effects
				(font
					(size 0.6 0.6)
					(thickness 0.11)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 90)
			(layer "F.Fab")
			(hide yes)
			(uuid "a8a047a2-6697-4847-91a1-49c61b86d126")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 90)
			(layer "F.Fab")
			(hide yes)
			(uuid "d301ccab-3786-4e5b-94d6-f1c027dcc1a0")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d213ea")
		(attr smd)
		(fp_line
			(start -2 -1.65)
			(end -2 1.65)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8ee38563-5c2c-4534-9a10-5eff844dc056")
		)
		(fp_line
			(start -2 1.65)
			(end 2 1.65)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9b2ab11e-fc73-42b0-8768-ffc5864e221b")
		)
		(fp_line
			(start 2.1 -1.7)
			(end -2.1 -1.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "24d1e402-3b7d-4d27-be3b-f1af25b434dc")
		)
		(fp_line
			(start -2.1 -1.7)
			(end -2.1 1.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "9dca9cea-d889-4bab-9a5a-f9a013f57179")
		)
		(fp_line
			(start 2.1 1.7)
			(end 2.1 -1.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2f649982-4c5a-4995-8ba1-c9d3cbe694c4")
		)
		(fp_line
			(start -2.1 1.7)
			(end 2.1 1.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "c76264c8-a2dd-4fe6-a75b-591179721da1")
		)
		(fp_line
			(start 1.6 -1.25)
			(end -1.6 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "34ae9f16-2627-4f1c-a870-4cc814cfeb21")
		)
		(fp_line
			(start -1.6 -1.25)
			(end -1.6 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "487f00d9-2d1a-4175-a718-30890f47e2c6")
		)
		(fp_line
			(start -1.6 0.25)
			(end -0.6 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ac5d0a30-296a-4fc0-aaca-199c84aeced8")
		)
		(fp_line
			(start 1.6 1.25)
			(end 1.6 -1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "31e23c07-7282-4cee-b13f-a28179432936")
		)
		(fp_line
			(start -1.6 1.25)
			(end 1.6 1.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "03730976-a029-4148-aced-0781f5f5099e")
		)
		(fp_text user "${REFERENCE}"
			(at 0.1 0.65 90)
			(layer "F.Fab")
			(uuid "d10570fc-f037-4d68-b98e-6b75005009b6")
			(effects
				(font
					(size 0.7 0.7)
					(thickness 0.105)
				)
			)
		)
		(pad "1" smd rect
			(at -1.1 0.85 90)
			(size 1.4 1.2)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 1 "Net-(C1-Pad1)")
			(uuid "30b75131-edac-45a5-ae60-eccab3dc8954")
		)
		(pad "2" smd rect
			(at 1.1 0.85 90)
			(size 1.4 1.2)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 2 "GND")
			(uuid "3ab829ed-d584-4751-96c2-cd4c2f073338")
		)
		(pad "3" smd rect
			(at 1.1 -0.85 90)
			(size 1.4 1.2)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 3 "Net-(C2-Pad1)")
			(uuid "3be0e8f1-aacd-41ce-993d-1e79fba666da")
		)
		(pad "4" smd rect
			(at -1.1 -0.85 90)
			(size 1.4 1.2)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 2 "GND")
			(uuid "4baccfe9-2e0e-4ecc-9db2-46a81f92160f")
		)
		(embedded_fonts no)
		(model "${KIPRJMOD}/3d/CRY_SMD3225.STEP"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz -90 0 0)
			)
		)
	)
	(footprint "Diode_SMD:D_SOD-523"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e211a5")
		(at 95.804 40.842)
		(descr "http://www.diodes.com/datasheets/ap02001.pdf p.144")
		(tags "Diode SOD523")
		(property "Reference" "D2"
			(at 0 1.458 180)
			(layer "F.SilkS")
			(uuid "57e8dc4d-e468-4c32-b19b-edaa907296bd")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "BAT54"
			(at -1.296 -2.858 180)
			(layer "F.Fab")
			(uuid "1a32d92f-8b0c-46f4-b305-d8450fda61b0")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "63bef58d-d414-4d17-8e41-bb0de32493a8")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "d6456799-0b4a-44de-b246-79a2f2a39e56")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d22068")
		(attr smd)
		(fp_line
			(start -1.15 -0.6)
			(end -1.15 0.6)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3dbd9049-76fa-47c6-af3d-54f1f5ab4e00")
		)
		(fp_line
			(start 0.7 -0.6)
			(end -1.15 -0.6)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "13ab1948-7c31-4488-b8d1-************")
		)
		(fp_line
			(start 0.7 0.6)
			(end -1.15 0.6)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cbfb6329-9c54-426a-b513-b0c2e80db9b4")
		)
		(fp_line
			(start -1.25 -0.7)
			(end 1.25 -0.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "605478ec-b821-4129-9eb1-a0bd5ef35e46")
		)
		(fp_line
			(start -1.25 0.7)
			(end -1.25 -0.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "81e781d2-77fb-402e-ab89-9fc13825e501")
		)
		(fp_line
			(start 1.25 -0.7)
			(end 1.25 0.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "dd4058be-40d2-4e01-855c-319bdedb5dd2")
		)
		(fp_line
			(start 1.25 0.7)
			(end -1.25 0.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b6b4ef9a-9cd5-4892-bed8-852c1e362edd")
		)
		(fp_line
			(start -0.65 -0.45)
			(end 0.65 -0.45)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d2f6bcbd-c6c1-47db-ae8e-2d5eb3450917")
		)
		(fp_line
			(start -0.65 0.45)
			(end -0.65 -0.45)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8f0cfb96-bf50-4286-be78-c3fa015b7d65")
		)
		(fp_line
			(start -0.2 0)
			(end -0.35 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "85dfa14f-ee84-4c66-bfea-ec6cda107985")
		)
		(fp_line
			(start -0.2 0)
			(end 0.1 0.2)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9fa2c4fd-1688-491b-93ac-6a78f46bd184")
		)
		(fp_line
			(start -0.2 0.2)
			(end -0.2 -0.2)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "7edb0f7a-14bc-4916-8245-c2204b105bbe")
		)
		(fp_line
			(start 0.1 -0.2)
			(end -0.2 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5060e344-885a-4c2a-beb0-a6a04af9cae1")
		)
		(fp_line
			(start 0.1 0)
			(end 0.25 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "de71d645-b8b4-490c-9bb8-c24f6eee5cf8")
		)
		(fp_line
			(start 0.1 0.2)
			(end 0.1 -0.2)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f7b71ab4-6c57-4d2c-b6f8-a7d0353ee649")
		)
		(fp_line
			(start 0.65 -0.45)
			(end 0.65 0.45)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d5b6757a-0a10-4b45-924c-a1d9890dde66")
		)
		(fp_line
			(start 0.65 0.45)
			(end -0.65 0.45)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "140597c2-b471-4de3-bbe2-ca99bcdeea38")
		)
		(fp_text user "${REFERENCE}"
			(at 0 -1.3 180)
			(layer "F.Fab")
			(uuid "31cf0718-084e-4c2f-9da2-6b3ae46b7cb2")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" smd rect
			(at 0.7 0 180)
			(size 0.6 0.7)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 6 "/SENSOR_TRACK")
			(uuid "c70ccc72-c4e1-4928-bd12-254b419385c6")
		)
		(pad "2" smd rect
			(at -0.7 0 180)
			(size 0.6 0.7)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 13 "/MISO/SENSE_HIGH")
			(uuid "50d48606-2333-45d6-b1c9-13aae24aa73f")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Diode_SMD.3dshapes/D_SOD-523.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Diode_SMD:D_SOD-523"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e2166c")
		(at 93.01 40.842)
		(descr "http://www.diodes.com/datasheets/ap02001.pdf p.144")
		(tags "Diode SOD523")
		(property "Reference" "D1"
			(at 0 1.458 180)
			(layer "F.SilkS")
			(uuid "3686a255-3edf-4edd-9a3a-b39a1529554f")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "BAT54"
			(at 1.11 -2.858 180)
			(layer "F.Fab")
			(uuid "c2df4145-1dc3-4ca1-8845-d1c0cbd3eb91")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "a386611d-0b6f-455e-b7b1-fa02061c592b")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "32e0ec79-e00b-4b8f-9ae3-908b0ee34b14")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d21fcd")
		(attr smd)
		(fp_line
			(start -1.15 -0.6)
			(end -1.15 0.6)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "758e91ee-92ee-4a27-b12d-1003e80bcbd9")
		)
		(fp_line
			(start 0.7 -0.6)
			(end -1.15 -0.6)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "36c5f6b9-eb69-4346-8c0a-659cb3a2e50c")
		)
		(fp_line
			(start 0.7 0.6)
			(end -1.15 0.6)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2e4c6344-e485-4ee1-b522-518bd275525c")
		)
		(fp_line
			(start -1.25 -0.7)
			(end 1.25 -0.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "56f400e2-dab2-4662-9452-0119958684fb")
		)
		(fp_line
			(start -1.25 0.7)
			(end -1.25 -0.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "13a3e326-7b97-464a-9afa-5f086705a621")
		)
		(fp_line
			(start 1.25 -0.7)
			(end 1.25 0.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5e07b171-31ac-4ae2-8635-f04925920f9b")
		)
		(fp_line
			(start 1.25 0.7)
			(end -1.25 0.7)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "883fd6d2-9d22-4d9d-94fe-4e80aba465e3")
		)
		(fp_line
			(start -0.65 -0.45)
			(end 0.65 -0.45)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6622a5f9-32ba-41e4-85cd-4933f0b1e78c")
		)
		(fp_line
			(start -0.65 0.45)
			(end -0.65 -0.45)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1528900b-c88b-4ace-82e4-73a11938d40a")
		)
		(fp_line
			(start -0.2 0)
			(end -0.35 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "75f7f36e-111b-4a65-a5ca-918eba9aa2c1")
		)
		(fp_line
			(start -0.2 0)
			(end 0.1 0.2)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b490cd7f-69fe-4a85-b790-9eb1508ef4f7")
		)
		(fp_line
			(start -0.2 0.2)
			(end -0.2 -0.2)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3c065ac2-601c-46b2-bed8-49aef9b798c3")
		)
		(fp_line
			(start 0.1 -0.2)
			(end -0.2 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a7944428-f1d2-4555-bb6e-e3f555d0445f")
		)
		(fp_line
			(start 0.1 0)
			(end 0.25 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3754e345-1bf7-4fa9-8634-7dac4c239097")
		)
		(fp_line
			(start 0.1 0.2)
			(end 0.1 -0.2)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "db63edab-f1c3-4d8b-b755-5cb9ab1ef21a")
		)
		(fp_line
			(start 0.65 -0.45)
			(end 0.65 0.45)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8c267ca3-8573-4c47-a96f-602e2e1f2ddf")
		)
		(fp_line
			(start 0.65 0.45)
			(end -0.65 0.45)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a8531faf-4dde-4355-a4a1-5105acd9b6d6")
		)
		(fp_text user "${REFERENCE}"
			(at 0 -1.3 180)
			(layer "F.Fab")
			(uuid "824da308-e4fa-44d6-b0bd-0d50249b5dcb")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" smd rect
			(at 0.7 0 180)
			(size 0.6 0.7)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 5 "/SENSE_LOW")
			(uuid "40beb6d9-660e-4b2c-9679-e0e015182c2f")
		)
		(pad "2" smd rect
			(at -0.7 0 180)
			(size 0.6 0.7)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(net 6 "/SENSOR_TRACK")
			(uuid "7a17ede0-90f2-431a-99f5-673a70a293cd")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Diode_SMD.3dshapes/D_SOD-523.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Capacitor_SMD:C_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e23d66")
		(at 91.25 28.9 180)
		(descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "capacitor")
		(property "Reference" "C1"
			(at 3.75 0 180)
			(layer "F.SilkS")
			(uuid "6e7a7e8b-8025-4eca-b712-ad2f9b1cd3fa")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "33p"
			(at 2.35 0 180)
			(layer "F.Fab")
			(uuid "7fa90fa5-1de3-4f84-9f51-c543700f54dd")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "a87dabf9-a092-425a-8bc2-8dbf16c0c37d")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "57c5b875-e104-4b01-af74-96eca5752bf6")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d21881")
		(attr smd)
		(fp_line
			(start -0.14058 0.51)
			(end 0.14058 0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e3a904c3-0219-457f-93fb-b993a3be67d7")
		)
		(fp_line
			(start -0.14058 -0.51)
			(end 0.14058 -0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b119dbbc-ef1d-4440-af87-c462d8349382")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "25eaff60-dba7-4e6b-9cc0-9cf2181b316a")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b8c5519d-1dfa-4273-af5c-010c0fd96983")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a0a69559-4f83-43a0-840b-532e6b10fc5b")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1aa7f6ce-87b0-4c12-ae4d-6660e3118cb3")
		)
		(fp_line
			(start 0.8 0.4)
			(end -0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b990cdf7-66d3-4c90-a1e3-cd21c263ee55")
		)
		(fp_line
			(start 0.8 -0.4)
			(end 0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9a6f5683-deaf-42f2-ae26-9b737de7f1fd")
		)
		(fp_line
			(start -0.8 0.4)
			(end -0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5db3cc12-a7bd-462d-8340-1c7ab5315da7")
		)
		(fp_line
			(start -0.8 -0.4)
			(end 0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "cc08f992-3f4b-4a7c-9f0b-713679a6c2a5")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 180)
			(layer "F.Fab")
			(uuid "630d1077-fe0e-4376-996d-9e0df0586be9")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.775 0 180)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 1 "Net-(C1-Pad1)")
			(uuid "2d57da7b-972d-484f-bd56-abafb70319f7")
		)
		(pad "2" smd roundrect
			(at 0.775 0 180)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 2 "GND")
			(uuid "744cbc6b-f2a6-436d-bb8c-56928bdb73fa")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Capacitor_SMD:C_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e23d6b")
		(at 91.05 23.1)
		(descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "capacitor")
		(property "Reference" "C2"
			(at -0.25 -1.1 0)
			(layer "F.SilkS")
			(uuid "cd309038-cd3e-45e4-92b8-02fe8734ee1a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "33pp"
			(at -2.75 0 0)
			(layer "F.Fab")
			(uuid "b3a04cc1-80f8-4b73-a72e-7b8734530c31")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "6430e156-eb34-4681-a674-9ce0d8a34240")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "ff1082dc-c033-452c-b97d-012cb7b5f0d6")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d218de")
		(attr smd)
		(fp_line
			(start -0.14058 -0.51)
			(end 0.14058 -0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8a86e1b0-98d1-435a-97bb-9e78d7907e5e")
		)
		(fp_line
			(start -0.14058 0.51)
			(end 0.14058 0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "34a8ebba-dcd5-45f3-930a-592baa46ba52")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a94b86b1-3cfa-4858-9562-c263e940c381")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "8b4b11ba-3d12-48fc-be42-0e76100a901b")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "682f1dcf-449a-413c-8585-46757ed2f419")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b4b9d37b-adb5-4236-9632-87a96c8879cc")
		)
		(fp_line
			(start -0.8 -0.4)
			(end 0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "68338612-2b7a-4413-bc5c-abc0063f1905")
		)
		(fp_line
			(start -0.8 0.4)
			(end -0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "086e6180-4638-45b6-9f13-948d8ce8d83b")
		)
		(fp_line
			(start 0.8 -0.4)
			(end 0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9fe350fc-46e4-46cc-8f10-562fdbd708eb")
		)
		(fp_line
			(start 0.8 0.4)
			(end -0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6e412ee0-f74f-443f-a331-bf3b99f96b80")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "b0e8e120-7ff2-44a5-bcc3-fed1b6f4ec89")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.775 0)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 3 "Net-(C2-Pad1)")
			(uuid "e9a7bcd9-3f31-4262-8746-0bee8226a81e")
		)
		(pad "2" smd roundrect
			(at 0.775 0)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 2 "GND")
			(uuid "f2cd211e-01c3-4a1a-9309-d37e0d8c80a0")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Capacitor_SMD:C_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e263fc")
		(at 94.90234 22.577514)
		(descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "capacitor")
		(property "Reference" "C3"
			(at 0 -1.43 0)
			(layer "F.SilkS")
			(uuid "b589f7b7-f9cf-422a-8712-db21dbaf6e3e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1u"
			(at -1.80234 0.022486 0)
			(layer "F.Fab")
			(uuid "a9631be3-bec0-413a-97c8-fbfac9489a56")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "16ecfaa7-72f3-48ec-8d4b-f90a9424b764")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "1340e8a9-13d0-4d7e-9781-84979fbd0d09")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d21e5c")
		(attr smd)
		(fp_line
			(start -0.14058 -0.51)
			(end 0.14058 -0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "977b4ae1-71b5-415c-8803-61ab5ec7b945")
		)
		(fp_line
			(start -0.14058 0.51)
			(end 0.14058 0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d8cbd62e-d783-4d58-b542-dd52adef669d")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d8a779b9-3296-4616-adb5-7060f787340a")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "64743429-26b9-4695-a7ba-639e832225b3")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "bf928718-ea4f-4bc2-99c7-801bd5be70ce")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e49cb9a5-8441-44c8-8c4e-9079e5cee936")
		)
		(fp_line
			(start -0.8 -0.4)
			(end 0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c7ff6f99-3991-4329-a7d5-cd98f57df7f6")
		)
		(fp_line
			(start -0.8 0.4)
			(end -0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "66c4753e-e460-4251-9b6d-54d478d028e7")
		)
		(fp_line
			(start 0.8 -0.4)
			(end 0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "77d75b07-4923-4ec9-a0fc-ab25cbb8c286")
		)
		(fp_line
			(start 0.8 0.4)
			(end -0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "dd7b5059-c81f-4aa3-b679-f5997974c7cf")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "33faac90-2822-451d-ad42-080a701416c2")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.775 0)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 4 "VCC")
			(uuid "ad3b3a12-231b-44a2-baa6-8a25b3ec5091")
		)
		(pad "2" smd roundrect
			(at 0.775 0)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 2 "GND")
			(uuid "0535b049-b7aa-493c-badb-eb0c255f0a56")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Capacitor_SMD:C_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e26406")
		(at 95.8 38.8)
		(descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "capacitor")
		(property "Reference" "C5"
			(at -5.5 0 0)
			(layer "F.SilkS")
			(uuid "1003c8ad-6a88-4e21-bc0e-09674c2adc4f")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1n"
			(at 1.85 -0.05 0)
			(layer "F.Fab")
			(uuid "e9e045b1-344e-4998-8959-7823b80c4c71")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "fe48ca67-1ca1-4980-a5e1-ce32210e84cf")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "70499952-8a04-46c9-bf21-797edae30152")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d22484")
		(attr smd)
		(fp_line
			(start -0.14058 -0.51)
			(end 0.14058 -0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2bedebd9-3bda-48bd-bc83-97ca3787256d")
		)
		(fp_line
			(start -0.14058 0.51)
			(end 0.14058 0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b31120b1-ad3e-4b56-91db-4a6c0a262437")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2d9d30f4-25d3-48a3-9284-c48a04ff760c")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b8311f5a-77b2-4287-a3e9-23be3a62cf87")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "eadc09a0-a0e8-4d9b-b8a3-46ed326b3d55")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "104667eb-6acc-490a-80ed-d4531af30d20")
		)
		(fp_line
			(start -0.8 -0.4)
			(end 0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "5f49c1a9-e4ee-4336-9764-ff222d0d0aeb")
		)
		(fp_line
			(start -0.8 0.4)
			(end -0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0260c4b1-7e25-4f82-b7b5-afa938f5a1fb")
		)
		(fp_line
			(start 0.8 -0.4)
			(end 0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c20a26a1-a127-4d88-b148-0ee1becd9022")
		)
		(fp_line
			(start 0.8 0.4)
			(end -0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c70e1d61-634f-468d-a9f9-fe073aad9366")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "fcfd68f7-92a2-459b-bbe5-a8f6f0aeb69d")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.775 0)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 13 "/MISO/SENSE_HIGH")
			(uuid "1bd771cf-a11d-452a-9670-5d2050d7d725")
		)
		(pad "2" smd roundrect
			(at 0.775 0)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 2 "GND")
			(uuid "001f9868-fbb1-4d9a-a84b-63477863ae23")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Capacitor_SMD:C_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e2640b")
		(at 91.2 30.4 180)
		(descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "capacitor")
		(property "Reference" "C6"
			(at 3.7 0 180)
			(layer "F.SilkS")
			(uuid "06ee912b-2416-4854-84df-43cb64f7818c")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1u"
			(at 1.8 0 180)
			(layer "F.Fab")
			(uuid "ff3cd314-55ee-45ab-9823-8ef2480002e0")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "d4ab6fa4-b579-4a97-9bcf-63c827d04379")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "85305dc5-dbb7-40c4-8155-5a3760b65b10")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054df1d15")
		(attr smd)
		(fp_line
			(start -0.14058 0.51)
			(end 0.14058 0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b9370e54-c142-460c-8e09-b3099a70669d")
		)
		(fp_line
			(start -0.14058 -0.51)
			(end 0.14058 -0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4f802d2d-aab0-4a14-b97c-d237c6f40356")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "fa9577b9-6363-40ce-b58a-34ab9a4ca201")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e6d881ab-418c-4b19-94b5-d9e82da16d40")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3792abae-4ea8-4788-aeed-95bbaf4935e9")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1bce4d30-43bb-4c01-b97d-ac782831f73b")
		)
		(fp_line
			(start 0.8 0.4)
			(end -0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9f0d4970-bfef-4c14-9f57-04fd0348b028")
		)
		(fp_line
			(start 0.8 -0.4)
			(end 0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "cf54d16d-e094-449d-a488-9e591ee121db")
		)
		(fp_line
			(start -0.8 0.4)
			(end -0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "84a2595f-f0cb-42ea-b098-a043093fa84a")
		)
		(fp_line
			(start -0.8 -0.4)
			(end 0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "57fc5f18-ed72-4f63-bed1-5351771aeddc")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 180)
			(layer "F.Fab")
			(uuid "ba5d29cb-a461-4442-8aa7-66936efd18bf")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.775 0 180)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 8 "/RESET")
			(uuid "1f12f485-b502-40c2-9cac-60bcd22e0190")
		)
		(pad "2" smd roundrect
			(at 0.775 0 180)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 2 "GND")
			(uuid "ab91c980-5bc0-4829-bda6-c91889bea4af")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Resistor_SMD:R_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e26415")
		(at 91.2 32 180)
		(descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R1"
			(at 3.5 0 180)
			(layer "F.SilkS")
			(uuid "5fad7d1e-a881-43f1-a740-be5c6598a218")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "10k"
			(at 2.2 0 180)
			(layer "F.Fab")
			(uuid "54f48c18-b78d-4a9f-9707-eb209234fbad")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "95888a02-207d-4e6b-b9f8-b746431aed73")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "549ac3d5-3d87-413c-b536-8417aa1ef82f")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054df19bf")
		(attr smd)
		(fp_line
			(start -0.237258 0.5225)
			(end 0.237258 0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7f302c7b-6066-4aab-a07c-0123779a88d8")
		)
		(fp_line
			(start -0.237258 -0.5225)
			(end 0.237258 -0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b3711c7c-aff5-4754-b968-9ca5849c89c4")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "00f85380-cebc-490a-9a6f-0103d7b63783")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "9313d7f5-36ca-42cc-90e7-66fc7254dff1")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "9df283c9-f0b7-4eb9-a6c7-c58dd7b4af18")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "815d90ed-eb62-412d-8ec3-150810959a22")
		)
		(fp_line
			(start 0.8 0.4125)
			(end -0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1f2bcda6-ff35-47e3-85cb-777cee3cdce8")
		)
		(fp_line
			(start 0.8 -0.4125)
			(end 0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "48abaf3a-4a97-4304-a381-e7b2c6773c87")
		)
		(fp_line
			(start -0.8 0.4125)
			(end -0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d1836921-3712-4a5b-bf82-3c2f8a53f7ee")
		)
		(fp_line
			(start -0.8 -0.4125)
			(end 0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "867b0ee0-5f45-4145-abf9-48fa96d963a0")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 180)
			(layer "F.Fab")
			(uuid "4382ff83-b0b8-49f6-b8f8-8da9eef4522d")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.825 0 180)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 8 "/RESET")
			(uuid "e137c246-f91b-4eae-9615-c2846d3fbd27")
		)
		(pad "2" smd roundrect
			(at 0.825 0 180)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 4 "VCC")
			(uuid "06657575-b0e7-4022-a209-0aa46115e503")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Resistor_SMD:R_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e2641a")
		(at 97.328 33.984)
		(descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R2"
			(at -2.528 0.016 0)
			(layer "F.SilkS")
			(uuid "c6baf375-aa36-4f45-b89a-84d4b4df555c")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "10k 0.1%"
			(at 4.272 -0.084 0)
			(layer "F.Fab")
			(uuid "b9329634-c45d-4c96-803d-76d6c78d46a5")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "927eeb42-421c-41c0-817f-08ef3f9b424a")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "e6836323-9d31-40d6-9bdc-22b81e4d43b8")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054e06028")
		(attr smd)
		(fp_line
			(start -0.237258 -0.5225)
			(end 0.237258 -0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "78994974-eb46-4698-9423-cff4f3ed9af5")
		)
		(fp_line
			(start -0.237258 0.5225)
			(end 0.237258 0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f0ecea13-e2e1-422b-8e73-113773f96a12")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "70b41526-8972-4a0d-8ab0-dfb5ca1c75fd")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ad58036f-ecfd-4809-9319-f797481f105e")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "66e90f56-aa17-4e01-afdd-a552ea91c709")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "886f821b-d8a5-4c0e-9629-1523132ffc88")
		)
		(fp_line
			(start -0.8 -0.4125)
			(end 0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "377c510b-14aa-45ff-a35c-77534c3ed496")
		)
		(fp_line
			(start -0.8 0.4125)
			(end -0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "404b5c60-83b7-4e11-b26f-8a99723ab5c4")
		)
		(fp_line
			(start 0.8 -0.4125)
			(end 0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "85616a3a-6a24-4d43-b147-12b97a9c68b8")
		)
		(fp_line
			(start 0.8 0.4125)
			(end -0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ff20fc51-0bd5-4ef6-af4e-fb1720fa029f")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "60997ef1-70e6-4abc-b28e-74eba63064e3")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.825 0)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 14 "Net-(IC1-Pad11)")
			(uuid "b5853c94-5493-498c-8bcb-766f32800920")
		)
		(pad "2" smd roundrect
			(at 0.825 0)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 7 "/THERMISTOR")
			(uuid "313fd5d4-e52f-4a5e-a57c-9506890a6d88")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Resistor_SMD:R_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e2641f")
		(at 90 40.95)
		(descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R4"
			(at -2.3 -0.05 0)
			(layer "F.SilkS")
			(uuid "9002ba52-8280-42b5-a44c-08b9d2fcaaae")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "330"
			(at -2.2 -0.25 0)
			(layer "F.Fab")
			(uuid "7b165b3a-06a0-4758-8d0d-f22334236dc6")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "dac8ca74-488c-42a0-9294-e313995e3c92")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "455daab1-5ce0-459d-aaf4-7b203f086e08")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d2209d")
		(attr smd)
		(fp_line
			(start -0.237258 -0.5225)
			(end 0.237258 -0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "83ce08f5-1bbb-4ccc-88a4-a41af3739e07")
		)
		(fp_line
			(start -0.237258 0.5225)
			(end 0.237258 0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a59fdd7e-2af8-474b-95aa-e37cc73e2032")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5fdf5ecf-1ca4-49d3-9de4-c29f0ac29b6f")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "00a748f2-7ba8-44f5-8c69-9c244797f1d6")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ed07736c-ce17-406c-b1ac-748fa06b1a37")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b6356707-c1bc-48ec-9c50-aed1014e1962")
		)
		(fp_line
			(start -0.8 -0.4125)
			(end 0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0f429065-0b12-47f6-ada8-013b9f907a27")
		)
		(fp_line
			(start -0.8 0.4125)
			(end -0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3870d4c3-d313-4f50-8d68-1501ec5bda6a")
		)
		(fp_line
			(start 0.8 -0.4125)
			(end 0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6e00a250-a79a-4fae-a58e-f181a21e55a1")
		)
		(fp_line
			(start 0.8 0.4125)
			(end -0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b4a9ea74-d29b-4e93-9e73-9e23dc8a4fa7")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "08ab83df-586a-408d-8edb-3cc6a9690c9b")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.825 0)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 6 "/SENSOR_TRACK")
			(uuid "df5342fd-212b-4082-8b85-fd4cbacb3a5c")
		)
		(pad "2" smd roundrect
			(at 0.825 0)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 9 "/EXCITATION")
			(uuid "064c29ad-3466-4678-b61c-32314106bb7f")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Resistor_SMD:R_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e26424")
		(at 92.764533 35.762 180)
		(descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R5"
			(at 4.564533 0.062 180)
			(layer "F.SilkS")
			(uuid "43b0200f-97b9-43a1-b0af-70705776332a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1M"
			(at 2.01 -0.038 180)
			(layer "F.Fab")
			(uuid "c0df007b-ef3a-4e82-87e0-3eab1375cc8e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "8287c9d2-949e-493a-a1fd-79c0a3e7d48e")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "748a8e50-c004-4d0f-9948-8f549cf6c3e9")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d22174")
		(attr smd)
		(fp_line
			(start -0.237258 0.5225)
			(end 0.237258 0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "face5393-9872-445b-9144-34958e3d89c3")
		)
		(fp_line
			(start -0.237258 -0.5225)
			(end 0.237258 -0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5f1c6ae9-e1ef-4df6-99b2-9bfe309b1568")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ba2dbd3e-f648-4eef-9548-53348b6ede24")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a372d351-07d4-4a5c-9e6b-dc818b22a3f6")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "22dcc76d-dc2c-4b0f-b0bc-b9c5d3df4911")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "22791b1c-71f5-44be-9123-69d853812f95")
		)
		(fp_line
			(start 0.8 0.4125)
			(end -0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "e5cac6f1-43ca-4196-9ee6-4614f9749cf2")
		)
		(fp_line
			(start 0.8 -0.4125)
			(end 0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a490376e-8a41-4238-baeb-14ec995bc52e")
		)
		(fp_line
			(start -0.8 0.4125)
			(end -0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "04eabf82-2e21-4051-88da-da8536eaf0cf")
		)
		(fp_line
			(start -0.8 -0.4125)
			(end 0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d9435ff8-1457-47d7-83b1-59f8012955aa")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 180)
			(layer "F.Fab")
			(uuid "260febb4-311e-496d-8b2b-0d13e94acf16")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.825 0 180)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 5 "/SENSE_LOW")
			(uuid "f4c92cea-91fe-4cd1-8681-c5c7cab5fe96")
		)
		(pad "2" smd roundrect
			(at 0.825 0 180)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 14 "Net-(IC1-Pad11)")
			(uuid "2a278b20-023b-43ca-a187-669f7665589e")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Resistor_SMD:R_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e2642e")
		(at 92.76272 37.312)
		(descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R7"
			(at -4.61 -0.012 0)
			(layer "F.SilkS")
			(uuid "ab7bda0b-b8a1-40b4-9f0e-2b73c0746ab4")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1M"
			(at -2.01 0.06 0)
			(layer "F.Fab")
			(uuid "a4c9ded1-9109-4e05-b50a-9ab62db1508e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "95487eed-33a2-4c89-9ac7-ec2b3226b084")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "a8e08772-35be-452b-b5d1-43bd8095b74e")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d222f8")
		(attr smd)
		(fp_line
			(start -0.237258 -0.5225)
			(end 0.237258 -0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d386c4eb-6152-4380-baa3-2d7c57819767")
		)
		(fp_line
			(start -0.237258 0.5225)
			(end 0.237258 0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "98e9062b-6663-4cdb-bb9e-2ded443e7a2a")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a9c139a0-0b91-4ac0-8bed-98d9c468f959")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3c68037a-9ef9-4d45-895e-1ecbe91e35a7")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "9609ad04-562b-4bff-a45a-e789c0ecb00c")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e1a6735d-ba85-4f72-837d-86d9449f7f74")
		)
		(fp_line
			(start -0.8 -0.4125)
			(end 0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c65a21f5-5e96-4eb4-90a6-bfafb5a313a2")
		)
		(fp_line
			(start -0.8 0.4125)
			(end -0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ac7b3e9f-7827-45e8-b845-0d0f5ce77515")
		)
		(fp_line
			(start 0.8 -0.4125)
			(end 0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8bef052a-d65c-44b0-a79e-211f68112506")
		)
		(fp_line
			(start 0.8 0.4125)
			(end -0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9fe0ede1-**************-19635ee46914")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "ac6b3717-c790-48b1-a88c-035dbf73821b")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.825 0)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 2 "GND")
			(uuid "f908c45f-46a5-4546-a50f-7185ad1160e4")
		)
		(pad "2" smd roundrect
			(at 0.825 0)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 5 "/SENSE_LOW")
			(uuid "be9ffc4c-be39-4319-baf9-b1baeb7cd2aa")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Resistor_SMD:R_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-000054e26433")
		(at 95.804 37.3 180)
		(descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R8"
			(at 5.604 0 180)
			(layer "F.SilkS")
			(uuid "67589e3e-6438-42c3-930f-754ab764fbad")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1M"
			(at -1.996 0.14 180)
			(layer "F.Fab")
			(uuid "f7fc3b3d-8f37-4d6a-869c-28549aacc07e")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "0d0289bc-3abc-4941-81e4-5c5b58160ac3")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "9d1f00fd-a651-4e28-969d-98197c274863")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d221c8")
		(attr smd)
		(fp_line
			(start -0.237258 0.5225)
			(end 0.237258 0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c72af071-273b-4d5a-8c01-e2970b9acd06")
		)
		(fp_line
			(start -0.237258 -0.5225)
			(end 0.237258 -0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ae3fbd53-75e4-4d58-bf7e-17c4e43f0297")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b8ebf70d-470c-4f1b-af90-2ac5cdad2d53")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "faebd2f0-7a53-4939-a626-2122db9a5336")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1175f161-04e7-43ea-824c-110b6a79e7e2")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b4aab823-8a27-4983-a6a9-069f4a3a459a")
		)
		(fp_line
			(start 0.8 0.4125)
			(end -0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "83d11db6-d03a-449a-9965-959ad7340ca8")
		)
		(fp_line
			(start 0.8 -0.4125)
			(end 0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "42f4e347-81f6-479f-bb9c-954eca653033")
		)
		(fp_line
			(start -0.8 0.4125)
			(end -0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f9432b69-3d1f-4a9e-8d4f-cd035e385fb4")
		)
		(fp_line
			(start -0.8 -0.4125)
			(end 0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6b47fd03-a5f3-45e4-bbaa-01b86e51917c")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 180)
			(layer "F.Fab")
			(uuid "fa8b8517-7e3d-4c88-89ef-e850704213ab")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.825 0 180)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 2 "GND")
			(uuid "d25c8d95-5885-4971-90e9-83d5862a4596")
		)
		(pad "2" smd roundrect
			(at 0.825 0 180)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 13 "/MISO/SENSE_HIGH")
			(uuid "f0bf24fe-57ca-4679-9287-8e2cf15a76ae")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "w_logo:Logo_silk_OSHW_6x6mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-0000553fcb2c")
		(at 95.2 57.2)
		(descr "Open Hardware Logo, 6x6mm")
		(property "Reference" "G***"
			(at 0 0 0)
			(layer "F.SilkS")
			(hide yes)
			(uuid "56d2f610-3b46-4f25-8b81-50832a644549")
			(effects
				(font
					(size 0.22606 0.22606)
					(thickness 0.04318)
				)
			)
		)
		(property "Value" "LOGO"
			(at 0 0.3 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "278051d4-d37b-4d0b-be58-e44d64041e6f")
			(effects
				(font
					(size 0.22606 0.22606)
					(thickness 0.04318)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "aec208dd-ea0f-49e2-b314-0d2fda984eee")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "d0c09059-1787-4961-9ff8-c0e527459eb4")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(attr through_hole)
		(fp_line
			(start -2.68 2.75)
			(end -2.68 2.95)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b547a229-ef4a-4d7f-852d-118051c45ca2")
		)
		(fp_line
			(start -2.68 2.95)
			(end -2.66 3.01)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7754949f-0705-4691-83a2-8a80093b015f")
		)
		(fp_line
			(start -2.66 3.01)
			(end -2.64 3.04)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b608a191-2b15-45e3-9c42-d8a7cba1d807")
		)
		(fp_line
			(start -2.65 2.68)
			(end -2.68 2.75)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b05566ed-a109-4b54-9681-8efb804e673d")
		)
		(fp_line
			(start -2.64 3.04)
			(end -2.59 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0d463871-ffe9-4570-8ac3-5f4d3c48cf2d")
		)
		(fp_line
			(start -2.63 2.65)
			(end -2.65 2.68)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e99ce5fa-7150-457c-a120-fabf7d4e4b69")
		)
		(fp_line
			(start -2.59 2.62)
			(end -2.63 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "eaff47c9-84a6-490d-9d61-bbb9ad4205cd")
		)
		(fp_line
			(start -2.59 3.08)
			(end -2.51 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d9ba6739-d479-4532-8416-e9148691eba6")
		)
		(fp_line
			(start -2.51 2.62)
			(end -2.59 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "14d1395d-e0eb-46ce-968a-50fc7968ebce")
		)
		(fp_line
			(start -2.51 3.08)
			(end -2.46 3.05)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "10342f09-464d-4651-ae17-22a26a0b59f7")
		)
		(fp_line
			(start -2.46 2.66)
			(end -2.51 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9a839960-1e8f-4a14-8d9f-227fbe3770d7")
		)
		(fp_line
			(start -2.46 3.05)
			(end -2.44 3.02)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "82575ea7-05ae-4f60-ae30-3c2df185d624")
		)
		(fp_line
			(start -2.44 2.69)
			(end -2.46 2.66)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "eb263666-de4a-4cc7-8c73-4173f8c8a4db")
		)
		(fp_line
			(start -2.44 3.02)
			(end -2.42 2.95)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d32c4fb8-99ce-400c-9930-6a56a0cbbe17")
		)
		(fp_line
			(start -2.42 2.75)
			(end -2.44 2.69)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f1d32224-4044-4a27-8e0f-d3e6659f6f75")
		)
		(fp_line
			(start -2.42 2.95)
			(end -2.42 2.75)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "551afd25-1e5c-4c69-9fd9-1d25d83e1671")
		)
		(fp_line
			(start -2.2 2.65)
			(end -2.16 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fd669a14-9141-4b3d-8789-f4732199469a")
		)
		(fp_line
			(start -2.2 3.32)
			(end -2.2 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "34109363-cbdf-49a6-b327-0b017f04b67d")
		)
		(fp_line
			(start -2.16 2.62)
			(end -2.06 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "760ac1ef-798e-455a-9758-7fc016d2329c")
		)
		(fp_line
			(start -2.15 3.08)
			(end -2.2 3.05)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a999da68-6963-4612-aa85-f88cde4a8c53")
		)
		(fp_line
			(start -2.06 2.62)
			(end -2.02 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a79ed4cc-bc26-4410-9f8d-aa6018dc279b")
		)
		(fp_line
			(start -2.05 3.08)
			(end -2.15 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ad264466-886b-4b84-8478-7d75cc8fc39b")
		)
		(fp_line
			(start -2.02 2.65)
			(end -1.99 2.68)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c8d515e4-ee32-4a6c-ade5-01bea26a51fa")
		)
		(fp_line
			(start -2.01 3.05)
			(end -2.05 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e211a77f-f545-41d9-ac9a-63ba2d6f9854")
		)
		(fp_line
			(start -1.99 2.68)
			(end -1.97 2.74)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3dd32134-6858-44ef-9f0b-d675264240f1")
		)
		(fp_line
			(start -1.99 3.02)
			(end -2.01 3.05)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "51dfe875-2442-4530-8c8e-1b14aa80caeb")
		)
		(fp_line
			(start -1.97 2.74)
			(end -1.97 2.96)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "38057aaf-3e9e-4c20-a54d-b7ec20bfcfe4")
		)
		(fp_line
			(start -1.97 2.96)
			(end -1.99 3.02)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9e1cf7b2-151c-470f-80f1-c4e13d909070")
		)
		(fp_line
			(start -1.77 2.71)
			(end -1.77 2.98)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6980fcd6-034d-4fe5-98a0-92bb632aa69d")
		)
		(fp_line
			(start -1.77 2.98)
			(end -1.75 3.05)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1b5435b7-0079-49d2-8fc1-0aa323f74c97")
		)
		(fp_line
			(start -1.75 3.05)
			(end -1.71 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f0d98bb0-c306-461c-acfd-552311feb42b")
		)
		(fp_line
			(start -1.74 2.65)
			(end -1.77 2.71)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c45108bc-ae4e-401c-add7-38938f4532ab")
		)
		(fp_line
			(start -1.71 3.08)
			(end -1.61 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "73b59341-3a9d-498d-8bbc-beb4689aa21f")
		)
		(fp_line
			(start -1.7 2.62)
			(end -1.74 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5171303b-7d38-4e69-a5d2-13c2189e1c4f")
		)
		(fp_line
			(start -1.61 3.08)
			(end -1.56 3.05)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "18a12263-f3f7-471b-a67b-dce7c704986b")
		)
		(fp_line
			(start -1.6 2.62)
			(end -1.7 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "150849ab-e0a2-4b41-85c0-ca0f1c2d31ae")
		)
		(fp_line
			(start -1.56 2.66)
			(end -1.6 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fcf214dd-fee0-451d-a106-912886ffd23d")
		)
		(fp_line
			(start -1.54 2.73)
			(end -1.56 2.66)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "25c3b9c9-fc11-45b3-9402-b2e9956919fe")
		)
		(fp_line
			(start -1.54 2.85)
			(end -1.77 2.85)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3a7fe10b-eb9f-4acb-bdf8-987d57927c3a")
		)
		(fp_line
			(start -1.54 2.85)
			(end -1.54 2.73)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4a81d8ce-a570-4374-b9c2-01d3a74012e3")
		)
		(fp_line
			(start -1.32 2.62)
			(end -1.32 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "786efb71-a66d-4db7-8153-9033556bba8c")
		)
		(fp_line
			(start -1.32 2.68)
			(end -1.3 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4df4d973-8f12-4b91-9786-3df87130ab9c")
		)
		(fp_line
			(start -1.3 2.65)
			(end -1.26 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1d6c292d-6030-4175-82f5-bc43620ead94")
		)
		(fp_line
			(start -1.26 2.62)
			(end -1.17 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "57fb906c-f930-4384-9135-286c2a74479c")
		)
		(fp_line
			(start -1.17 2.62)
			(end -1.13 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "33bbc59b-1339-44c1-904e-df6b85ec2ecb")
		)
		(fp_line
			(start -1.13 2.65)
			(end -1.11 2.71)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "56e41706-d1d0-4d02-8df8-ee2cf9fa908e")
		)
		(fp_line
			(start -1.11 2.71)
			(end -1.11 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e4be56f0-d5dc-4a3c-86b9-ab2d7cc5b60e")
		)
		(fp_line
			(start -0.49 2.38)
			(end -0.49 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6edd029b-05a8-4aa9-a64c-2acdf3aace2e")
		)
		(fp_line
			(start -0.49 2.69)
			(end -0.47 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "30a56524-d168-4e71-8f96-ccb40353e236")
		)
		(fp_line
			(start -0.47 2.65)
			(end -0.42 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "51f3ab50-1477-4aff-9042-eeb594dd0005")
		)
		(fp_line
			(start -0.42 2.62)
			(end -0.34 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "03fa3810-a5cc-4c23-9587-8a795b23888e")
		)
		(fp_line
			(start -0.34 2.62)
			(end -0.3 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "cf92d9e4-40e8-4603-bedb-a4ea3f07052d")
		)
		(fp_line
			(start -0.3 2.65)
			(end -0.28 2.71)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "14e95128-64dd-4541-8cb5-afda68fead66")
		)
		(fp_line
			(start -0.28 2.71)
			(end -0.28 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3a4c7c66-867c-452a-895f-7599a839507b")
		)
		(fp_line
			(start -0.06 2.91)
			(end -0.06 2.98)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "21a653fa-d113-4b43-b4c0-d37fc582b919")
		)
		(fp_line
			(start -0.06 2.98)
			(end -0.04 3.04)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ad901185-2ce7-46a8-9753-a8eaf7e339e3")
		)
		(fp_line
			(start -0.04 2.65)
			(end 0.01 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "18338f25-1b3c-4a89-82e0-29cd24df1a28")
		)
		(fp_line
			(start -0.04 3.04)
			(end 0.01 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5f354474-5805-4b06-a85f-4d424f1c52cd")
		)
		(fp_line
			(start -0.03 2.84)
			(end -0.06 2.91)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "067de09e-81be-4bc8-acb0-f7cd2be3865a")
		)
		(fp_line
			(start 0.01 2.62)
			(end 0.11 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d3bccce9-fb64-4e53-878d-cafaea3f012f")
		)
		(fp_line
			(start 0.01 3.08)
			(end 0.13 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "727bf869-69fc-4879-86d6-1ecc3f877551")
		)
		(fp_line
			(start 0.02 2.81)
			(end -0.03 2.84)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "60c3ea6d-c792-4c60-85d0-fe2d879ce5a3")
		)
		(fp_line
			(start 0.11 2.62)
			(end 0.15 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "832140ce-233d-430c-b0dd-375a1a590de3")
		)
		(fp_line
			(start 0.13 3.08)
			(end 0.18 3.04)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "739211a0-df21-4e3e-8236-865809446fda")
		)
		(fp_line
			(start 0.14 2.81)
			(end 0.02 2.81)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8c377c3e-3487-4f10-9a33-e16f07eb6edc")
		)
		(fp_line
			(start 0.15 2.65)
			(end 0.18 2.71)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "97ccd5aa-7098-4064-a933-d699110b4721")
		)
		(fp_line
			(start 0.18 2.71)
			(end 0.18 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "41eb99da-d103-4f4f-b368-404beae0c12c")
		)
		(fp_line
			(start 0.18 2.78)
			(end 0.14 2.81)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d15e03d1-ad8f-41f2-8b57-724741231b2b")
		)
		(fp_line
			(start 0.42 2.62)
			(end 0.42 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0fab1b2d-d138-48f3-b2cf-d9baae3f1f6d")
		)
		(fp_line
			(start 0.42 2.74)
			(end 0.44 2.67)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f00e93f9-1d33-496f-8268-9ba6d75bd480")
		)
		(fp_line
			(start 0.44 2.67)
			(end 0.46 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "12bdaf4d-2cb0-409b-af69-e276bc9ebaac")
		)
		(fp_line
			(start 0.46 2.65)
			(end 0.51 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fe9e8910-7586-440e-8f0c-1304bd09faff")
		)
		(fp_line
			(start 0.51 2.62)
			(end 0.56 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "67420f22-444e-48da-833f-b4ade87e5525")
		)
		(fp_line
			(start 0.7 2.75)
			(end 0.73 2.68)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "de4e55c9-2b4c-469f-841a-17220746a11a")
		)
		(fp_line
			(start 0.7 2.95)
			(end 0.7 2.75)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2fbd9f10-c0e9-426a-8b07-184841ef886b")
		)
		(fp_line
			(start 0.73 2.68)
			(end 0.75 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2a66e988-e043-4133-a752-71ca52fa7dcf")
		)
		(fp_line
			(start 0.73 3.02)
			(end 0.7 2.95)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6087b801-1dd2-42f1-a946-f46093507bae")
		)
		(fp_line
			(start 0.75 2.65)
			(end 0.81 2.61)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "60d1f356-304e-449d-a87e-d40ba536b8b1")
		)
		(fp_line
			(start 0.75 3.05)
			(end 0.73 3.02)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5524d412-8b67-4d9f-a446-6b3e56941b1a")
		)
		(fp_line
			(start 0.79 3.08)
			(end 0.75 3.05)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "36a1cf8c-4255-45a2-b740-baae5bcf37a3")
		)
		(fp_line
			(start 0.81 2.61)
			(end 0.88 2.61)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c1e66986-227d-4822-9c33-c2a12e56869e")
		)
		(fp_line
			(start 0.88 2.61)
			(end 0.94 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3428b0c5-7faa-4a00-bca4-75cb1389bf1c")
		)
		(fp_line
			(start 0.9 3.08)
			(end 0.79 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b77e7d24-61d9-4e9f-99de-e85e1d127e56")
		)
		(fp_line
			(start 0.94 2.38)
			(end 0.94 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "838897f1-e6f6-4e5a-b213-e24197423688")
		)
		(fp_line
			(start 0.94 3.05)
			(end 0.9 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "33067b69-c938-457c-9bc1-9fd564e774d6")
		)
		(fp_line
			(start 1.13 2.62)
			(end 1.23 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4930d7a4-f07c-4673-b4ca-8f41b80584e7")
		)
		(fp_line
			(start 1.23 3.08)
			(end 1.32 2.74)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fbf9fea1-ea1f-4fd7-a0b7-b92742842fbc")
		)
		(fp_line
			(start 1.32 2.74)
			(end 1.42 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0bfc2f9e-016d-48e3-817c-c7b029d8acd5")
		)
		(fp_line
			(start 1.42 3.08)
			(end 1.52 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b0310435-13fe-4d76-a666-437bfc81dfd3")
		)
		(fp_line
			(start 1.68 2.91)
			(end 1.68 2.98)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3dfa592e-f656-4bdd-adeb-b495d94847bd")
		)
		(fp_line
			(start 1.68 2.98)
			(end 1.7 3.04)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "158f5247-9e8e-4ab8-b9e5-b1229a85d00f")
		)
		(fp_line
			(start 1.7 2.65)
			(end 1.75 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "de85919b-30dd-4c16-a0f8-28df5f48da4d")
		)
		(fp_line
			(start 1.7 3.04)
			(end 1.75 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b5271fe9-2892-49f8-b649-9bd22768c1d5")
		)
		(fp_line
			(start 1.71 2.84)
			(end 1.68 2.91)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d7a56512-0658-4ebe-9aeb-b36ac6b83fff")
		)
		(fp_line
			(start 1.75 2.62)
			(end 1.85 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fac64908-fd66-463a-8fe9-2f20271b2a26")
		)
		(fp_line
			(start 1.75 3.08)
			(end 1.87 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2d946317-4990-47db-a126-be898638699c")
		)
		(fp_line
			(start 1.76 2.81)
			(end 1.71 2.84)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "76d69273-1404-41cf-9a09-cecb9c436046")
		)
		(fp_line
			(start 1.85 2.62)
			(end 1.89 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6eb3d9b4-5115-4676-b686-da5310244802")
		)
		(fp_line
			(start 1.87 3.08)
			(end 1.92 3.04)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "dd4d9b4b-3718-49ae-b766-c9524a43f0ea")
		)
		(fp_line
			(start 1.88 2.81)
			(end 1.76 2.81)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ef7f63f9-e435-4355-b332-0592b5845c36")
		)
		(fp_line
			(start 1.89 2.65)
			(end 1.92 2.71)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a0b98b80-0725-456c-8e9e-604192902c64")
		)
		(fp_line
			(start 1.92 2.71)
			(end 1.92 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "737176e7-b7cb-47b4-bee9-1133d6704b2e")
		)
		(fp_line
			(start 1.92 2.78)
			(end 1.88 2.81)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "524eab0a-beb2-4ad0-b443-71c5271dc43e")
		)
		(fp_line
			(start 2.16 2.62)
			(end 2.16 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4eca2e3c-a0a0-4837-834e-836c7cdab3cb")
		)
		(fp_line
			(start 2.16 2.74)
			(end 2.18 2.67)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7b858268-0144-4a86-b273-f9e7c7984a10")
		)
		(fp_line
			(start 2.18 2.67)
			(end 2.2 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ee237cd6-c92a-4e1b-9c57-353f1786264b")
		)
		(fp_line
			(start 2.2 2.65)
			(end 2.25 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "62853e84-c6ef-4517-839b-5809e73e898d")
		)
		(fp_line
			(start 2.25 2.62)
			(end 2.3 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a88dcbec-83f1-43c2-b914-14e29ea43047")
		)
		(fp_line
			(start 2.44 2.71)
			(end 2.44 2.98)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ba4ce63f-b39f-4b51-a4d1-e0ed159ca3d3")
		)
		(fp_line
			(start 2.44 2.98)
			(end 2.46 3.05)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1d7ac1e9-56ee-4010-b372-e6be277ab01e")
		)
		(fp_line
			(start 2.46 3.05)
			(end 2.5 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "998f80d9-7180-4dd8-a80f-d5ac6aa03316")
		)
		(fp_line
			(start 2.47 2.65)
			(end 2.44 2.71)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f0da3084-9583-4380-ba80-1bfe174349fe")
		)
		(fp_line
			(start 2.5 3.08)
			(end 2.6 3.08)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "95e14921-3724-4141-bbd2-a03c92454a6e")
		)
		(fp_line
			(start 2.51 2.62)
			(end 2.47 2.65)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "40bbe344-dd87-4f48-9b0d-d7c77eee32c6")
		)
		(fp_line
			(start 2.6 3.08)
			(end 2.65 3.05)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "ad429585-5b1a-4ab4-b23d-dff5ba7c7764")
		)
		(fp_line
			(start 2.61 2.62)
			(end 2.51 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3a06c310-a769-4b77-8849-56ce5b44ffa4")
		)
		(fp_line
			(start 2.65 2.66)
			(end 2.61 2.62)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "9f25c61a-be18-4e55-aab4-94380aa27a9c")
		)
		(fp_line
			(start 2.67 2.73)
			(end 2.65 2.66)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "06f98d60-5220-4f17-8ef6-a0f97ca7ce5a")
		)
		(fp_line
			(start 2.67 2.85)
			(end 2.44 2.85)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "83118600-128a-49ec-82aa-47665e2ba07f")
		)
		(fp_line
			(start 2.67 2.85)
			(end 2.67 2.73)
			(stroke
				(width 0.075)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4a3997b0-fb01-4a7f-9f05-bfd1360acbb0")
		)
		(fp_poly
			(pts
				(xy -1.51384 2.24536) (xy -1.48844 2.23012) (xy -1.43002 2.19456) (xy -1.3462 2.13868) (xy -1.24714 2.07264)
				(xy -1.14808 2.0066) (xy -1.0668 1.95326) (xy -1.01092 1.91516) (xy -0.98552 1.90246) (xy -0.97282 1.90754)
				(xy -0.9271 1.9304) (xy -0.85852 1.96596) (xy -0.81788 1.98628) (xy -0.75692 2.01168) (xy -0.7239 2.0193)
				(xy -0.71882 2.00914) (xy -0.69596 1.96088) (xy -0.6604 1.8796) (xy -0.61468 1.77038) (xy -0.5588 1.64338)
				(xy -0.50292 1.50876) (xy -0.4445 1.36906) (xy -0.38862 1.23444) (xy -0.34036 1.11506) (xy -0.29972 1.01854)
				(xy -0.27432 0.94996) (xy -0.26416 0.92202) (xy -0.2667 0.9144) (xy -0.29972 0.88392) (xy -0.35306 0.84328)
				(xy -0.47244 0.74676) (xy -0.58928 0.60198) (xy -0.6604 0.43688) (xy -0.68326 0.25146) (xy -0.66294 0.08128)
				(xy -0.5969 -0.08128) (xy -0.4826 -0.2286) (xy -0.3429 -0.33782) (xy -0.18034 -0.4064) (xy 0 -0.42926)
				(xy 0.17272 -0.40894) (xy 0.34036 -0.3429) (xy 0.48768 -0.23114) (xy 0.55118 -0.16002) (xy 0.63754 -0.01016)
				(xy 0.6858 0.14732) (xy 0.69088 0.18796) (xy 0.68326 0.36322) (xy 0.63246 0.5334) (xy 0.53848 0.68326)
				(xy 0.40894 0.80772) (xy 0.3937 0.81788) (xy 0.33528 0.8636) (xy 0.29464 0.89408) (xy 0.26416 0.91948)
				(xy 0.48768 1.45796) (xy 0.52324 1.54178) (xy 0.5842 1.6891) (xy 0.63754 1.8161) (xy 0.68072 1.9177)
				(xy 0.7112 1.98374) (xy 0.7239 2.01168) (xy 0.7239 2.01422) (xy 0.74422 2.01676) (xy 0.78486 2.00152)
				(xy 0.86106 1.96596) (xy 0.90932 1.94056) (xy 0.96774 1.91262) (xy 0.99314 1.90246) (xy 1.016 1.91516)
				(xy 1.06934 1.95072) (xy 1.15062 2.00406) (xy 1.24714 2.06756) (xy 1.33858 2.13106) (xy 1.4224 2.18694)
				(xy 1.48336 2.22504) (xy 1.51384 2.24282) (xy 1.51892 2.24282) (xy 1.54432 2.22758) (xy 1.59258 2.18694)
				(xy 1.66624 2.11836) (xy 1.77038 2.01422) (xy 1.78562 1.99898) (xy 1.87198 1.91262) (xy 1.94056 1.83896)
				(xy 1.98628 1.78816) (xy 2.00406 1.7653) (xy 1.98882 1.73482) (xy 1.95072 1.67386) (xy 1.89484 1.5875)
				(xy 1.82626 1.48844) (xy 1.64846 1.22936) (xy 1.74498 0.98552) (xy 1.77546 0.90932) (xy 1.81356 0.82042)
				(xy 1.8415 0.75438) (xy 1.85674 0.72644) (xy 1.88214 0.71628) (xy 1.95072 0.70104) (xy 2.04724 0.68072)
				(xy 2.16154 0.6604) (xy 2.2733 0.64008) (xy 2.37236 0.61976) (xy 2.44348 0.60706) (xy 2.4765 0.59944)
				(xy 2.48412 0.59436) (xy 2.49174 0.57912) (xy 2.49428 0.5461) (xy 2.49682 0.48514) (xy 2.49936 0.39116)
				(xy 2.49936 0.25146) (xy 2.49936 0.23622) (xy 2.49682 0.10668) (xy 2.49428 0) (xy 2.49174 -0.06604)
				(xy 2.48666 -0.09398) (xy 2.45618 -0.1016) (xy 2.38506 -0.11684) (xy 2.286 -0.13462) (xy 2.16662 -0.15748)
				(xy 2.159 -0.16002) (xy 2.04216 -0.18288) (xy 1.9431 -0.2032) (xy 1.87198 -0.21844) (xy 1.84404 -0.2286)
				(xy 1.83642 -0.23622) (xy 1.81356 -0.28194) (xy 1.78054 -0.3556) (xy 1.7399 -0.4445) (xy 1.7018 -0.53848)
				(xy 1.66878 -0.6223) (xy 1.64592 -0.68326) (xy 1.6383 -0.7112) (xy 1.64084 -0.71374) (xy 1.65862 -0.74168)
				(xy 1.69926 -0.80264) (xy 1.75514 -0.88646) (xy 1.82372 -0.98806) (xy 1.8288 -0.99568) (xy 1.89738 -1.09474)
				(xy 1.95326 -1.1811) (xy 1.98882 -1.23952) (xy 2.00406 -1.26746) (xy 2.00406 -1.27) (xy 1.9812 -1.30048)
				(xy 1.9304 -1.35636) (xy 1.85674 -1.43256) (xy 1.77038 -1.52146) (xy 1.74244 -1.54686) (xy 1.64338 -1.64338)
				(xy 1.57734 -1.70434) (xy 1.53416 -1.73736) (xy 1.51384 -1.74498) (xy 1.48336 -1.7272) (xy 1.41986 -1.68656)
				(xy 1.33604 -1.62814) (xy 1.23444 -1.55956) (xy 1.22682 -1.55448) (xy 1.12776 -1.4859) (xy 1.04394 -1.43002)
				(xy 0.98552 -1.38938) (xy 0.95758 -1.37414) (xy 0.95504 -1.37414) (xy 0.9144 -1.38684) (xy 0.84328 -1.41224)
				(xy 0.75438 -1.44526) (xy 0.66294 -1.48336) (xy 0.57912 -1.51892) (xy 0.51562 -1.54686) (xy 0.48514 -1.56464)
				(xy 0.47498 -1.6002) (xy 0.4572 -1.6764) (xy 0.43688 -1.778) (xy 0.41148 -1.89992) (xy 0.40894 -1.92024)
				(xy 0.38608 -2.03962) (xy 0.3683 -2.13868) (xy 0.35306 -2.20726) (xy 0.34544 -2.2352) (xy 0.3302 -2.23774)
				(xy 0.27178 -2.24282) (xy 0.18288 -2.24536) (xy 0.07366 -2.24536) (xy -0.0381 -2.24536) (xy -0.14732 -2.24282)
				(xy -0.2413 -2.24028) (xy -0.30988 -2.2352) (xy -0.33782 -2.23012) (xy -0.33782 -2.22758) (xy -0.34798 -2.18948)
				(xy -0.36576 -2.11582) (xy -0.38608 -2.01168) (xy -0.40894 -1.88976) (xy -0.41402 -1.8669) (xy -0.43688 -1.75006)
				(xy -0.4572 -1.651) (xy -0.4699 -1.58496) (xy -0.47752 -1.55702) (xy -0.49022 -1.55194) (xy -0.53848 -1.53162)
				(xy -0.61722 -1.4986) (xy -0.71628 -1.45796) (xy -0.94488 -1.36652) (xy -1.22682 -1.55702) (xy -1.25222 -1.5748)
				(xy -1.35382 -1.64338) (xy -1.4351 -1.69926) (xy -1.49352 -1.73736) (xy -1.51638 -1.75006) (xy -1.51892 -1.75006)
				(xy -1.54686 -1.72466) (xy -1.60274 -1.67132) (xy -1.67894 -1.59766) (xy -1.76784 -1.5113) (xy -1.83134 -1.44526)
				(xy -1.91008 -1.36652) (xy -1.95834 -1.31318) (xy -1.98628 -1.28016) (xy -1.9939 -1.25984) (xy -1.99136 -1.2446)
				(xy -1.97358 -1.21666) (xy -1.93294 -1.1557) (xy -1.87452 -1.06934) (xy -1.80594 -0.97028) (xy -1.75006 -0.88646)
				(xy -1.6891 -0.79248) (xy -1.651 -0.72644) (xy -1.63576 -0.69342) (xy -1.64084 -0.68072) (xy -1.65862 -0.62484)
				(xy -1.69418 -0.54102) (xy -1.73482 -0.44196) (xy -1.83388 -0.22098) (xy -1.97866 -0.19304) (xy -2.06756 -0.17526)
				(xy -2.18948 -0.1524) (xy -2.30886 -0.12954) (xy -2.49174 -0.09398) (xy -2.49936 0.58166) (xy -2.47142 0.59436)
				(xy -2.44348 0.60198) (xy -2.3749 0.61722) (xy -2.27838 0.63754) (xy -2.16154 0.65786) (xy -2.06502 0.67564)
				(xy -1.96596 0.69596) (xy -1.89484 0.70866) (xy -1.86436 0.71628) (xy -1.8542 0.72644) (xy -1.83134 0.7747)
				(xy -1.79578 0.8509) (xy -1.75514 0.94234) (xy -1.71704 1.03632) (xy -1.68148 1.12522) (xy -1.65862 1.19126)
				(xy -1.64846 1.22428) (xy -1.66116 1.25222) (xy -1.69926 1.31064) (xy -1.7526 1.39192) (xy -1.82118 1.49098)
				(xy -1.88722 1.5875) (xy -1.94564 1.67132) (xy -1.98374 1.73228) (xy -2.00152 1.76022) (xy -1.99136 1.778)
				(xy -1.95326 1.82626) (xy -1.8796 1.90246) (xy -1.76784 2.01168) (xy -1.75006 2.02946) (xy -1.6637 2.11328)
				(xy -1.59004 2.18186) (xy -1.5367 2.22758) (xy -1.51384 2.24536)
			)
			(stroke
				(width 0.00254)
				(type solid)
			)
			(fill yes)
			(layer "F.SilkS")
			(uuid "dde0aa0c-dd76-412d-922e-b113c5fc2336")
		)
		(embedded_fonts no)
	)
	(footprint "Package_SO:SOIC-14_3.9x8.7mm_P1.27mm"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-0000556e1316")
		(at 97.1 28)
		(descr "SOIC, 14 Pin (JEDEC MS-012AB, https://www.analog.com/media/en/package-pcb-resources/package/pkg_pdf/soic_narrow-r/r_14.pdf), generated with kicad-footprint-generator ipc_gullwing_generator.py")
		(tags "SOIC SO")
		(property "Reference" "IC1"
			(at 0.7 -5.28 0)
			(layer "F.SilkS")
			(uuid "8263b6df-a1c8-4788-b9af-1fe3d5c4eaeb")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "ATTINY441-SSU"
			(at 0 5.28 0)
			(layer "F.Fab")
			(uuid "0eb35945-ae96-48bc-a63a-754509c6b7c4")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "0a670a2f-4868-47cf-84f1-bac05e5c5026")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "70326319-09d8-470d-af6f-ac37b8d89a64")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-0000556e13d2")
		(attr smd)
		(fp_line
			(start 0 -4.435)
			(end -3.45 -4.435)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "92c0114b-4726-4c33-8cd0-3a1f6c54f893")
		)
		(fp_line
			(start 0 -4.435)
			(end 1.95 -4.435)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d226a843-ef2e-4720-b42e-997a869df4d1")
		)
		(fp_line
			(start 0 4.435)
			(end -1.95 4.435)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "43411174-a3b4-488f-a5aa-e091a3f668fa")
		)
		(fp_line
			(start 0 4.435)
			(end 1.95 4.435)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7010881f-a0c2-457c-9530-483ac0b62894")
		)
		(fp_line
			(start -3.7 -4.58)
			(end -3.7 4.58)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2a73e281-1a35-4647-84e1-878b72a54f2e")
		)
		(fp_line
			(start -3.7 4.58)
			(end 3.7 4.58)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "57d6bce9-83dd-4cc6-8883-45badec03bdd")
		)
		(fp_line
			(start 3.7 -4.58)
			(end -3.7 -4.58)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a13920eb-4c0c-43b4-817b-095c2d756bce")
		)
		(fp_line
			(start 3.7 4.58)
			(end 3.7 -4.58)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5488308e-9bd6-4de2-8069-9d80ac7ae786")
		)
		(fp_line
			(start -1.95 -3.35)
			(end -0.975 -4.325)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "61352cd2-1221-4d9b-a3b7-47e34b5bcf43")
		)
		(fp_line
			(start -1.95 4.325)
			(end -1.95 -3.35)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a4ab5b22-2a7a-4ce4-90e3-8016df76b2aa")
		)
		(fp_line
			(start -0.975 -4.325)
			(end 1.95 -4.325)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6393c525-46d3-4f9b-ac6b-3bb585b0c4e9")
		)
		(fp_line
			(start 1.95 -4.325)
			(end 1.95 4.325)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b78552e9-1bc3-4358-a8bb-069b6d89332e")
		)
		(fp_line
			(start 1.95 4.325)
			(end -1.95 4.325)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "46730724-fd14-48ae-bba3-bb2c74f53b92")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "442e8e69-9e65-4f6a-8780-a81ca81c102e")
			(effects
				(font
					(size 0.98 0.98)
					(thickness 0.15)
				)
			)
		)
		(pad "1" smd roundrect
			(at -2.475 -3.81)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 4 "VCC")
			(uuid "623b33ef-9574-4c5b-9e23-ef194c48315d")
		)
		(pad "2" smd roundrect
			(at -2.475 -2.54)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 3 "Net-(C2-Pad1)")
			(uuid "c9baa80c-9852-4fb1-9f24-1aa0f7c17b6f")
		)
		(pad "3" smd roundrect
			(at -2.475 -1.27)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 1 "Net-(C1-Pad1)")
			(uuid "5cbc9dbf-22df-4122-86b5-ea06290d0368")
		)
		(pad "4" smd roundrect
			(at -2.475 0)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 8 "/RESET")
			(uuid "0baa47fd-f10b-4c63-9355-e7380420198a")
		)
		(pad "5" smd roundrect
			(at -2.475 1.27)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 9 "/EXCITATION")
			(uuid "c123608e-b3b7-46b2-8e59-ff2c5065cc77")
		)
		(pad "6" smd roundrect
			(at -2.475 2.54)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 5 "/SENSE_LOW")
			(uuid "bb0e2206-2f12-4148-b28c-256da3fda7c4")
		)
		(pad "7" smd roundrect
			(at -2.475 3.81)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 12 "/MOSI/SDA")
			(uuid "8660b990-68c3-4be0-99a9-89baaa85670a")
		)
		(pad "8" smd roundrect
			(at 2.475 3.81)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 13 "/MISO/SENSE_HIGH")
			(uuid "780e5f7c-bb9d-4791-b837-efe92bb2fb3a")
		)
		(pad "9" smd roundrect
			(at 2.475 2.54)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 11 "/SCK/SCL")
			(uuid "e1cd05d5-e613-4013-9c70-be994f45f36e")
		)
		(pad "10" smd roundrect
			(at 2.475 1.27)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 7 "/THERMISTOR")
			(uuid "cbd10b8a-3dbe-495e-a731-52d5b0df7bc5")
		)
		(pad "11" smd roundrect
			(at 2.475 0)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 14 "Net-(IC1-Pad11)")
			(uuid "40538488-b0e5-4a49-805a-b8c44e50d40f")
		)
		(pad "12" smd roundrect
			(at 2.475 -1.27)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 15 "Net-(IC1-Pad12)")
			(uuid "e91ae643-4b8b-46f6-9abc-bdc35b7467a5")
		)
		(pad "13" smd roundrect
			(at 2.475 -2.54)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 10 "Net-(D3-Pad1)")
			(uuid "687d19bd-27ce-469a-b145-f540aef36172")
		)
		(pad "14" smd roundrect
			(at 2.475 -3.81)
			(size 1.95 0.6)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 2 "GND")
			(uuid "e1707116-c097-4923-b315-6965d1c7deb2")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Package_SO.3dshapes/SOIC-14_3.9x8.7mm_P1.27mm.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Capacitor_SMD:C_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005b57778d")
		(at 92.8 38.8 180)
		(descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "capacitor")
		(property "Reference" "C4"
			(at 4.65 -0.05 180)
			(layer "F.SilkS")
			(uuid "7a0dcf52-737e-4614-b7a1-bc6b5dc07d99")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1n"
			(at 1.75 0.15 180)
			(layer "F.Fab")
			(uuid "a29d1d92-5bd0-4aef-bd0b-965637ac0856")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "efde1850-0615-467f-a3c7-7617c9fae3ba")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "c945f455-eda7-4bac-a82f-5733b625e6fe")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d2222a")
		(attr smd)
		(fp_line
			(start -0.14058 0.51)
			(end 0.14058 0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "563157a4-e5eb-41e5-8073-5d68008d9aa3")
		)
		(fp_line
			(start -0.14058 -0.51)
			(end 0.14058 -0.51)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "fe44a267-1635-439a-be19-917e380fd410")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "fb6abe0d-3c37-4db7-a71a-581c13b5193b")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "0e517350-1fbd-4de8-b640-bde853b18bda")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "6a599500-ef3b-4549-b810-a1295cfa00d8")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ddc07c23-e966-4243-a184-7a94db1464c0")
		)
		(fp_line
			(start 0.8 0.4)
			(end -0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "9a8582a2-b25b-4a7f-b7ad-bcdcb80ad50c")
		)
		(fp_line
			(start 0.8 -0.4)
			(end 0.8 0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "094d7458-e75a-49bd-937a-f50811dc0a70")
		)
		(fp_line
			(start -0.8 0.4)
			(end -0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a921d6a8-ed8d-44e2-bc99-9b31cbb644b3")
		)
		(fp_line
			(start -0.8 -0.4)
			(end 0.8 -0.4)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "dde60f53-4d1a-4b85-87d3-19858b630cd7")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 180)
			(layer "F.Fab")
			(uuid "5c58b8a7-a7fd-4fb7-9da3-1c9fbb656019")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.775 0 180)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 5 "/SENSE_LOW")
			(uuid "137c827a-a7ea-4610-a451-02d4c9581f90")
		)
		(pad "2" smd roundrect
			(at 0.775 0 180)
			(size 0.9 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 2 "GND")
			(uuid "d94e4792-6202-4249-a54d-004da6a9090f")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Resistor_SMD:R_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005bf533cf")
		(at 95.804 35.762)
		(descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R6"
			(at -5.504 -0.062 0)
			(layer "F.SilkS")
			(uuid "31f8f8e1-14c5-4ffc-a9c0-ceda2008f6d2")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "1M"
			(at -0.204 -1.062 0)
			(layer "F.Fab")
			(uuid "d0d43b70-0826-449e-a3c4-16b6de6524fa")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "019c688b-ca23-4e25-95ee-200b610582f7")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "7266c631-c920-425d-a1b4-c8add979ff45")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054d2252c")
		(attr smd)
		(fp_line
			(start -0.237258 -0.5225)
			(end 0.237258 -0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c685f092-fd8a-48ae-9734-224f2d928dda")
		)
		(fp_line
			(start -0.237258 0.5225)
			(end 0.237258 0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "5934bac7-e4f8-4581-a6d8-90278945b766")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "10cc5eec-1232-40d7-888b-18900e5eb661")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d75238b4-2ada-419d-b6b9-f2040b7a5398")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3dbe9cbd-3e53-4c0b-8967-c7bf8f8a569f")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3fbeed33-dbf1-44ce-9478-5e255eaa6e38")
		)
		(fp_line
			(start -0.8 -0.4125)
			(end 0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "31645e79-c510-45f8-8f81-f71489cdeca1")
		)
		(fp_line
			(start -0.8 0.4125)
			(end -0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "97a04b6d-91ce-4759-8267-992c150d451b")
		)
		(fp_line
			(start 0.8 -0.4125)
			(end 0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bf044859-6ff8-422b-8e71-5870b49cc000")
		)
		(fp_line
			(start 0.8 0.4125)
			(end -0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "dd1b9330-4be5-43b6-86b2-af204013ec29")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "07e8ea44-8465-44e4-a669-86598c7d89d4")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.825 0)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 13 "/MISO/SENSE_HIGH")
			(uuid "c10da7fa-21be-4442-a18a-8c846932e3ee")
		)
		(pad "2" smd roundrect
			(at 0.825 0)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 14 "Net-(IC1-Pad11)")
			(uuid "bd8cf64d-acad-4ab2-84c8-96b4820b4193")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Resistor_SMD:R_0603_1608Metric"
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005bf53407")
		(at 98.8 35.844001)
		(descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "TH1"
			(at 1.3 -1.6 0)
			(layer "F.SilkS")
			(uuid "3449415a-0ac1-42fa-9f5d-ad2c3c4f9509")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "NCP18XH103F03RB"
			(at 8.3 0 0)
			(layer "F.Fab")
			(uuid "808dc2ee-b652-46a5-a23a-9d8f35828d32")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "9ae8e9fc-5263-4226-9f54-e36738c708c7")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(layer "F.Fab")
			(hide yes)
			(uuid "a451d90a-59a5-42b0-aa6c-3e8d6a08449f")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054e05f89")
		(attr smd)
		(fp_line
			(start -0.237258 -0.5225)
			(end 0.237258 -0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "23e6f0d2-af8c-408c-a653-b4e67a10e4cb")
		)
		(fp_line
			(start -0.237258 0.5225)
			(end 0.237258 0.5225)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4fa9a428-e612-4191-be44-f1c1b24187c7")
		)
		(fp_line
			(start -1.48 -0.73)
			(end 1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1ce5b42c-69ad-496c-ad78-f820f2f36dda")
		)
		(fp_line
			(start -1.48 0.73)
			(end -1.48 -0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "aba02093-8535-43d6-89f6-a2268c1bc9f3")
		)
		(fp_line
			(start 1.48 -0.73)
			(end 1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "1d264c47-2500-4eba-84d7-1378477532f9")
		)
		(fp_line
			(start 1.48 0.73)
			(end -1.48 0.73)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2b4a354a-06fe-4eb6-bd62-de9ff575b463")
		)
		(fp_line
			(start -0.8 -0.4125)
			(end 0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "373ed019-b6e1-4683-8371-820fe3cdc17c")
		)
		(fp_line
			(start -0.8 0.4125)
			(end -0.8 -0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3f5b4004-e7c1-4b03-b155-ee07ca7bcdd0")
		)
		(fp_line
			(start 0.8 -0.4125)
			(end 0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "48f580a0-2776-47eb-8170-77ced12e2065")
		)
		(fp_line
			(start 0.8 0.4125)
			(end -0.8 0.4125)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "51db9802-0fb1-48ea-9bd5-e933f52a6949")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "7d5be268-9291-43cc-9d1c-e10b9041357a")
			(effects
				(font
					(size 0.4 0.4)
					(thickness 0.06)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.825 0)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 7 "/THERMISTOR")
			(uuid "4f9d5a1b-2cac-4529-9dcb-e8dbb00cc219")
		)
		(pad "2" smd roundrect
			(at 0.825 0)
			(size 0.8 0.95)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.25)
			(net 2 "GND")
			(uuid "5029aac3-dfe7-429b-8dd2-9035dc4a9061")
		)
		(embedded_fonts no)
		(model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "chirp_logo:ziogas"
		(layer "B.Cu")
		(uuid "00000000-0000-0000-0000-00005608dfbe")
		(at 95.95 82.8 -90)
		(property "Reference" "G***"
			(at 0 0 270)
			(layer "B.SilkS")
			(hide yes)
			(uuid "860d2b5e-3982-458e-b971-2bdf8a95183f")
			(effects
				(font
					(size 1.524 1.524)
					(thickness 0.3)
				)
				(justify mirror)
			)
		)
		(property "Value" "LOGO"
			(at 0.75 0 270)
			(layer "B.Fab")
			(hide yes)
			(uuid "36cb7289-b9ef-4e13-9073-2e32ea5dd1c5")
			(effects
				(font
					(size 1.524 1.524)
					(thickness 0.3)
				)
				(justify mirror)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(layer "F.Fab")
			(hide yes)
			(uuid "b8a4e799-c917-42e1-b78f-4bed786fedc6")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(layer "F.Fab")
			(hide yes)
			(uuid "b31e14ed-38dc-49af-973d-0fd26970fcdd")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(attr through_hole)
		(fp_poly
			(pts
				(xy 18.477643 6.43786) (xy 18.483798 6.35) (xy 18.580153 6.099892) (xy 18.671523 6.055629) (xy 18.822559 6.195173)
				(xy 18.856907 6.35) (xy 18.908991 6.410261) (xy 19.016884 6.192764) (xy 19.050278 6.094805) (xy 19.140935 5.696586)
				(xy 19.129127 5.451221) (xy 19.119704 5.438468) (xy 18.918051 5.394371) (xy 18.452257 5.35603) (xy 17.794604 5.327901)
				(xy 17.120346 5.315186) (xy 16.294477 5.30314) (xy 15.745995 5.277424) (xy 15.415882 5.227911) (xy 15.245117 5.144472)
				(xy 15.174684 5.016981) (xy 15.16638 4.979889) (xy 15.090301 4.773162) (xy 14.966701 4.879737) (xy 14.9564 4.895783)
				(xy 14.848438 4.978697) (xy 14.806803 4.753649) (xy 14.805224 4.66788) (xy 14.738436 4.317276) (xy 14.505126 4.206263)
				(xy 14.466225 4.205298) (xy 14.192243 4.310476) (xy 14.129801 4.457615) (xy 13.989563 4.663102)
				(xy 13.793377 4.709933) (xy 13.519395 4.604755) (xy 13.456953 4.457615) (xy 13.391208 4.324971)
				(xy 13.153497 4.246894) (xy 12.683109 4.211339) (xy 12.185436 4.205298) (xy 11.562225 4.186565)
				(xy 11.14949 4.135595) (xy 11.006242 4.060225) (xy 11.010396 4.049196) (xy 10.958239 3.881308) (xy 10.793532 3.793645)
				(xy 10.549791 3.618841) (xy 10.321545 3.315653) (xy 10.17127 2.996283) (xy 10.161435 2.772936) (xy 10.20537 2.737277)
				(xy 10.809715 2.423284) (xy 11.285038 1.974653) (xy 11.524444 1.505128) (xy 11.555917 1.074325)
				(xy 11.386022 0.817885) (xy 10.967874 0.698136) (xy 10.491788 0.675423) (xy 9.953214 0.600745) (xy 9.585943 0.410953)
				(xy 9.455284 0.150702) (xy 9.503973 0) (xy 9.506904 -0.26707) (xy 9.419867 -0.42053) (xy 9.30377 -0.704395)
				(xy 9.331025 -0.833396) (xy 9.324945 -1.086209) (xy 9.177294 -1.382829) (xy 8.974279 -1.845281)
				(xy 8.915231 -2.224184) (xy 8.861848 -2.566339) (xy 8.637173 -2.683991) (xy 8.478602 -2.691391)
				(xy 7.995786 -2.611543) (xy 7.59549 -2.459565) (xy 7.103455 -2.21955) (xy 6.728476 -2.052605) (xy 6.345921 -1.887593)
				(xy 5.796267 -1.643979) (xy 5.360438 -1.447768) (xy 4.928027 -1.26159) (xy 6.560264 -1.26159) (xy 6.64437 -1.345696)
				(xy 6.728476 -1.26159) (xy 6.64437 -1.177484) (xy 6.560264 -1.26159) (xy 4.928027 -1.26159) (xy 4.819551 -1.214885)
				(xy 4.438112 -1.113158) (xy 4.072191 -1.128344) (xy 3.577855 -1.246198) (xy 3.510107 -1.264641)
				(xy 2.811009 -1.448645) (xy 2.085019 -1.629397) (xy 1.808278 -1.694713) (xy 1.345782 -1.826769)
				(xy 1.057311 -1.958868) (xy 1.009271 -2.017284) (xy 0.856297 -2.108836) (xy 0.4524 -2.222417) (xy -0.119886 -2.335493)
				(xy -0.210265 -2.350243) (xy -1.165003 -2.520065) (xy -1.835953 -2.710811) (xy -2.274215 -2.975557)
				(xy -2.530886 -3.367379) (xy -2.657063 -3.939351) (xy -2.703844 -4.744551) (xy -2.7083 -4.944977)
				(xy -2.742332 -5.657862) (xy -2.803897 -6.154601) (xy -2.879415 -6.411295) (xy -2.955308 -6.404048)
				(xy -3.017994 -6.108961) (xy -3.052657 -5.550994) (xy -3.082651 -4.457616) (xy -3.181392 -5.381324)
				(xy -3.268818 -5.948106) (xy -3.396745 -6.251611) (xy -3.588462 -6.363564) (xy -3.797136 -6.363833)
				(xy -3.768095 -6.186762) (xy -3.714621 -6.08171) (xy -3.598098 -5.699347) (xy -3.533233 -5.168456)
				(xy -3.528757 -5.015364) (xy -3.451483 -4.395231) (xy -3.262975 -3.729828) (xy -3.187609 -3.545585)
				(xy -2.984963 -2.992904) (xy -2.989801 -2.674946) (xy -3.023092 -2.62883) (xy -3.133316 -2.345547)
				(xy -3.186128 -1.858707) (xy -3.184603 -1.68212) (xy -2.691391 -1.68212) (xy -2.629845 -1.820577)
				(xy -2.57925 -1.794261) (xy -2.559118 -1.594633) (xy -2.57925 -1.569978) (xy -2.679252 -1.593069)
				(xy -2.691391 -1.68212) (xy -3.184603 -1.68212) (xy -3.181269 -1.296284) (xy -3.142144 -0.978459)
				(xy 0.388828 -0.978459) (xy 0.524449 -1.075547) (xy 0.548008 -1.087745) (xy 0.89452 -1.187765) (xy 1.338373 -1.137354)
				(xy 1.682119 -1.039728) (xy 2.307034 -0.851541) (xy 3.032894 -0.64699) (xy 3.364238 -0.558471) (xy 3.641594 -0.484669)
				(xy 5.466887 -0.484669) (xy 5.863999 -0.831077) (xy 6.213654 -1.111671) (xy 6.372164 -1.167855)
				(xy 6.392052 -1.112724) (xy 6.284095 -1.009272) (xy 8.410596 -1.009272) (xy 8.472142 -1.147729)
				(xy 8.522737 -1.121413) (xy 8.542869 -0.921785) (xy 8.522737 -0.897131) (xy 8.422735 -0.920221)
				(xy 8.410596 -1.009272) (xy 6.284095 -1.009272) (xy 6.264638 -0.990627) (xy 5.955342 -0.78217) (xy 5.92947 -0.766317)
				(xy 5.466887 -0.484669) (xy 3.641594 -0.484669) (xy 3.918135 -0.411084) (xy 4.121403 -0.354435)
				(xy 6.920277 -0.354435) (xy 7.075119 -0.519059) (xy 7.161286 -0.58513) (xy 7.510772 -0.80159) (xy 7.685099 -0.747371)
				(xy 7.737194 -0.399094) (xy 7.737748 -0.330506) (xy 7.715169 0.013299) (xy 7.682987 0.032814) (xy 8.524616 0.032814)
				(xy 8.540327 -0.207442) (xy 8.622247 -0.460274) (xy 8.700036 -0.397489) (xy 8.763457 -0.24331) (xy 8.87683 0.13919)
				(xy 8.903408 0.314967) (xy 8.858985 0.457) (xy 8.680279 0.350835) (xy 8.524616 0.032814) (xy 7.682987 0.032814)
				(xy 7.595359 0.085949) (xy 7.359271 -0.016867) (xy 7.004712 -0.216549) (xy 6.920277 -0.354435) (xy 4.121403 -0.354435)
				(xy 4.359245 -0.288151) (xy 4.531306 -0.236003) (xy 4.547917 -0.123671) (xy 4.27824 0.082784) (xy 3.970038 0.252127)
				(xy 3.166866 0.658745) (xy 1.668859 -0.118357) (xy 0.996217 -0.472692) (xy 0.585084 -0.711305) (xy 0.39583 -0.86847)
				(xy 0.388828 -0.978459) (xy -3.142144 -0.978459) (xy -3.118483 -0.786254) (xy -3.027361 -0.503789)
				(xy -2.871946 -0.298389) (xy -2.656668 -0.305604) (xy -2.413727 -0.419486) (xy -2.117461 -0.542914)
				(xy -1.886008 -0.505129) (xy -1.598784 -0.268141) (xy -1.446962 -0.114905) (xy -1.072811 0.373277)
				(xy -0.977205 0.80804) (xy -0.981723 0.85185) (xy -1.071192 1.153749) (xy -1.301962 1.2337) (xy -1.526258 1.211551)
				(xy -2.048726 1.194604) (xy -2.43692 1.246018) (xy -2.714144 1.36514) (xy -2.834991 1.607725) (xy -2.859603 2.024952)
				(xy -2.876335 2.450804) (xy -2.966078 2.620539) (xy -3.188158 2.614186) (xy -3.280133 2.592254)
				(xy -3.600281 2.564758) (xy -3.699988 2.729809) (xy -3.700663 2.757261) (xy -3.590436 2.955222)
				(xy -3.222714 3.026419) (xy -3.13102 3.027814) (xy -2.770327 3.003052) (xy -2.585422 2.866749) (xy -2.488272 2.52588)
				(xy -2.454489 2.312914) (xy -2.347599 1.598013) (xy -0.42053 1.598013) (xy -0.36782 1.051324) (xy -0.276734 0.65829)
				(xy -0.143751 0.512864) (xy -0.031969 0.637704) (xy 0 0.919247) (xy 0.002057 0.925165) (xy 0.336423 0.925165)
				(xy 0.420529 0.841059) (xy 0.504635 0.925165) (xy 0.420529 1.009271) (xy 0.336423 0.925165) (xy 0.002057 0.925165)
				(xy 0.060531 1.093377) (xy 0.672847 1.093377) (xy 0.756953 1.009271) (xy 0.841059 1.093377) (xy 0.756953 1.177483)
				(xy 0.672847 1.093377) (xy 0.060531 1.093377) (xy 0.147161 1.342583) (xy 0.415769 1.508046) (xy 5.046357 1.508046)
				(xy 5.161746 1.337694) (xy 5.443322 1.046533) (xy 5.794195 0.723452) (xy 6.117479 0.457339) (xy 6.316285 0.337084)
				(xy 6.323418 0.336423) (xy 6.525576 0.374087) (xy 6.93509 0.469299) (xy 7.158624 0.524605) (xy 7.90596 0.712786)
				(xy 7.90596 1.553486) (xy 7.894246 2.047863) (xy 7.833553 2.28129) (xy 7.685548 2.32907) (xy 7.527483 2.297046)
				(xy 6.480948 2.021476) (xy 5.738295 1.808946) (xy 5.274557 1.651518) (xy 5.064767 1.54125) (xy 5.046357 1.508046)
				(xy 0.415769 1.508046) (xy 0.569605 1.602809) (xy 1.166424 1.682119) (xy 1.535747 1.72837) (xy 1.672522 1.914286)
				(xy 1.68397 2.060596) (xy 1.712873 2.483929) (xy 1.779638 3.002989) (xy 2.524679 3.002989) (xy 2.553162 2.633402)
				(xy 2.622694 2.479734) (xy 2.791003 2.424629) (xy 3.122854 2.480708) (xy 3.164241 2.494121) (xy 8.642245 2.494121)
				(xy 8.671835 1.793749) (xy 8.695293 1.337359) (xy 8.718154 1.04346) (xy 8.724223 1.003772) (xy 8.885003 0.975995)
				(xy 9.279881 0.968161) (xy 9.807341 0.97685) (xy 10.365869 0.998646) (xy 10.853951 1.03013) (xy 11.170072 1.067886)
				(xy 11.232105 1.088207) (xy 11.220715 1.255743) (xy 11.173361 1.402321) (xy 10.901104 1.741192)
				(xy 10.408527 2.067385) (xy 9.808155 2.319286) (xy 9.298655 2.428819) (xy 8.642245 2.494121) (xy 3.164241 2.494121)
				(xy 3.678051 2.660637) (xy 3.95298 2.761482) (xy 4.484944 2.924773) (xy 4.959945 3.014106) (xy 5.046357 3.019319)
				(xy 5.532345 3.051634) (xy 6.139982 3.126453) (xy 6.782977 3.228323) (xy 7.375041 3.34179) (xy 7.82988 3.451401)
				(xy 8.061206 3.541702) (xy 8.074172 3.561554) (xy 7.916813 3.615255) (xy 7.488069 3.659325) (xy 6.852939 3.689285)
				(xy 6.076422 3.700655) (xy 6.055629 3.700662) (xy 5.197456 3.695994) (xy 4.62038 3.67667) (xy 4.26914 3.634707)
				(xy 4.088477 3.562124) (xy 4.02313 3.450939) (xy 4.017573 3.406291) (xy 3.994077 3.198979) (xy 3.944424 3.318431)
				(xy 3.921133 3.406291) (xy 3.755977 3.642502) (xy 3.52641 3.687363) (xy 3.373878 3.530192) (xy 3.364238 3.448344)
				(xy 3.22959 3.225334) (xy 3.11192 3.196026) (xy 2.891277 3.331451) (xy 2.859602 3.458271) (xy 2.785466 3.616556)
				(xy 3.027814 3.616556) (xy 3.11192 3.53245) (xy 3.196026 3.616556) (xy 3.11192 3.700662) (xy 3.027814 3.616556)
				(xy 2.785466 3.616556) (xy 2.778054 3.63238) (xy 2.69139 3.616556) (xy 2.574256 3.388643) (xy 2.524679 3.002989)
				(xy 1.779638 3.002989) (xy 1.780952 3.013204) (xy 1.790085 3.069867) (xy 1.840906 3.480347) (xy 1.766098 3.657807)
				(xy 1.502213 3.699859) (xy 1.380038 3.700662) (xy 0.944023 3.612256) (xy 0.7888 3.406291) (xy 0.72487 3.198979)
				(xy 0.698515 3.318431) (xy 0.69236 3.406291) (xy 0.571072 3.638751) (xy 0.351172 3.691472) (xy 0.185316 3.552205)
				(xy 0.168211 3.448344) (xy 0.085011 3.221734) (xy -0.085949 3.248554) (xy -0.220472 3.490397) (xy -0.284402 3.697709)
				(xy -0.310757 3.578257) (xy -0.316912 3.490397) (xy -0.454676 3.255938) (xy -0.725644 3.20525) (xy -0.981221 3.338334)
				(xy -1.061531 3.490397) (xy -1.125461 3.697709) (xy -1.151816 3.578257) (xy -1.157971 3.490397)
				(xy -1.203407 3.354642) (xy -1.358354 3.267949) (xy -1.683345 3.219723) (xy -2.238913 3.199371)
				(xy -2.839984 3.196026) (xy -3.650969 3.205058) (xy -4.184119 3.255876) (xy -4.497966 3.384065)
				(xy -4.651043 3.625208) (xy -4.70188 4.01489) (xy -4.707637 4.331457) (xy -4.772822 4.626923) (xy -4.878146 4.709933)
				(xy -5.026576 4.849301) (xy -5.058181 5.004304) (xy -5.080479 5.202125) (xy -5.151815 5.089093)
				(xy -5.190907 4.991857) (xy -5.359682 4.737566) (xy -5.466888 4.685038) (xy -5.648875 4.820703)
				(xy -5.742868 4.991857) (xy -5.838967 5.200781) (xy -5.871046 5.097448) (xy -5.875595 5.004304)
				(xy -5.947275 4.822515) (xy -6.195537 4.733659) (xy -6.698835 4.709936) (xy -6.706329 4.709933)
				(xy -7.52524 4.709933) (xy -7.668214 3.995033) (xy -7.723882 3.547113) (xy -7.769546 2.857176) (xy -7.800465 2.019589)
				(xy -7.811896 1.13543) (xy -7.812604 -1.009272) (xy -8.537409 -1.009272) (xy -9.096264 -0.950222)
				(xy -9.353452 -0.770231) (xy -9.359038 -0.756954) (xy -9.52415 -0.534362) (xy -9.606077 -0.504636)
				(xy -9.739427 -0.641145) (xy -9.756292 -0.756954) (xy -9.89094 -0.979964) (xy -10.00861 -1.009272)
				(xy -10.23162 -0.874624) (xy -10.260928 -0.756954) (xy -10.37977 -0.55261) (xy -10.63917 -0.516574)
				(xy -10.893441 -0.640398) (xy -10.986034 -0.799007) (xy -11.049964 -1.006319) (xy -11.076319 -0.886867)
				(xy -11.082474 -0.799007) (xy -11.177118 -0.6008) (xy -11.478477 -0.515557) (xy -11.774835 -0.504636)
				(xy -12.227519 -0.539203) (xy -12.427736 -0.665534) (xy -12.459505 -0.799007) (xy -12.486868 -0.979045)
				(xy -12.571392 -0.85756) (xy -12.615895 -0.756954) (xy -12.729363 -0.516558) (xy -12.771009 -0.585256)
				(xy -12.785564 -0.756954) (xy -12.835221 -0.946278) (xy -12.898872 -0.873026) (xy -13.110655 -0.526925)
				(xy -13.422343 -0.194199) (xy -13.737478 0.038649) (xy -13.959602 0.085115) (xy -13.964288 0.082438)
				(xy -14.145858 0.12549) (xy -14.257784 0.361552) (xy -14.363122 0.756953) (xy -14.482012 0.336423)
				(xy -14.569508 0.054548) (xy -14.604976 0.086885) (xy -14.61767 0.29437) (xy -14.689927 0.590165)
				(xy -14.79095 0.672847) (xy -14.940555 0.530316) (xy -15.020361 0.29437) (xy -15.093258 -0.084106)
				(xy -15.116166 0.29437) (xy -15.189013 0.553668) (xy -15.430274 0.658524) (xy -15.727815 0.672847)
				(xy -16.163258 0.727562) (xy -16.315493 0.902185) (xy -16.316557 0.925165) (xy -16.388946 1.155293)
				(xy -16.542762 1.130052) (xy -16.682976 0.880505) (xy -16.70248 0.799006) (xy -16.775378 0.420529)
				(xy -16.798285 0.799006) (xy -16.837342 1.003129) (xy -16.974714 1.116803) (xy -17.287559 1.166188)
				(xy -17.853036 1.177446) (xy -17.91457 1.177483) (xy -18.506289 1.187968) (xy -18.836186 1.234673)
				(xy -18.9786 1.340471) (xy -19.007947 1.513907) (xy -18.962652 1.717403) (xy -18.771233 1.817838)
				(xy -18.350385 1.849261) (xy -18.184885 1.850331) (xy -17.605598 1.807366) (xy -17.309709 1.670632)
				(xy -17.264998 1.598013) (xy -17.06994 1.38791) (xy -16.818313 1.365904) (xy -16.660295 1.531995)
				(xy -16.652981 1.598013) (xy -16.518333 1.821023) (xy -16.400663 1.850331) (xy -16.177653 1.715683)
				(xy -16.148345 1.598013) (xy -16.013697 1.375003) (xy -15.896027 1.345695) (xy -15.673017 1.211047)
				(xy -15.643709 1.093377) (xy -15.507767 0.903777) (xy -15.186546 0.827372) (xy -14.809951 0.85972)
				(xy -14.507891 0.996379) (xy -14.413966 1.13543) (xy -14.350036 1.342742) (xy -14.323682 1.22329)
				(xy -14.317527 1.13543) (xy -14.18757 0.910383) (xy -13.801809 0.841075) (xy -13.793378 0.841059)
				(xy -13.392745 0.765833) (xy -13.288742 0.588741) (xy -13.271546 0.474919) (xy -13.185908 0.396094)
				(xy -12.980758 0.346552) (xy -12.605023 0.320578) (xy -12.00763 0.312456) (xy -11.137508 0.31647)
				(xy -10.875341 0.318666) (xy -10.154023 0.317134) (xy -9.701548 0.28882) (xy -9.450627 0.221009)
				(xy -9.333973 0.100985) (xy -9.306183 0.022133) (xy -9.131184 -0.207747) (xy -8.72699 -0.26736)
				(xy -8.69177 -0.266213) (xy -8.158279 -0.245112) (xy -8.055856 2.610888) (xy -8.020855 3.55223)
				(xy -7.988164 4.367893) (xy -7.960205 5.002192) (xy -7.939404 5.399445) (xy -7.929697 5.50894) (xy -7.763992 5.534105)
				(xy -7.372522 5.549129) (xy -7.149007 5.550993) (xy -6.650089 5.584014) (xy -6.422832 5.696293)
				(xy -6.392053 5.803311) (xy -6.292991 5.969617) (xy -5.956154 6.045369) (xy -5.6351 6.055629) (xy -5.121329 6.017461)
				(xy -4.896271 5.894732) (xy -4.878146 5.821308) (xy -4.742034 5.563975) (xy -4.615334 5.486136)
				(xy -4.457571 5.350258) (xy -4.419269 5.053774) (xy -4.466221 4.62708) (xy -4.579921 3.868874) (xy -3.719762 3.868874)
				(xy -3.208332 3.885794) (xy -3.137302 3.90567) (xy 8.786376 3.90567) (xy 8.84386 3.7406) (xy 8.941514 3.737458)
				(xy 9.133342 3.638122) (xy 9.191721 3.448344) (xy 9.228926 3.053375) (xy 9.288302 2.89145) (xy 9.409144 2.859625)
				(xy 9.416448 2.859602) (xy 9.584937 2.99646) (xy 9.820273 3.342383) (xy 9.929651 3.54254) (xy 10.27806 4.225478)
				(xy 9.567666 4.173335) (xy 9.113066 4.09463) (xy 8.825228 3.959861) (xy 8.786376 3.90567) (xy -3.137302 3.90567)
				(xy -2.950538 3.957931) (xy -2.864391 4.117355) (xy -2.859603 4.205298) (xy -2.788181 4.493446)
				(xy -2.635213 4.503374) (xy -2.492644 4.245102) (xy -2.47368 4.163245) (xy -2.417022 3.919608) (xy -2.387846 3.997542)
				(xy -2.378037 4.121192) (xy -2.3582 4.233061) (xy -2.28747 4.316102) (xy -2.123168 4.373782) (xy -1.822616 4.40957)
				(xy -1.343136 4.426935) (xy -0.64205 4.429345) (xy 0.32332 4.42027) (xy 1.16478 4.409139) (xy 4.684851 4.360663)
				(xy 4.758591 4.745563) (xy 4.83233 5.130463) (xy 4.855238 4.751986) (xy 4.966156 4.454236) (xy 5.286976 4.37351)
				(xy 5.6391 4.480761) (xy 5.768704 4.751986) (xy 5.841602 5.130463) (xy 5.864509 4.751986) (xy 5.951526 4.476722)
				(xy 6.231594 4.378938) (xy 6.392052 4.37351) (xy 6.760457 4.425022) (xy 6.900159 4.627105) (xy 6.913456 4.751986)
				(xy 6.933592 5.002063) (xy 6.984674 4.949234) (xy 7.0649 4.709933) (xy 7.167309 4.470657) (xy 7.21497 4.563321)
				(xy 7.216345 4.583774) (xy 7.304517 4.833882) (xy 7.38899 4.878145) (xy 7.557292 4.739584) (xy 7.621795 4.583774)
				(xy 7.685725 4.376462) (xy 7.71208 4.495914) (xy 7.718235 4.583774) (xy 7.876273 4.831287) (xy 8.074172 4.878145)
				(xy 8.35078 4.98653) (xy 8.410596 5.137497) (xy 8.518517 5.444306) (xy 8.742501 5.753106) (xy 9.074406 6.109365)
				(xy 9.227764 5.70402) (xy 9.288146 5.266976) (xy 9.211686 5.08841) (xy 9.152548 4.91549) (xy 9.320444 4.878145)
				(xy 9.616095 5.010848) (xy 9.695461 5.130463) (xy 9.897214 5.353841) (xy 10.008609 5.382781) (xy 10.252057 5.247439)
				(xy 10.321757 5.130463) (xy 10.505884 4.896514) (xy 10.692854 4.955748) (xy 10.78233 5.256622) (xy 10.8035 5.516717)
				(xy 10.855692 5.451908) (xy 10.908489 5.292892) (xy 11.048455 5.073363) (xy 11.347551 4.961753)
				(xy 11.816887 4.922617) (xy 12.329577 4.933033) (xy 12.572948 5.022249) (xy 12.615894 5.138665)
				(xy 12.687022 5.365227) (xy 12.742052 5.395281) (xy 13.41185 5.525005) (xy 13.796161 5.746804) (xy 13.839758 5.808058)
				(xy 14.045389 6.034297) (xy 14.236897 5.935678) (xy 14.424719 5.50894) (xy 14.600092 4.962251) (xy 14.617264 5.50894)
				(xy 14.678736 5.90182) (xy 14.863008 6.047742) (xy 14.958526 6.055629) (xy 15.26297 6.187614) (xy 15.359543 6.35)
				(xy 15.423473 6.557312) (xy 15.449828 6.43786) (xy 15.455983 6.35) (xy 15.506512 6.204908) (xy 15.677892 6.116479)
				(xy 16.035185 6.071385) (xy 16.643451 6.056297) (xy 16.892964 6.055629) (xy 17.599347 6.065223)
				(xy 18.037125 6.102871) (xy 18.273789 6.181867) (xy 18.376834 6.315505) (xy 18.387358 6.35) (xy 18.451288 6.557312)
				(xy 18.477643 6.43786)
			)
			(stroke
				(width 0.01)
				(type solid)
			)
			(fill yes)
			(layer "F.SilkS")
			(uuid "b43741e6-9eb1-48fc-9e00-be90cf25e67c")
		)
		(embedded_fonts no)
	)
	(footprint "Pin_Headers:Pin_Header_Straight_1x04_Pitch2.54mm"
		(layer "B.Cu")
		(uuid "00000000-0000-0000-0000-000058971503")
		(at 91.7 14.799451 -90)
		(descr "Through hole straight pin header, 1x04, 2.54mm pitch, single row")
		(tags "Through hole pin header THT 1x04 2.54mm single row")
		(property "Reference" "P2"
			(at 0.17 -10 270)
			(layer "B.SilkS")
			(hide yes)
			(uuid "9b7b8d8f-1944-4e68-bb3a-f254ebb416b5")
			(effects
				(font
					(size 0.7 0.7)
					(thickness 0.11)
				)
				(justify mirror)
			)
		)
		(property "Value" "CONN_01X04"
			(at 0 -10.01 270)
			(layer "B.Fab")
			(uuid "83ac3052-6e85-4491-b8f9-196d0c08f6c3")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
				(justify mirror)
			)
		)
		(property "Datasheet" ""
			(at 0 0 270)
			(layer "F.Fab")
			(hide yes)
			(uuid "f02a21e8-1b0f-4c99-8318-c7a8c467c334")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 270)
			(layer "F.Fab")
			(hide yes)
			(uuid "16f88e25-dfee-4cd5-9a3e-a95ea9bdc1ed")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(path "/00000000-0000-0000-0000-000054e1fe0e")
		(attr through_hole)
		(fp_line
			(start -1.6 1.6)
			(end -1.6 -9.2)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "fd436434-e911-4589-9adf-d68244199b62")
		)
		(fp_line
			(start 1.6 1.6)
			(end -1.6 1.6)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "df713674-234c-4e89-9d77-4a89f905eb80")
		)
		(fp_line
			(start -1.6 -9.2)
			(end 1.6 -9.2)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "f547c455-15a6-4f98-bbdd-10c271cdb6d6")
		)
		(fp_line
			(start 1.6 -9.2)
			(end 1.6 1.6)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "B.CrtYd")
			(uuid "9c530476-9fac-4645-8de3-b74e7b1d4ca0")
		)
		(fp_line
			(start -1.27 1.27)
			(end -1.27 -8.89)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "649b9405-a1a5-49c1-8b9d-a9ce842a620b")
		)
		(fp_line
			(start 1.27 1.27)
			(end -1.27 1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "121274b6-d474-422b-b60d-ad655d344136")
		)
		(fp_line
			(start -1.27 -8.89)
			(end 1.27 -8.89)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "e95e0728-6792-48b2-b0a4-2be519522354")
		)
		(fp_line
			(start 1.27 -8.89)
			(end 1.27 1.27)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "B.Fab")
			(uuid "a230d801-db14-4411-8660-fd3077f1f036")
		)
		(pad "1" smd rect
			(at 0 0 270)
			(size 2.5 1.5)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 2 "GND")
			(uuid "41887a32-68d0-4d81-8b39-12846e0479bd")
		)
		(pad "2" smd oval
			(at 0 -2.54 270)
			(size 2.5 1.5)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 12 "/MOSI/SDA")
			(uuid "c886c17c-8ef0-4808-a908-813e57226d1e")
		)
		(pad "3" smd oval
			(at 0 -5.08 270)
			(size 2.5 1.5)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 11 "/SCK/SCL")
			(uuid "7c00002a-9224-4913-accd-277a6086ea90")
		)
		(pad "4" smd oval
			(at 0 -7.62 270)
			(size 2.5 1.5)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 4 "VCC")
			(uuid "d8385ba2-a9ff-4547-924c-e2ef7436c3a1")
		)
		(embedded_fonts no)
	)
	(footprint "programming_header:Pin_Header_Straight_2x03"
		(layer "B.Cu")
		(uuid "00000000-0000-0000-0000-00005b3f6bdc")
		(at 98.3 27.2 180)
		(descr "Through hole pin header")
		(tags "pin header")
		(property "Reference" "P1"
			(at 0 3.81 180)
			(layer "B.SilkS")
			(hide yes)
			(uuid "d38bf16e-5ae6-4951-8c98-0f6e646c36c0")
			(effects
				(font
					(size 0.7 0.7)
					(thickness 0.11)
				)
				(justify mirror)
			)
		)
		(property "Value" "CONN_02X03"
			(at 0.25 -3 0)
			(layer "B.SilkS")
			(hide yes)
			(uuid "3f2cd4cc-d3c4-4fde-8dea-f05874be8bc5")
			(effects
				(font
					(size 0.7 0.7)
					(thickness 0.11)
				)
				(justify mirror)
			)
		)
		(property "Datasheet" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "408e5c49-5146-4f6f-bfd5-8c6da82b1f2e")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 180)
			(layer "F.Fab")
			(hide yes)
			(uuid "d46acaf9-9a9e-4cf6-bfe7-9e73afb8a879")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(attr through_hole)
		(pad "1" smd rect
			(at -2.54 -1.27 180)
			(size 1.7272 1.7272)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 13 "/MISO/SENSE_HIGH")
			(uuid "748a682b-8a57-403d-b7a3-985ebe69da5c")
		)
		(pad "2" smd circle
			(at -2.54 1.27 180)
			(size 1.7272 1.7272)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 4 "VCC")
			(uuid "697c152f-ccfb-4050-937c-4a3ead5503be")
		)
		(pad "3" smd circle
			(at 0 -1.27 180)
			(size 1.7272 1.7272)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 11 "/SCK/SCL")
			(uuid "984f1209-d4e5-4380-ad7b-863e8560e48e")
		)
		(pad "4" smd circle
			(at 0 1.27 180)
			(size 1.7272 1.7272)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 12 "/MOSI/SDA")
			(uuid "3e6e2273-ac22-451c-9401-ae77ee993d27")
		)
		(pad "5" smd circle
			(at 2.54 -1.27 180)
			(size 1.7272 1.7272)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 8 "/RESET")
			(uuid "53ac73f9-362a-4f6f-9e7c-eae60826eef0")
		)
		(pad "6" smd circle
			(at 2.54 1.27 180)
			(size 1.7272 1.7272)
			(layers "B.Cu" "B.Mask" "B.Paste")
			(net 2 "GND")
			(uuid "9fac306c-312e-41c7-a693-1df3019b4590")
		)
		(embedded_fonts no)
	)
	(gr_circle
		(center 98.8 35.7)
		(end 100 35.6)
		(stroke
			(width 0.15)
			(type solid)
		)
		(fill no)
		(layer "F.SilkS")
		(uuid "83452332-6a63-4b64-8fa9-d563803a35c2")
	)
	(gr_line
		(start 87.3 47)
		(end 103.3 47)
		(stroke
			(width 1)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9e0f7a09-f1f3-4b3e-8fab-a9e9855f6a2d")
	)
	(gr_line
		(start 99.1 37.2)
		(end 99.9 39.3)
		(stroke
			(width 0.15)
			(type solid)
		)
		(layer "F.SilkS")
		(uuid "9f2fe541-5357-4bd3-a21e-3fb8627c372d")
	)
	(gr_line
		(start 87.3 47)
		(end 103.3 47)
		(stroke
			(width 1)
			(type solid)
		)
		(layer "B.SilkS")
		(uuid "d5b0e13d-d8fe-4718-bb8a-f044f4d4756a")
	)
	(gr_line
		(start 99.2 19.9)
		(end 104.3 25)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "14e6fb4f-4096-47eb-a949-6895db9aedde")
	)
	(gr_line
		(start 104.3 40.2)
		(end 104.3 25)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "2ae314a4-a854-4f85-a31b-2bf3cc0fe9eb")
	)
	(gr_line
		(start 87.3 42.2)
		(end 86.3 41.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "2b850d5c-18fb-44b4-a9bb-01a25a9d4a73")
	)
	(gr_line
		(start 85.3 45.2)
		(end 85.3 100.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "2ed78678-2e55-4a5b-856b-1b61df9149dc")
	)
	(gr_line
		(start 103.3 43.2)
		(end 105.3 45.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "409e5f6d-056d-4a9b-8a53-5b241a49d58f")
	)
	(gr_line
		(start 99.2 16.4)
		(end 99.2 19.9)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "42f3b809-cb13-4830-a027-a34bf245d974")
	)
	(gr_line
		(start 99.2 13.2)
		(end 91.4 13.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "464e463d-eec7-45b9-8e2c-556370d089da")
	)
	(gr_line
		(start 91.4 16.4)
		(end 91.4 19.9)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "521802ac-4cf7-417e-86a6-1608e9b47509")
	)
	(gr_line
		(start 104.3 40.2)
		(end 104.3 41.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "583e37bd-0878-4f7b-af88-db050fa6abd9")
	)
	(gr_line
		(start 104.3 41.2)
		(end 103.3 42.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "652bcb40-cad2-4705-9899-c07f80b2ae53")
	)
	(gr_line
		(start 91.4 19.9)
		(end 86.3 25)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "76894a17-1a17-4812-8f06-9823e8f86179")
	)
	(gr_line
		(start 95.3 113.2)
		(end 85.3 100.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "85c760ce-bc50-43e9-ac03-dbd40b462d98")
	)
	(gr_line
		(start 95.3 113.2)
		(end 105.3 100.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "8cbfa994-f5c0-4241-b5c6-0c766f2e0ed4")
	)
	(gr_line
		(start 87.3 42.2)
		(end 87.3 43.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "a2726226-0b3e-414a-b86a-2dd6705d057c")
	)
	(gr_line
		(start 103.3 42.2)
		(end 103.3 43.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "a549c2f2-6a0e-425d-b7f5-f113f73af978")
	)
	(gr_arc
		(start 91.4 16.4)
		(mid 89.8 14.8)
		(end 91.4 13.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "a96bda40-83b5-476d-a55c-939e1c798d13")
	)
	(gr_line
		(start 105.3 100.2)
		(end 105.3 45.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "b48788e8-26c8-4c75-a3cf-8ab7f8aabbd6")
	)
	(gr_line
		(start 85.3 45.2)
		(end 87.3 43.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "b7f1d961-4d66-44a9-a4ce-1e4a1fd3f5aa")
	)
	(gr_line
		(start 86.3 41.2)
		(end 86.3 40.2)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "d16a42c7-236b-422a-9cfe-352de928bdd6")
	)
	(gr_arc
		(start 99.2 13.2)
		(mid 100.8 14.8)
		(end 99.2 16.4)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "e874c920-a5f0-4338-aae8-1c6c15ad6158")
	)
	(gr_line
		(start 86.3 40.2)
		(end 86.3 25)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "Edge.Cuts")
		(uuid "f113b040-6f16-4618-8a55-b901a16ee21c")
	)
	(gr_text "temp\nsensor"
		(at 100.2 40.3 0)
		(layer "F.SilkS")
		(uuid "2c0adef3-4bb6-46cd-bc8b-f6af692fca7a")
		(effects
			(font
				(size 0.7 0.7)
				(thickness 0.11)
			)
		)
	)
	(gr_text "Soil moisture \nsensor v2.7.9"
		(at 95.7 51.5 0)
		(layer "F.SilkS")
		(uuid "32bfacc4-9128-49ae-b8b1-ec0ae5e94cf5")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3)
			)
		)
	)
	(gr_text "Catnip Electronics\nhttp://wemakethings.net/chirp\<EMAIL>"
		(at 94 76.5 270)
		(layer "B.SilkS")
		(uuid "267662ab-8ca1-4a26-bd17-063483758945")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3)
			)
			(justify mirror)
		)
	)
	(gr_text "GND\n\nSDA\n\nSCL\n\nVCC"
		(at 95.5 17.5 90)
		(layer "B.SilkS")
		(uuid "87f783d8-05f6-49d7-ab62-67355ed38528")
		(effects
			(font
				(size 0.6 0.6)
				(thickness 0.11)
			)
			(justify mirror)
		)
	)
	(dimension
		(type aligned)
		(layer "Cmts.User")
		(uuid "160a44b7-01b8-4d8e-b0a3-82303631fc34")
		(pts
			(xy 86.3 13.2) (xy 104.3 13.2)
		)
		(height -11)
		(format
			(prefix "")
			(suffix "")
			(units 2)
			(units_format 1)
			(precision 4)
		)
		(style
			(thickness 0.15)
			(arrow_length 1.27)
			(text_position_mode 0)
			(arrow_direction outward)
			(extension_height 0.58642)
			(extension_offset 0)
			(keep_text_aligned yes)
		)
		(gr_text "18.0000 mm"
			(at 95.3 1.05 0)
			(layer "Cmts.User")
			(uuid "160a44b7-01b8-4d8e-b0a3-82303631fc34")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
	)
	(dimension
		(type aligned)
		(layer "Cmts.User")
		(uuid "18ff0d68-7748-4d12-bd66-5815364b1148")
		(pts
			(xy 86 13) (xy 86 46.8)
		)
		(height 5.1)
		(format
			(prefix "")
			(suffix "")
			(units 2)
			(units_format 1)
			(precision 4)
		)
		(style
			(thickness 0.15)
			(arrow_length 1.27)
			(text_position_mode 0)
			(arrow_direction outward)
			(extension_height 0.58642)
			(extension_offset 0)
			(keep_text_aligned yes)
		)
		(gr_text "33.8000 mm"
			(at 79.75 29.9 90)
			(layer "Cmts.User")
			(uuid "18ff0d68-7748-4d12-bd66-5815364b1148")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
	)
	(dimension
		(type aligned)
		(layer "Cmts.User")
		(uuid "199ddb72-ea28-4edf-96be-bc91804246fc")
		(pts
			(xy 104.3 113.2) (xy 104.3 13.2)
		)
		(height 4.25)
		(format
			(prefix "")
			(suffix "")
			(units 2)
			(units_format 1)
			(precision 4)
		)
		(style
			(thickness 0.175)
			(arrow_length 1.27)
			(text_position_mode 0)
			(arrow_direction outward)
			(extension_height 0.58642)
			(extension_offset 0)
			(keep_text_aligned yes)
		)
		(gr_text "100.0000 mm"
			(at 107.675 63.2 90)
			(layer "Cmts.User")
			(uuid "199ddb72-ea28-4edf-96be-bc91804246fc")
			(effects
				(font
					(size 0.7 0.7)
					(thickness 0.175)
				)
			)
		)
	)
	(dimension
		(type aligned)
		(layer "Cmts.User")
		(uuid "87dec97e-f37b-403a-a604-8e8a2da8435f")
		(pts
			(xy 104.3 100.2) (xy 86.3 100.2)
		)
		(height -13)
		(format
			(prefix "")
			(suffix "")
			(units 2)
			(units_format 1)
			(precision 4)
		)
		(style
			(thickness 0.175)
			(arrow_length 1.27)
			(text_position_mode 0)
			(arrow_direction outward)
			(extension_height 0.58642)
			(extension_offset 0)
			(keep_text_aligned yes)
		)
		(gr_text "18.0000 mm"
			(at 95.3 112.325 0)
			(layer "Cmts.User")
			(uuid "87dec97e-f37b-403a-a604-8e8a2da8435f")
			(effects
				(font
					(size 0.7 0.7)
					(thickness 0.175)
				)
			)
		)
	)
	(dimension
		(type orthogonal)
		(layer "Cmts.User")
		(uuid "b17fe8a6-d64d-4cd9-ac44-2867d66f37e0")
		(pts
			(xy 100.8 14.8) (xy 104.3 25)
		)
		(height -4.2)
		(orientation 0)
		(format
			(prefix "")
			(suffix "")
			(units 2)
			(units_format 1)
			(precision 1)
		)
		(style
			(thickness 0.15)
			(arrow_length 1.27)
			(text_position_mode 2)
			(arrow_direction outward)
			(extension_height 0.58642)
			(extension_offset 0.5)
			(keep_text_aligned yes)
		)
		(gr_text "3.5 mm"
			(at 110.6 10.8 0)
			(layer "Cmts.User")
			(uuid "b17fe8a6-d64d-4cd9-ac44-2867d66f37e0")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
				(justify right)
			)
		)
	)
	(segment
		(start 92.47 26.73)
		(end 92.1 27.1)
		(width 0.254)
		(layer "F.Cu")
		(net 1)
		(uuid "61643b17-731f-4c9b-abd4-ad0270834b7c")
	)
	(segment
		(start 92 28.9)
		(end 92 27.2)
		(width 0.508)
		(layer "F.Cu")
		(net 1)
		(uuid "6953b9a0-9705-4ee7-b793-213e61bc92b5")
	)
	(segment
		(start 94.4 26.73)
		(end 92.47 26.73)
		(width 0.254)
		(layer "F.Cu")
		(net 1)
		(uuid "7b65aa77-d659-44cb-be32-288ca0578c95")
	)
	(segment
		(start 92 27.2)
		(end 92.1 27.1)
		(width 0.508)
		(layer "F.Cu")
		(net 1)
		(uuid "831cc63d-**************-44b3ca1865e0")
	)
	(segment
		(start 99.8 24.19)
		(end 100.83519 24.19)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "18b8613c-711e-4f8f-b8a1-d4c6951e6be7")
	)
	(segment
		(start 90.4 27.1)
		(end 90.4 27.85)
		(width 0.254)
		(layer "F.Cu")
		(net 2)
		(uuid "1e21f476-62de-4d9c-850c-7a6c0189c577")
	)
	(segment
		(start 91.8 23.85)
		(end 91.85 23.9)
		(width 0.254)
		(layer "F.Cu")
		(net 2)
		(uuid "22ba0a67-292e-460d-8dea-272beaf918a7")
	)
	(segment
		(start 96.6 37.586)
		(end 96.554 37.54)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "4b53ab5b-81f0-4025-a064-f3b67c4a4cbe")
	)
	(segment
		(start 90.5 28.25)
		(end 90.45 28.2)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "5631cc64-d1c5-468d-a30d-26ba945897b2")
	)
	(segment
		(start 99.55 35.7)
		(end 100.1 35.7)
		(width 0.254)
		(layer "F.Cu")
		(net 2)
		(uuid "6115760a-a83d-476d-95a6-b0bb592be285")
	)
	(segment
		(start 95.65234 21.65234)
		(end 95.6 21.6)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "715bd13b-f0bf-48f9-8ac5-b03ff19d465b")
	)
	(segment
		(start 90.4 28.9)
		(end 89.75 28.9)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "79da43bc-576d-43bb-8bac-39dace543651")
	)
	(segment
		(start 95.65234 22.577514)
		(end 95.65234 21.65234)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "8e6e09a9-6671-4dd1-850f-5aefde2cfe99")
	)
	(segment
		(start 92.1 24.9)
		(end 92.1 24.15)
		(width 0.254)
		(layer "F.Cu")
		(net 2)
		(uuid "941ffa22-acf9-4b2b-8e53-384a407ca6ef")
	)
	(segment
		(start 99.8 24.19)
		(end 100.56 24.19)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "a3cc5572-311c-4f95-bb8e-96a5a3bb20cc")
	)
	(segment
		(start 95.65234 22.577514)
		(end 95.65234 23.226298)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "b926a6c1-74c7-43b0-9faa-c8aa19ca8b2a")
	)
	(segment
		(start 90.4 27.85)
		(end 90.45 27.9)
		(width 0.254)
		(layer "F.Cu")
		(net 2)
		(uuid "cc24bc01-e2ff-4dde-a162-dc40024ad055")
	)
	(segment
		(start 90.5 28.9)
		(end 90.5 28.25)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "d92a68b2-0dac-4dd2-ae4d-a197b5fdbdbd")
	)
	(segment
		(start 91.8 23.1)
		(end 91.8 23.85)
		(width 0.254)
		(layer "F.Cu")
		(net 2)
		(uuid "df6322d4-c8ae-4684-916d-7024c234fc14")
	)
	(segment
		(start 100.56 24.19)
		(end 101.05 23.7)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "e105bf61-2bea-4bce-b963-56a847417d15")
	)
	(segment
		(start 95.65234 23.226298)
		(end 95.863021 23.436979)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "e11b56b0-5517-48d8-9a15-abe250873968")
	)
	(segment
		(start 100.83519 24.19)
		(end 101.04519 24.4)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "ed881680-20d0-4aaf-93a9-226954938e27")
	)
	(segment
		(start 99.8 24.19)
		(end 99.8 23.950006)
		(width 0.508)
		(layer "F.Cu")
		(net 2)
		(uuid "f5eba2e8-b7d9-4635-8268-b148171de813")
	)
	(segment
		(start 92.1 24.15)
		(end 91.85 23.9)
		(width 0.254)
		(layer "F.Cu")
		(net 2)
		(uuid "fab08bee-7738-45fb-8248-89cdcb6e2618")
	)
	(via
		(at 88.8 23.6)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "02287bfc-426c-471a-91e5-fd29bcdb4136")
	)
	(via
		(at 89.6 30.4)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "12213cd9-a61b-448c-8438-ccdf98fd6a8a")
	)
	(via
		(at 90.45 28.2)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "13688e98-5fda-407c-a90a-7ac2a4ecf0df")
	)
	(via
		(at 97.5 37.3)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "15fef19f-98fb-4361-a6d1-1c90a239d925")
	)
	(via
		(at 101.05 23.7)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "22b7a113-1309-4756-8073-76e28fa76dfe")
	)
	(via
		(at 91.7 14.1)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "2cc9103c-198e-453a-a12b-997bed92c286")
	)
	(via
		(at 88.8 30.2)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "36a4b8cf-a50e-4c3c-8f03-a38d628db057")
	)
	(via
		(at 96 26.1)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "39818f7d-e4d0-4823-80e7-3c52d1bfbad0")
	)
	(via
		(at 95.6 21.6)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "3bdeb8c5-6ce3-4727-ad76-34fedfc0eced")
	)
	(via
		(at 90.8 21.9)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "4513515a-11b4-45dd-911f-1d84f1f0926b")
	)
	(via
		(at 101.04519 24.4)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "4b2c4474-69b2-4909-b613-5e991463250c")
	)
	(via
		(at 97.5 38.8)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "5e0d68d8-7a6e-48e5-b6a0-28de14474d30")
	)
	(via
		(at 99.96281 23.2)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "64d29b00-d988-43b4-9d01-3bdf30ba3d4a")
	)
	(via
		(at 88.8 27.2)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "79f1ac83-418b-4e95-8a1a-b267c4b176a3")
	)
	(via
		(at 89.75 28.9)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "8a9e86e1-e2b4-4c17-b725-75eea64464c3")
	)
	(via
		(at 91.85 23.9)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "8f58b9b2-552d-4d8e-a0e7-055806dda576")
	)
	(via
		(at 91.7 15.3)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "91565440-3e32-4355-b054-c3606c42ca89")
	)
	(via
		(at 88.8 28.3)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "93fd2653-b28a-4b2d-a419-fd2bd5133e1a")
	)
	(via
		(at 95.863021 23.436979)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "9a56b521-c197-4856-bfb5-f422ea52c480")
	)
	(via
		(at 88.8 25.3)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "a4a27297-3a71-415f-83f3-88c6a5d14719")
	)
	(via
		(at 91.2 38.8)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "aaa68433-f3a7-47cf-b71e-2065761d5192")
	)
	(via
		(at 91.8 21.9)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "ac44b93d-0cf3-4a23-8898-10d6ee97aeba")
	)
	(via
		(at 99.136204 33.126252)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "ae7644c8-0c4d-43e3-9c87-2f9d03779410")
	)
	(via
		(at 100.5 35.8)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "b4ea43d7-afee-4873-a8e8-ad4424c925b1")
	)
	(via
		(at 88.8 24.5)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "b93066b2-a70f-4f82-a599-0272b2a4be68")
	)
	(via
		(at 88.8 31.1)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "bbbf73cd-26bd-428d-927a-1edaf0644a4d")
	)
	(via
		(at 88.8 26.2)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "db724816-66f4-413d-b765-c9310bc6307e")
	)
	(via
		(at 91.2 37.3)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "fd1d9652-a2b0-4773-a582-1c1b9a761dea")
	)
	(via
		(at 88.8 29.3)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 2)
		(uuid "fec7d2b7-27c2-4e45-b535-87fd46ef64ef")
	)
	(segment
		(start 95.6 26.094552)
		(end 95.594552 26.1)
		(width 0.254)
		(layer "In2.Cu")
		(net 2)
		(uuid "222265ea-1607-45ab-a5ba-6f987346d777")
	)
	(segment
		(start 91.304801 25.904801)
		(end 90.4 25)
		(width 0.254)
		(layer "F.Cu")
		(net 3)
		(uuid "4063894e-762e-4ecf-bd44-fa3dded12f63")
	)
	(segment
		(start 90.25 23.15)
		(end 90.25 24.75)
		(width 0.508)
		(layer "F.Cu")
		(net 3)
		(uuid "62077f6c-0739-4188-90ef-d3145f28c4f7")
	)
	(segment
		(start 90.4 25)
		(end 90.4 24.9)
		(width 0.254)
		(layer "F.Cu")
		(net 3)
		(uuid "77c5af4c-f0dd-4784-b2ae-f2870832f3be")
	)
	(segment
		(start 91.304801 25.904801)
		(end 92.951199 25.904801)
		(width 0.254)
		(layer "F.Cu")
		(net 3)
		(uuid "ab876805-9089-49be-88df-9ae0c850a54f")
	)
	(segment
		(start 90.3 24.8)
		(end 90.4 24.9)
		(width 0.254)
		(layer "F.Cu")
		(net 3)
		(uuid "af65433f-7cf1-4537-98cb-0a707d3d112d")
	)
	(segment
		(start 92.951199 25.904801)
		(end 93.396 25.46)
		(width 0.254)
		(layer "F.Cu")
		(net 3)
		(uuid "b2c86243-debc-412f-bc31-f4cb2f6bcee2")
	)
	(segment
		(start 93.396 25.46)
		(end 94.4 25.46)
		(width 0.254)
		(layer "F.Cu")
		(net 3)
		(uuid "ea3d6f4e-5755-4e87-ab13-b122a4a8d444")
	)
	(segment
		(start 90.25 24.75)
		(end 90.4 24.9)
		(width 0.508)
		(layer "F.Cu")
		(net 3)
		(uuid "f4f154c1-8ed2-4318-a93b-cb860c27b838")
	)
	(segment
		(start 94.129854 22.6)
		(end 93.3 22.6)
		(width 0.5)
		(layer "F.Cu")
		(net 4)
		(uuid "140c65fe-6565-4347-bd0d-fe0747b61b0e")
	)
	(segment
		(start 94.127514 22.577514)
		(end 94.15234 22.577514)
		(width 0.5)
		(layer "F.Cu")
		(net 4)
		(uuid "5053eca3-a54d-44ca-82e2-e72ae7b8952a")
	)
	(segment
		(start 94.1 22.55)
		(end 94.127514 22.577514)
		(width 0.5)
		(layer "F.Cu")
		(net 4)
		(uuid "54081c99-3d9c-4fa9-a86a-69267d680e1c")
	)
	(segment
		(start 90.45 32)
		(end 89.6 32)
		(width 0.254)
		(layer "F.Cu")
		(net 4)
		(uuid "58679211-f41c-46b1-bbbe-fa97cc822e72")
	)
	(segment
		(start 94.15234 22.577514)
		(end 94.15234 21.65234)
		(width 0.5)
		(layer "F.Cu")
		(net 4)
		(uuid "6f88e3b0-e7b8-4205-bf18-98fe39c01a42")
	)
	(segment
		(start 94.4 24.19)
		(end 94.15234 23.94234)
		(width 0.5)
		(layer "F.Cu")
		(net 4)
		(uuid "9616df2f-e951-4369-a572-3c0fa5ad572a")
	)
	(segment
		(start 94.15234 22.577514)
		(end 94.129854 22.6)
		(width 0.5)
		(layer "F.Cu")
		(net 4)
		(uuid "d4794ca4-155b-4479-86ca-6d6d14e2cf6a")
	)
	(segment
		(start 94.15234 23.94234)
		(end 94.15234 22.577514)
		(width 0.5)
		(layer "F.Cu")
		(net 4)
		(uuid "ef6cc7d9-5697-4290-836a-9f2fdf15f70b")
	)
	(segment
		(start 94.15234 21.65234)
		(end 94.1 21.6)
		(width 0.5)
		(layer "F.Cu")
		(net 4)
		(uuid "f82b6cef-5512-4318-ab42-e313cb056601")
	)
	(via
		(at 99.3 14.2)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 4)
		(uuid "77ea6d13-339f-4fa3-9716-425c8febb380")
	)
	(via
		(at 94.1 21.6)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 4)
		(uuid "a62690ed-32c9-419d-9e3a-18608c8dbe19")
	)
	(via
		(at 89.6 32)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 4)
		(uuid "a74230dd-fb8e-45fe-bc9b-39fbc359a273")
	)
	(via
		(at 99.3 15.4)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 4)
		(uuid "bd35d2b1-2ede-4c23-829a-f2bfa1160b0a")
	)
	(via
		(at 101.1 26.2)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 4)
		(uuid "ce56084f-5e0d-4e82-a29d-d02f9958a2d4")
	)
	(via
		(at 93.3 22.6)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 4)
		(uuid "f6564416-80c1-4be6-a474-d12739d7c643")
	)
	(segment
		(start 93.575 40.707)
		(end 93.71 40.842)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "283d93fa-f29e-4ea0-9b62-c4c1a3264085")
	)
	(segment
		(start 93.76 34.018894)
		(end 93.245199 33.504093)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "3347fb1b-67ab-4ef8-97e3-746ea869977a")
	)
	(segment
		(start 93.245199 33.504093)
		(end 93.245199 31.244801)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "42eb44f2-b8ef-4740-81ee-90623c4cf58e")
	)
	(segment
		(start 93.76 35.762)
		(end 93.76 34.018894)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "569216f2-fb34-47fd-996a-f54548d36f9b")
	)
	(segment
		(start 93.95 30.54)
		(end 94.4 30.54)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "6e5d9266-2e9e-49f0-8850-990b2dcb1fe1")
	)
	(segment
		(start 93.58772 38.78728)
		(end 93.575 38.8)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "6e7d34ad-2937-496f-8b51-53161401a9c6")
	)
	(segment
		(start 93.575 38.8)
		(end 93.575 40.707)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "8750e460-072c-47a9-9ffd-a769deb47994")
	)
	(segment
		(start 93.58772 37.312)
		(end 93.58772 38.78728)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "aeb5b20b-3606-4dc4-976d-51f6585abecd")
	)
	(segment
		(start 93.589533 35.762)
		(end 93.589533 37.310187)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "b1bbbc17-db81-4481-b50a-3e2da85ff852")
	)
	(segment
		(start 93.245199 31.244801)
		(end 93.95 30.54)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "e8b9f498-3c54-4099-825f-762623fea7a2")
	)
	(segment
		(start 93.589533 37.310187)
		(end 93.58772 37.312)
		(width 0.254)
		(layer "F.Cu")
		(net 5)
		(uuid "eed306e9-356a-41a3-9ff0-8304a3665368")
	)
	(segment
		(start 96.504 41.446)
		(end 96.504 41.223)
		(width 0.254)
		(layer "F.Cu")
		(net 6)
		(uuid "00000000-0000-0000-0000-00005b3b47d2")
	)
	(segment
		(start 92.31 41.446)
		(end 92.813999 41.949999)
		(width 0.254)
		(layer "F.Cu")
		(net 6)
		(uuid "084fac4e-14fa-4919-bfc5-557879421f75")
	)
	(segment
		(start 96.504 41.223)
		(end 96.504 41.596)
		(width 0.254)
		(layer "F.Cu")
		(net 6)
		(uuid "0d130c81-ef52-4167-8580-56adc58eb4d7")
	)
	(segment
		(start 96.504 41.596)
		(end 96.5 41.6)
		(width 0.254)
		(layer "F.Cu")
		(net 6)
		(uuid "27b10b7a-cb69-43cb-b507-fa256d025c38")
	)
	(segment
		(start 92.31 40.842)
		(end 92.31 41.446)
		(width 0.254)
		(layer "F.Cu")
		(net 6)
		(uuid "57d5bc5f-7c4a-4acc-bed7-4356ad1d014e")
	)
	(segment
		(start 89.25 40.95)
		(end 89.25 41.650004)
		(width 0.254)
		(layer "F.Cu")
		(net 6)
		(uuid "76e597f7-63b6-40ed-8acc-5795876e30ce")
	)
	(segment
		(start 96.504 40.842)
		(end 96.504 41.596)
		(width 0.254)
		(layer "F.Cu")
		(net 6)
		(uuid "7a6d634b-5d9c-4b0a-9b6b-90eac5bbb957")
	)
	(segment
		(start 96.150001 41.949999)
		(end 96.5 41.6)
		(width 0.254)
		(layer "F.Cu")
		(net 6)
		(uuid "d30adada-ea0d-4217-83a2-8a10e6cc929b")
	)
	(segment
		(start 89.25 41.650004)
		(end 89.5 41.900004)
		(width 0.254)
		(layer "F.Cu")
		(net 6)
		(uuid "dbeb0cd5-e32d-4bee-9679-a305838fb795")
	)
	(segment
		(start 92.813999 41.949999)
		(end 96.150001 41.949999)
		(width 0.254)
		(layer "F.Cu")
		(net 6)
		(uuid "e6ae43d5-0632-41f7-b8bf-73a1febca3b6")
	)
	(via
		(at 89.5 41.900004)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 6)
		(uuid "2987f2ed-cc43-4d28-a45c-dc4d663662d1")
	)
	(via
		(at 96.5 41.6)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 6)
		(uuid "dbaccc5d-5b32-4c15-b772-1c47bdbcb925")
	)
	(segment
		(start 89.5 43.4)
		(end 89.5 41.900004)
		(width 0.254)
		(layer "In1.Cu")
		(net 6)
		(uuid "2a190f9e-7d8b-4cb6-941a-8d7307205b54")
	)
	(segment
		(start 94.556936 49.2)
		(end 94.428468 49.071532)
		(width 0.508)
		(layer "In1.Cu")
		(net 6)
		(uuid "64347a6c-**************-50809c872517")
	)
	(segment
		(start 95.3 49.2)
		(end 94.556936 49.2)
		(width 0.508)
		(layer "In1.Cu")
		(net 6)
		(uuid "6b761bf8-9e13-4552-b335-89f6ed2136fa")
	)
	(segment
		(start 96.504 46.996)
		(end 94.428468 49.071532)
		(width 0.508)
		(layer "In1.Cu")
		(net 6)
		(uuid "72919ea5-eff8-4342-9179-a1581d9667e0")
	)
	(segment
		(start 95.3 49.2)
		(end 89.5 43.4)
		(width 0.254)
		(layer "In1.Cu")
		(net 6)
		(uuid "9c257ed4-42b9-4a0e-a4be-466953356c52")
	)
	(segment
		(start 95.3 49.2)
		(end 95.3 97.2)
		(width 5)
		(layer "In1.Cu")
		(net 6)
		(uuid "ad7ba459-8c78-4aeb-b176-2f7dbfd810bb")
	)
	(segment
		(start 94.428468 49.071532)
		(end 94.3 49.2)
		(width 0.508)
		(layer "In1.Cu")
		(net 6)
		(uuid "bbc566ea-f0cf-4568-b8c4-66a77ab6e518")
	)
	(segment
		(start 96.504 41.5)
		(end 96.504 46.996)
		(width 0.254)
		(layer "In1.Cu")
		(net 6)
		(uuid "f5f9f588-a854-4df2-a6f9-dd932d2d76c4")
	)
	(segment
		(start 100.295778 33.984)
		(end 101.386612 32.893166)
		(width 0.254)
		(layer "F.Cu")
		(net 7)
		(uuid "4df08eca-68e8-4679-ad6c-5200150daefb")
	)
	(segment
		(start 98.078 35.672)
		(end 98.05 35.7)
		(width 0.508)
		(layer "F.Cu")
		(net 7)
		(uuid "53ef1942-d2c9-4111-aa75-12e784c71341")
	)
	(segment
		(start 101.386612 29.986612)
		(end 100.67 29.27)
		(width 0.254)
		(layer "F.Cu")
		(net 7)
		(uuid "8375be40-a4ec-49b9-870e-acebdaaf578d")
	)
	(segment
		(start 98.078 33.984)
		(end 100.295778 33.984)
		(width 0.254)
		(layer "F.Cu")
		(net 7)
		(uuid "9d96a905-bc36-4421-8ba1-f76918e1658b")
	)
	(segment
		(start 98.078 33.984)
		(end 98.078 35.672)
		(width 0.508)
		(layer "F.Cu")
		(net 7)
		(uuid "b34fc1e0-1431-4c64-ae78-f9d6ddd4d993")
	)
	(segment
		(start 100.67 29.27)
		(end 99.8 29.27)
		(width 0.254)
		(layer "F.Cu")
		(net 7)
		(uuid "f88b5357-e91f-450d-af06-9ee941a22889")
	)
	(segment
		(start 101.386612 32.893166)
		(end 101.386612 29.986612)
		(width 0.254)
		(layer "F.Cu")
		(net 7)
		(uuid "feafee34-cd1a-4330-8431-4addd5bc4c30")
	)
	(segment
		(start 95.1 28)
		(end 95.1 28)
		(width 0.254)
		(layer "F.Cu")
		(net 8)
		(uuid "00000000-0000-0000-0000-00005b49caa0")
	)
	(segment
		(start 91.95 31.003288)
		(end 92.561118 30.39217)
		(width 0.254)
		(layer "F.Cu")
		(net 8)
		(uuid "2571ad76-a832-4b2d-a258-d07b7fd5b68a")
	)
	(segment
		(start 94.4 28)
		(end 95.1 28)
		(width 0.254)
		(layer "F.Cu")
		(net 8)
		(uuid "620819b5-ee79-40e1-952e-89477e92d9f5")
	)
	(segment
		(start 95.29 28)
		(end 95.76 28.47)
		(width 0.254)
		(layer "F.Cu")
		(net 8)
		(uuid "6f326d7d-cb25-47d6-9497-5acad73f5a27")
	)
	(segment
		(start 91.95 32)
		(end 91.95 31.003288)
		(width 0.254)
		(layer "F.Cu")
		(net 8)
		(uuid "8963c3be-a545-42b6-b442-4503c24c9d11")
	)
	(segment
		(start 94.4 28)
		(end 95.29 28)
		(width 0.254)
		(layer "F.Cu")
		(net 8)
		(uuid "a4cc8d2c-9d6f-4a2a-97a7-d1853171e3b0")
	)
	(via
		(at 95.76 28.47)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 8)
		(uuid "16a7bc39-adee-4f5b-bc72-b85b2f0508c0")
	)
	(via
		(at 92.561118 30.39217)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 8)
		(uuid "74c5d5c5-8e48-4383-a975-861857879b8e")
	)
	(segment
		(start 95.76 28.47)
		(end 93.83783 30.39217)
		(width 0.254)
		(layer "B.Cu")
		(net 8)
		(uuid "52584e1c-7ea4-48cc-adf5-f2aae72d182c")
	)
	(segment
		(start 93.83783 30.39217)
		(end 92.561118 30.39217)
		(width 0.254)
		(layer "B.Cu")
		(net 8)
		(uuid "f9f4c7a9-2e28-46b9-b667-7608c3194d05")
	)
	(segment
		(start 94.4 29.27)
		(end 93.95 29.27)
		(width 0.254)
		(layer "F.Cu")
		(net 9)
		(uuid "214b2ab0-f49e-49d9-a449-404511ca6b3f")
	)
	(segment
		(start 90.75 40.149996)
		(end 90.55 39.949996)
		(width 0.254)
		(layer "F.Cu")
		(net 9)
		(uuid "498f6e7c-e9e9-45e1-a666-b688873cb2ca")
	)
	(segment
		(start 94.4 29.27)
		(end 93.13 29.27)
		(width 0.254)
		(layer "F.Cu")
		(net 9)
		(uuid "5a17c380-943a-42e2-a488-3977f26ef7a9")
	)
	(segment
		(start 93.13 29.27)
		(end 93.1 29.3)
		(width 0.254)
		(layer "F.Cu")
		(net 9)
		(uuid "b1762a90-79f1-469d-a438-1dd343ac24c9")
	)
	(segment
		(start 90.75 40.95)
		(end 90.75 40.149996)
		(width 0.254)
		(layer "F.Cu")
		(net 9)
		(uuid "e47cd3f4-e366-47b4-9bff-a43e0745f53d")
	)
	(via
		(at 93.1 29.3)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 9)
		(uuid "2b0bb4ac-3713-4e97-8f5d-29ba56d1c012")
	)
	(via
		(at 90.55 39.949996)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 9)
		(uuid "acc9180f-fa9e-4369-8073-2516bca5d48a")
	)
	(segment
		(start 90.55 39.949996)
		(end 90.3 39.699996)
		(width 0.254)
		(layer "In2.Cu")
		(net 9)
		(uuid "3bdea3e9-3acc-47a9-b2f5-65f78e3f84e5")
	)
	(segment
		(start 90.3 39.699996)
		(end 90.3 31.605026)
		(width 0.254)
		(layer "In2.Cu")
		(net 9)
		(uuid "a020a55e-545c-4647-9fa9-abbb45d14542")
	)
	(segment
		(start 90.3 31.605026)
		(end 92.605026 29.3)
		(width 0.254)
		(layer "In2.Cu")
		(net 9)
		(uuid "b3751e2a-7b30-4622-aeb4-ceec88fc752f")
	)
	(segment
		(start 92.605026 29.3)
		(end 93.1 29.3)
		(width 0.254)
		(layer "In2.Cu")
		(net 9)
		(uuid "f58fffb7-0fab-4382-b4c4-fedff4e71497")
	)
	(segment
		(start 99.35 30.54)
		(end 98.94459 30.54)
		(width 0.254)
		(layer "F.Cu")
		(net 11)
		(uuid "67fbaa9c-8b73-4efb-bba2-6751fed5cecd")
	)
	(segment
		(start 98.94459 30.54)
		(end 97.904811 29.500221)
		(width 0.254)
		(layer "F.Cu")
		(net 11)
		(uuid "8549f749-8157-45f7-8f68-dc20e693277a")
	)
	(via
		(at 97.904811 29.500221)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 11)
		(uuid "3174eb2e-87b8-4534-89b3-f75432fa820f")
	)
	(segment
		(start 99.671599 25.571599)
		(end 99.671599 27.098401)
		(width 0.254)
		(layer "B.Cu")
		(net 11)
		(uuid "2327d447-de1b-4009-81ea-99fdaac265ae")
	)
	(segment
		(start 98.3 29.105032)
		(end 97.904811 29.500221)
		(width 0.254)
		(layer "B.Cu")
		(net 11)
		(uuid "4337fbe1-3df9-4a61-a9fe-799866a09a91")
	)
	(segment
		(start 98.3 28.47)
		(end 98.3 29.105032)
		(width 0.254)
		(layer "B.Cu")
		(net 11)
		(uuid "563fd622-43e5-473e-9368-8cae348ad7f1")
	)
	(segment
		(start 99.671599 27.098401)
		(end 98.3 28.47)
		(width 0.254)
		(layer "B.Cu")
		(net 11)
		(uuid "77b4eeb8-abfc-4c28-a152-7b49d7bca8db")
	)
	(segment
		(start 96.762697 14.799451)
		(end 96.762697 22.662697)
		(width 0.254)
		(layer "B.Cu")
		(net 11)
		(uuid "beb6a009-507b-432b-9026-e30997669eec")
	)
	(segment
		(start 96.762697 22.662697)
		(end 99.671599 25.571599)
		(width 0.254)
		(layer "B.Cu")
		(net 11)
		(uuid "d7b83682-adf2-4c74-8403-8348e42052da")
	)
	(segment
		(start 97.6 26.6)
		(end 97.250001 26.949999)
		(width 0.254)
		(layer "F.Cu")
		(net 12)
		(uuid "146a09c6-cc8c-42f3-8842-d59834cd488c")
	)
	(segment
		(start 97.250001 30.149999)
		(end 95.59 31.81)
		(width 0.254)
		(layer "F.Cu")
		(net 12)
		(uuid "1d537f77-fe0c-424e-a6ac-7a4e7d0d4c4e")
	)
	(segment
		(start 97.250001 26.949999)
		(end 97.250001 30.149999)
		(width 0.254)
		(layer "F.Cu")
		(net 12)
		(uuid "c015c882-1415-4d90-9473-7f627b829102")
	)
	(segment
		(start 95.59 31.81)
		(end 94.4 31.81)
		(width 0.254)
		(layer "F.Cu")
		(net 12)
		(uuid "ee3371f4-1022-485c-81b6-6e4f33399606")
	)
	(via
		(at 97.6 26.6)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 12)
		(uuid "fffb3f82-b8b5-4861-a8df-2e4a2dc5e1a8")
	)
	(segment
		(start 98.3 25.93)
		(end 98.27 25.93)
		(width 0.254)
		(layer "B.Cu")
		(net 12)
		(uuid "2f7ccf64-7499-4e74-8d01-e8eb53df829a")
	)
	(segment
		(start 94.222697 14.799451)
		(end 94.754801 15.331555)
		(width 0.254)
		(layer "B.Cu")
		(net 12)
		(uuid "378aff7f-51a1-4d08-a610-2406164e0b39")
	)
	(segment
		(start 94.754801 15.331555)
		(end 94.754801 23.254801)
		(width 0.254)
		(layer "B.Cu")
		(net 12)
		(uuid "4538b60f-1b28-4346-a500-23966bfaf386")
	)
	(segment
		(start 94.754801 23.254801)
		(end 97.43 25.93)
		(width 0.254)
		(layer "B.Cu")
		(net 12)
		(uuid "746980c3-f450-4669-b64f-b708ca7fa5b6")
	)
	(segment
		(start 98.27 25.93)
		(end 97.6 26.6)
		(width 0.254)
		(layer "B.Cu")
		(net 12)
		(uuid "de4bc4c8-ac8f-41f5-b462-8c42f78ee81c")
	)
	(segment
		(start 97.43 25.93)
		(end 98.3 25.93)
		(width 0.254)
		(layer "B.Cu")
		(net 12)
		(uuid "f909084b-0e4d-4e71-b490-e9480e153098")
	)
	(segment
		(start 94.979 37.3)
		(end 94.979 38.754)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "1f861e8a-16be-44fe-b029-22cb8a21c151")
	)
	(segment
		(start 94.979 35.762)
		(end 94.979 37.3)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "35dbf9cf-b1c8-48f6-8c80-8019ac13db9e")
	)
	(segment
		(start 99.41 31.81)
		(end 99.8 31.81)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "54f29b39-1bbc-4e14-b23d-70bdb0bd15fe")
	)
	(segment
		(start 99.8 31.9)
		(end 100.3 32.4)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "55521b28-2f33-4cc2-b0ee-f2cefb5a33d5")
	)
	(segment
		(start 95.025 40.763)
		(end 95.104 40.842)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "79385ff6-3de2-4fe3-a4ec-fcdc924063d0")
	)
	(segment
		(start 95.025 38.8)
		(end 95.025 40.763)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "7f4510d4-c014-4aec-b29f-d1e3f9237525")
	)
	(segment
		(start 98.796 31.81)
		(end 99.8 31.81)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "8a36adf4-7988-42d6-bebc-74e65dac0706")
	)
	(segment
		(start 95.054 33.246)
		(end 96.49 31.81)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "90ce205f-b0b6-4ad6-950b-20a05ddd24d9")
	)
	(segment
		(start 94.979 38.754)
		(end 95.025 38.8)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "d8da9ebe-74dd-4fac-9ef0-986396a78062")
	)
	(segment
		(start 96.49 31.81)
		(end 98.796 31.81)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "d949e8c2-2f8c-4c9d-97e2-19555a33a3a7")
	)
	(segment
		(start 99.8 31.81)
		(end 99.8 31.9)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "f59957ef-27de-4b1d-9343-1e5094307f9b")
	)
	(segment
		(start 95.054 35.762)
		(end 95.054 33.246)
		(width 0.254)
		(layer "F.Cu")
		(net 13)
		(uuid "fbddb5f6-b011-4750-b62f-b0301d0da931")
	)
	(via
		(at 101.2 28)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 13)
		(uuid "2110c67b-709b-438a-bc3d-e2036fc62792")
	)
	(via
		(at 100.3 32.4)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 13)
		(uuid "fe7d132e-e398-4ef9-bc4d-9307e5b875ad")
	)
	(segment
		(start 100.84 28.36)
		(end 100.850001 28.349999)
		(width 0.254)
		(layer "B.Cu")
		(net 13)
		(uuid "087a41a4-fb17-400f-b827-1827d73117cf")
	)
	(segment
		(start 100.84 28.47)
		(end 100.84 28.36)
		(width 0.254)
		(layer "B.Cu")
		(net 13)
		(uuid "d44ed9cc-c65d-4db0-8f23-9be902ec7151")
	)
	(segment
		(start 100.850001 28.349999)
		(end 101.2 28)
		(width 0.254)
		(layer "B.Cu")
		(net 13)
		(uuid "e9f3fde0-c671-4542-b057-b8a22fd4c80e")
	)
	(segment
		(start 100.3 32.4)
		(end 101.2 31.5)
		(width 0.254)
		(layer "In2.Cu")
		(net 13)
		(uuid "ae464859-a2a1-4309-a9f9-2d25ebcf2e8e")
	)
	(segment
		(start 101.2 31.5)
		(end 101.2 28.494974)
		(width 0.254)
		(layer "In2.Cu")
		(net 13)
		(uuid "dc35a108-8c92-4004-8e92-c6eb7d20f779")
	)
	(segment
		(start 101.2 28.494974)
		(end 101.2 28)
		(width 0.254)
		(layer "In2.Cu")
		(net 13)
		(uuid "f852b2b5-098c-4c47-b3e1-9febabc68a4d")
	)
	(segment
		(start 96.566 35.762)
		(end 96.566 34.72538)
		(width 0.508)
		(layer "F.Cu")
		(net 14)
		(uuid "16207089-7b17-4cff-8e3b-ec24f6a64114")
	)
	(segment
		(start 91.939533 35.762)
		(end 91.939533 35.282906)
		(width 0.254)
		(layer "F.Cu")
		(net 14)
		(uuid "40165b0f-5964-44dc-805b-4c09179ea8db")
	)
	(segment
		(start 91.939533 35.282906)
		(end 92.665449 34.55699)
		(width 0.254)
		(layer "F.Cu")
		(net 14)
		(uuid "47577eae-74d2-4602-bb57-d18aabd046d1")
	)
	(segment
		(start 101.818423 33.072027)
		(end 100.19045 34.7)
		(width 0.254)
		(layer "F.Cu")
		(net 14)
		(uuid "5d1125b7-4822-4925-a2ea-cb7353f97de0")
	)
	(segment
		(start 100.19045 34.7)
		(end 99.4 34.7)
		(width 0.254)
		(layer "F.Cu")
		(net 14)
		(uuid "75bc13f1-b250-4116-a53e-337126ae1686")
	)
	(segment
		(start 101.818423 29.787529)
		(end 101.818423 33.072027)
		(width 0.254)
		(layer "F.Cu")
		(net 14)
		(uuid "88aecb7b-7ec4-49c0-acde-058ce8a4580f")
	)
	(segment
		(start 96.566 34.72538)
		(end 96.14062 34.3)
		(width 0.508)
		(layer "F.Cu")
		(net 14)
		(uuid "9dc52236-e7fe-4813-bb46-f270e6bf4a22")
	)
	(segment
		(start 96.578 33.984)
		(end 96.262 34.3)
		(width 0.254)
		(layer "F.Cu")
		(net 14)
		(uuid "9fb6ce1a-a013-4161-9629-bee478c1de3d")
	)
	(segment
		(start 99.8 28)
		(end 100.030894 28)
		(width 0.254)
		(layer "F.Cu")
		(net 14)
		(uuid "ae8e55f8-509c-4b15-a9a6-878e812fbaa6")
	)
	(segment
		(start 96.262 34.3)
		(end 96.14062 34.3)
		(width 0.254)
		(layer "F.Cu")
		(net 14)
		(uuid "b9802065-db3d-4068-8b9b-e901aadf88c4")
	)
	(segment
		(start 100.030894 28)
		(end 101.818423 29.787529)
		(width 0.254)
		(layer "F.Cu")
		(net 14)
		(uuid "bb2c802e-38fd-4a62-b815-3a8109976afb")
	)
	(via
		(at 99.4 34.7)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 14)
		(uuid "05c0082a-c0dd-4932-9dbc-8f1b2c47997e")
	)
	(via
		(at 96.14062 34.3)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 14)
		(uuid "98b9a772-ad6d-4034-aaf8-2624c951cd38")
	)
	(via
		(at 92.665449 34.55699)
		(size 0.7)
		(drill 0.4)
		(layers "F.Cu" "B.Cu")
		(remove_unused_layers yes)
		(keep_end_layers yes)
		(zone_layer_connections)
		(net 14)
		(uuid "d94f9262-e47a-48dc-b21f-467d3a42657d")
	)
	(segment
		(start 95.88363 34.55699)
		(end 96.14062 34.3)
		(width 0.254)
		(layer "In2.Cu")
		(net 14)
		(uuid "4009692c-8aea-43a2-ae19-212421fd0eb9")
	)
	(segment
		(start 96.54062 34.7)
		(end 96.14062 34.3)
		(width 0.254)
		(layer "In2.Cu")
		(net 14)
		(uuid "50897a77-333a-4052-ac2a-9b78095caffa")
	)
	(segment
		(start 92.665449 34.55699)
		(end 95.88363 34.55699)
		(width 0.254)
		(layer "In2.Cu")
		(net 14)
		(uuid "bee99546-729b-48af-8144-092542a27c11")
	)
	(segment
		(start 99.4 34.7)
		(end 96.54062 34.7)
		(width 0.254)
		(layer "In2.Cu")
		(net 14)
		(uuid "cd64501b-0a29-4344-9b77-694cabd31120")
	)
	(zone
		(net 2)
		(net_name "GND")
		(layer "F.Cu")
		(uuid "00000000-0000-0000-0000-00005c07d704")
		(hatch edge 0.508)
		(connect_pads
			(clearance 0.2)
		)
		(min_thickness 0.254)
		(filled_areas_thickness no)
		(fill yes
			(thermal_gap 0.508)
			(thermal_bridge_width 0.508)
		)
		(polygon
			(pts
				(xy 87.8 13.2) (xy 102.8 13.2) (xy 102.8 43.2) (xy 87.8 43.2)
			)
		)
		(filled_polygon
			(layer "F.Cu")
			(pts
				(xy 98.427515 32.147667) (xy 98.468638 32.175119) (xy 98.536044 32.242407) (xy 98.552635 32.254242)
				(xy 98.639632 32.296768) (xy 98.657939 32.302426) (xy 98.71223 32.310346) (xy 98.72124 32.310999)
				(xy 99.621152 32.310999) (xy 99.669753 32.320666) (xy 99.710954 32.348196) (xy 99.738485 32.389398)
				(xy 99.747065 32.421422) (xy 99.762111 32.535713) (xy 99.766317 32.551407) (xy 99.815568 32.670309)
				(xy 99.823691 32.68438) (xy 99.902032 32.786476) (xy 99.913518 32.797961) (xy 100.015625 32.876311)
				(xy 100.029689 32.884431) (xy 100.148592 32.933682) (xy 100.164279 32.937886) (xy 100.291882 32.954685)
				(xy 100.308118 32.954685) (xy 100.43572 32.937886) (xy 100.451407 32.933682) (xy 100.57031 32.884431)
				(xy 100.589438 32.873388) (xy 100.636361 32.857459) (xy 100.685808 32.860699) (xy 100.730251 32.882615)
				(xy 100.762924 32.91987) (xy 100.778853 32.966793) (xy 100.775613 33.01624) (xy 100.753697 33.060683)
				(xy 100.742743 33.073175) (xy 100.197115 33.618803) (xy 100.155913 33.646333) (xy 100.107312 33.656)
				(xy 98.853324 33.656) (xy 98.804723 33.646333) (xy 98.763521 33.618803) (xy 98.739836 33.583356)
				(xy 98.739199 33.583681) (xy 98.736454 33.578295) (xy 98.735991 33.577601) (xy 98.735701 33.576817)
				(xy 98.685928 33.47913) (xy 98.674503 33.463405) (xy 98.598594 33.387496) (xy 98.582869 33.376071)
				(xy 98.487225 33.327338) (xy 98.468741 33.321333) (xy 98.389417 33.308769) (xy 98.379644 33.308)
				(xy 97.926358 33.308) (xy 97.916591 33.308769) (xy 97.837252 33.321334) (xy 97.818775 33.327338)
				(xy 97.72313 33.376071) (xy 97.707405 33.387496) (xy 97.631496 33.463405) (xy 97.620071 33.47913)
				(xy 97.571338 33.574774) (xy 97.565333 33.593258) (xy 97.552769 33.672582) (xy 97.552 33.682355)
				(xy 97.552 34.285641) (xy 97.552769 34.295408) (xy 97.565334 34.374747) (xy 97.571338 34.393223)
				(xy 97.609158 34.46745) (xy 97.622609 34.515142) (xy 97.623 34.525106) (xy 97.623 35.118571) (xy 97.613333 35.167172)
				(xy 97.585803 35.208374) (xy 97.553654 35.23173) (xy 97.545129 35.236073) (xy 97.529405 35.247497)
				(xy 97.453496 35.323406) (xy 97.442073 35.339128) (xy 97.43605 35.350951) (xy 97.405373 35.389866)
				(xy 97.362138 35.414079) (xy 97.312929 35.419905) (xy 97.265236 35.406455) (xy 97.226321 35.375778)
				(xy 97.209733 35.350953) (xy 97.161926 35.257128) (xy 97.150503 35.241405) (xy 97.074588 35.16549)
				(xy 97.073349 35.16459) (xy 97.069205 35.160107) (xy 97.067527 35.158429) (xy 97.067592 35.158363)
				(xy 97.039713 35.128202) (xy 97.022563 35.081711) (xy 97.021 35.061847) (xy 97.021 34.759931) (xy 97.02188 34.745003)
				(xy 97.024815 34.720201) (xy 97.024235 34.701734) (xy 97.015263 34.652611) (xy 97.014607 34.648673)
				(xy 97.006091 34.592025) (xy 97.008426 34.542527) (xy 97.028936 34.498495) (xy 97.035926 34.488873)
				(xy 97.084661 34.393225) (xy 97.090666 34.374741) (xy 97.10323 34.295417) (xy 97.103999 34.285644)
				(xy 97.103999 33.682358) (xy 97.10323 33.672591) (xy 97.090665 33.593252) (xy 97.084661 33.574775)
				(xy 97.035928 33.47913) (xy 97.024503 33.463405) (xy 96.948594 33.387496) (xy 96.932869 33.376071)
				(xy 96.837225 33.327338) (xy 96.818741 33.321333) (xy 96.739417 33.308769) (xy 96.729644 33.308)
				(xy 96.276358 33.308) (xy 96.266591 33.308769) (xy 96.187252 33.321334) (xy 96.168775 33.327338)
				(xy 96.07313 33.376071) (xy 96.057405 33.387496) (xy 95.981496 33.463405) (xy 95.970071 33.47913)
				(xy 95.921338 33.574774) (xy 95.915333 33.593258) (xy 95.902769 33.672582) (xy 95.902 33.682355)
				(xy 95.902 33.72595) (xy 95.892333 33.774551) (xy 95.864803 33.815753) (xy 95.852312 33.826706)
				(xy 95.754146 33.90203) (xy 95.742649 33.913527) (xy 95.664311 34.015619) (xy 95.656188 34.02969)
				(xy 95.626333 34.101767) (xy 95.598802 34.142969) (xy 95.5576 34.170499) (xy 95.509 34.180166) (xy 95.460399 34.170499)
				(xy 95.419197 34.142968) (xy 95.391667 34.101766) (xy 95.382 34.053166) (xy 95.382 33.434466) (xy 95.391667 33.385865)
				(xy 95.419197 33.344663) (xy 96.588663 32.175197) (xy 96.629865 32.147667) (xy 96.678466 32.138)
				(xy 98.378914 32.138)
			)
		)
		(filled_polygon
			(layer "F.Cu")
			(pts
				(xy 99.180183 13.400952) (xy 99.210168 13.401005) (xy 99.258752 13.410757) (xy 99.299906 13.438359)
				(xy 99.327364 13.479608) (xy 99.336947 13.528226) (xy 99.327195 13.57681) (xy 99.299593 13.617964)
				(xy 99.258344 13.645422) (xy 99.226524 13.653918) (xy 99.164286 13.662111) (xy 99.148592 13.666317)
				(xy 99.02969 13.715568) (xy 99.015619 13.723691) (xy 98.913527 13.802029) (xy 98.902029 13.813527)
				(xy 98.823691 13.915619) (xy 98.815568 13.92969) (xy 98.766317 14.048592) (xy 98.762113 14.064279)
				(xy 98.745315 14.191881) (xy 98.745315 14.208118) (xy 98.762113 14.33572) (xy 98.766317 14.351407)
				(xy 98.815568 14.470309) (xy 98.823691 14.48438) (xy 98.902032 14.586476) (xy 98.913518 14.597961)
				(xy 99.015625 14.676311) (xy 99.036902 14.688596) (xy 99.036177 14.68985) (xy 99.066624 14.71019)
				(xy 99.094158 14.751389) (xy 99.103829 14.799989) (xy 99.094166 14.848591) (xy 99.066639 14.889795)
				(xy 99.036176 14.910153) (xy 99.0369 14.911406) (xy 99.015619 14.923691) (xy 98.913527 15.002029)
				(xy 98.902029 15.013527) (xy 98.823691 15.115619) (xy 98.815568 15.12969) (xy 98.766317 15.248592)
				(xy 98.762113 15.264279) (xy 98.745315 15.391881) (xy 98.745315 15.408118) (xy 98.762113 15.53572)
				(xy 98.766317 15.551407) (xy 98.815568 15.670309) (xy 98.823691 15.68438) (xy 98.902032 15.786476)
				(xy 98.913518 15.797961) (xy 99.015625 15.876311) (xy 99.029689 15.884431) (xy 99.148592 15.933682)
				(xy 99.164279 15.937886) (xy 99.226794 15.946116) (xy 99.273717 15.962044) (xy 99.310973 15.994717)
				(xy 99.33289 16.039159) (xy 99.336132 16.088606) (xy 99.320204 16.135529) (xy 99.287531 16.172785)
				(xy 99.243089 16.194702) (xy 99.209992 16.19903) (xy 99.18143 16.198979) (xy 99.173925 16.199814)
				(xy 99.136655 16.208281) (xy 99.130557 16.210405) (xy 99.096087 16.22692) (xy 99.089699 16.230919)
				(xy 99.059781 16.254717) (xy 99.055206 16.259276) (xy 99.031309 16.289105) (xy 99.027285 16.295484)
				(xy 99.010644 16.329907) (xy 99.008506 16.335978) (xy 99.001077 16.368155) (xy 99.001054 16.376664)
				(xy 99.001064 16.37671) (xy 99.001054 16.37688) (xy 99.001054 16.377044) (xy 99.001045 16.377043)
				(xy 99.001 16.377848) (xy 99.001 16.397761) (xy 99.000876 16.445103) (xy 98.998603 16.445097) (xy 98.99861 16.445359)
				(xy 99.001 16.445359) (xy 99.001 19.863924) (xy 99.000184 19.878299) (xy 98.99875 19.890879) (xy 99.000221 19.904089)
				(xy 99.000703 19.912784) (xy 99.002919 19.932461) (xy 99.004264 19.940412) (xy 99.00568 19.953143)
				(xy 99.0101 19.965835) (xy 99.012745 19.975042) (xy 99.014387 19.979017) (xy 99.019008 19.987396)
				(xy 99.024845 19.999524) (xy 99.032827 20.009541) (xy 99.037647 20.016349) (xy 99.050073 20.031956)
				(xy 99.05578 20.038345) (xy 99.063882 20.048513) (xy 99.074211 20.056758) (xy 99.084782 20.066209)
				(xy 102.763803 23.74523) (xy 102.791333 23.786432) (xy 102.801 23.835033) (xy 102.801 43.074) (xy 102.791333 43.122601)
				(xy 102.763803 43.163803) (xy 102.722601 43.191333) (xy 102.674 43.201) (xy 87.926 43.201) (xy 87.877399 43.191333)
				(xy 87.836197 43.163803) (xy 87.808667 43.122601) (xy 87.799 43.074) (xy 87.799 40.588807) (xy 88.174 40.588807)
				(xy 88.174 41.311187) (xy 88.174051 41.312948) (xy 88.175458 41.322781) (xy 88.213126 41.485938)
				(xy 88.217739 41.499037) (xy 88.289866 41.648239) (xy 88.29727 41.659996) (xy 88.40066 41.789511)
				(xy 88.410492 41.799343) (xy 88.503515 41.873602) (xy 88.535467 41.911478) (xy 88.550491 41.958698)
				(xy 88.550838 41.962228) (xy 88.559587 42.066416) (xy 88.561784 42.078391) (xy 88.609819 42.24591)
				(xy 88.614295 42.257213) (xy 88.693961 42.412226) (xy 88.700551 42.422451) (xy 88.808805 42.559034)
				(xy 88.817252 42.567781) (xy 88.949966 42.68073) (xy 88.959962 42.687677) (xy 89.112086 42.772697)
				(xy 89.123242 42.77757) (xy 89.288985 42.831424) (xy 89.300868 42.834037) (xy 89.473921 42.854672)
				(xy 89.486077 42.854926) (xy 89.659844 42.841556) (xy 89.671825 42.839443) (xy 89.839682 42.792577)
				(xy 89.851027 42.788177) (xy 90.006578 42.709602) (xy 90.016853 42.70308) (xy 90.154178 42.59579)
				(xy 90.162997 42.587392) (xy 90.276868 42.455472) (xy 90.283885 42.445525) (xy 90.369963 42.294001)
				(xy 90.374916 42.282876) (xy 90.429924 42.117517) (xy 90.432618 42.105656) (xy 90.454797 41.930095)
				(xy 90.455282 41.923158) (xy 90.455556 41.903536) (xy 90.455264 41.896566) (xy 90.442172 41.763036)
				(xy 90.447051 41.713723) (xy 90.470429 41.670032) (xy 90.508748 41.638613) (xy 90.556173 41.624249)
				(xy 90.583596 41.625063) (xy 90.583614 41.62484) (xy 90.588197 41.6252) (xy 90.588444 41.625208)
				(xy 90.588595 41.625231) (xy 90.598355 41.625999) (xy 91.051641 41.625999) (xy 91.061408 41.62523)
				(xy 91.140747 41.612665) (xy 91.159224 41.606661) (xy 91.254869 41.557928) (xy 91.270594 41.546503)
				(xy 91.311847 41.505251) (xy 91.353049 41.477721) (xy 91.40165 41.468054) (xy 91.450251 41.477721)
				(xy 91.491453 41.505251) (xy 91.502406 41.517742) (xy 91.576369 41.614134) (xy 91.588065 41.62583)
				(xy 91.615595 41.667032) (xy 91.617639 41.672296) (xy 91.620565 41.680356) (xy 91.622006 41.684552)
				(xy 91.64133 41.744202) (xy 91.649678 41.762435) (xy 91.658602 41.780256) (xy 91.692978 41.832687)
				(xy 91.695343 41.836436) (xy 91.728761 41.891509) (xy 91.735331 41.900435) (xy 91.738701 41.90425)
				(xy 91.738684 41.904264) (xy 91.741702 41.907667) (xy 91.752977 41.921152) (xy 91.801604 41.967217)
				(xy 91.804067 41.969614) (xy 92.253383 42.418931) (xy 92.265867 42.433458) (xy 92.273641 42.444022)
				(xy 92.283483 42.454576) (xy 92.31642 42.482558) (xy 92.323996 42.489542) (xy 92.326729 42.492275)
				(xy 92.332134 42.497087) (xy 92.350579 42.51168) (xy 92.354008 42.514491) (xy 92.401788 42.555083)
				(xy 92.418423 42.566177) (xy 92.435434 42.576683) (xy 92.492238 42.603232) (xy 92.496221 42.605179)
				(xy 92.552041 42.633682) (xy 92.570781 42.640651) (xy 92.589751 42.646962) (xy 92.651142 42.659732)
				(xy 92.655465 42.66071) (xy 92.718023 42.676017) (xy 92.728994 42.677686) (xy 92.734061 42.678001)
				(xy 92.734059 42.678022) (xy 92.738585 42.678294) (xy 92.756094 42.679856) (xy 92.823047 42.678045)
				(xy 92.826482 42.677999) (xy 96.085164 42.677999) (xy 96.104266 42.679444) (xy 96.117236 42.681417)
				(xy 96.13166 42.68192) (xy 96.174734 42.678417) (xy 96.18503 42.677999) (xy 96.188882 42.677999)
				(xy 96.196107 42.677579) (xy 96.219465 42.674856) (xy 96.223874 42.67442) (xy 96.286385 42.669335)
				(xy 96.30596 42.665423) (xy 96.325448 42.660816) (xy 96.384364 42.639432) (xy 96.388554 42.637993)
				(xy 96.448202 42.618668) (xy 96.466436 42.61032) (xy 96.484258 42.601395) (xy 96.536695 42.567016)
				(xy 96.540562 42.564579) (xy 96.540899 42.564375) (xy 96.587482 42.547477) (xy 96.596923 42.546394)
				(xy 96.659844 42.541552) (xy 96.671825 42.539439) (xy 96.839682 42.492573) (xy 96.851027 42.488173)
				(xy 97.006578 42.409598) (xy 97.016853 42.403076) (xy 97.154178 42.295786) (xy 97.162997 42.287388)
				(xy 97.276868 42.155468) (xy 97.283885 42.145521) (xy 97.369963 41.993997) (xy 97.374916 41.982872)
				(xy 97.429924 41.817513) (xy 97.432618 41.805652) (xy 97.454797 41.630091) (xy 97.455282 41.623154)
				(xy 97.455556 41.603532) (xy 97.455264 41.596562) (xy 97.437999 41.420475) (xy 97.435635 41.40854)
				(xy 97.405965 41.310266) (xy 97.401173 41.260945) (xy 97.401631 41.256984) (xy 97.404466 41.235446)
				(xy 97.404999 41.227301) (xy 97.404999 40.456699) (xy 97.404465 40.448558) (xy 97.390598 40.343225)
				(xy 97.386396 40.32754) (xy 97.332106 40.196473) (xy 97.323983 40.182402) (xy 97.237628 40.069862)
				(xy 97.226138 40.058373) (xy 97.113595 39.972016) (xy 97.099519 39.96389) (xy 97.08999 39.959943)
				(xy 97.048788 39.932414) (xy 97.021257 39.891212) (xy 97.011589 39.842612) (xy 97.021256 39.794011)
				(xy 97.048785 39.752809) (xy 97.089987 39.725278) (xy 97.098395 39.722138) (xy 97.105739 39.719687)
				(xy 97.1188 39.713568) (xy 97.251605 39.631386) (xy 97.262904 39.622431) (xy 97.373238 39.511904)
				(xy 97.382178 39.500585) (xy 97.46412 39.367648) (xy 97.470219 39.354569) (xy 97.519526 39.205917)
				(xy 97.522368 39.192661) (xy 97.531674 39.101828) (xy 97.532 39.095455) (xy 97.532 39.072261) (xy 97.527647 39.057438)
				(xy 97.526673 39.056594) (xy 97.519346 39.055) (xy 96.447 39.055) (xy 96.398399 39.045333) (xy 96.357197 39.017803)
				(xy 96.329667 38.976601) (xy 96.32 38.928) (xy 96.32 38.336654) (xy 96.83 38.336654) (xy 96.83 38.527738)
				(xy 96.834352 38.542561) (xy 96.835326 38.543405) (xy 96.842654 38.545) (xy 97.514739 38.545) (xy 97.529561 38.540647)
				(xy 97.530405 38.539673) (xy 97.532 38.532345) (xy 97.532 38.504573) (xy 97.531667 38.498144) (xy 97.522115 38.406093)
				(xy 97.519246 38.392806) (xy 97.469689 38.244264) (xy 97.463568 38.231198) (xy 97.400681 38.129573)
				(xy 97.383327 38.083158) (xy 97.385057 38.033635) (xy 97.400045 37.996955) (xy 97.47419 37.874526)
				(xy 97.480345 37.860893) (xy 97.527288 37.711097) (xy 97.529877 37.698167) (xy 97.535736 37.634408)
				(xy 97.536 37.628666) (xy 97.536 37.572261) (xy 97.531647 37.557438) (xy 97.530673 37.556594) (xy 97.523346 37.555)
				(xy 96.901261 37.555) (xy 96.886438 37.559352) (xy 96.885594 37.560326) (xy 96.884 37.567654) (xy 96.884 38.221479)
				(xy 96.874333 38.27008) (xy 96.85298 38.304646) (xy 96.831593 38.329326) (xy 96.83 38.336654) (xy 96.32 38.336654)
				(xy 96.32 37.878521) (xy 96.329667 37.82992) (xy 96.35102 37.795354) (xy 96.372406 37.770673) (xy 96.374 37.763345)
				(xy 96.374 37.172) (xy 96.383667 37.123399) (xy 96.411197 37.082197) (xy 96.452399 37.054667) (xy 96.501 37.045)
				(xy 97.518738 37.045) (xy 97.53356 37.040647) (xy 97.534404 37.039673) (xy 97.535999 37.032345)
				(xy 97.535999 36.971319) (xy 97.535736 36.965594) (xy 97.529879 36.90184) (xy 97.527287 36.8889)
				(xy 97.480345 36.739106) (xy 97.47419 36.725473) (xy 97.433885 36.658921) (xy 97.416977 36.612342)
				(xy 97.419182 36.562838) (xy 97.440163 36.517946) (xy 97.476727 36.484501) (xy 97.523306 36.467593)
				(xy 97.57281 36.469798) (xy 97.600173 36.479974) (xy 97.640777 36.500662) (xy 97.659258 36.506667)
				(xy 97.738582 36.519231) (xy 97.748355 36.52) (xy 98.201641 36.52) (xy 98.211408 36.519231) (xy 98.290747 36.506666)
				(xy 98.309224 36.500662) (xy 98.404869 36.451929) (xy 98.420594 36.440504) (xy 98.496503 36.364595)
				(xy 98.507928 36.34887) (xy 98.520305 36.32458) (xy 98.550982 36.285665) (xy 98.594217 36.261452)
				(xy 98.643427 36.255627) (xy 98.691119 36.269078) (xy 98.730034 36.299755) (xy 98.754247 36.34299)
				(xy 98.754651 36.344258) (xy 98.773651 36.404886) (xy 98.779807 36.418523) (xy 98.860646 36.552003)
				(xy 98.869874 36.563771) (xy 98.980229 36.674126) (xy 98.991997 36.683354) (xy 99.125477 36.764193)
				(xy 99.139106 36.770346) (xy 99.288902 36.817289) (xy 99.301832 36.819878) (xy 99.356485 36.8249)
				(xy 99.367561 36.821648) (xy 99.368405 36.820674) (xy 99.37 36.813346) (xy 99.37 36.111655) (xy 99.88 36.111655)
				(xy 99.88 36.808738) (xy 99.884352 36.823561) (xy 99.885326 36.824405) (xy 99.889357 36.825282)
				(xy 99.948159 36.81988) (xy 99.961099 36.817288) (xy 100.110893 36.770346) (xy 100.124522 36.764193)
				(xy 100.258002 36.683354) (xy 100.26977 36.674126) (xy 100.380125 36.563771) (xy 100.389353 36.552003)
				(xy 100.470192 36.418523) (xy 100.476345 36.404894) (xy 100.523288 36.255098) (xy 100.525877 36.242168)
				(xy 100.531736 36.178409) (xy 100.532 36.172667) (xy 100.532 36.116262) (xy 100.527647 36.101439)
				(xy 100.526673 36.100595) (xy 100.519346 36.099001) (xy 99.897261 36.099001) (xy 99.882438 36.103353)
				(xy 99.881594 36.104327) (xy 99.88 36.111655) (xy 99.37 36.111655) (xy 99.37 35.716001) (xy 99.379667 35.6674)
				(xy 99.407197 35.626198) (xy 99.448399 35.598668) (xy 99.497 35.589001) (xy 100.514738 35.589001)
				(xy 100.52956 35.584648) (xy 100.530404 35.583674) (xy 100.531999 35.576346) (xy 100.531999 35.51532)
				(xy 100.531736 35.509595) (xy 100.525879 35.445841) (xy 100.523287 35.432901) (xy 100.476345 35.283107)
				(xy 100.470192 35.269478) (xy 100.389356 35.136002) (xy 100.384481 35.129786) (xy 100.362098 35.085576)
				(xy 100.358337 35.036166) (xy 100.373771 34.989078) (xy 100.402786 34.954127) (xy 100.411569 34.946756)
				(xy 100.428945 34.92605) (xy 100.436429 34.917881) (xy 102.036311 33.318) (xy 102.04448 33.310515)
				(xy 102.065184 33.293142) (xy 102.079028 33.276643) (xy 102.092541 33.253239) (xy 102.098493 33.243897)
				(xy 102.114005 33.221741) (xy 102.124665 33.198881) (xy 102.133295 33.175171) (xy 102.137991 33.148538)
				(xy 102.14039 33.137717) (xy 102.147384 33.111613) (xy 102.149262 33.090163) (xy 102.146906 33.063238)
				(xy 102.146423 33.052169) (xy 102.146423 29.807386) (xy 102.146906 29.796316) (xy 102.149261 29.769392)
				(xy 102.147385 29.747936) (xy 102.140388 29.721824) (xy 102.137989 29.711005) (xy 102.133295 29.684385)
				(xy 102.124664 29.660674) (xy 102.114004 29.637812) (xy 102.098496 29.615664) (xy 102.092543 29.60632)
				(xy 102.079027 29.582911) (xy 102.065184 29.566413) (xy 102.044473 29.549034) (xy 102.036304 29.54155)
				(xy 101.250002 28.755248) (xy 101.222472 28.714046) (xy 101.212805 28.665445) (xy 101.222472 28.616844)
				(xy 101.250002 28.575642) (xy 101.291204 28.548112) (xy 101.323227 28.539532) (xy 101.335713 28.537887)
				(xy 101.351407 28.533682) (xy 101.47031 28.484431) (xy 101.484374 28.476311) (xy 101.586481 28.397961)
				(xy 101.597967 28.386476) (xy 101.676308 28.28438) (xy 101.684431 28.270309) (xy 101.733682 28.151407)
				(xy 101.737886 28.13572) (xy 101.754685 28.008118) (xy 101.754685 27.991881) (xy 101.737886 27.864279)
				(xy 101.733682 27.848592) (xy 101.684431 27.72969) (xy 101.676308 27.715619) (xy 101.597967 27.613523)
				(xy 101.586481 27.602038) (xy 101.484374 27.523688) (xy 101.47031 27.515568) (xy 101.351407 27.466317)
				(xy 101.33572 27.462113) (xy 101.208118 27.445315) (xy 101.191882 27.445315) (xy 101.064279 27.462113)
				(xy 101.048592 27.466317) (xy 100.92969 27.515568) (xy 100.915619 27.523691) (xy 100.808477 27.605905)
				(xy 100.764035 27.627822) (xy 100.714588 27.631062) (xy 100.667665 27.615134) (xy 100.64144 27.595029)
				(xy 100.613955 27.567591) (xy 100.597364 27.555757) (xy 100.510367 27.513231) (xy 100.49206 27.507573)
				(xy 100.437769 27.499653) (xy 100.428759 27.499) (xy 98.721277 27.499) (xy 98.712191 27.499666)
				(xy 98.657366 27.507735) (xy 98.639027 27.513434) (xy 98.552122 27.556102) (xy 98.535551 27.567965)
				(xy 98.467592 27.636044) (xy 98.455757 27.652635) (xy 98.413231 27.739632) (xy 98.407573 27.757939)
				(xy 98.399653 27.81223) (xy 98.399 27.82124) (xy 98.399 28.178722) (xy 98.399666 28.187808) (xy 98.407735 28.242633)
				(xy 98.413434 28.260972) (xy 98.456102 28.347877) (xy 98.467965 28.364448) (xy 98.536044 28.432407)
				(xy 98.552635 28.444242) (xy 98.639632 28.486768) (xy 98.657939 28.492426) (xy 98.71223 28.500346)
				(xy 98.72124 28.500999) (xy 100.015427 28.500999) (xy 100.064028 28.510666) (xy 100.105229 28.538196)
				(xy 100.10523 28.538196) (xy 100.119231 28.552197) (xy 100.146761 28.593399) (xy 100.156428 28.642)
				(xy 100.146761 28.690601) (xy 100.119231 28.731803) (xy 100.078029 28.759333) (xy 100.029428 28.769)
				(xy 98.721277 28.769) (xy 98.712191 28.769666) (xy 98.657366 28.777735) (xy 98.639027 28.783434)
				(xy 98.552122 28.826102) (xy 98.535551 28.837965) (xy 98.467592 28.906044) (xy 98.455756 28.922635)
				(xy 98.415245 29.005513) (xy 98.385216 29.044931) (xy 98.342389 29.069857) (xy 98.293283 29.076496)
				(xy 98.245374 29.063838) (xy 98.223835 29.050496) (xy 98.189189 29.023911) (xy 98.175121 29.015789)
				(xy 98.056218 28.966538) (xy 98.040531 28.962334) (xy 97.912929 28.945536) (xy 97.896693 28.945536)
				(xy 97.76909 28.962334) (xy 97.745361 28.968693) (xy 97.745208 28.968124) (xy 97.705003 28.976123)
				(xy 97.656402 28.966456) (xy 97.6152 28.938927) (xy 97.587669 28.897725) (xy 97.578001 28.849125)
				(xy 97.578001 27.270027) (xy 97.587668 27.221426) (xy 97.615198 27.180224) (xy 97.6564 27.152694)
				(xy 97.688424 27.144114) (xy 97.735713 27.137888) (xy 97.751407 27.133682) (xy 97.87031 27.084431)
				(xy 97.884374 27.076311) (xy 97.986481 26.997961) (xy 97.997967 26.986476) (xy 98.076308 26.88438)
				(xy 98.084431 26.870309) (xy 98.133682 26.751407) (xy 98.137886 26.73572) (xy 98.146086 26.673433)
				(xy 98.162014 26.62651) (xy 98.17927 26.606833) (xy 98.172079 26.600347) (xy 98.150785 26.555603)
				(xy 98.147991 26.541025) (xy 98.137888 26.464286) (xy 98.133682 26.448592) (xy 98.084431 26.32969)
				(xy 98.076308 26.315619) (xy 97.997967 26.213523) (xy 97.986481 26.202038) (xy 97.884374 26.123688)
				(xy 97.87031 26.115568) (xy 97.751407 26.066317) (xy 97.73572 26.062113) (xy 97.608118 26.045315)
				(xy 97.591882 26.045315) (xy 97.464279 26.062113) (xy 97.448592 26.066317) (xy 97.32969 26.115568)
				(xy 97.315619 26.123691) (xy 97.213527 26.202029) (xy 97.202029 26.213527) (xy 97.123691 26.315619)
				(xy 97.115568 26.32969) (xy 97.066317 26.448592) (xy 97.062113 26.464279) (xy 97.045315 26.591881)
				(xy 97.045315 26.608122) (xy 97.046352 26.616001) (xy 97.04311 26.665448) (xy 97.021192 26.70989)
				(xy 97.004867 26.726942) (xy 96.989395 26.745382) (xy 96.975884 26.768784) (xy 96.969932 26.778127)
				(xy 96.954417 26.800284) (xy 96.943759 26.823139) (xy 96.935127 26.846856) (xy 96.930431 26.873489)
				(xy 96.928034 26.884303) (xy 96.921039 26.910407) (xy 96.919162 26.931864) (xy 96.921518 26.958791)
				(xy 96.922001 26.969859) (xy 96.922001 29.961533) (xy 96.912334 30.010134) (xy 96.884804 30.051336)
				(xy 95.991121 30.945019) (xy 95.949919 30.972549) (xy 95.901318 30.982216) (xy 95.852717 30.972549)
				(xy 95.811515 30.945019) (xy 95.783985 30.903817) (xy 95.774318 30.855216) (xy 95.783985 30.806615)
				(xy 95.786692 30.800613) (xy 95.792426 30.78206) (xy 95.800346 30.727769) (xy 95.800999 30.718759)
				(xy 95.800999 30.361277) (xy 95.800333 30.352191) (xy 95.792264 30.297366) (xy 95.786565 30.279027)
				(xy 95.743897 30.192122) (xy 95.732034 30.175551) (xy 95.663955 30.107592) (xy 95.647364 30.095757)
				(xy 95.560367 30.053231) (xy 95.54206 30.047573) (xy 95.487769 30.039653) (xy 95.478759 30.039)
				(xy 93.771277 30.039) (xy 93.762191 30.039666) (xy 93.707366 30.047735) (xy 93.689027 30.053434)
				(xy 93.602122 30.096102) (xy 93.585551 30.107965) (xy 93.517592 30.176044) (xy 93.505757 30.192635)
				(xy 93.463231 30.279632) (xy 93.457573 30.297939) (xy 93.449653 30.352229) (xy 93.449 30.36124)
				(xy 93.449001 30.524532) (xy 93.439334 30.573133) (xy 93.411804 30.614335) (xy 93.411804 30.614336)
				(xy 93.272081 30.754059) (xy 93.230879 30.781589) (xy 93.182278 30.791256) (xy 93.133677 30.781589)
				(xy 93.092475 30.754059) (xy 93.064945 30.712857) (xy 93.055278 30.664256) (xy 93.064945 30.615655)
				(xy 93.064946 30.615655) (xy 93.094799 30.543584) (xy 93.099004 30.52789) (xy 93.115803 30.400288)
				(xy 93.115803 30.384051) (xy 93.099004 30.256449) (xy 93.0948 30.240762) (xy 93.045549 30.12186)
				(xy 93.037428 30.107794) (xy 92.999986 30.058998) (xy 92.978069 30.014555) (xy 92.974828 29.965108)
				(xy 92.990757 29.918185) (xy 93.023429 29.880929) (xy 93.067872 29.859012) (xy 93.100742 29.854685)
				(xy 93.108118 29.854685) (xy 93.23572 29.837886) (xy 93.251407 29.833682) (xy 93.37031 29.784431)
				(xy 93.384374 29.776311) (xy 93.456888 29.72067) (xy 93.501331 29.698753) (xy 93.550778 29.695513)
				(xy 93.593086 29.709874) (xy 93.593205 29.709633) (xy 93.595091 29.710555) (xy 93.597701 29.711441)
				(xy 93.600224 29.713064) (xy 93.689632 29.756768) (xy 93.707939 29.762426) (xy 93.76223 29.770346)
				(xy 93.77124 29.770999) (xy 95.478722 29.770999) (xy 95.487808 29.770333) (xy 95.542633 29.762264)
				(xy 95.560972 29.756565) (xy 95.647877 29.713897) (xy 95.664448 29.702034) (xy 95.732407 29.633955)
				(xy 95.744242 29.617364) (xy 95.786768 29.530367) (xy 95.792426 29.51206) (xy 95.800346 29.457769)
				(xy 95.800999 29.448759) (xy 95.800999 29.130717) (xy 95.810666 29.082117) (xy 95.838196 29.040915)
				(xy 95.879398 29.013384) (xy 95.895131 29.008044) (xy 95.911406 29.003683) (xy 96.03031 28.954431)
				(xy 96.044374 28.946311) (xy 96.146481 28.867961) (xy 96.157967 28.856476) (xy 96.236308 28.75438)
				(xy 96.244431 28.740309) (xy 96.293682 28.621407) (xy 96.297886 28.60572) (xy 96.314685 28.478118)
				(xy 96.314685 28.461881) (xy 96.297886 28.334279) (xy 96.293682 28.318592) (xy 96.244431 28.19969)
				(xy 96.236308 28.185619) (xy 96.157967 28.083523) (xy 96.146481 28.072038) (xy 96.044374 27.993688)
				(xy 96.03031 27.985568) (xy 95.911404 27.936315) (xy 95.894583 27.931809) (xy 95.85014 27.909894)
				(xy 95.817466 27.872639) (xy 95.801537 27.825716) (xy 95.800789 27.818408) (xy 95.800333 27.81219)
				(xy 95.792264 27.757366) (xy 95.786565 27.739027) (xy 95.743897 27.652122) (xy 95.732034 27.635551)
				(xy 95.663955 27.567592) (xy 95.647364 27.555757) (xy 95.560367 27.513231) (xy 95.54206 27.507573)
				(xy 95.487769 27.499653) (xy 95.478759 27.499) (xy 93.771277 27.499) (xy 93.762191 27.499666) (xy 93.707366 27.507735)
				(xy 93.689027 27.513434) (xy 93.602122 27.556102) (xy 93.585551 27.567965) (xy 93.517592 27.636044)
				(xy 93.505757 27.652635) (xy 93.463231 27.739632) (xy 93.457573 27.757939) (xy 93.449653 27.81223)
				(xy 93.449 27.82124) (xy 93.449 28.178722) (xy 93.449666 28.187808) (xy 93.457735 28.242633) (xy 93.463434 28.260972)
				(xy 93.506102 28.347877) (xy 93.517965 28.364448) (xy 93.586044 28.432407) (xy 93.602635 28.444242)
				(xy 93.689632 28.486768) (xy 93.707939 28.492426) (xy 93.76223 28.500346) (xy 93.77124 28.500999)
				(xy 95.096951 28.500999) (xy 95.145552 28.510666) (xy 95.186753 28.538196) (xy 95.214284 28.579398)
				(xy 95.219231 28.597869) (xy 95.219958 28.597675) (xy 95.223027 28.609128) (xy 95.226269 28.658575)
				(xy 95.210341 28.705498) (xy 95.177669 28.742755) (xy 95.133227 28.764672) (xy 95.100355 28.769)
				(xy 93.771277 28.769) (xy 93.762191 28.769666) (xy 93.707366 28.777735) (xy 93.689027 28.783434)
				(xy 93.602122 28.826102) (xy 93.585549 28.837967) (xy 93.585114 28.838404) (xy 93.582438 28.840194)
				(xy 93.577016 28.844077) (xy 93.576893 28.843906) (xy 93.543935 28.865968) (xy 93.495342 28.875676)
				(xy 93.446733 28.866049) (xy 93.417924 28.849432) (xy 93.384374 28.823688) (xy 93.37031 28.815568)
				(xy 93.251407 28.766317) (xy 93.23572 28.762113) (xy 93.108118 28.745315) (xy 93.091882 28.745315)
				(xy 92.964279 28.762113) (xy 92.948592 28.766317) (xy 92.8516 28.806493) (xy 92.802999 28.81616)
				(xy 92.754398 28.806493) (xy 92.713197 28.778963) (xy 92.685666 28.737761) (xy 92.675999 28.68916)
				(xy 92.675999 28.601973) (xy 92.675722 28.596102) (xy 92.673906 28.576886) (xy 92.670663 28.562108)
				(xy 92.633832 28.457226) (xy 92.625184 28.440894) (xy 92.560179 28.352884) (xy 92.547113 28.339818)
				(xy 92.506547 28.309855) (xy 92.473197 28.273204) (xy 92.456411 28.22658) (xy 92.455 28.207699)
				(xy 92.455 28.127999) (xy 92.464667 28.079398) (xy 92.492197 28.038196) (xy 92.533399 28.010666)
				(xy 92.582 28.000999) (xy 92.713657 28.000999) (xy 92.725811 27.999801) (xy 92.766352 27.991738)
				(xy 92.788658 27.982499) (xy 92.834675 27.951752) (xy 92.851752 27.934675) (xy 92.882501 27.888655)
				(xy 92.891737 27.866357) (xy 92.899801 27.825819) (xy 92.900999 27.813648) (xy 92.900999 27.185)
				(xy 92.910666 27.136399) (xy 92.938196 27.095198) (xy 92.979398 27.067667) (xy 93.027999 27.058)
				(xy 93.428914 27.058) (xy 93.477515 27.067667) (xy 93.518638 27.095119) (xy 93.586044 27.162407)
				(xy 93.602635 27.174242) (xy 93.689632 27.216768) (xy 93.707939 27.222426) (xy 93.76223 27.230346)
				(xy 93.77124 27.230999) (xy 95.478722 27.230999) (xy 95.487808 27.230333) (xy 95.542633 27.222264)
				(xy 95.560972 27.216565) (xy 95.647877 27.173897) (xy 95.664448 27.162034) (xy 95.732407 27.093955)
				(xy 95.744242 27.077364) (xy 95.786768 26.990367) (xy 95.792426 26.97206) (xy 95.800346 26.917769)
				(xy 95.800999 26.908759) (xy 95.800999 26.551277) (xy 95.800333 26.542191) (xy 95.792264 26.487366)
				(xy 95.786565 26.469027) (xy 95.743897 26.382122) (xy 95.732034 26.365551) (xy 95.663955 26.297592)
				(xy 95.647364 26.285757) (xy 95.560367 26.243231) (xy 95.54206 26.237573) (xy 95.487769 26.229653)
				(xy 95.478759 26.229) (xy 93.771277 26.229) (xy 93.762191 26.229666) (xy 93.707366 26.237735) (xy 93.689027 26.243434)
				(xy 93.602122 26.286102) (xy 93.585552 26.297965) (xy 93.51891 26.364724) (xy 93.477732 26.39229)
				(xy 93.42914 26.402) (xy 93.219993 26.402) (xy 93.171392 26.392333) (xy 93.13019 26.364803) (xy 93.10266 26.323601)
				(xy 93.092993 26.275) (xy 93.10266 26.226399) (xy 93.13019 26.185197) (xy 93.154018 26.166913) (xy 93.172317 26.151559)
				(xy 93.189694 26.130851) (xy 93.197178 26.122682) (xy 93.417455 25.902405) (xy 93.458657 25.874875)
				(xy 93.507258 25.865208) (xy 93.555859 25.874875) (xy 93.581007 25.888815) (xy 93.602636 25.904243)
				(xy 93.689632 25.946768) (xy 93.707939 25.952426) (xy 93.76223 25.960346) (xy 93.77124 25.960999)
				(xy 95.478722 25.960999) (xy 95.487808 25.960333) (xy 95.542633 25.952264) (xy 95.560972 25.946565)
				(xy 95.647877 25.903897) (xy 95.664448 25.892034) (xy 95.732407 25.823955) (xy 95.744242 25.807364)
				(xy 95.786768 25.720367) (xy 95.792426 25.70206) (xy 95.800346 25.647769) (xy 95.800999 25.638759)
				(xy 95.800999 25.281277) (xy 95.800333 25.272191) (xy 95.792264 25.217366) (xy 95.786565 25.199027)
				(xy 95.743897 25.112122) (xy 95.732034 25.095551) (xy 95.663955 25.027592) (xy 95.647364 25.015757)
				(xy 95.560367 24.973231) (xy 95.54206 24.967573) (xy 95.487769 24.959653) (xy 95.478759 24.959)
				(xy 93.771277 24.959) (xy 93.762191 24.959666) (xy 93.707366 24.967735) (xy 93.689027 24.973434)
				(xy 93.602122 25.016102) (xy 93.585552 25.027965) (xy 93.51891 25.094724) (xy 93.477732 25.12229)
				(xy 93.42914 25.132) (xy 93.415857 25.132) (xy 93.404787 25.131517) (xy 93.377863 25.129161) (xy 93.356407 25.131037)
				(xy 93.330295 25.138035) (xy 93.319476 25.140434) (xy 93.292855 25.145127) (xy 93.273561 25.152151)
				(xy 93.224585 25.159691) (xy 93.203124 25.15691) (xy 93.194344 25.155) (xy 91.972 25.155) (xy 91.923399 25.145333)
				(xy 91.882197 25.117803) (xy 91.854667 25.076601) (xy 91.845 25.028) (xy 91.845 24.772) (xy 91.854667 24.723399)
				(xy 91.882197 24.682197) (xy 91.923399 24.654667) (xy 91.972 24.645) (xy 93.189738 24.645) (xy 93.20456 24.640647)
				(xy 93.205404 24.639673) (xy 93.206999 24.632345) (xy 93.206999 24.462414) (xy 93.216666 24.413813)
				(xy 93.244196 24.372611) (xy 93.285398 24.345081) (xy 93.333999 24.335414) (xy 93.3826 24.345081)
				(xy 93.423802 24.372611) (xy 93.451332 24.413813) (xy 93.455278 24.424727) (xy 93.463431 24.450966)
				(xy 93.506102 24.537877) (xy 93.517965 24.554448) (xy 93.586044 24.622407) (xy 93.602635 24.634242)
				(xy 93.689632 24.676768) (xy 93.707939 24.682426) (xy 93.76223 24.690346) (xy 93.77124 24.690999)
				(xy 95.478722 24.690999) (xy 95.487808 24.690333) (xy 95.542633 24.682264) (xy 95.560972 24.676565)
				(xy 95.647877 24.633897) (xy 95.664448 24.622034) (xy 95.732407 24.553955) (xy 95.744242 24.537364)
				(xy 95.784113 24.455799) (xy 98.099476 24.455799) (xy 98.140048 24.595447) (xy 98.146244 24.609766)
				(xy 98.222741 24.739116) (xy 98.232295 24.751432) (xy 98.338567 24.857704) (xy 98.350883 24.867258)
				(xy 98.428654 24.913251) (xy 98.465565 24.946312) (xy 98.487016 24.990982) (xy 98.489739 25.04046)
				(xy 98.473321 25.087214) (xy 98.467398 25.096316) (xy 98.455757 25.112635) (xy 98.413231 25.199632)
				(xy 98.407573 25.217939) (xy 98.399653 25.27223) (xy 98.399 25.28124) (xy 98.399 25.638722) (xy 98.399666 25.647808)
				(xy 98.407735 25.702633) (xy 98.413434 25.720972) (xy 98.456102 25.807877) (xy 98.467965 25.824448)
				(xy 98.536044 25.892407) (xy 98.552635 25.904242) (xy 98.639632 25.946768) (xy 98.657939 25.952426)
				(xy 98.71223 25.960346) (xy 98.72124 25.960999) (xy 100.430895 25.960999) (xy 100.479496 25.970666)
				(xy 100.520698 25.998196) (xy 100.548228 26.039398) (xy 100.557895 26.087999) (xy 100.556809 26.104573)
				(xy 100.554966 26.118574) (xy 100.539038 26.165498) (xy 100.506367 26.202754) (xy 100.461924 26.224672)
				(xy 100.429052 26.229) (xy 98.721277 26.229) (xy 98.712191 26.229666) (xy 98.657366 26.237735) (xy 98.639027 26.243434)
				(xy 98.552122 26.286102) (xy 98.535551 26.297965) (xy 98.467592 26.366044) (xy 98.455757 26.382635)
				(xy 98.413231 26.469632) (xy 98.407573 26.487939) (xy 98.399574 26.54278) (xy 98.382993 26.589476)
				(xy 98.366777 26.607453) (xy 98.372755 26.612696) (xy 98.394672 26.657138) (xy 98.399 26.690008)
				(xy 98.399 26.908722) (xy 98.399666 26.917808) (xy 98.407735 26.972633) (xy 98.413434 26.990972)
				(xy 98.456102 27.077877) (xy 98.467965 27.094448) (xy 98.536044 27.162407) (xy 98.552635 27.174242)
				(xy 98.639632 27.216768) (xy 98.657939 27.222426) (xy 98.71223 27.230346) (xy 98.72124 27.230999)
				(xy 100.428722 27.230999) (xy 100.437808 27.230333) (xy 100.492633 27.222264) (xy 100.510972 27.216565)
				(xy 100.597877 27.173897) (xy 100.614448 27.162034) (xy 100.682407 27.093955) (xy 100.694242 27.077364)
				(xy 100.736768 26.990367) (xy 100.742426 26.97206) (xy 100.750346 26.917769) (xy 100.751 26.908759)
				(xy 100.751 26.841907) (xy 100.760667 26.793306) (xy 100.788197 26.752104) (xy 100.829399 26.724574)
				(xy 100.878 26.714907) (xy 100.9266 26.724574) (xy 100.948589 26.733682) (xy 100.964279 26.737886)
				(xy 101.091882 26.754685) (xy 101.108118 26.754685) (xy 101.23572 26.737886) (xy 101.251407 26.733682)
				(xy 101.37031 26.684431) (xy 101.384374 26.676311) (xy 101.486481 26.597961) (xy 101.497967 26.586476)
				(xy 101.576308 26.48438) (xy 101.584431 26.470309) (xy 101.633682 26.351407) (xy 101.637886 26.33572)
				(xy 101.654685 26.208118) (xy 101.654685 26.191881) (xy 101.637886 26.064279) (xy 101.633682 26.048592)
				(xy 101.584431 25.92969) (xy 101.576308 25.915619) (xy 101.497967 25.813523) (xy 101.486481 25.802038)
				(xy 101.384374 25.723688) (xy 101.37031 25.715568) (xy 101.251407 25.666317) (xy 101.23572 25.662113)
				(xy 101.108118 25.645315) (xy 101.091882 25.645315) (xy 100.964279 25.662113) (xy 100.948589 25.666317)
				(xy 100.9266 25.675426) (xy 100.877999 25.685093) (xy 100.829398 25.675425) (xy 100.788197 25.647895)
				(xy 100.760667 25.606693) (xy 100.751 25.558093) (xy 100.750999 25.281277) (xy 100.750333 25.272191)
				(xy 100.742264 25.217366) (xy 100.736565 25.199027) (xy 100.693898 25.112125) (xy 100.682716 25.096506)
				(xy 100.662284 25.051361) (xy 100.660684 25.001834) (xy 100.678159 24.955465) (xy 100.712048 24.919312)
				(xy 100.721331 24.91326) (xy 100.799116 24.867258) (xy 100.811432 24.857704) (xy 100.917704 24.751432)
				(xy 100.927258 24.739116) (xy 101.003755 24.609766) (xy 101.009951 24.595447) (xy 101.048938 24.461255)
				(xy 101.0489 24.44794) (xy 101.042414 24.445) (xy 98.113268 24.445) (xy 98.100489 24.448752) (xy 98.099476 24.455799)
				(xy 95.784113 24.455799) (xy 95.786768 24.450367) (xy 95.792426 24.43206) (xy 95.800346 24.377769)
				(xy 95.800999 24.368759) (xy 95.800999 24.011277) (xy 95.800333 24.002191) (xy 95.792263 23.94736)
				(xy 95.78952 23.938533) (xy 95.789519 23.938532) (xy 95.786566 23.929029) (xy 95.781516 23.918744)
				(xy 98.101061 23.918744) (xy 98.101099 23.932059) (xy 98.107586 23.935) (xy 99.302739 23.935) (xy 99.317561 23.930647)
				(xy 99.318405 23.929673) (xy 99.32 23.922345) (xy 99.32 23.400261) (xy 99.318647 23.395654) (xy 99.83 23.395654)
				(xy 99.83 23.917738) (xy 99.834352 23.932561) (xy 99.835326 23.933405) (xy 99.842654 23.935) (xy 101.036732 23.935)
				(xy 101.04951 23.931247) (xy 101.050523 23.9242) (xy 101.009951 23.784552) (xy 101.003755 23.770233)
				(xy 100.927258 23.640883) (xy 100.917704 23.628567) (xy 100.811432 23.522295) (xy 100.799116 23.512741)
				(xy 100.669766 23.436244) (xy 100.655447 23.430048) (xy 100.509699 23.387704) (xy 100.497213 23.385423)
				(xy 100.468847 23.383191) (xy 100.463973 23.383) (xy 99.847261 23.383) (xy 99.832438 23.387352)
				(xy 99.831594 23.388326) (xy 99.83 23.395654) (xy 99.318647 23.395654) (xy 99.315647 23.385439)
				(xy 99.314673 23.384595) (xy 99.307346 23.383001) (xy 98.686031 23.383001) (xy 98.681145 23.383192)
				(xy 98.652795 23.385423) (xy 98.640292 23.387706) (xy 98.494552 23.430048) (xy 98.480233 23.436244)
				(xy 98.350883 23.512741) (xy 98.338567 23.522295) (xy 98.232295 23.628567) (xy 98.222741 23.640883)
				(xy 98.146244 23.770233) (xy 98.140048 23.784552) (xy 98.101061 23.918744) (xy 95.781516 23.918744)
				(xy 95.743897 23.842122) (xy 95.732034 23.825551) (xy 95.663955 23.757592) (xy 95.647364 23.745757)
				(xy 95.560367 23.703231) (xy 95.542059 23.697573) (xy 95.528948 23.695661) (xy 95.482251 23.67908)
				(xy 95.445455 23.645891) (xy 95.42416 23.601147) (xy 95.421609 23.55166) (xy 95.42234 23.547631)
				(xy 95.42234 22.845168) (xy 95.93234 22.845168) (xy 95.93234 23.542252) (xy 95.936692 23.557075)
				(xy 95.937666 23.557919) (xy 95.944994 23.559514) (xy 95.947766 23.559514) (xy 95.954195 23.559181)
				(xy 96.046246 23.549629) (xy 96.059533 23.54676) (xy 96.208075 23.497203) (xy 96.22114 23.491082)
				(xy 96.353945 23.4089) (xy 96.365244 23.399945) (xy 96.475578 23.289418) (xy 96.484518 23.278099)
				(xy 96.56646 23.145162) (xy 96.572559 23.132083) (xy 96.621866 22.983431) (xy 96.624708 22.970175)
				(xy 96.634014 22.879342) (xy 96.63434 22.872969) (xy 96.63434 22.849775) (xy 96.629987 22.834952)
				(xy 96.629013 22.834108) (xy 96.621686 22.832514) (xy 95.949601 22.832514) (xy 95.934778 22.836866)
				(xy 95.933934 22.83784) (xy 95.93234 22.845168) (xy 95.42234 22.845168) (xy 95.42234 21.612775)
				(xy 95.420987 21.608168) (xy 95.93234 21.608168) (xy 95.93234 22.305252) (xy 95.936692 22.320075)
				(xy 95.937666 22.320919) (xy 95.944994 22.322514) (xy 96.617079 22.322514) (xy 96.631901 22.318161)
				(xy 96.632745 22.317187) (xy 96.63434 22.309859) (xy 96.63434 22.282087) (xy 96.634007 22.275658)
				(xy 96.624455 22.183607) (xy 96.621586 22.17032) (xy 96.572029 22.021778) (xy 96.565908 22.008713)
				(xy 96.483726 21.875908) (xy 96.474771 21.864609) (xy 96.364244 21.754275) (xy 96.352925 21.745335)
				(xy 96.219988 21.663393) (xy 96.206909 21.657294) (xy 96.058257 21.607987) (xy 96.045001 21.605145)
				(xy 95.954168 21.595839) (xy 95.949333 21.595592) (xy 95.934778 21.599866) (xy 95.933934 21.60084)
				(xy 95.93234 21.608168) (xy 95.420987 21.608168) (xy 95.417987 21.597952) (xy 95.417013 21.597108)
				(xy 95.409686 21.595514) (xy 95.406914 21.595514) (xy 95.400484 21.595846) (xy 95.308433 21.605398)
				(xy 95.295146 21.608267) (xy 95.146604 21.657824) (xy 95.133539 21.663945) (xy 95.000734 21.746127)
				(xy 94.989435 21.755082) (xy 94.879101 21.865609) (xy 94.870166 21.876922) (xy 94.838452 21.928374)
				(xy 94.80472 21.964674) (xy 94.759665 21.985302) (xy 94.710145 21.987118) (xy 94.6637 21.969846)
				(xy 94.6274 21.936114) (xy 94.606772 21.891059) (xy 94.60334 21.861734) (xy 94.60334 21.849924)
				(xy 94.613007 21.801323) (xy 94.633682 21.751407) (xy 94.637886 21.73572) (xy 94.654685 21.608118)
				(xy 94.654685 21.591881) (xy 94.637886 21.464279) (xy 94.633682 21.448592) (xy 94.584431 21.32969)
				(xy 94.576308 21.315619) (xy 94.497967 21.213523) (xy 94.486481 21.202038) (xy 94.384374 21.123688)
				(xy 94.37031 21.115568) (xy 94.251407 21.066317) (xy 94.23572 21.062113) (xy 94.108118 21.045315)
				(xy 94.091882 21.045315) (xy 93.964279 21.062113) (xy 93.948592 21.066317) (xy 93.82969 21.115568)
				(xy 93.815619 21.123691) (xy 93.713527 21.202029) (xy 93.702029 21.213527) (xy 93.623691 21.315619)
				(xy 93.615568 21.32969) (xy 93.566317 21.448592) (xy 93.562113 21.464279) (xy 93.545315 21.591881)
				(xy 93.545315 21.608118) (xy 93.562113 21.73572) (xy 93.566317 21.751407) (xy 93.615569 21.870311)
				(xy 93.616873 21.87257) (xy 93.618544 21.877495) (xy 93.618753 21.877998) (xy 93.61872 21.878011)
				(xy 93.6328 21.919494) (xy 93.629558 21.96894) (xy 93.60764 22.013383) (xy 93.596689 22.02587) (xy 93.585541 22.037018)
				(xy 93.544339 22.064548) (xy 93.495738 22.074215) (xy 93.459822 22.067073) (xy 93.459448 22.068472)
				(xy 93.435718 22.062113) (xy 93.308118 22.045315) (xy 93.291882 22.045315) (xy 93.164279 22.062113)
				(xy 93.148592 22.066317) (xy 93.02969 22.115568) (xy 93.015619 22.123691) (xy 92.913527 22.202029)
				(xy 92.902029 22.213527) (xy 92.823691 22.315619) (xy 92.815567 22.329692) (xy 92.814915 22.331267)
				(xy 92.813025 22.334094) (xy 92.811406 22.3369) (xy 92.811222 22.336793) (xy 92.787385 22.372469)
				(xy 92.746184 22.399999) (xy 92.697583 22.409667) (xy 92.648982 22.4) (xy 92.607858 22.372548) (xy 92.511904 22.276761)
				(xy 92.500585 22.267821) (xy 92.367648 22.185879) (xy 92.354569 22.17978) (xy 92.205917 22.130473)
				(xy 92.192661 22.127631) (xy 92.101828 22.118325) (xy 92.096993 22.118078) (xy 92.082438 22.122352)
				(xy 92.081594 22.123326) (xy 92.08 22.130654) (xy 92.08 22.827738) (xy 92.084352 22.842561) (xy 92.085326 22.843405)
				(xy 92.092654 22.845) (xy 92.730845 22.845) (xy 92.779446 22.854667) (xy 92.820648 22.882197) (xy 92.831601 22.894687)
				(xy 92.902035 22.986479) (xy 92.913518 22.997961) (xy 93.015625 23.076311) (xy 93.029689 23.084431)
				(xy 93.148592 23.133682) (xy 93.164279 23.137886) (xy 93.291882 23.154685) (xy 93.308118 23.154685)
				(xy 93.43572 23.137886) (xy 93.451407 23.133682) (xy 93.480163 23.121772) (xy 93.528764 23.112105)
				(xy 93.577365 23.121772) (xy 93.604217 23.136949) (xy 93.649793 23.170612) (xy 93.683143 23.207263)
				(xy 93.699929 23.253887) (xy 93.70134 23.272768) (xy 93.70134 23.618262) (xy 93.691673 23.666863)
				(xy 93.664143 23.708065) (xy 93.630311 23.732263) (xy 93.602122 23.746102) (xy 93.585551 23.757965)
				(xy 93.517592 23.826044) (xy 93.505757 23.842635) (xy 93.463231 23.929632) (xy 93.457573 23.947939)
				(xy 93.449653 24.00223) (xy 93.449 24.01124) (xy 93.449 24.057198) (xy 93.439333 24.105799) (xy 93.411803 24.147001)
				(xy 93.370601 24.174531) (xy 93.322 24.184198) (xy 93.273399 24.174531) (xy 93.232197 24.147001)
				(xy 93.204667 24.105799) (xy 93.198443 24.086571) (xy 93.197518 24.08268) (xy 93.152413 23.962364)
				(xy 93.143946 23.9469) (xy 93.067529 23.844936) (xy 93.055063 23.83247) (xy 92.953099 23.756053)
				(xy 92.937635 23.747586) (xy 92.831261 23.707708) (xy 92.789146 23.681596) (xy 92.760231 23.641354)
				(xy 92.748916 23.59311) (xy 92.7553 23.548807) (xy 92.769526 23.505917) (xy 92.772368 23.492661)
				(xy 92.781674 23.401828) (xy 92.782 23.395455) (xy 92.782 23.372261) (xy 92.777647 23.357438) (xy 92.776673 23.356594)
				(xy 92.769346 23.355) (xy 91.697 23.355) (xy 91.648399 23.345333) (xy 91.607197 23.317803) (xy 91.579667 23.276601)
				(xy 91.57 23.228) (xy 91.57 22.135261) (xy 91.565647 22.120438) (xy 91.564673 22.119594) (xy 91.557346 22.118)
				(xy 91.554574 22.118) (xy 91.548144 22.118332) (xy 91.456093 22.127884) (xy 91.442806 22.130753)
				(xy 91.294264 22.18031) (xy 91.281199 22.186431) (xy 91.148394 22.268613) (xy 91.137095 22.277568)
				(xy 91.026761 22.388095) (xy 91.017824 22.399411) (xy 90.961774 22.490342) (xy 90.928042 22.526641)
				(xy 90.882987 22.547269) (xy 90.833467 22.549085) (xy 90.787022 22.531812) (xy 90.778209 22.525857)
				(xy 90.709102 22.474813) (xy 90.692773 22.466167) (xy 90.587888 22.429334) (xy 90.573109 22.426091)
				(xy 90.553912 22.424277) (xy 90.548032 22.424) (xy 90.001973 22.424) (xy 89.996102 22.424277) (xy 89.976886 22.426093)
				(xy 89.962108 22.429336) (xy 89.857226 22.466167) (xy 89.840894 22.474815) (xy 89.752888 22.539817)
				(xy 89.739817 22.552888) (xy 89.674815 22.640894) (xy 89.666167 22.657226) (xy 89.629334 22.762111)
				(xy 89.626091 22.77689) (xy 89.624277 22.796087) (xy 89.624 22.801967) (xy 89.624 23.398026) (xy 89.624277 23.403897)
				(xy 89.626093 23.423113) (xy 89.629336 23.437891) (xy 89.666167 23.542773) (xy 89.674815 23.559105)
				(xy 89.73982 23.647115) (xy 89.757804 23.6651) (xy 89.785334 23.706303) (xy 89.795 23.754901) (xy 89.795 23.89799)
				(xy 89.785333 23.946591) (xy 89.757803 23.987793) (xy 89.716601 24.015323) (xy 89.716598 24.015324)
				(xy 89.71134 24.017501) (xy 89.665324 24.048247) (xy 89.648247 24.065324) (xy 89.617498 24.111344)
				(xy 89.608262 24.133642) (xy 89.600198 24.17418) (xy 89.599 24.186351) (xy 89.599 25.613656) (xy 89.600198 25.625811)
				(xy 89.608261 25.666352) (xy 89.6175 25.688658) (xy 89.655198 25.745078) (xy 89.653353 25.74631)
				(xy 89.667738 25.767838) (xy 89.677405 25.816439) (xy 89.667738 25.86504) (xy 89.640208 25.906242)
				(xy 89.599006 25.933772) (xy 89.594987 25.935357) (xy 89.562362 25.947587) (xy 89.5469 25.956053)
				(xy 89.444936 26.03247) (xy 89.43247 26.044936) (xy 89.356053 26.1469) (xy 89.347586 26.162364)
				(xy 89.302478 26.282689) (xy 89.298882 26.297814) (xy 89.293365 26.348602) (xy 89.293 26.355344)
				(xy 89.293 26.827738) (xy 89.297352 26.842561) (xy 89.298326 26.843405) (xy 89.305654 26.845) (xy 90.528 26.845)
				(xy 90.576601 26.854667) (xy 90.617803 26.882197) (xy 90.645333 26.923399) (xy 90.655 26.972) (xy 90.655 27.858738)
				(xy 90.659352 27.873561) (xy 90.684825 27.895634) (xy 90.684055 27.896522) (xy 90.709842 27.92325)
				(xy 90.727983 27.969363) (xy 90.73 27.991906) (xy 90.73 29.826095) (xy 90.720333 29.874696) (xy 90.69898 29.909262)
				(xy 90.681594 29.929326) (xy 90.68 29.936654) (xy 90.68 30.528) (xy 90.670333 30.576601) (xy 90.642803 30.617803)
				(xy 90.601601 30.645333) (xy 90.553 30.655) (xy 89.485261 30.655) (xy 89.470438 30.659352) (xy 89.469594 30.660326)
				(xy 89.468 30.667654) (xy 89.468 30.695426) (xy 89.468332 30.701855) (xy 89.477884 30.793906) (xy 89.480753 30.807193)
				(xy 89.53031 30.955735) (xy 89.536431 30.9688) (xy 89.618613 31.101605) (xy 89.627568 31.112904)
				(xy 89.738089 31.223232) (xy 89.739707 31.22451) (xy 89.741639 31.226776) (xy 89.743315 31.228449)
				(xy 89.74318 31.228583) (xy 89.771858 31.262217) (xy 89.787131 31.309358) (xy 89.783202 31.358755)
				(xy 89.760669 31.402888) (xy 89.722962 31.435039) (xy 89.675821 31.450312) (xy 89.644424 31.450094)
				(xy 89.608121 31.445315) (xy 89.591882 31.445315) (xy 89.464279 31.462113) (xy 89.448592 31.466317)
				(xy 89.32969 31.515568) (xy 89.315619 31.523691) (xy 89.213527 31.602029) (xy 89.202029 31.613527)
				(xy 89.123691 31.715619) (xy 89.115568 31.72969) (xy 89.066317 31.848592) (xy 89.062113 31.864279)
				(xy 89.045315 31.991881) (xy 89.045315 32.008118) (xy 89.062113 32.13572) (xy 89.066317 32.151407)
				(xy 89.115568 32.270309) (xy 89.123691 32.28438) (xy 89.202032 32.386476) (xy 89.213518 32.397961)
				(xy 89.315625 32.476311) (xy 89.329689 32.484431) (xy 89.448592 32.533682) (xy 89.464279 32.537886)
				(xy 89.591882 32.554685) (xy 89.608118 32.554685) (xy 89.73572 32.537886) (xy 89.751407 32.533682)
				(xy 89.754444 32.532425) (xy 89.803044 32.522755) (xy 89.851646 32.53242) (xy 89.892849 32.559948)
				(xy 89.892854 32.559952) (xy 89.929405 32.596503) (xy 89.94513 32.607928) (xy 90.040774 32.656661)
				(xy 90.059258 32.662666) (xy 90.138582 32.67523) (xy 90.148355 32.675999) (xy 90.601641 32.675999)
				(xy 90.611408 32.67523) (xy 90.690747 32.662665) (xy 90.709224 32.656661) (xy 90.804869 32.607928)
				(xy 90.820594 32.596503) (xy 90.896503 32.520594) (xy 90.907928 32.504869) (xy 90.956661 32.409225)
				(xy 90.962666 32.390741) (xy 90.97523 32.311417) (xy 90.975999 32.301644) (xy 90.975999 31.698358)
				(xy 90.97523 31.688591) (xy 90.962665 31.609252) (xy 90.956661 31.590775) (xy 90.904574 31.488547)
				(xy 90.891123 31.440855) (xy 90.896948 31.391645) (xy 90.921161 31.348411) (xy 90.960076 31.317733)
				(xy 90.963859 31.315884) (xy 90.968799 31.313569) (xy 91.101605 31.231386) (xy 91.112904 31.222431)
				(xy 91.223238 31.111904) (xy 91.232175 31.100588) (xy 91.288226 31.009658) (xy 91.321958 30.973359)
				(xy 91.367013 30.952731) (xy 91.416533 30.950915) (xy 91.462978 30.968188) (xy 91.471791 30.974143)
				(xy 91.540899 31.025187) (xy 91.554427 31.03235) (xy 91.592855 31.063635) (xy 91.616386 31.107244)
				(xy 91.622 31.144588) (xy 91.622 31.308296) (xy 91.612333 31.356897) (xy 91.584803 31.398099) (xy 91.503496 31.479405)
				(xy 91.492071 31.49513) (xy 91.443338 31.590774) (xy 91.437333 31.609258) (xy 91.424769 31.688582)
				(xy 91.424 31.698355) (xy 91.424 32.301641) (xy 91.424769 32.311408) (xy 91.437334 32.390747) (xy 91.443338 32.409224)
				(xy 91.492071 32.504869) (xy 91.503496 32.520594) (xy 91.579405 32.596503) (xy 91.59513 32.607928)
				(xy 91.690774 32.656661) (xy 91.709258 32.662666) (xy 91.788582 32.67523) (xy 91.798355 32.675999)
				(xy 92.251641 32.675999) (xy 92.261408 32.67523) (xy 92.340747 32.662665) (xy 92.359224 32.656661)
				(xy 92.454869 32.607928) (xy 92.470594 32.596503) (xy 92.546503 32.520594) (xy 92.557928 32.504869)
				(xy 92.606661 32.409225) (xy 92.612666 32.390741) (xy 92.62523 32.311417) (xy 92.625999 32.301644)
				(xy 92.625999 31.698358) (xy 92.62523 31.688591) (xy 92.612665 31.609252) (xy 92.606661 31.590775)
				(xy 92.557928 31.49513) (xy 92.546503 31.479405) (xy 92.470594 31.403496) (xy 92.454869 31.392071)
				(xy 92.350319 31.338801) (xy 92.351105 31.337257) (xy 92.322509 31.321238) (xy 92.291836 31.28232)
				(xy 92.27839 31.234626) (xy 92.278 31.224676) (xy 92.278 31.191754) (xy 92.287667 31.143153) (xy 92.315197 31.101951)
				(xy 92.359173 31.057975) (xy 92.389548 31.03554) (xy 92.409101 31.025186) (xy 92.481518 30.971699)
				(xy 92.526355 30.950601) (xy 92.556972 30.946855) (xy 92.569236 30.946855) (xy 92.696838 30.930056)
				(xy 92.712525 30.925852) (xy 92.810829 30.885134) (xy 92.85943 30.875467) (xy 92.908031 30.885134)
				(xy 92.949232 30.912664) (xy 92.976763 30.953866) (xy 92.98643 31.002467) (xy 92.976763 31.051068)
				(xy 92.963463 31.075311) (xy 92.949613 31.09509) (xy 92.938957 31.117941) (xy 92.930325 31.141658)
				(xy 92.925629 31.168291) (xy 92.923232 31.179105) (xy 92.916237 31.205209) (xy 92.91436 31.226666)
				(xy 92.916716 31.253593) (xy 92.917199 31.264661) (xy 92.917199 33.484235) (xy 92.916716 33.495304)
				(xy 92.91436 33.522227) (xy 92.916237 33.543684) (xy 92.923232 33.569789) (xy 92.925629 33.580603)
				(xy 92.930325 33.607235) (xy 92.938957 33.630952) (xy 92.949615 33.653807) (xy 92.96513 33.675965)
				(xy 92.971082 33.685308) (xy 92.984591 33.708706) (xy 92.99844 33.725211) (xy 93.019149 33.742588)
				(xy 93.027318 33.750072) (xy 93.394803 34.117557) (xy 93.422333 34.158759) (xy 93.432 34.20736)
				(xy 93.432 34.329303) (xy 93.422333 34.377904) (xy 93.394803 34.419106) (xy 93.353601 34.446636)
				(xy 93.305 34.456303) (xy 93.256399 34.446636) (xy 93.215197 34.419106) (xy 93.187667 34.377904)
				(xy 93.14988 34.28668) (xy 93.141757 34.272609) (xy 93.063416 34.170513) (xy 93.05193 34.159028)
				(xy 92.949823 34.080678) (xy 92.935759 34.072558) (xy 92.816856 34.023307) (xy 92.801169 34.019103)
				(xy 92.673567 34.002305) (xy 92.657331 34.002305) (xy 92.529728 34.019103) (xy 92.514041 34.023307)
				(xy 92.395139 34.072558) (xy 92.381068 34.080681) (xy 92.278976 34.159019) (xy 92.267478 34.170517)
				(xy 92.18914 34.272609) (xy 92.181017 34.28668) (xy 92.131766 34.405582) (xy 92.127562 34.421269)
				(xy 92.110764 34.548871) (xy 92.110764 34.565115) (xy 92.11221 34.576099) (xy 92.10897 34.625546)
				(xy 92.087053 34.669989) (xy 92.0761 34.682479) (xy 91.721652 35.036927) (xy 91.713483 35.044412)
				(xy 91.68426 35.068933) (xy 91.683174 35.067639) (xy 91.658886 35.087132) (xy 91.633097 35.095634)
				(xy 91.633296 35.096245) (xy 91.605308 35.105338) (xy 91.509663 35.154071) (xy 91.493938 35.165496)
				(xy 91.418029 35.241405) (xy 91.406604 35.25713) (xy 91.357871 35.352774) (xy 91.351866 35.371258)
				(xy 91.339302 35.450582) (xy 91.338533 35.460355) (xy 91.338533 36.063641) (xy 91.339302 36.073408)
				(xy 91.351867 36.152747) (xy 91.357871 36.171224) (xy 91.409124 36.271815) (xy 91.422575 36.319508)
				(xy 91.41675 36.368717) (xy 91.392537 36.411952) (xy 91.361756 36.438102) (xy 91.304718 36.472646)
				(xy 91.292949 36.481874) (xy 91.182594 36.592229) (xy 91.173366 36.603997) (xy 91.092527 36.737477)
				(xy 91.086374 36.751106) (xy 91.039431 36.900902) (xy 91.036842 36.913832) (xy 91.030983 36.977591)
				(xy 91.03072 36.983333) (xy 91.03072 37.039738) (xy 91.035072 37.054561) (xy 91.036046 37.055405)
				(xy 91.043374 37.057) (xy 92.06572 37.057) (xy 92.114321 37.066667) (xy 92.155523 37.094197) (xy 92.183053 37.135399)
				(xy 92.19272 37.184) (xy 92.19272 37.758738) (xy 92.197072 37.773561) (xy 92.207161 37.782303) (xy 92.225429 37.790255)
				(xy 92.259838 37.825913) (xy 92.277982 37.872025) (xy 92.28 37.894577) (xy 92.28 38.928) (xy 92.270333 38.976601)
				(xy 92.242803 39.017803) (xy 92.201601 39.045333) (xy 92.153 39.055) (xy 91.085261 39.055) (xy 91.070438 39.059352)
				(xy 91.069594 39.060326) (xy 91.068 39.067654) (xy 91.068 39.095426) (xy 91.068332 39.101855) (xy 91.077884 39.193906)
				(xy 91.080753 39.207193) (xy 91.13031 39.355735) (xy 91.136431 39.3688) (xy 91.213614 39.493527)
				(xy 91.230968 39.539942) (xy 91.229238 39.589464) (xy 91.208689 39.634556) (xy 91.172448 39.668351)
				(xy 91.126033 39.685705) (xy 91.076511 39.683975) (xy 91.031419 39.663426) (xy 91.004863 39.637668)
				(xy 90.947967 39.563519) (xy 90.936481 39.552034) (xy 90.834374 39.473684) (xy 90.82031 39.465564)
				(xy 90.701407 39.416313) (xy 90.68572 39.412109) (xy 90.558118 39.395311) (xy 90.541882 39.395311)
				(xy 90.414279 39.412109) (xy 90.398592 39.416313) (xy 90.27969 39.465564) (xy 90.265619 39.473687)
				(xy 90.163527 39.552025) (xy 90.152029 39.563523) (xy 90.073691 39.665615) (xy 90.065568 39.679686)
				(xy 90.016317 39.798588) (xy 90.012113 39.814275) (xy 90.000264 39.904288) (xy 89.984336 39.951211)
				(xy 89.951663 39.988467) (xy 89.907221 40.010384) (xy 89.857774 40.013626) (xy 89.810851 39.997698)
				(xy 89.806677 39.99518) (xy 89.798239 39.989866) (xy 89.649037 39.917739) (xy 89.635938 39.913126)
				(xy 89.472781 39.875458) (xy 89.462948 39.87405) (xy 89.461192 39.874) (xy 88.888812 39.874) (xy 88.887051 39.874051)
				(xy 88.877218 39.875458) (xy 88.714061 39.913126) (xy 88.700962 39.917739) (xy 88.55176 39.989866)
				(xy 88.540003 39.99727) (xy 88.410488 40.10066) (xy 88.40066 40.110488) (xy 88.29727 40.240003)
				(xy 88.289866 40.25176) (xy 88.217739 40.400962) (xy 88.213126 40.414061) (xy 88.175458 40.577218)
				(xy 88.174051 40.587047) (xy 88.174 40.588807) (xy 87.799 40.588807) (xy 87.799 37.579654) (xy 91.030721 37.579654)
				(xy 91.030721 37.64068) (xy 91.030983 37.646405) (xy 91.03684 37.710159) (xy 91.039432 37.723099)
				(xy 91.086374 37.872893) (xy 91.092527 37.886522) (xy 91.177333 38.026552) (xy 91.176204 38.027235)
				(xy 91.193423 38.06124) (xy 91.197187 38.11065) (xy 91.181756 38.157739) (xy 91.179216 38.162047)
				(xy 91.135877 38.232355) (xy 91.12978 38.24543) (xy 91.080473 38.394082) (xy 91.077631 38.407338)
				(xy 91.068325 38.498171) (xy 91.068 38.504544) (xy 91.068 38.527738) (xy 91.072352 38.542561) (xy 91.073326 38.543405)
				(xy 91.080654 38.545) (xy 91.752739 38.545) (xy 91.767561 38.540647) (xy 91.768405 38.539673) (xy 91.77 38.532345)
				(xy 91.77 38.353261) (xy 91.765647 38.338438) (xy 91.755558 38.329696) (xy 91.737291 38.321745)
				(xy 91.702882 38.286087) (xy 91.684738 38.239975) (xy 91.68272 38.217423) (xy 91.68272 37.584261)
				(xy 91.678367 37.569438) (xy 91.677393 37.568594) (xy 91.670066 37.567) (xy 91.047982 37.567) (xy 91.033159 37.571352)
				(xy 91.032315 37.572326) (xy 91.030721 37.579654) (xy 87.799 37.579654) (xy 87.799 30.104544) (xy 89.468 30.104544)
				(xy 89.468 30.127738) (xy 89.472352 30.142561) (xy 89.473326 30.143405) (xy 89.480654 30.145) (xy 90.152739 30.145)
				(xy 90.167561 30.140647) (xy 90.168405 30.139673) (xy 90.17 30.132345) (xy 90.17 29.473905) (xy 90.179667 29.425304)
				(xy 90.20102 29.390738) (xy 90.218405 29.370673) (xy 90.22 29.363345) (xy 90.22 29.172261) (xy 90.215647 29.157438)
				(xy 90.214673 29.156594) (xy 90.207346 29.155) (xy 89.535261 29.155) (xy 89.520438 29.159352) (xy 89.519594 29.160326)
				(xy 89.518 29.167654) (xy 89.518 29.195426) (xy 89.518332 29.201855) (xy 89.527884 29.293906) (xy 89.530753 29.307193)
				(xy 89.58031 29.455735) (xy 89.586431 29.4688) (xy 89.633398 29.544698) (xy 89.650751 29.591112)
				(xy 89.649022 29.640635) (xy 89.628473 29.685726) (xy 89.625068 29.690241) (xy 89.617819 29.699419)
				(xy 89.535879 29.832351) (xy 89.52978 29.84543) (xy 89.480473 29.994082) (xy 89.477631 30.007338)
				(xy 89.468325 30.098171) (xy 89.468 30.104544) (xy 87.799 30.104544) (xy 87.799 27.367654) (xy 89.293001 27.367654)
				(xy 89.293001 27.844651) (xy 89.293366 27.851402) (xy 89.298881 27.90218) (xy 89.30248 27.917314)
				(xy 89.347586 28.037635) (xy 89.356053 28.053099) (xy 89.43247 28.155063) (xy 89.44494 28.167533)
				(xy 89.527032 28.229058) (xy 89.560125 28.265941) (xy 89.576584 28.31268) (xy 89.573904 28.362161)
				(xy 89.571409 28.370666) (xy 89.530474 28.494079) (xy 89.527631 28.507338) (xy 89.518325 28.598171)
				(xy 89.518 28.604544) (xy 89.518 28.627738) (xy 89.522352 28.642561) (xy 89.523326 28.643405) (xy 89.530654 28.645)
				(xy 90.202739 28.645) (xy 90.217561 28.640647) (xy 90.218405 28.639673) (xy 90.22 28.632345) (xy 90.22 28.366261)
				(xy 90.215647 28.351438) (xy 90.190175 28.329366) (xy 90.190944 28.328477) (xy 90.165158 28.30175)
				(xy 90.147017 28.255637) (xy 90.145 28.233094) (xy 90.145 27.372261) (xy 90.140647 27.357438) (xy 90.139673 27.356594)
				(xy 90.132346 27.355) (xy 89.310262 27.355) (xy 89.295439 27.359352) (xy 89.294595 27.360326) (xy 89.293001 27.367654)
				(xy 87.799 27.367654) (xy 87.799 23.835033) (xy 87.808667 23.786432) (xy 87.836197 23.74523) (xy 91.515204 20.066223)
				(xy 91.525946 20.056636) (xy 91.535855 20.048753) (xy 91.544157 20.038373) (xy 91.54996 20.031886)
				(xy 91.562321 20.01639) (xy 91.566981 20.009832) (xy 91.574979 19.99983) (xy 91.58083 19.987727)
				(xy 91.585466 19.979355) (xy 91.587126 19.975358) (xy 91.589782 19.966172) (xy 91.594226 19.95348)
				(xy 91.595665 19.940755) (xy 91.597071 19.932523) (xy 91.599321 19.912702) (xy 91.599804 19.904143)
				(xy 91.601263 19.891235) (xy 91.599791 19.878111) (xy 91.599 19.863954) (xy 91.599 16.405876) (xy 91.599032 16.368852)
				(xy 91.591798 16.337011) (xy 91.589314 16.329898) (xy 91.572735 16.29545) (xy 91.5693 16.289974)
				(xy 91.54551 16.260067) (xy 91.540189 16.254737) (xy 91.510308 16.230883) (xy 91.50485 16.227446)
				(xy 91.470436 16.210809) (xy 91.463328 16.208313) (xy 91.426059 16.199777) (xy 91.419647 16.199046)
				(xy 91.37901 16.198976) (xy 91.369267 16.198584) (xy 91.190799 16.184538) (xy 91.171115 16.181421)
				(xy 90.976783 16.134766) (xy 90.95783 16.128608) (xy 90.773193 16.052129) (xy 90.755436 16.043081)
				(xy 90.585033 15.938657) (xy 90.568911 15.926943) (xy 90.416942 15.797149) (xy 90.402851 15.783058)
				(xy 90.273057 15.631089) (xy 90.261343 15.614967) (xy 90.156919 15.444564) (xy 90.147871 15.426807)
				(xy 90.071392 15.24217) (xy 90.065234 15.223217) (xy 90.018579 15.028885) (xy 90.015461 15.009201)
				(xy 89.999781 14.809964) (xy 89.999781 14.790036) (xy 90.015461 14.590799) (xy 90.018579 14.571115)
				(xy 90.065234 14.376783) (xy 90.071392 14.35783) (xy 90.147871 14.173193) (xy 90.156919 14.155436)
				(xy 90.261343 13.985033) (xy 90.273057 13.968911) (xy 90.402851 13.816942) (xy 90.416942 13.802851)
				(xy 90.568911 13.673057) (xy 90.585033 13.661343) (xy 90.755436 13.556919) (xy 90.773193 13.547871)
				(xy 90.95783 13.471392) (xy 90.976783 13.465234) (xy 91.171115 13.418579) (xy 91.190798 13.415462)
				(xy 91.370179 13.401344) (xy 91.38037 13.400952) (xy 91.41837 13.401019) (xy 91.436448 13.399) (xy 99.162976 13.399)
			)
		)
	)
	(zone
		(net 2)
		(net_name "GND")
		(layer "B.Cu")
		(uuid "00000000-0000-0000-0000-00005c07d701")
		(hatch edge 0.508)
		(connect_pads
			(clearance 0.2)
		)
		(min_thickness 0.254)
		(filled_areas_thickness no)
		(fill yes
			(thermal_gap 0.508)
			(thermal_bridge_width 0.508)
		)
		(polygon
			(pts
				(xy 87.8 13.2) (xy 102.8 13.2) (xy 102.8 43.2) (xy 87.8 43.2)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 93.537545 13.408667) (xy 93.578747 13.436197) (xy 93.606277 13.477399) (xy 93.615944 13.526)
				(xy 93.606277 13.574601) (xy 93.578747 13.615803) (xy 93.576204 13.618275) (xy 93.520985 13.670492)
				(xy 93.51275 13.680032) (xy 93.409202 13.827914) (xy 93.403056 13.83891) (xy 93.331355 14.004602)
				(xy 93.327545 14.016614) (xy 93.290308 14.194857) (xy 93.289081 14.204303) (xy 93.289 14.207415)
				(xy 93.289 15.34458) (xy 93.289318 15.350875) (xy 93.302979 15.48536) (xy 93.305512 15.497701) (xy 93.3595 15.669979)
				(xy 93.364465 15.681561) (xy 93.451993 15.839467) (xy 93.459177 15.849804) (xy 93.576665 15.986878)
				(xy 93.585793 15.995571) (xy 93.728451 16.106227) (xy 93.739127 16.112899) (xy 93.901117 16.192608)
				(xy 93.912922 16.196998) (xy 94.087632 16.242507) (xy 94.100082 16.244434) (xy 94.280377 16.253883)
				(xy 94.293597 16.253237) (xy 94.342612 16.260518) (xy 94.38511 16.286003) (xy 94.41462 16.325811)
				(xy 94.426649 16.373881) (xy 94.426801 16.380085) (xy 94.426801 20.948898) (xy 94.417134 20.997499)
				(xy 94.389604 21.038701) (xy 94.348402 21.066231) (xy 94.299801 21.075898) (xy 94.259602 21.067902)
				(xy 94.25945 21.068472) (xy 94.23572 21.062113) (xy 94.108118 21.045315) (xy 94.091882 21.045315)
				(xy 93.964279 21.062113) (xy 93.948592 21.066317) (xy 93.82969 21.115568) (xy 93.815619 21.123691)
				(xy 93.713527 21.202029) (xy 93.702029 21.213527) (xy 93.623691 21.315619) (xy 93.615568 21.32969)
				(xy 93.566317 21.448592) (xy 93.562113 21.464279) (xy 93.545315 21.591881) (xy 93.545315 21.608118)
				(xy 93.562113 21.73572) (xy 93.566317 21.751407) (xy 93.615568 21.870309) (xy 93.627852 21.891586)
				(xy 93.626375 21.892438) (xy 93.641996 21.924112) (xy 93.645238 21.973559) (xy 93.629311 22.020482)
				(xy 93.596639 22.057739) (xy 93.552197 22.079657) (xy 93.50275 22.082899) (xy 93.470723 22.074317)
				(xy 93.451413 22.066318) (xy 93.43572 22.062113) (xy 93.308118 22.045315) (xy 93.291882 22.045315)
				(xy 93.164279 22.062113) (xy 93.148592 22.066317) (xy 93.02969 22.115568) (xy 93.015619 22.123691)
				(xy 92.913527 22.202029) (xy 92.902029 22.213527) (xy 92.823691 22.315619) (xy 92.815568 22.32969)
				(xy 92.766317 22.448592) (xy 92.762113 22.464279) (xy 92.745315 22.591881) (xy 92.745315 22.608118)
				(xy 92.762113 22.73572) (xy 92.766317 22.751407) (xy 92.815568 22.870309) (xy 92.823691 22.88438)
				(xy 92.902032 22.986476) (xy 92.913518 22.997961) (xy 93.015625 23.076311) (xy 93.029689 23.084431)
				(xy 93.148592 23.133682) (xy 93.164279 23.137886) (xy 93.291882 23.154685) (xy 93.308118 23.154685)
				(xy 93.43572 23.137886) (xy 93.451407 23.133682) (xy 93.57031 23.084431) (xy 93.584374 23.076311)
				(xy 93.686481 22.997961) (xy 93.697967 22.986476) (xy 93.776308 22.88438) (xy 93.784431 22.870309)
				(xy 93.833682 22.751407) (xy 93.837886 22.73572) (xy 93.854685 22.608118) (xy 93.854685 22.591881)
				(xy 93.837886 22.464279) (xy 93.833682 22.448592) (xy 93.784431 22.32969) (xy 93.772148 22.308414)
				(xy 93.773624 22.307561) (xy 93.758004 22.275888) (xy 93.754762 22.226441) (xy 93.770689 22.179518)
				(xy 93.803361 22.142261) (xy 93.847803 22.120343) (xy 93.89725 22.117101) (xy 93.929277 22.125683)
				(xy 93.948586 22.133681) (xy 93.964279 22.137886) (xy 94.091882 22.154685) (xy 94.108118 22.154685)
				(xy 94.23572 22.137886) (xy 94.25945 22.131528) (xy 94.259602 22.132097) (xy 94.299803 22.124102)
				(xy 94.348404 22.13377) (xy 94.389605 22.161301) (xy 94.417135 22.202503) (xy 94.426801 22.251102)
				(xy 94.426801 23.234943) (xy 94.426318 23.246012) (xy 94.423962 23.272935) (xy 94.425839 23.294392)
				(xy 94.432834 23.320497) (xy 94.435231 23.331311) (xy 94.439927 23.357943) (xy 94.448559 23.38166)
				(xy 94.459217 23.404515) (xy 94.474732 23.426673) (xy 94.480684 23.436016) (xy 94.494193 23.459414)
				(xy 94.508042 23.475919) (xy 94.528751 23.493296) (xy 94.53692 23.50078) (xy 95.43596 24.39982)
				(xy 95.46349 24.441022) (xy 95.473157 24.489623) (xy 95.46349 24.538224) (xy 95.43596 24.579426)
				(xy 95.394758 24.606956) (xy 95.385613 24.610339) (xy 95.231918 24.660573) (xy 95.222506 24.664529)
				(xy 95.031687 24.763863) (xy 95.023037 24.76931) (xy 95.000675 24.7861) (xy 94.992693 24.796794)
				(xy 94.999058 24.808432) (xy 96.88083 26.690204) (xy 96.894384 26.697605) (xy 96.899422 26.697245)
				(xy 96.920396 26.686959) (xy 96.96985 26.683826) (xy 97.016739 26.699856) (xy 97.053924 26.732609)
				(xy 97.070333 26.761102) (xy 97.115568 26.870309) (xy 97.123691 26.88438) (xy 97.202032 26.986476)
				(xy 97.213518 26.997961) (xy 97.315625 27.076311) (xy 97.329689 27.084431) (xy 97.448592 27.133682)
				(xy 97.464279 27.137886) (xy 97.591882 27.154685) (xy 97.608118 27.154685) (xy 97.73572 27.137886)
				(xy 97.751407 27.133682) (xy 97.87031 27.084431) (xy 97.884378 27.076309) (xy 97.982868 27.000736)
				(xy 98.02731 26.978819) (xy 98.075217 26.975385) (xy 98.271531 26.998793) (xy 98.283687 26.999047)
				(xy 98.479665 26.983968) (xy 98.491643 26.981856) (xy 98.68096 26.928997) (xy 98.692317 26.924592)
				(xy 98.867739 26.83598) (xy 98.878021 26.829455) (xy 99.032904 26.708446) (xy 99.041711 26.70006)
				(xy 99.120461 26.608828) (xy 99.159536 26.578355) (xy 99.207298 26.565154) (xy 99.256476 26.571236)
				(xy 99.299584 26.595675) (xy 99.330057 26.63475) (xy 99.343258 26.682512) (xy 99.343599 26.691813)
				(xy 99.343599 26.909935) (xy 99.333932 26.958536) (xy 99.306402 26.999738) (xy 98.850302 27.455838)
				(xy 98.8091 27.483368) (xy 98.760499 27.493035) (xy 98.711898 27.483368) (xy 98.711286 27.483112)
				(xy 98.708702 27.482026) (xy 98.520951 27.423908) (xy 98.509015 27.421457) (xy 98.313553 27.400913)
				(xy 98.301377 27.400829) (xy 98.105634 27.418643) (xy 98.093685 27.420922) (xy 97.905129 27.476416)
				(xy 97.89385 27.480974) (xy 97.719662 27.572037) (xy 97.709476 27.578703) (xy 97.5563 27.701859)
				(xy 97.547602 27.710377) (xy 97.421261 27.860945) (xy 97.414392 27.870977) (xy 97.319699 28.043221)
				(xy 97.314902 28.054413) (xy 97.255474 28.241759) (xy 97.252943 28.253666) (xy 97.231034 28.448978)
				(xy 97.230864 28.461154) (xy 97.247312 28.657018) (xy 97.249509 28.668993) (xy 97.303685 28.857928)
				(xy 97.308161 28.869231) (xy 97.39801 29.044058) (xy 97.404604 29.054289) (xy 97.418783 29.072179)
				(xy 97.441396 29.116271) (xy 97.445413 29.165661) (xy 97.430225 29.212829) (xy 97.429239 29.214568)
				(xy 97.420377 29.229915) (xy 97.371128 29.348813) (xy 97.366924 29.3645) (xy 97.350126 29.492102)
				(xy 97.350126 29.508339) (xy 97.366924 29.635941) (xy 97.371128 29.651628) (xy 97.420379 29.77053)
				(xy 97.428502 29.784601) (xy 97.506843 29.886697) (xy 97.518329 29.898182) (xy 97.620436 29.976532)
				(xy 97.6345 29.984652) (xy 97.753403 30.033903) (xy 97.76909 30.038107) (xy 97.896693 30.054906)
				(xy 97.912929 30.054906) (xy 98.040531 30.038107) (xy 98.056218 30.033903) (xy 98.175121 29.984652)
				(xy 98.189185 29.976532) (xy 98.291292 29.898182) (xy 98.302778 29.886697) (xy 98.381119 29.784601)
				(xy 98.389242 29.77053) (xy 98.438493 29.651628) (xy 98.442699 29.635934) (xy 98.445472 29.614872)
				(xy 98.4614 29.567949) (xy 98.494073 29.530693) (xy 98.537232 29.509127) (xy 98.68096 29.468997)
				(xy 98.692317 29.464592) (xy 98.867739 29.37598) (xy 98.878021 29.369455) (xy 99.032904 29.248446)
				(xy 99.041711 29.24006) (xy 99.170145 29.091267) (xy 99.177161 29.081323) (xy 99.274249 28.910416)
				(xy 99.279195 28.899308) (xy 99.341236 28.712805) (xy 99.343931 28.700942) (xy 99.368903 28.503274)
				(xy 99.369388 28.496343) (xy 99.369706 28.473533) (xy 99.369414 28.466561) (xy 99.349975 28.268303)
				(xy 99.34761 28.256362) (xy 99.290802 28.068207) (xy 99.286551 28.057892) (xy 99.276969 28.009274)
				(xy 99.286722 27.960691) (xy 99.314166 27.919694) (xy 99.558597 27.675263) (xy 99.599799 27.647733)
				(xy 99.6484 27.638066) (xy 99.697001 27.647733) (xy 99.738203 27.675263) (xy 99.765733 27.716465)
				(xy 99.7754 27.765066) (xy 99.7754 29.347256) (xy 99.776598 29.359411) (xy 99.784661 29.399952)
				(xy 99.7939 29.422258) (xy 99.824647 29.468275) (xy 99.841724 29.485352) (xy 99.887744 29.516101)
				(xy 99.910042 29.525337) (xy 99.95058 29.533401) (xy 99.962751 29.534599) (xy 101.717256 29.534599)
				(xy 101.729411 29.533401) (xy 101.769952 29.525338) (xy 101.792258 29.516099) (xy 101.838275 29.485352)
				(xy 101.855352 29.468275) (xy 101.886101 29.422255) (xy 101.895337 29.399957) (xy 101.903401 29.359419)
				(xy 101.904599 29.347248) (xy 101.904599 27.592743) (xy 101.903401 27.580588) (xy 101.895338 27.540047)
				(xy 101.886099 27.517741) (xy 101.855352 27.471724) (xy 101.838275 27.454647) (xy 101.792255 27.423898)
				(xy 101.769957 27.414662) (xy 101.729419 27.406598) (xy 101.717248 27.4054) (xy 100.093188 27.405401)
				(xy 100.044587 27.395734) (xy 100.003385 27.368204) (xy 99.975855 27.327002) (xy 99.966188 27.278401)
				(xy 99.973936 27.239442) (xy 99.973415 27.239303) (xy 99.973903 27.237483) (xy 99.97505 27.233846)
				(xy 99.975372 27.232225) (xy 99.975312 27.232203) (xy 99.975455 27.231809) (xy 99.975855 27.2298)
				(xy 99.977777 27.22543) (xy 99.98647 27.201547) (xy 99.991167 27.174915) (xy 99.993565 27.1641)
				(xy 100.00056 27.137992) (xy 100.002437 27.116534) (xy 100.000082 27.089608) (xy 99.999599 27.07854)
				(xy 99.999599 26.887635) (xy 100.009266 26.839034) (xy 100.036796 26.797832) (xy 100.077998 26.770302)
				(xy 100.126599 26.760635) (xy 100.1752 26.770302) (xy 100.208911 26.790921) (xy 100.224823 26.804464)
				(xy 100.234811 26.811405) (xy 100.406384 26.907294) (xy 100.417545 26.91217) (xy 100.60447 26.972906)
				(xy 100.616356 26.97552) (xy 100.811531 26.998793) (xy 100.823687 26.999047) (xy 101.019665 26.983968)
				(xy 101.031643 26.981856) (xy 101.22096 26.928997) (xy 101.232317 26.924592) (xy 101.407739 26.83598)
				(xy 101.418021 26.829455) (xy 101.572904 26.708446) (xy 101.581711 26.70006) (xy 101.710145 26.551267)
				(xy 101.717161 26.541323) (xy 101.814249 26.370416) (xy 101.819195 26.359308) (xy 101.881236 26.172805)
				(xy 101.883931 26.160942) (xy 101.908903 25.963274) (xy 101.909388 25.956343) (xy 101.909706 25.933533)
				(xy 101.909414 25.926561) (xy 101.889975 25.728303) (xy 101.88761 25.716362) (xy 101.830802 25.528206)
				(xy 101.826162 25.51695) (xy 101.733889 25.343409) (xy 101.727153 25.33327) (xy 101.602925 25.180951)
				(xy 101.594353 25.172318) (xy 101.442907 25.047032) (xy 101.432821 25.040228) (xy 101.259927 24.946744)
				(xy 101.248698 24.942024) (xy 101.060951 24.883908) (xy 101.049015 24.881457) (xy 100.853553 24.860913)
				(xy 100.841377 24.860829) (xy 100.645634 24.878643) (xy 100.633685 24.880922) (xy 100.445129 24.936416)
				(xy 100.43385 24.940974) (xy 100.259662 25.032037) (xy 100.249476 25.038703) (xy 100.0963 25.161859)
				(xy 100.087597 25.170382) (xy 100.015465 25.256346) (xy 99.97682 25.287363) (xy 99.929247 25.301229)
				(xy 99.879988 25.295834) (xy 99.836544 25.271999) (xy 99.828375 25.264515) (xy 97.127894 22.564034)
				(xy 97.100364 22.522832) (xy 97.090697 22.474231) (xy 97.090697 16.287072) (xy 97.100364 16.238471)
				(xy 97.127894 16.197269) (xy 97.169096 16.169739) (xy 97.173843 16.167884) (xy 97.193098 16.160798)
				(xy 97.204423 16.155275) (xy 97.357854 16.060144) (xy 97.367843 16.052452) (xy 97.499014 15.928409)
				(xy 97.507249 15.918869) (xy 97.610797 15.770987) (xy 97.616943 15.759991) (xy 97.688644 15.594299)
				(xy 97.692454 15.582287) (xy 97.729691 15.404044) (xy 97.730918 15.394598) (xy 97.731 15.391486)
				(xy 97.731 14.254321) (xy 97.730681 14.248026) (xy 97.71702 14.113541) (xy 97.714487 14.1012) (xy 97.660499 13.928922)
				(xy 97.655534 13.91734) (xy 97.568006 13.759434) (xy 97.560818 13.749092) (xy 97.440443 13.608648)
				(xy 97.416155 13.565456) (xy 97.410244 13.516257) (xy 97.423612 13.468541) (xy 97.454222 13.429573)
				(xy 97.497414 13.405285) (xy 97.53687 13.399) (xy 98.568944 13.399) (xy 98.617545 13.408667) (xy 98.658747 13.436197)
				(xy 98.686277 13.477399) (xy 98.695944 13.526) (xy 98.686277 13.574601) (xy 98.658747 13.615803)
				(xy 98.656204 13.618275) (xy 98.600985 13.670492) (xy 98.59275 13.680032) (xy 98.489202 13.827914)
				(xy 98.483056 13.83891) (xy 98.411355 14.004602) (xy 98.407545 14.016614) (xy 98.370308 14.194857)
				(xy 98.369081 14.204303) (xy 98.369 14.207415) (xy 98.369 15.34458) (xy 98.369318 15.350875) (xy 98.382979 15.48536)
				(xy 98.385512 15.497701) (xy 98.4395 15.669979) (xy 98.444465 15.681561) (xy 98.531993 15.839467)
				(xy 98.539177 15.849804) (xy 98.656665 15.986878) (xy 98.665793 15.995571) (xy 98.808451 16.106227)
				(xy 98.819127 16.112899) (xy 98.946914 16.175779) (xy 98.986254 16.205911) (xy 99.011068 16.248803)
				(xy 99.017578 16.297927) (xy 99.014624 16.318141) (xy 99.014327 16.319435) (xy 99.00851 16.335961)
				(xy 99.001077 16.368155) (xy 99.001054 16.376664) (xy 99.001064 16.37671) (xy 99.001054 16.37688)
				(xy 99.001054 16.377044) (xy 99.001045 16.377043) (xy 99.001 16.377848) (xy 99.001 16.397761) (xy 99.000876 16.445103)
				(xy 98.998603 16.445097) (xy 98.99861 16.445359) (xy 99.001 16.445359) (xy 99.001 19.863924) (xy 99.000184 19.878299)
				(xy 98.99875 19.890879) (xy 99.000221 19.904089) (xy 99.000703 19.912784) (xy 99.002919 19.932461)
				(xy 99.004264 19.940412) (xy 99.00568 19.953143) (xy 99.0101 19.965835) (xy 99.012745 19.975042)
				(xy 99.014387 19.979017) (xy 99.019008 19.987396) (xy 99.024845 19.999524) (xy 99.032827 20.009541)
				(xy 99.037647 20.016349) (xy 99.050073 20.031956) (xy 99.05578 20.038345) (xy 99.063882 20.048513)
				(xy 99.074211 20.056758) (xy 99.084782 20.066209) (xy 102.763803 23.74523) (xy 102.791333 23.786432)
				(xy 102.801 23.835033) (xy 102.801 43.074) (xy 102.791333 43.122601) (xy 102.763803 43.163803) (xy 102.722601 43.191333)
				(xy 102.674 43.201) (xy 87.926 43.201) (xy 87.877399 43.191333) (xy 87.836197 43.163803) (xy 87.808667 43.122601)
				(xy 87.799 43.074) (xy 87.799 41.892751) (xy 88.545003 41.892751) (xy 88.559587 42.066417) (xy 88.561784 42.078391)
				(xy 88.609819 42.24591) (xy 88.614295 42.257213) (xy 88.693961 42.412226) (xy 88.700551 42.422451)
				(xy 88.808805 42.559034) (xy 88.817252 42.567781) (xy 88.949966 42.68073) (xy 88.959962 42.687677)
				(xy 89.112086 42.772697) (xy 89.123242 42.77757) (xy 89.288985 42.831424) (xy 89.300868 42.834037)
				(xy 89.473921 42.854672) (xy 89.486077 42.854926) (xy 89.659844 42.841556) (xy 89.671825 42.839443)
				(xy 89.839682 42.792577) (xy 89.851027 42.788177) (xy 90.006578 42.709602) (xy 90.016853 42.70308)
				(xy 90.154178 42.59579) (xy 90.162997 42.587392) (xy 90.276868 42.455472) (xy 90.283885 42.445525)
				(xy 90.369963 42.294001) (xy 90.374916 42.282876) (xy 90.429924 42.117517) (xy 90.432618 42.105656)
				(xy 90.454797 41.930095) (xy 90.455282 41.923158) (xy 90.455556 41.903536) (xy 90.455264 41.896566)
				(xy 90.437998 41.720476) (xy 90.435636 41.708545) (xy 90.413098 41.633895) (xy 90.413098 41.633894)
				(xy 90.400675 41.592747) (xy 95.545003 41.592747) (xy 95.559587 41.766413) (xy 95.561784 41.778387)
				(xy 95.609819 41.945906) (xy 95.614295 41.957209) (xy 95.693961 42.112222) (xy 95.700551 42.122447)
				(xy 95.808805 42.25903) (xy 95.817252 42.267777) (xy 95.949966 42.380726) (xy 95.959962 42.387673)
				(xy 96.112086 42.472693) (xy 96.123242 42.477566) (xy 96.288985 42.53142) (xy 96.300868 42.534033)
				(xy 96.473921 42.554668) (xy 96.486077 42.554922) (xy 96.659844 42.541552) (xy 96.671825 42.539439)
				(xy 96.839682 42.492573) (xy 96.851027 42.488173) (xy 97.006578 42.409598) (xy 97.016853 42.403076)
				(xy 97.154178 42.295786) (xy 97.162997 42.287388) (xy 97.276868 42.155468) (xy 97.283885 42.145521)
				(xy 97.369963 41.993997) (xy 97.374916 41.982872) (xy 97.429924 41.817513) (xy 97.432618 41.805652)
				(xy 97.454797 41.630091) (xy 97.455282 41.623154) (xy 97.455556 41.603532) (xy 97.455264 41.596562)
				(xy 97.437998 41.420472) (xy 97.435635 41.408534) (xy 97.385265 41.241704) (xy 97.380626 41.230451)
				(xy 97.29881 41.076578) (xy 97.292075 41.066439) (xy 97.181929 40.931386) (xy 97.173356 40.922754)
				(xy 97.039077 40.811669) (xy 97.028987 40.804863) (xy 96.875687 40.721974) (xy 96.864466 40.717257)
				(xy 96.697995 40.665725) (xy 96.686062 40.663275) (xy 96.512756 40.64506) (xy 96.50058 40.644976)
				(xy 96.327024 40.660771) (xy 96.315074 40.66305) (xy 96.14789 40.712255) (xy 96.136611 40.716812)
				(xy 95.982167 40.797553) (xy 95.971981 40.804218) (xy 95.836163 40.913419) (xy 95.827471 40.921932)
				(xy 95.715449 41.055434) (xy 95.708575 41.065473) (xy 95.624619 41.21819) (xy 95.619824 41.229377)
				(xy 95.567131 41.395485) (xy 95.564599 41.407394) (xy 95.545173 41.580583) (xy 95.545003 41.592747)
				(xy 90.400675 41.592747) (xy 90.385265 41.541708) (xy 90.380626 41.530455) (xy 90.29881 41.376582)
				(xy 90.292075 41.366443) (xy 90.181929 41.23139) (xy 90.173356 41.222758) (xy 90.039077 41.111673)
				(xy 90.028987 41.104867) (xy 89.875687 41.021978) (xy 89.864466 41.017261) (xy 89.697995 40.965729)
				(xy 89.686062 40.963279) (xy 89.512756 40.945064) (xy 89.50058 40.94498) (xy 89.327024 40.960775)
				(xy 89.315074 40.963054) (xy 89.14789 41.012259) (xy 89.136611 41.016816) (xy 88.982167 41.097557)
				(xy 88.971981 41.104222) (xy 88.836163 41.213423) (xy 88.827471 41.221936) (xy 88.715449 41.355438)
				(xy 88.708575 41.365477) (xy 88.624619 41.518194) (xy 88.619824 41.529381) (xy 88.567131 41.695489)
				(xy 88.564599 41.707398) (xy 88.545173 41.880587) (xy 88.545003 41.892751) (xy 87.799 41.892751)
				(xy 87.799 39.941877) (xy 89.995315 39.941877) (xy 89.995315 39.958114) (xy 90.012113 40.085716)
				(xy 90.016317 40.101403) (xy 90.065568 40.220305) (xy 90.073691 40.234376) (xy 90.152032 40.336472)
				(xy 90.163518 40.347957) (xy 90.265625 40.426307) (xy 90.279689 40.434427) (xy 90.398592 40.483678)
				(xy 90.414279 40.487882) (xy 90.541882 40.504681) (xy 90.558118 40.504681) (xy 90.68572 40.487882)
				(xy 90.701407 40.483678) (xy 90.82031 40.434427) (xy 90.834374 40.426307) (xy 90.936481 40.347957)
				(xy 90.947967 40.336472) (xy 91.026308 40.234376) (xy 91.034431 40.220305) (xy 91.083682 40.101403)
				(xy 91.087886 40.085716) (xy 91.104685 39.958114) (xy 91.104685 39.941877) (xy 91.087886 39.814275)
				(xy 91.083682 39.798588) (xy 91.034431 39.679686) (xy 91.026308 39.665615) (xy 90.947967 39.563519)
				(xy 90.936481 39.552034) (xy 90.834374 39.473684) (xy 90.82031 39.465564) (xy 90.701407 39.416313)
				(xy 90.68572 39.412109) (xy 90.558118 39.395311) (xy 90.541882 39.395311) (xy 90.414279 39.412109)
				(xy 90.398592 39.416313) (xy 90.27969 39.465564) (xy 90.265619 39.473687) (xy 90.163527 39.552025)
				(xy 90.152029 39.563523) (xy 90.073691 39.665615) (xy 90.065568 39.679686) (xy 90.016317 39.798588)
				(xy 90.012113 39.814275) (xy 89.995315 39.941877) (xy 87.799 39.941877) (xy 87.799 34.548871) (xy 92.110764 34.548871)
				(xy 92.110764 34.565108) (xy 92.127562 34.69271) (xy 92.131766 34.708397) (xy 92.181017 34.827299)
				(xy 92.18914 34.84137) (xy 92.267481 34.943466) (xy 92.278967 34.954951) (xy 92.381074 35.033301)
				(xy 92.395138 35.041421) (xy 92.514041 35.090672) (xy 92.529728 35.094876) (xy 92.657331 35.111675)
				(xy 92.673567 35.111675) (xy 92.801169 35.094876) (xy 92.816856 35.090672) (xy 92.935759 35.041421)
				(xy 92.949823 35.033301) (xy 93.05193 34.954951) (xy 93.063416 34.943466) (xy 93.141757 34.84137)
				(xy 93.14988 34.827299) (xy 93.199131 34.708397) (xy 93.203335 34.69271) (xy 93.220134 34.565108)
				(xy 93.220134 34.548871) (xy 93.203335 34.421269) (xy 93.199131 34.405582) (xy 93.168717 34.332155)
				(xy 93.152034 34.291881) (xy 95.585935 34.291881) (xy 95.585935 34.308118) (xy 95.602733 34.43572)
				(xy 95.606937 34.451407) (xy 95.656188 34.570309) (xy 95.664311 34.58438) (xy 95.742652 34.686476)
				(xy 95.754138 34.697961) (xy 95.856245 34.776311) (xy 95.870309 34.784431) (xy 95.989212 34.833682)
				(xy 96.004899 34.837886) (xy 96.132502 34.854685) (xy 96.148738 34.854685) (xy 96.27634 34.837886)
				(xy 96.292027 34.833682) (xy 96.41093 34.784431) (xy 96.424994 34.776311) (xy 96.527101 34.697961)
				(xy 96.533182 34.691881) (xy 98.845315 34.691881) (xy 98.845315 34.708118) (xy 98.862113 34.83572)
				(xy 98.866317 34.851407) (xy 98.915568 34.970309) (xy 98.923691 34.98438) (xy 99.002032 35.086476)
				(xy 99.013518 35.097961) (xy 99.115625 35.176311) (xy 99.129689 35.184431) (xy 99.248592 35.233682)
				(xy 99.264279 35.237886) (xy 99.391882 35.254685) (xy 99.408118 35.254685) (xy 99.53572 35.237886)
				(xy 99.551407 35.233682) (xy 99.67031 35.184431) (xy 99.684374 35.176311) (xy 99.786481 35.097961)
				(xy 99.797967 35.086476) (xy 99.876308 34.98438) (xy 99.884431 34.970309) (xy 99.933682 34.851407)
				(xy 99.937886 34.83572) (xy 99.954685 34.708118) (xy 99.954685 34.691881) (xy 99.937886 34.564279)
				(xy 99.933682 34.548592) (xy 99.884431 34.42969) (xy 99.876308 34.415619) (xy 99.797967 34.313523)
				(xy 99.786481 34.302038) (xy 99.684374 34.223688) (xy 99.67031 34.215568) (xy 99.551407 34.166317)
				(xy 99.53572 34.162113) (xy 99.408118 34.145315) (xy 99.391882 34.145315) (xy 99.264279 34.162113)
				(xy 99.248592 34.166317) (xy 99.12969 34.215568) (xy 99.115619 34.223691) (xy 99.013527 34.302029)
				(xy 99.002029 34.313527) (xy 98.923691 34.415619) (xy 98.915568 34.42969) (xy 98.866317 34.548592)
				(xy 98.862113 34.564279) (xy 98.845315 34.691881) (xy 96.533182 34.691881) (xy 96.538587 34.686476)
				(xy 96.600153 34.606242) (xy 96.600153 34.606241) (xy 96.61693 34.584375) (xy 96.625051 34.570309)
				(xy 96.674302 34.451407) (xy 96.678506 34.43572) (xy 96.695305 34.308118) (xy 96.695305 34.291881)
				(xy 96.678506 34.164279) (xy 96.674302 34.148592) (xy 96.625051 34.02969) (xy 96.616928 34.015619)
				(xy 96.538587 33.913523) (xy 96.527101 33.902038) (xy 96.424994 33.823688) (xy 96.41093 33.815568)
				(xy 96.292027 33.766317) (xy 96.27634 33.762113) (xy 96.148738 33.745315) (xy 96.132502 33.745315)
				(xy 96.004899 33.762113) (xy 95.989212 33.766317) (xy 95.87031 33.815568) (xy 95.856239 33.823691)
				(xy 95.754147 33.902029) (xy 95.742649 33.913527) (xy 95.664311 34.015619) (xy 95.656188 34.02969)
				(xy 95.606937 34.148592) (xy 95.602733 34.164279) (xy 95.585935 34.291881) (xy 93.152034 34.291881)
				(xy 93.14988 34.28668) (xy 93.141757 34.272609) (xy 93.063416 34.170513) (xy 93.05193 34.159028)
				(xy 92.949823 34.080678) (xy 92.935759 34.072558) (xy 92.816856 34.023307) (xy 92.801169 34.019103)
				(xy 92.673567 34.002305) (xy 92.657331 34.002305) (xy 92.529728 34.019103) (xy 92.514041 34.023307)
				(xy 92.395139 34.072558) (xy 92.381068 34.080681) (xy 92.278976 34.159019) (xy 92.267478 34.170517)
				(xy 92.18914 34.272609) (xy 92.181017 34.28668) (xy 92.131766 34.405582) (xy 92.127562 34.421269)
				(xy 92.110764 34.548871) (xy 87.799 34.548871) (xy 87.799 31.991881) (xy 89.045315 31.991881) (xy 89.045315 32.008118)
				(xy 89.062113 32.13572) (xy 89.066317 32.151407) (xy 89.115568 32.270309) (xy 89.123691 32.28438)
				(xy 89.202032 32.386476) (xy 89.213518 32.397961) (xy 89.315625 32.476311) (xy 89.329689 32.484431)
				(xy 89.448592 32.533682) (xy 89.464279 32.537886) (xy 89.591882 32.554685) (xy 89.608118 32.554685)
				(xy 89.73572 32.537886) (xy 89.751407 32.533682) (xy 89.87031 32.484431) (xy 89.884374 32.476311)
				(xy 89.986481 32.397961) (xy 89.992562 32.391881) (xy 99.745315 32.391881) (xy 99.745315 32.408118)
				(xy 99.762113 32.53572) (xy 99.766317 32.551407) (xy 99.815568 32.670309) (xy 99.823691 32.68438)
				(xy 99.902032 32.786476) (xy 99.913518 32.797961) (xy 100.015625 32.876311) (xy 100.029689 32.884431)
				(xy 100.148592 32.933682) (xy 100.164279 32.937886) (xy 100.291882 32.954685) (xy 100.308118 32.954685)
				(xy 100.43572 32.937886) (xy 100.451407 32.933682) (xy 100.57031 32.884431) (xy 100.584374 32.876311)
				(xy 100.686481 32.797961) (xy 100.697967 32.786476) (xy 100.776308 32.68438) (xy 100.784431 32.670309)
				(xy 100.833682 32.551407) (xy 100.837886 32.53572) (xy 100.854685 32.408118) (xy 100.854685 32.391881)
				(xy 100.837886 32.264279) (xy 100.833682 32.248592) (xy 100.784431 32.12969) (xy 100.776308 32.115619)
				(xy 100.697967 32.013523) (xy 100.686481 32.002038) (xy 100.584374 31.923688) (xy 100.57031 31.915568)
				(xy 100.451407 31.866317) (xy 100.43572 31.862113) (xy 100.308118 31.845315) (xy 100.291882 31.845315)
				(xy 100.164279 31.862113) (xy 100.148592 31.866317) (xy 100.02969 31.915568) (xy 100.015619 31.923691)
				(xy 99.913527 32.002029) (xy 99.902029 32.013527) (xy 99.823691 32.115619) (xy 99.815568 32.12969)
				(xy 99.766317 32.248592) (xy 99.762113 32.264279) (xy 99.745315 32.391881) (xy 89.992562 32.391881)
				(xy 89.997967 32.386476) (xy 90.059533 32.306242) (xy 90.059533 32.306241) (xy 90.07631 32.284375)
				(xy 90.084431 32.270309) (xy 90.133682 32.151407) (xy 90.137886 32.13572) (xy 90.154685 32.008118)
				(xy 90.154685 31.991881) (xy 90.137886 31.864279) (xy 90.133682 31.848592) (xy 90.084431 31.72969)
				(xy 90.076308 31.715619) (xy 89.997967 31.613523) (xy 89.986481 31.602038) (xy 89.884374 31.523688)
				(xy 89.87031 31.515568) (xy 89.751407 31.466317) (xy 89.73572 31.462113) (xy 89.608118 31.445315)
				(xy 89.591882 31.445315) (xy 89.464279 31.462113) (xy 89.448592 31.466317) (xy 89.32969 31.515568)
				(xy 89.315619 31.523691) (xy 89.213527 31.602029) (xy 89.202029 31.613527) (xy 89.123691 31.715619)
				(xy 89.115568 31.72969) (xy 89.066317 31.848592) (xy 89.062113 31.864279) (xy 89.045315 31.991881)
				(xy 87.799 31.991881) (xy 87.799 30.384051) (xy 92.006433 30.384051) (xy 92.006433 30.400288) (xy 92.023231 30.52789)
				(xy 92.027435 30.543577) (xy 92.076686 30.662479) (xy 92.084809 30.67655) (xy 92.16315 30.778646)
				(xy 92.174636 30.790131) (xy 92.276743 30.868481) (xy 92.290807 30.876601) (xy 92.40971 30.925852)
				(xy 92.425397 30.930056) (xy 92.553 30.946855) (xy 92.569236 30.946855) (xy 92.696838 30.930056)
				(xy 92.712525 30.925852) (xy 92.831428 30.876601) (xy 92.845492 30.868481) (xy 92.947599 30.790131)
				(xy 92.959082 30.778649) (xy 92.965829 30.769857) (xy 93.003085 30.737185) (xy 93.050008 30.721257)
				(xy 93.066585 30.72017) (xy 93.817972 30.72017) (xy 93.829041 30.720653) (xy 93.855964 30.723008)
				(xy 93.877421 30.721131) (xy 93.903526 30.714137) (xy 93.91434 30.71174) (xy 93.940972 30.707043)
				(xy 93.964689 30.698411) (xy 93.987544 30.687753) (xy 94.009702 30.672239) (xy 94.019045 30.666287)
				(xy 94.042443 30.652777) (xy 94.058948 30.638928) (xy 94.076325 30.61822) (xy 94.083809 30.610051)
				(xy 95.210166 29.483694) (xy 95.251368 29.456164) (xy 95.299969 29.446497) (xy 95.339214 29.452713)
				(xy 95.52447 29.512906) (xy 95.536356 29.51552) (xy 95.731531 29.538793) (xy 95.743687 29.539047)
				(xy 95.939665 29.523968) (xy 95.951643 29.521856) (xy 96.14096 29.468997) (xy 96.152317 29.464592)
				(xy 96.327739 29.37598) (xy 96.338021 29.369455) (xy 96.492904 29.248446) (xy 96.501711 29.24006)
				(xy 96.630145 29.091267) (xy 96.637161 29.081323) (xy 96.734249 28.910416) (xy 96.739195 28.899308)
				(xy 96.801236 28.712805) (xy 96.803931 28.700942) (xy 96.828903 28.503274) (xy 96.829388 28.496343)
				(xy 96.829706 28.473533) (xy 96.829414 28.466561) (xy 96.809975 28.268303) (xy 96.80761 28.256362)
				(xy 96.750802 28.068206) (xy 96.746162 28.05695) (xy 96.653889 27.883409) (xy 96.647153 27.87327)
				(xy 96.522925 27.720951) (xy 96.514353 27.712318) (xy 96.362907 27.587032) (xy 96.352821 27.580228)
				(xy 96.179927 27.486744) (xy 96.168701 27.482025) (xy 96.167109 27.481533) (xy 96.165659 27.480747)
				(xy 96.162982 27.479622) (xy 96.163093 27.479356) (xy 96.123542 27.457923) (xy 96.092326 27.419439)
				(xy 96.078214 27.371938) (xy 96.083354 27.322652) (xy 96.106964 27.279085) (xy 96.145448 27.247869)
				(xy 96.168177 27.238571) (xy 96.256898 27.211953) (xy 96.266428 27.208217) (xy 96.459596 27.113585)
				(xy 96.468391 27.108343) (xy 96.518298 27.072744) (xy 96.526209 27.062668) (xy 96.5196 27.050226)
				(xy 95.7722 26.302826) (xy 95.758646 26.295425) (xy 95.757361 26.295517) (xy 95.751056 26.299569)
				(xy 94.997598 27.053027) (xy 94.991219 27.06471) (xy 94.995994 27.071089) (xy 95.161325 27.1677)
				(xy 95.170542 27.172117) (xy 95.355817 27.242866) (xy 95.397771 27.269235) (xy 95.426441 27.309652)
				(xy 95.437461 27.357964) (xy 95.429155 27.406816) (xy 95.402786 27.44877) (xy 95.362369 27.47744)
				(xy 95.358092 27.47926) (xy 95.353852 27.480973) (xy 95.179662 27.572037) (xy 95.169476 27.578703)
				(xy 95.0163 27.701859) (xy 95.007602 27.710377) (xy 94.881261 27.860945) (xy 94.874392 27.870977)
				(xy 94.779699 28.043221) (xy 94.774902 28.054413) (xy 94.715474 28.241759) (xy 94.712943 28.253666)
				(xy 94.691034 28.448978) (xy 94.690864 28.461154) (xy 94.707312 28.657018) (xy 94.709509 28.668993)
				(xy 94.763683 28.857919) (xy 94.768166 28.869242) (xy 94.769532 28.871899) (xy 94.770346 28.874747)
				(xy 94.770451 28.875012) (xy 94.770424 28.875022) (xy 94.783152 28.919544) (xy 94.777501 28.968773)
				(xy 94.753441 29.012093) (xy 94.746382 29.019758) (xy 93.739167 30.026973) (xy 93.697965 30.054503)
				(xy 93.649364 30.06417) (xy 93.333435 30.06417) (xy 93.284834 30.054503) (xy 93.243632 30.026973)
				(xy 93.216102 29.985771) (xy 93.206435 29.93717) (xy 93.216102 29.888569) (xy 93.243632 29.847367)
				(xy 93.284834 29.819837) (xy 93.37031 29.784431) (xy 93.384374 29.776311) (xy 93.486481 29.697961)
				(xy 93.497967 29.686476) (xy 93.576308 29.58438) (xy 93.584431 29.570309) (xy 93.633682 29.451407)
				(xy 93.637886 29.43572) (xy 93.654685 29.308118) (xy 93.654685 29.291881) (xy 93.637886 29.164279)
				(xy 93.633682 29.148592) (xy 93.584431 29.02969) (xy 93.576308 29.015619) (xy 93.497967 28.913523)
				(xy 93.486481 28.902038) (xy 93.384374 28.823688) (xy 93.37031 28.815568) (xy 93.251407 28.766317)
				(xy 93.23572 28.762113) (xy 93.108118 28.745315) (xy 93.091882 28.745315) (xy 92.964279 28.762113)
				(xy 92.948592 28.766317) (xy 92.82969 28.815568) (xy 92.815619 28.823691) (xy 92.713527 28.902029)
				(xy 92.702029 28.913527) (xy 92.623691 29.015619) (xy 92.615568 29.02969) (xy 92.566317 29.148592)
				(xy 92.562113 29.164279) (xy 92.545315 29.291881) (xy 92.545315 29.308118) (xy 92.562113 29.43572)
				(xy 92.566317 29.451407) (xy 92.615568 29.570309) (xy 92.623689 29.584375) (xy 92.661132 29.633172)
				(xy 92.683049 29.677615) (xy 92.68629 29.727062) (xy 92.670361 29.773985) (xy 92.637689 29.811241)
				(xy 92.593246 29.833158) (xy 92.560376 29.837485) (xy 92.553 29.837485) (xy 92.425397 29.854283)
				(xy 92.40971 29.858487) (xy 92.290808 29.907738) (xy 92.276737 29.915861) (xy 92.174645 29.994199)
				(xy 92.163147 30.005697) (xy 92.084809 30.107789) (xy 92.076686 30.12186) (xy 92.027435 30.240762)
				(xy 92.023231 30.256449) (xy 92.006433 30.384051) (xy 87.799 30.384051) (xy 87.799 25.90152) (xy 94.385408 25.90152)
				(xy 94.39779 26.116276) (xy 94.399214 26.126409) (xy 94.446509 26.336273) (xy 94.449564 26.346021)
				(xy 94.530496 26.545333) (xy 94.535107 26.55446) (xy 94.617347 26.688663) (xy 94.627221 26.697596)
				(xy 94.635229 26.694144) (xy 95.387173 25.9422) (xy 95.394574 25.928646) (xy 95.394482 25.927361)
				(xy 95.39043 25.921056) (xy 94.63945 25.170076) (xy 94.628624 25.164164) (xy 94.616982 25.173285)
				(xy 94.567074 25.246447) (xy 94.562028 25.255328) (xy 94.471452 25.45046) (xy 94.467922 25.460054)
				(xy 94.410434 25.667348) (xy 94.408516 25.677402) (xy 94.385657 25.891298) (xy 94.385408 25.90152)
				(xy 87.799 25.90152) (xy 87.799 23.835033) (xy 87.808667 23.786432) (xy 87.836197 23.74523) (xy 91.515204 20.066223)
				(xy 91.525946 20.056636) (xy 91.535855 20.048753) (xy 91.544157 20.038373) (xy 91.54996 20.031886)
				(xy 91.562321 20.01639) (xy 91.566981 20.009832) (xy 91.574979 19.99983) (xy 91.58083 19.987727)
				(xy 91.585466 19.979355) (xy 91.587126 19.975358) (xy 91.589782 19.966172) (xy 91.594226 19.95348)
				(xy 91.595665 19.940755) (xy 91.597071 19.932523) (xy 91.599321 19.912702) (xy 91.599804 19.904143)
				(xy 91.601263 19.891235) (xy 91.599791 19.878111) (xy 91.599 19.863954) (xy 91.599 16.405876) (xy 91.599032 16.368852)
				(xy 91.591798 16.337011) (xy 91.589314 16.329898) (xy 91.572735 16.29545) (xy 91.5693 16.289974)
				(xy 91.54551 16.260067) (xy 91.540189 16.254737) (xy 91.510309 16.230884) (xy 91.495504 16.221561)
				(xy 91.492677 16.2193) (xy 91.46076 16.181394) (xy 91.445779 16.13416) (xy 91.445 16.120119) (xy 91.445 15.071712)
				(xy 91.443647 15.067105) (xy 91.955 15.067105) (xy 91.955 16.539188) (xy 91.959352 16.554011) (xy 91.960326 16.554855)
				(xy 91.967654 16.55645) (xy 92.494652 16.55645) (xy 92.501402 16.556084) (xy 92.55218 16.550569)
				(xy 92.567314 16.54697) (xy 92.687635 16.501864) (xy 92.703099 16.493397) (xy 92.805063 16.41698)
				(xy 92.817529 16.404514) (xy 92.893946 16.30255) (xy 92.902413 16.287086) (xy 92.947521 16.166761)
				(xy 92.951117 16.151636) (xy 92.956634 16.100848) (xy 92.957 16.094106) (xy 92.957 15.071712) (xy 92.952647 15.056889)
				(xy 92.951673 15.056045) (xy 92.944346 15.054451) (xy 91.972261 15.054451) (xy 91.957438 15.058803)
				(xy 91.956594 15.059777) (xy 91.955 15.067105) (xy 91.443647 15.067105) (xy 91.440647 15.056889)
				(xy 91.439673 15.056045) (xy 91.432346 15.054451) (xy 90.460262 15.054451) (xy 90.445439 15.058803)
				(xy 90.444595 15.059777) (xy 90.443001 15.067105) (xy 90.443001 15.461098) (xy 90.433334 15.509699)
				(xy 90.405804 15.550901) (xy 90.364602 15.578431) (xy 90.316001 15.588098) (xy 90.2674 15.578431)
				(xy 90.226198 15.550901) (xy 90.207716 15.527456) (xy 90.156919 15.444564) (xy 90.147871 15.426807)
				(xy 90.071392 15.24217) (xy 90.065234 15.223217) (xy 90.018579 15.028885) (xy 90.015461 15.009201)
				(xy 89.999781 14.809964) (xy 89.999781 14.790036) (xy 90.015461 14.590799) (xy 90.018579 14.571115)
				(xy 90.065234 14.376783) (xy 90.071392 14.35783) (xy 90.147871 14.173193) (xy 90.156919 14.155436)
				(xy 90.207715 14.072546) (xy 90.241352 14.036158) (xy 90.286353 14.015413) (xy 90.335868 14.013468)
				(xy 90.382358 14.030619) (xy 90.418746 14.064256) (xy 90.439491 14.109257) (xy 90.443 14.138904)
				(xy 90.443 14.527189) (xy 90.447352 14.542012) (xy 90.448326 14.542856) (xy 90.455654 14.544451)
				(xy 92.939738 14.544451) (xy 92.95456 14.540098) (xy 92.955404 14.539124) (xy 92.956999 14.531796)
				(xy 92.956999 13.526) (xy 92.966666 13.477399) (xy 92.994196 13.436197) (xy 93.035398 13.408667)
				(xy 93.083999 13.399) (xy 93.488944 13.399)
			)
		)
		(filled_polygon
			(layer "B.Cu")
			(pts
				(xy 96.077545 13.408667) (xy 96.118747 13.436197) (xy 96.146277 13.477399) (xy 96.155944 13.526)
				(xy 96.146277 13.574601) (xy 96.118747 13.615803) (xy 96.116204 13.618275) (xy 96.060985 13.670492)
				(xy 96.05275 13.680032) (xy 95.949202 13.827914) (xy 95.943056 13.83891) (xy 95.871355 14.004602)
				(xy 95.867545 14.016614) (xy 95.830308 14.194857) (xy 95.829081 14.204303) (xy 95.829 14.207415)
				(xy 95.829 15.34458) (xy 95.829318 15.350875) (xy 95.842979 15.48536) (xy 95.845512 15.497701) (xy 95.8995 15.669979)
				(xy 95.904465 15.681561) (xy 95.991993 15.839467) (xy 95.999177 15.849804) (xy 96.116665 15.986878)
				(xy 96.125793 15.995571) (xy 96.268448 16.106225) (xy 96.279126 16.112898) (xy 96.363768 16.154547)
				(xy 96.403107 16.184679) (xy 96.427921 16.227571) (xy 96.434697 16.268499) (xy 96.434697 22.642839)
				(xy 96.434214 22.653908) (xy 96.431858 22.680831) (xy 96.433735 22.702288) (xy 96.44073 22.728393)
				(xy 96.443127 22.739207) (xy 96.447823 22.765839) (xy 96.456455 22.789556) (xy 96.467113 22.812411)
				(xy 96.482628 22.834569) (xy 96.48858 22.843912) (xy 96.502089 22.86731) (xy 96.515938 22.883815)
				(xy 96.536647 22.901192) (xy 96.544816 22.908676) (xy 98.288946 24.652806) (xy 98.316476 24.694008)
				(xy 98.326143 24.742609) (xy 98.316476 24.79121) (xy 98.288946 24.832412) (xy 98.247744 24.859942)
				(xy 98.210653 24.869086) (xy 98.105634 24.878643) (xy 98.093685 24.880922) (xy 97.905129 24.936416)
				(xy 97.89385 24.940974) (xy 97.719662 25.032037) (xy 97.709476 25.038703) (xy 97.5563 25.161859)
				(xy 97.547602 25.170377) (xy 97.448091 25.288971) (xy 97.409445 25.319987) (xy 97.361872 25.333854)
				(xy 97.312614 25.328459) (xy 97.269169 25.304625) (xy 97.261 25.29714) (xy 95.119998 23.156138)
				(xy 95.092468 23.114936) (xy 95.082801 23.066335) (xy 95.082801 15.772757) (xy 95.092468 15.724156)
				(xy 95.093246 15.722319) (xy 95.148644 15.594299) (xy 95.152454 15.582287) (xy 95.189691 15.404044)
				(xy 95.190918 15.394598) (xy 95.191 15.391486) (xy 95.191 14.254321) (xy 95.190681 14.248026) (xy 95.17702 14.113541)
				(xy 95.174487 14.1012) (xy 95.120499 13.928922) (xy 95.115534 13.91734) (xy 95.028006 13.759434)
				(xy 95.020818 13.749092) (xy 94.900443 13.608648) (xy 94.876155 13.565456) (xy 94.870244 13.516257)
				(xy 94.883612 13.468541) (xy 94.914222 13.429573) (xy 94.957414 13.405285) (xy 94.99687 13.399)
				(xy 96.028944 13.399)
			)
		)
	)
	(zone
		(net 2)
		(net_name "GND")
		(layer "In1.Cu")
		(uuid "00000000-0000-0000-0000-00005c07d707")
		(hatch edge 0.508)
		(connect_pads
			(clearance 0.2)
		)
		(min_thickness 0.254)
		(filled_areas_thickness no)
		(fill yes
			(thermal_gap 0.508)
			(thermal_bridge_width 0.508)
		)
		(polygon
			(pts
				(xy 102.8 99.1) (xy 95.3 109.6) (xy 87.8 99.1) (xy 87.8 13.2) (xy 102.8 13.2)
			)
		)
		(polygon
			(pts
				(xy 94.9 40.7) (xy 94.886251 45.1) (xy 93.163539 45.1) (xy 90.799103 42.400705) (xy 90.8 40.8) (xy 88.3 40.8)
				(xy 88.3 45.2) (xy 90.4 47.2) (xy 90.4 99.3) (xy 93.495238 102.8) (xy 97.214677 102.8) (xy 100.1 99.2)
				(xy 100.2 46.2) (xy 98.1 46.2) (xy 98.1 40.7)
			)
		)
		(filled_polygon
			(layer "In1.Cu")
			(pts
				(xy 99.180183 13.400952) (xy 99.22099 13.401024) (xy 99.230733 13.401416) (xy 99.409201 13.415462)
				(xy 99.428885 13.418579) (xy 99.623217 13.465234) (xy 99.64217 13.471392) (xy 99.826807 13.547871)
				(xy 99.844564 13.556919) (xy 100.014967 13.661343) (xy 100.031089 13.673057) (xy 100.183058 13.802851)
				(xy 100.197149 13.816942) (xy 100.326943 13.968911) (xy 100.338657 13.985033) (xy 100.443081 14.155436)
				(xy 100.452129 14.173193) (xy 100.528608 14.35783) (xy 100.534766 14.376783) (xy 100.581421 14.571115)
				(xy 100.584539 14.590799) (xy 100.600219 14.790036) (xy 100.600219 14.809964) (xy 100.584539 15.009201)
				(xy 100.581421 15.028885) (xy 100.534766 15.223217) (xy 100.528608 15.24217) (xy 100.452129 15.426807)
				(xy 100.443081 15.444564) (xy 100.338657 15.614967) (xy 100.326943 15.631089) (xy 100.197149 15.783058)
				(xy 100.183058 15.797149) (xy 100.031089 15.926943) (xy 100.014967 15.938657) (xy 99.844564 16.043081)
				(xy 99.826807 16.052129) (xy 99.64217 16.128608) (xy 99.623217 16.134766) (xy 99.428885 16.181421)
				(xy 99.409201 16.184539) (xy 99.229822 16.198656) (xy 99.219632 16.199047) (xy 99.18143 16.198979)
				(xy 99.173925 16.199814) (xy 99.136655 16.208281) (xy 99.130557 16.210405) (xy 99.096087 16.22692)
				(xy 99.089699 16.230919) (xy 99.059781 16.254717) (xy 99.055206 16.259276) (xy 99.031309 16.289105)
				(xy 99.027285 16.295484) (xy 99.010644 16.329907) (xy 99.008506 16.335978) (xy 99.001077 16.368155)
				(xy 99.001054 16.376664) (xy 99.001064 16.37671) (xy 99.001054 16.37688) (xy 99.001054 16.377044)
				(xy 99.001045 16.377043) (xy 99.001 16.377848) (xy 99.001 16.397761) (xy 99.000876 16.445103) (xy 98.998603 16.445097)
				(xy 98.99861 16.445359) (xy 99.001 16.445359) (xy 99.001 19.863924) (xy 99.000184 19.878299) (xy 98.99875 19.890879)
				(xy 99.000221 19.904089) (xy 99.000703 19.912784) (xy 99.002919 19.932461) (xy 99.004264 19.940412)
				(xy 99.00568 19.953143) (xy 99.0101 19.965835) (xy 99.012745 19.975042) (xy 99.014387 19.979017)
				(xy 99.019008 19.987396) (xy 99.024845 19.999524) (xy 99.032827 20.009541) (xy 99.037647 20.016349)
				(xy 99.050073 20.031956) (xy 99.05578 20.038345) (xy 99.063882 20.048513) (xy 99.074211 20.056758)
				(xy 99.084782 20.066209) (xy 102.763803 23.74523) (xy 102.791333 23.786432) (xy 102.801 23.835033)
				(xy 102.801 99.059622) (xy 102.791333 99.108223) (xy 102.777344 99.133439) (xy 95.403344 109.457039)
				(xy 95.367229 109.490968) (xy 95.320879 109.508494) (xy 95.27135 109.506948) (xy 95.226183 109.486566)
				(xy 95.196656 109.457039) (xy 87.822656 99.133439) (xy 87.802274 99.088272) (xy 87.799 99.059622)
				(xy 87.799 40.813654) (xy 88.301 40.813654) (xy 88.301 45.181738) (xy 88.308268 45.206493) (xy 90.361586 47.162034)
				(xy 90.390113 47.202553) (xy 90.400962 47.250903) (xy 90.401 47.254) (xy 90.401 99.281738) (xy 90.40886 99.308509)
				(xy 93.483891 102.785659) (xy 93.505118 102.799) (xy 97.196416 102.799) (xy 97.219283 102.792285)
				(xy 97.220343 102.79133) (xy 100.0878 99.213622) (xy 100.099025 99.186315) (xy 100.198965 46.218258)
				(xy 100.194641 46.203428) (xy 100.193715 46.202623) (xy 100.186287 46.201) (xy 98.226 46.201) (xy 98.177399 46.191333)
				(xy 98.136197 46.163803) (xy 98.108667 46.122601) (xy 98.099 46.074) (xy 98.099 40.718261) (xy 98.094647 40.703438)
				(xy 98.093673 40.702594) (xy 98.086346 40.701) (xy 96.831154 40.701) (xy 96.793599 40.69532) (xy 96.697995 40.665725)
				(xy 96.686062 40.663275) (xy 96.512756 40.64506) (xy 96.50058 40.644976) (xy 96.327024 40.660771)
				(xy 96.315074 40.66305) (xy 96.203688 40.695833) (xy 96.167831 40.701) (xy 94.918261 40.701) (xy 94.903438 40.705352)
				(xy 94.902525 40.706405) (xy 94.900957 40.713558) (xy 94.887643 44.974397) (xy 94.877824 45.022967)
				(xy 94.850166 45.064083) (xy 94.808878 45.091484) (xy 94.760644 45.101) (xy 93.220673 45.101) (xy 93.172072 45.091333)
				(xy 93.13087 45.063803) (xy 93.125141 45.057681) (xy 90.829598 42.437036) (xy 90.804846 42.394108)
				(xy 90.79813 42.353284) (xy 90.798989 40.818261) (xy 90.794645 40.803436) (xy 90.793684 40.802602)
				(xy 90.786328 40.801) (xy 88.318261 40.801) (xy 88.303438 40.805352) (xy 88.302594 40.806326) (xy 88.301 40.813654)
				(xy 87.799 40.813654) (xy 87.799 39.940277) (xy 90.125291 39.940277) (xy 90.125291 39.959714) (xy 90.143074 40.071994)
				(xy 90.14908 40.090479) (xy 90.200689 40.191767) (xy 90.212114 40.207492) (xy 90.292503 40.287881)
				(xy 90.308228 40.299306) (xy 90.409516 40.350915) (xy 90.428001 40.356921) (xy 90.540281 40.374705)
				(xy 90.559719 40.374705) (xy 90.671998 40.356921) (xy 90.690483 40.350915) (xy 90.791771 40.299306)
				(xy 90.807496 40.287881) (xy 90.887885 40.207492) (xy 90.89931 40.191767) (xy 90.950919 40.090479)
				(xy 90.956925 40.071994) (xy 90.974709 39.959714) (xy 90.974709 39.940277) (xy 90.956925 39.827997)
				(xy 90.950919 39.809512) (xy 90.89931 39.708224) (xy 90.887885 39.692499) (xy 90.807496 39.61211)
				(xy 90.791771 39.600685) (xy 90.690483 39.549076) (xy 90.671998 39.54307) (xy 90.559719 39.525287)
				(xy 90.540281 39.525287) (xy 90.428001 39.54307) (xy 90.409516 39.549076) (xy 90.308228 39.600685)
				(xy 90.292503 39.61211) (xy 90.212114 39.692499) (xy 90.200689 39.708224) (xy 90.14908 39.809512)
				(xy 90.143074 39.827997) (xy 90.125291 39.940277) (xy 87.799 39.940277) (xy 87.799 34.547271) (xy 92.24074 34.547271)
				(xy 92.24074 34.566708) (xy 92.258523 34.678988) (xy 92.264529 34.697473) (xy 92.316138 34.798761)
				(xy 92.327563 34.814486) (xy 92.407952 34.894875) (xy 92.423677 34.9063) (xy 92.524965 34.957909)
				(xy 92.54345 34.963915) (xy 92.65573 34.981699) (xy 92.675168 34.981699) (xy 92.787447 34.963915)
				(xy 92.805932 34.957909) (xy 92.90722 34.9063) (xy 92.922945 34.894875) (xy 93.003334 34.814486)
				(xy 93.014759 34.798761) (xy 93.066368 34.697473) (xy 93.072374 34.678988) (xy 93.090158 34.566708)
				(xy 93.090158 34.547271) (xy 93.072374 34.434991) (xy 93.066368 34.416509) (xy 93.026339 34.337946)
				(xy 93.026339 34.337945) (xy 93.014759 34.315218) (xy 93.003334 34.299493) (xy 92.994122 34.290281)
				(xy 95.715911 34.290281) (xy 95.715911 34.309718) (xy 95.733694 34.421998) (xy 95.7397 34.440483)
				(xy 95.791309 34.541771) (xy 95.802734 34.557496) (xy 95.883123 34.637885) (xy 95.898848 34.64931)
				(xy 96.000136 34.700919) (xy 96.018621 34.706925) (xy 96.130901 34.724709) (xy 96.150339 34.724709)
				(xy 96.262618 34.706925) (xy 96.281103 34.700919) (xy 96.301981 34.690281) (xy 98.975291 34.690281)
				(xy 98.975291 34.709718) (xy 98.993074 34.821998) (xy 98.99908 34.840483) (xy 99.050689 34.941771)
				(xy 99.062114 34.957496) (xy 99.142503 35.037885) (xy 99.158228 35.04931) (xy 99.259516 35.100919)
				(xy 99.278001 35.106925) (xy 99.390281 35.124709) (xy 99.409719 35.124709) (xy 99.521998 35.106925)
				(xy 99.540483 35.100919) (xy 99.641771 35.04931) (xy 99.657496 35.037885) (xy 99.737885 34.957496)
				(xy 99.74931 34.941771) (xy 99.800919 34.840483) (xy 99.806925 34.821998) (xy 99.824709 34.709718)
				(xy 99.824709 34.690281) (xy 99.806925 34.578001) (xy 99.800919 34.559516) (xy 99.74931 34.458228)
				(xy 99.737885 34.442503) (xy 99.657496 34.362114) (xy 99.641771 34.350689) (xy 99.540483 34.29908)
				(xy 99.521998 34.293074) (xy 99.409719 34.275291) (xy 99.390281 34.275291) (xy 99.278001 34.293074)
				(xy 99.259516 34.29908) (xy 99.158228 34.350689) (xy 99.142503 34.362114) (xy 99.062114 34.442503)
				(xy 99.050689 34.458228) (xy 98.99908 34.559516) (xy 98.993074 34.578001) (xy 98.975291 34.690281)
				(xy 96.301981 34.690281) (xy 96.382391 34.64931) (xy 96.398116 34.637885) (xy 96.445516 34.590486)
				(xy 96.478505 34.557496) (xy 96.48993 34.541771) (xy 96.541539 34.440483) (xy 96.547545 34.421998)
				(xy 96.565329 34.309718) (xy 96.565329 34.290281) (xy 96.547545 34.178001) (xy 96.541539 34.159516)
				(xy 96.48993 34.058228) (xy 96.478505 34.042503) (xy 96.398116 33.962114) (xy 96.382391 33.950689)
				(xy 96.281103 33.89908) (xy 96.262618 33.893074) (xy 96.150339 33.875291) (xy 96.130901 33.875291)
				(xy 96.018621 33.893074) (xy 96.000136 33.89908) (xy 95.898848 33.950689) (xy 95.883123 33.962114)
				(xy 95.802734 34.042503) (xy 95.791309 34.058228) (xy 95.7397 34.159516) (xy 95.733694 34.178001)
				(xy 95.715911 34.290281) (xy 92.994122 34.290281) (xy 92.922945 34.219104) (xy 92.90722 34.207679)
				(xy 92.805932 34.15607) (xy 92.787447 34.150064) (xy 92.675168 34.132281) (xy 92.65573 34.132281)
				(xy 92.54345 34.150064) (xy 92.524965 34.15607) (xy 92.423677 34.207679) (xy 92.407952 34.219104)
				(xy 92.327563 34.299493) (xy 92.316138 34.315218) (xy 92.264529 34.416506) (xy 92.258523 34.434991)
				(xy 92.24074 34.547271) (xy 87.799 34.547271) (xy 87.799 31.990281) (xy 89.175291 31.990281) (xy 89.175291 32.009718)
				(xy 89.193074 32.121998) (xy 89.19908 32.140483) (xy 89.250689 32.241771) (xy 89.262114 32.257496)
				(xy 89.342503 32.337885) (xy 89.358228 32.34931) (xy 89.459516 32.400919) (xy 89.478001 32.406925)
				(xy 89.590281 32.424709) (xy 89.609719 32.424709) (xy 89.721998 32.406925) (xy 89.740483 32.400919)
				(xy 89.761361 32.390281) (xy 99.875291 32.390281) (xy 99.875291 32.409718) (xy 99.893074 32.521998)
				(xy 99.89908 32.540483) (xy 99.950689 32.641771) (xy 99.962114 32.657496) (xy 100.042503 32.737885)
				(xy 100.058228 32.74931) (xy 100.159516 32.800919) (xy 100.178001 32.806925) (xy 100.290281 32.824709)
				(xy 100.309719 32.824709) (xy 100.421998 32.806925) (xy 100.440483 32.800919) (xy 100.541771 32.74931)
				(xy 100.557496 32.737885) (xy 100.637885 32.657496) (xy 100.64931 32.641771) (xy 100.700919 32.540483)
				(xy 100.706925 32.521998) (xy 100.724709 32.409718) (xy 100.724709 32.390281) (xy 100.706925 32.278001)
				(xy 100.700919 32.259516) (xy 100.64931 32.158228) (xy 100.637885 32.142503) (xy 100.557496 32.062114)
				(xy 100.541771 32.050689) (xy 100.440483 31.99908) (xy 100.421998 31.993074) (xy 100.309719 31.975291)
				(xy 100.290281 31.975291) (xy 100.178001 31.993074) (xy 100.159516 31.99908) (xy 100.058228 32.050689)
				(xy 100.042503 32.062114) (xy 99.962114 32.142503) (xy 99.950689 32.158228) (xy 99.89908 32.259516)
				(xy 99.893074 32.278001) (xy 99.875291 32.390281) (xy 89.761361 32.390281) (xy 89.841771 32.34931)
				(xy 89.857496 32.337885) (xy 89.904896 32.290486) (xy 89.937885 32.257496) (xy 89.94931 32.241771)
				(xy 90.000919 32.140483) (xy 90.006925 32.121998) (xy 90.024709 32.009718) (xy 90.024709 31.990281)
				(xy 90.006925 31.878001) (xy 90.000919 31.859516) (xy 89.94931 31.758228) (xy 89.937885 31.742503)
				(xy 89.857496 31.662114) (xy 89.841771 31.650689) (xy 89.740483 31.59908) (xy 89.721998 31.593074)
				(xy 89.609719 31.575291) (xy 89.590281 31.575291) (xy 89.478001 31.593074) (xy 89.459516 31.59908)
				(xy 89.358228 31.650689) (xy 89.342503 31.662114) (xy 89.262114 31.742503) (xy 89.250689 31.758228)
				(xy 89.19908 31.859516) (xy 89.193074 31.878001) (xy 89.175291 31.990281) (xy 87.799 31.990281)
				(xy 87.799 30.382451) (xy 92.136409 30.382451) (xy 92.136409 30.401888) (xy 92.154192 30.514168)
				(xy 92.160198 30.532653) (xy 92.211807 30.633941) (xy 92.223232 30.649666) (xy 92.303621 30.730055)
				(xy 92.319346 30.74148) (xy 92.420634 30.793089) (xy 92.439119 30.799095) (xy 92.551399 30.816879)
				(xy 92.570837 30.816879) (xy 92.683116 30.799095) (xy 92.701601 30.793089) (xy 92.802889 30.74148)
				(xy 92.818614 30.730055) (xy 92.899003 30.649666) (xy 92.910428 30.633941) (xy 92.962037 30.532653)
				(xy 92.968043 30.514168) (xy 92.985827 30.401888) (xy 92.985827 30.382451) (xy 92.968043 30.270171)
				(xy 92.962037 30.251686) (xy 92.910428 30.150398) (xy 92.899003 30.134673) (xy 92.818614 30.054284)
				(xy 92.802889 30.042859) (xy 92.701601 29.99125) (xy 92.683116 29.985244) (xy 92.570837 29.967461)
				(xy 92.551399 29.967461) (xy 92.439119 29.985244) (xy 92.420634 29.99125) (xy 92.319346 30.042859)
				(xy 92.303621 30.054284) (xy 92.223232 30.134673) (xy 92.211807 30.150398) (xy 92.160198 30.251686)
				(xy 92.154192 30.270171) (xy 92.136409 30.382451) (xy 87.799 30.382451) (xy 87.799 29.290281) (xy 92.675291 29.290281)
				(xy 92.675291 29.309718) (xy 92.693074 29.421998) (xy 92.69908 29.440483) (xy 92.750689 29.541771)
				(xy 92.762114 29.557496) (xy 92.842503 29.637885) (xy 92.858228 29.64931) (xy 92.959516 29.700919)
				(xy 92.978001 29.706925) (xy 93.090281 29.724709) (xy 93.109719 29.724709) (xy 93.221998 29.706925)
				(xy 93.240483 29.700919) (xy 93.341771 29.64931) (xy 93.357496 29.637885) (xy 93.437885 29.557496)
				(xy 93.44931 29.541771) (xy 93.475432 29.490502) (xy 97.480102 29.490502) (xy 97.480102 29.509939)
				(xy 97.497885 29.622219) (xy 97.503891 29.640704) (xy 97.5555 29.741992) (xy 97.566925 29.757717)
				(xy 97.647314 29.838106) (xy 97.663039 29.849531) (xy 97.764327 29.90114) (xy 97.782812 29.907146)
				(xy 97.895092 29.92493) (xy 97.91453 29.92493) (xy 98.026809 29.907146) (xy 98.045294 29.90114)
				(xy 98.146582 29.849531) (xy 98.162307 29.838106) (xy 98.242696 29.757717) (xy 98.254121 29.741992)
				(xy 98.30573 29.640704) (xy 98.311736 29.622219) (xy 98.32952 29.509939) (xy 98.32952 29.490502)
				(xy 98.311736 29.378222) (xy 98.30573 29.359737) (xy 98.254121 29.258449) (xy 98.242696 29.242724)
				(xy 98.162307 29.162335) (xy 98.146582 29.15091) (xy 98.045294 29.099301) (xy 98.026809 29.093295)
				(xy 97.91453 29.075512) (xy 97.895092 29.075512) (xy 97.782812 29.093295) (xy 97.764327 29.099301)
				(xy 97.663039 29.15091) (xy 97.647314 29.162335) (xy 97.566925 29.242724) (xy 97.5555 29.258449)
				(xy 97.503891 29.359737) (xy 97.497885 29.378222) (xy 97.480102 29.490502) (xy 93.475432 29.490502)
				(xy 93.500918 29.440483) (xy 93.506925 29.422) (xy 93.524709 29.309718) (xy 93.524709 29.290281)
				(xy 93.506925 29.178001) (xy 93.500919 29.159516) (xy 93.44931 29.058228) (xy 93.437885 29.042503)
				(xy 93.357496 28.962114) (xy 93.341771 28.950689) (xy 93.240483 28.89908) (xy 93.221998 28.893074)
				(xy 93.109719 28.875291) (xy 93.090281 28.875291) (xy 92.978001 28.893074) (xy 92.959516 28.89908)
				(xy 92.858228 28.950689) (xy 92.842503 28.962114) (xy 92.762114 29.042503) (xy 92.750689 29.058228)
				(xy 92.69908 29.159516) (xy 92.693074 29.178001) (xy 92.675291 29.290281) (xy 87.799 29.290281)
				(xy 87.799 28.460281) (xy 95.335291 28.460281) (xy 95.335291 28.479718) (xy 95.353074 28.591998)
				(xy 95.35908 28.610483) (xy 95.410689 28.711771) (xy 95.422114 28.727496) (xy 95.502503 28.807885)
				(xy 95.518228 28.81931) (xy 95.619516 28.870919) (xy 95.638001 28.876925) (xy 95.750281 28.894709)
				(xy 95.769719 28.894709) (xy 95.881998 28.876925) (xy 95.900483 28.870919) (xy 96.001771 28.81931)
				(xy 96.017496 28.807885) (xy 96.097885 28.727496) (xy 96.10931 28.711771) (xy 96.160919 28.610483)
				(xy 96.166925 28.591998) (xy 96.184709 28.479718) (xy 96.184709 28.460281) (xy 96.166925 28.348001)
				(xy 96.160919 28.329516) (xy 96.10931 28.228228) (xy 96.097885 28.212503) (xy 96.017496 28.132114)
				(xy 96.001773 28.120691) (xy 95.948304 28.093447) (xy 95.948304 28.093446) (xy 95.900483 28.06908)
				(xy 95.881998 28.063074) (xy 95.769719 28.045291) (xy 95.750281 28.045291) (xy 95.638001 28.063074)
				(xy 95.619516 28.06908) (xy 95.518228 28.120689) (xy 95.502503 28.132114) (xy 95.422114 28.212503)
				(xy 95.410689 28.228228) (xy 95.35908 28.329516) (xy 95.353074 28.348001) (xy 95.335291 28.460281)
				(xy 87.799 28.460281) (xy 87.799 27.990281) (xy 100.775291 27.990281) (xy 100.775291 28.009718)
				(xy 100.793074 28.121998) (xy 100.79908 28.140483) (xy 100.850689 28.241771) (xy 100.862114 28.257496)
				(xy 100.942503 28.337885) (xy 100.958228 28.34931) (xy 101.059516 28.400919) (xy 101.078001 28.406925)
				(xy 101.190281 28.424709) (xy 101.209719 28.424709) (xy 101.321998 28.406925) (xy 101.340483 28.400919)
				(xy 101.441771 28.34931) (xy 101.457496 28.337885) (xy 101.537885 28.257496) (xy 101.54931 28.241771)
				(xy 101.600919 28.140483) (xy 101.606925 28.121998) (xy 101.624709 28.009718) (xy 101.624709 27.990281)
				(xy 101.606925 27.878001) (xy 101.600919 27.859516) (xy 101.54931 27.758228) (xy 101.537885 27.742503)
				(xy 101.457496 27.662114) (xy 101.441771 27.650689) (xy 101.340483 27.59908) (xy 101.321998 27.593074)
				(xy 101.209719 27.575291) (xy 101.190281 27.575291) (xy 101.078001 27.593074) (xy 101.059516 27.59908)
				(xy 100.958228 27.650689) (xy 100.942503 27.662114) (xy 100.862114 27.742503) (xy 100.850689 27.758228)
				(xy 100.79908 27.859516) (xy 100.793074 27.878001) (xy 100.775291 27.990281) (xy 87.799 27.990281)
				(xy 87.799 26.590281) (xy 97.175291 26.590281) (xy 97.175291 26.609718) (xy 97.193074 26.721998)
				(xy 97.19908 26.740483) (xy 97.250689 26.841771) (xy 97.262114 26.857496) (xy 97.342503 26.937885)
				(xy 97.358228 26.94931) (xy 97.459516 27.000919) (xy 97.478001 27.006925) (xy 97.590281 27.024709)
				(xy 97.609719 27.024709) (xy 97.721998 27.006925) (xy 97.740483 27.000919) (xy 97.841771 26.94931)
				(xy 97.857496 26.937885) (xy 97.937885 26.857496) (xy 97.94931 26.841771) (xy 98.000919 26.740483)
				(xy 98.006925 26.721998) (xy 98.024709 26.609718) (xy 98.024709 26.590281) (xy 98.006925 26.478001)
				(xy 98.000919 26.459516) (xy 97.94931 26.358228) (xy 97.937885 26.342503) (xy 97.857496 26.262114)
				(xy 97.841771 26.250689) (xy 97.740483 26.19908) (xy 97.721998 26.193074) (xy 97.704363 26.190281)
				(xy 100.675291 26.190281) (xy 100.675291 26.209718) (xy 100.693074 26.321998) (xy 100.69908 26.340483)
				(xy 100.750689 26.441771) (xy 100.762114 26.457496) (xy 100.842503 26.537885) (xy 100.858228 26.54931)
				(xy 100.959516 26.600919) (xy 100.978001 26.606925) (xy 101.090281 26.624709) (xy 101.109719 26.624709)
				(xy 101.221998 26.606925) (xy 101.240483 26.600919) (xy 101.341771 26.54931) (xy 101.357496 26.537885)
				(xy 101.437885 26.457496) (xy 101.44931 26.441771) (xy 101.500919 26.340483) (xy 101.506925 26.321998)
				(xy 101.524709 26.209718) (xy 101.524709 26.190281) (xy 101.506925 26.078001) (xy 101.500919 26.059516)
				(xy 101.44931 25.958228) (xy 101.437885 25.942503) (xy 101.357496 25.862114) (xy 101.341771 25.850689)
				(xy 101.240483 25.79908) (xy 101.221998 25.793074) (xy 101.109719 25.775291) (xy 101.090281 25.775291)
				(xy 100.978001 25.793074) (xy 100.959516 25.79908) (xy 100.858228 25.850689) (xy 100.842503 25.862114)
				(xy 100.762114 25.942503) (xy 100.750689 25.958228) (xy 100.69908 26.059516) (xy 100.693074 26.078001)
				(xy 100.675291 26.190281) (xy 97.704363 26.190281) (xy 97.609719 26.175291) (xy 97.590281 26.175291)
				(xy 97.478001 26.193074) (xy 97.459516 26.19908) (xy 97.358228 26.250689) (xy 97.342503 26.262114)
				(xy 97.262114 26.342503) (xy 97.250689 26.358228) (xy 97.19908 26.459516) (xy 97.193074 26.478001)
				(xy 97.175291 26.590281) (xy 87.799 26.590281) (xy 87.799 23.835033) (xy 87.808667 23.786432) (xy 87.836197 23.74523)
				(xy 88.991146 22.590281) (xy 92.875291 22.590281) (xy 92.875291 22.609718) (xy 92.893074 22.721998)
				(xy 92.89908 22.740483) (xy 92.950689 22.841771) (xy 92.962114 22.857496) (xy 93.042503 22.937885)
				(xy 93.058228 22.94931) (xy 93.159516 23.000919) (xy 93.178001 23.006925) (xy 93.290281 23.024709)
				(xy 93.309719 23.024709) (xy 93.421998 23.006925) (xy 93.440483 23.000919) (xy 93.541771 22.94931)
				(xy 93.557496 22.937885) (xy 93.637885 22.857496) (xy 93.64931 22.841771) (xy 93.700919 22.740483)
				(xy 93.706925 22.721998) (xy 93.724709 22.609718) (xy 93.724709 22.590281) (xy 93.706925 22.478001)
				(xy 93.700919 22.459516) (xy 93.64931 22.358228) (xy 93.637885 22.342503) (xy 93.557496 22.262114)
				(xy 93.541771 22.250689) (xy 93.440483 22.19908) (xy 93.421998 22.193074) (xy 93.309719 22.175291)
				(xy 93.290281 22.175291) (xy 93.178001 22.193074) (xy 93.159516 22.19908) (xy 93.058228 22.250689)
				(xy 93.042503 22.262114) (xy 92.962114 22.342503) (xy 92.950689 22.358228) (xy 92.89908 22.459516)
				(xy 92.893074 22.478001) (xy 92.875291 22.590281) (xy 88.991146 22.590281) (xy 89.991146 21.590281)
				(xy 93.675291 21.590281) (xy 93.675291 21.609718) (xy 93.693074 21.721998) (xy 93.69908 21.740483)
				(xy 93.750689 21.841771) (xy 93.762114 21.857496) (xy 93.842503 21.937885) (xy 93.858228 21.94931)
				(xy 93.959516 22.000919) (xy 93.978001 22.006925) (xy 94.090281 22.024709) (xy 94.109719 22.024709)
				(xy 94.221998 22.006925) (xy 94.240483 22.000919) (xy 94.341771 21.94931) (xy 94.357496 21.937885)
				(xy 94.437885 21.857496) (xy 94.44931 21.841771) (xy 94.500919 21.740483) (xy 94.506925 21.721998)
				(xy 94.524709 21.609718) (xy 94.524709 21.590281) (xy 94.506925 21.478001) (xy 94.500919 21.459516)
				(xy 94.44931 21.358228) (xy 94.437885 21.342503) (xy 94.357496 21.262114) (xy 94.341771 21.250689)
				(xy 94.240483 21.19908) (xy 94.221998 21.193074) (xy 94.109719 21.175291) (xy 94.090281 21.175291)
				(xy 93.978001 21.193074) (xy 93.959516 21.19908) (xy 93.858228 21.250689) (xy 93.842503 21.262114)
				(xy 93.762114 21.342503) (xy 93.750689 21.358228) (xy 93.69908 21.459516) (xy 93.693074 21.478001)
				(xy 93.675291 21.590281) (xy 89.991146 21.590281) (xy 91.515204 20.066223) (xy 91.525946 20.056636)
				(xy 91.535855 20.048753) (xy 91.544157 20.038373) (xy 91.54996 20.031886) (xy 91.562321 20.01639)
				(xy 91.566981 20.009832) (xy 91.574979 19.99983) (xy 91.58083 19.987727) (xy 91.585466 19.979355)
				(xy 91.587126 19.975358) (xy 91.589782 19.966172) (xy 91.594226 19.95348) (xy 91.595665 19.940755)
				(xy 91.597071 19.932523) (xy 91.599321 19.912702) (xy 91.599804 19.904143) (xy 91.601263 19.891235)
				(xy 91.599791 19.878111) (xy 91.599 19.863954) (xy 91.599 16.405876) (xy 91.599032 16.368852) (xy 91.591798 16.337011)
				(xy 91.589314 16.329898) (xy 91.572735 16.29545) (xy 91.5693 16.289974) (xy 91.54551 16.260067)
				(xy 91.540189 16.254737) (xy 91.510308 16.230883) (xy 91.50485 16.227446) (xy 91.470436 16.210809)
				(xy 91.463328 16.208313) (xy 91.426059 16.199777) (xy 91.419647 16.199046) (xy 91.37901 16.198976)
				(xy 91.369267 16.198584) (xy 91.190799 16.184538) (xy 91.171115 16.181421) (xy 90.976783 16.134766)
				(xy 90.95783 16.128608) (xy 90.773193 16.052129) (xy 90.755436 16.043081) (xy 90.585033 15.938657)
				(xy 90.568911 15.926943) (xy 90.416942 15.797149) (xy 90.402851 15.783058) (xy 90.273057 15.631089)
				(xy 90.261343 15.614967) (xy 90.158195 15.446647) (xy 90.158184 15.446629) (xy 90.156908 15.444546)
				(xy 90.147871 15.426807) (xy 90.132741 15.390281) (xy 98.875291 15.390281) (xy 98.875291 15.409718)
				(xy 98.893074 15.521998) (xy 98.89908 15.540483) (xy 98.950689 15.641771) (xy 98.962114 15.657496)
				(xy 99.042503 15.737885) (xy 99.058228 15.74931) (xy 99.159516 15.800919) (xy 99.178001 15.806925)
				(xy 99.290281 15.824709) (xy 99.309719 15.824709) (xy 99.421998 15.806925) (xy 99.440483 15.800919)
				(xy 99.541771 15.74931) (xy 99.557496 15.737885) (xy 99.637885 15.657496) (xy 99.64931 15.641771)
				(xy 99.700919 15.540483) (xy 99.706925 15.521998) (xy 99.724709 15.409718) (xy 99.724709 15.390281)
				(xy 99.706925 15.278001) (xy 99.700919 15.259516) (xy 99.64931 15.158228) (xy 99.637885 15.142503)
				(xy 99.557496 15.062114) (xy 99.541771 15.050689) (xy 99.440483 14.99908) (xy 99.421998 14.993074)
				(xy 99.309719 14.975291) (xy 99.290281 14.975291) (xy 99.178001 14.993074) (xy 99.159516 14.99908)
				(xy 99.058228 15.050689) (xy 99.042503 15.062114) (xy 98.962114 15.142503) (xy 98.950689 15.158228)
				(xy 98.89908 15.259516) (xy 98.893074 15.278001) (xy 98.875291 15.390281) (xy 90.132741 15.390281)
				(xy 90.071392 15.24217) (xy 90.065234 15.223217) (xy 90.018579 15.028885) (xy 90.015461 15.009201)
				(xy 89.999781 14.809964) (xy 89.999781 14.790036) (xy 90.015461 14.590799) (xy 90.018579 14.571115)
				(xy 90.065234 14.376783) (xy 90.071392 14.35783) (xy 90.140793 14.190281) (xy 98.875291 14.190281)
				(xy 98.875291 14.209718) (xy 98.893074 14.321998) (xy 98.89908 14.340483) (xy 98.950689 14.441771)
				(xy 98.962114 14.457496) (xy 99.042503 14.537885) (xy 99.058228 14.54931) (xy 99.159516 14.600919)
				(xy 99.178001 14.606925) (xy 99.290281 14.624709) (xy 99.309719 14.624709) (xy 99.421998 14.606925)
				(xy 99.440483 14.600919) (xy 99.541771 14.54931) (xy 99.557496 14.537885) (xy 99.637885 14.457496)
				(xy 99.64931 14.441771) (xy 99.700919 14.340483) (xy 99.706925 14.321998) (xy 99.724709 14.209718)
				(xy 99.724709 14.190281) (xy 99.706925 14.078001) (xy 99.700919 14.059516) (xy 99.64931 13.958228)
				(xy 99.637885 13.942503) (xy 99.557496 13.862114) (xy 99.541771 13.850689) (xy 99.440483 13.79908)
				(xy 99.421998 13.793074) (xy 99.309719 13.775291) (xy 99.290281 13.775291) (xy 99.178001 13.793074)
				(xy 99.159516 13.79908) (xy 99.058228 13.850689) (xy 99.042503 13.862114) (xy 98.962114 13.942503)
				(xy 98.950689 13.958228) (xy 98.89908 14.059516) (xy 98.893074 14.078001) (xy 98.875291 14.190281)
				(xy 90.140793 14.190281) (xy 90.147871 14.173193) (xy 90.15692 14.155435) (xy 90.182355 14.11393)
				(xy 90.261343 13.985034) (xy 90.273057 13.968911) (xy 90.402851 13.816942) (xy 90.416942 13.802851)
				(xy 90.568911 13.673057) (xy 90.585033 13.661343) (xy 90.755436 13.556919) (xy 90.773193 13.547871)
				(xy 90.95783 13.471392) (xy 90.976783 13.465234) (xy 91.171115 13.418579) (xy 91.190798 13.415462)
				(xy 91.370179 13.401344) (xy 91.38037 13.400952) (xy 91.41837 13.401019) (xy 91.436448 13.399) (xy 99.162976 13.399)
			)
		)
	)
	(zone
		(net 4)
		(net_name "VCC")
		(layer "In2.Cu")
		(uuid "00000000-0000-0000-0000-00005c07d70a")
		(hatch edge 0.508)
		(connect_pads
			(clearance 0.2)
		)
		(min_thickness 0.254)
		(filled_areas_thickness no)
		(fill yes
			(thermal_gap 0.508)
			(thermal_bridge_width 0.508)
		)
		(polygon
			(pts
				(xy 102.8 43.2) (xy 87.8 43.2) (xy 87.8 13.2) (xy 102.8 13.2)
			)
		)
		(filled_polygon
			(layer "In2.Cu")
			(pts
				(xy 99.180183 13.400952) (xy 99.22099 13.401024) (xy 99.230733 13.401416) (xy 99.409201 13.415462)
				(xy 99.428885 13.418579) (xy 99.623217 13.465234) (xy 99.64217 13.471392) (xy 99.826807 13.547871)
				(xy 99.844564 13.556919) (xy 100.014967 13.661343) (xy 100.031089 13.673057) (xy 100.183058 13.802851)
				(xy 100.197149 13.816942) (xy 100.326943 13.968911) (xy 100.338657 13.985033) (xy 100.443081 14.155436)
				(xy 100.452129 14.173193) (xy 100.528608 14.35783) (xy 100.534766 14.376783) (xy 100.581421 14.571115)
				(xy 100.584539 14.590799) (xy 100.600219 14.790036) (xy 100.600219 14.809964) (xy 100.584539 15.009201)
				(xy 100.581421 15.028885) (xy 100.534766 15.223217) (xy 100.528608 15.24217) (xy 100.452129 15.426807)
				(xy 100.443081 15.444564) (xy 100.338657 15.614967) (xy 100.326943 15.631089) (xy 100.197149 15.783058)
				(xy 100.183058 15.797149) (xy 100.031089 15.926943) (xy 100.014967 15.938657) (xy 99.844564 16.043081)
				(xy 99.826807 16.052129) (xy 99.64217 16.128608) (xy 99.623217 16.134766) (xy 99.428885 16.181421)
				(xy 99.409201 16.184539) (xy 99.229822 16.198656) (xy 99.219632 16.199047) (xy 99.18143 16.198979)
				(xy 99.173925 16.199814) (xy 99.136655 16.208281) (xy 99.130557 16.210405) (xy 99.096087 16.22692)
				(xy 99.089699 16.230919) (xy 99.059781 16.254717) (xy 99.055206 16.259276) (xy 99.031309 16.289105)
				(xy 99.027285 16.295484) (xy 99.010644 16.329907) (xy 99.008506 16.335978) (xy 99.001077 16.368155)
				(xy 99.001054 16.376664) (xy 99.001064 16.37671) (xy 99.001054 16.37688) (xy 99.001054 16.377044)
				(xy 99.001045 16.377043) (xy 99.001 16.377848) (xy 99.001 16.397761) (xy 99.000876 16.445103) (xy 98.998603 16.445097)
				(xy 98.99861 16.445359) (xy 99.001 16.445359) (xy 99.001 19.863924) (xy 99.000184 19.878299) (xy 98.99875 19.890879)
				(xy 99.000221 19.904089) (xy 99.000703 19.912784) (xy 99.002919 19.932461) (xy 99.004264 19.940412)
				(xy 99.00568 19.953143) (xy 99.0101 19.965835) (xy 99.012745 19.975042) (xy 99.014387 19.979017)
				(xy 99.019008 19.987396) (xy 99.024845 19.999524) (xy 99.032827 20.009541) (xy 99.037647 20.016349)
				(xy 99.050073 20.031956) (xy 99.05578 20.038345) (xy 99.063882 20.048513) (xy 99.074211 20.056758)
				(xy 99.084782 20.066209) (xy 102.763803 23.74523) (xy 102.791333 23.786432) (xy 102.801 23.835033)
				(xy 102.801 43.074) (xy 102.791333 43.122601) (xy 102.763803 43.163803) (xy 102.722601 43.191333)
				(xy 102.674 43.201) (xy 87.926 43.201) (xy 87.877399 43.191333) (xy 87.836197 43.163803) (xy 87.808667 43.122601)
				(xy 87.799 43.074) (xy 87.799 41.895427) (xy 88.674568 41.895427) (xy 88.691177 42.064826) (xy 88.694064 42.078407)
				(xy 88.747793 42.239921) (xy 88.753618 42.252527) (xy 88.841796 42.398128) (xy 88.850268 42.409129)
				(xy 88.968508 42.53157) (xy 88.979209 42.540422) (xy 89.121642 42.633628) (xy 89.134033 42.639887)
				(xy 89.293581 42.699223) (xy 89.307053 42.702582) (xy 89.475768 42.725093) (xy 89.489657 42.725384)
				(xy 89.659172 42.709957) (xy 89.672772 42.707165) (xy 89.834658 42.654564) (xy 89.847306 42.648827)
				(xy 89.993514 42.56167) (xy 90.004579 42.553271) (xy 90.12784 42.435892) (xy 90.136769 42.425251)
				(xy 90.230962 42.283478) (xy 90.237311 42.271125) (xy 90.297757 42.111999) (xy 90.30121 42.098551)
				(xy 90.325333 41.926905) (xy 90.325933 41.91911) (xy 90.326145 41.903968) (xy 90.325763 41.896161)
				(xy 90.306442 41.723902) (xy 90.303367 41.71037) (xy 90.279736 41.642512) (xy 90.279736 41.642511)
				(xy 90.263338 41.595423) (xy 95.674568 41.595423) (xy 95.691177 41.764822) (xy 95.694064 41.778403)
				(xy 95.747793 41.939917) (xy 95.753618 41.952523) (xy 95.841796 42.098124) (xy 95.850268 42.109125)
				(xy 95.968508 42.231566) (xy 95.979209 42.240418) (xy 96.121642 42.333624) (xy 96.134033 42.339883)
				(xy 96.293581 42.399219) (xy 96.307053 42.402578) (xy 96.475768 42.425089) (xy 96.489657 42.42538)
				(xy 96.659172 42.409953) (xy 96.672772 42.407161) (xy 96.834658 42.35456) (xy 96.847306 42.348823)
				(xy 96.993514 42.261666) (xy 97.004579 42.253267) (xy 97.12784 42.135888) (xy 97.136769 42.125247)
				(xy 97.230962 41.983474) (xy 97.237311 41.971121) (xy 97.297757 41.811995) (xy 97.30121 41.798547)
				(xy 97.325333 41.626901) (xy 97.325933 41.619106) (xy 97.326145 41.603964) (xy 97.325763 41.596157)
				(xy 97.306442 41.423898) (xy 97.303366 41.410363) (xy 97.247387 41.249612) (xy 97.241384 41.237083)
				(xy 97.151186 41.092737) (xy 97.142558 41.081851) (xy 97.02262 40.961073) (xy 97.0118 40.952374)
				(xy 96.868074 40.861163) (xy 96.855596 40.855076) (xy 96.695248 40.797979) (xy 96.681725 40.794807)
				(xy 96.512703 40.774652) (xy 96.498814 40.774555) (xy 96.329537 40.792347) (xy 96.315976 40.795329)
				(xy 96.15484 40.850184) (xy 96.142264 40.856101) (xy 95.997298 40.945285) (xy 95.986347 40.95384)
				(xy 95.864737 41.07293) (xy 95.855961 41.083691) (xy 95.763752 41.22677) (xy 95.757577 41.23921)
				(xy 95.699361 41.399159) (xy 95.696096 41.412655) (xy 95.674762 41.581532) (xy 95.674568 41.595423)
				(xy 90.263338 41.595423) (xy 90.247387 41.549616) (xy 90.241384 41.537087) (xy 90.151186 41.392741)
				(xy 90.142558 41.381855) (xy 90.02262 41.261077) (xy 90.0118 41.252378) (xy 89.868074 41.161167)
				(xy 89.855596 41.15508) (xy 89.695248 41.097983) (xy 89.681725 41.094811) (xy 89.512703 41.074656)
				(xy 89.498814 41.074559) (xy 89.329537 41.092351) (xy 89.315976 41.095333) (xy 89.15484 41.150188)
				(xy 89.142264 41.156105) (xy 88.997298 41.245289) (xy 88.986347 41.253844) (xy 88.864737 41.372934)
				(xy 88.855961 41.383695) (xy 88.763752 41.526774) (xy 88.757577 41.539214) (xy 88.699361 41.699163)
				(xy 88.696096 41.712659) (xy 88.674762 41.881536) (xy 88.674568 41.895427) (xy 87.799 41.895427)
				(xy 87.799 31.586891) (xy 89.969161 31.586891) (xy 89.971517 31.613818) (xy 89.972 31.624886) (xy 89.972 39.680138)
				(xy 89.971517 39.691207) (xy 89.969161 39.71813) (xy 89.971038 39.739587) (xy 89.978033 39.765692)
				(xy 89.98043 39.776506) (xy 89.985126 39.803138) (xy 89.994374 39.828547) (xy 89.997707 39.839117)
				(xy 89.998193 39.840931) (xy 89.996094 39.841493) (xy 90.002914 39.869334) (xy 90.001949 39.891482)
				(xy 89.995315 39.94187) (xy 89.995315 39.958114) (xy 90.012113 40.085716) (xy 90.016317 40.101403)
				(xy 90.065568 40.220305) (xy 90.073691 40.234376) (xy 90.152032 40.336472) (xy 90.163518 40.347957)
				(xy 90.265625 40.426307) (xy 90.279689 40.434427) (xy 90.398592 40.483678) (xy 90.414279 40.487882)
				(xy 90.541882 40.504681) (xy 90.558118 40.504681) (xy 90.68572 40.487882) (xy 90.701407 40.483678)
				(xy 90.82031 40.434427) (xy 90.834374 40.426307) (xy 90.936481 40.347957) (xy 90.947967 40.336472)
				(xy 91.026308 40.234376) (xy 91.034431 40.220305) (xy 91.083682 40.101403) (xy 91.087886 40.085716)
				(xy 91.104685 39.958114) (xy 91.104685 39.941877) (xy 91.087886 39.814275) (xy 91.083682 39.798588)
				(xy 91.034431 39.679686) (xy 91.026308 39.665615) (xy 90.947967 39.563519) (xy 90.936481 39.552034)
				(xy 90.834374 39.473684) (xy 90.820308 39.465563) (xy 90.7064 39.418381) (xy 90.665198 39.390851)
				(xy 90.637667 39.349649) (xy 90.628 39.301048) (xy 90.628 39.126133) (xy 90.637667 39.077532) (xy 90.665197 39.03633)
				(xy 90.706399 39.0088) (xy 90.755 38.999133) (xy 90.803601 39.0088) (xy 90.844803 39.03633) (xy 90.857744 39.051482)
				(xy 90.862112 39.057494) (xy 90.942503 39.137885) (xy 90.958228 39.14931) (xy 91.059516 39.200919)
				(xy 91.078001 39.206925) (xy 91.190281 39.224709) (xy 91.209719 39.224709) (xy 91.321998 39.206925)
				(xy 91.340483 39.200919) (xy 91.441771 39.14931) (xy 91.457496 39.137885) (xy 91.537885 39.057496)
				(xy 91.54931 39.041771) (xy 91.600919 38.940483) (xy 91.606925 38.921998) (xy 91.624709 38.809718)
				(xy 91.624709 38.790281) (xy 97.075291 38.790281) (xy 97.075291 38.809718) (xy 97.093074 38.921998)
				(xy 97.09908 38.940483) (xy 97.150689 39.041771) (xy 97.162114 39.057496) (xy 97.242503 39.137885)
				(xy 97.258228 39.14931) (xy 97.359516 39.200919) (xy 97.378001 39.206925) (xy 97.490281 39.224709)
				(xy 97.509719 39.224709) (xy 97.621998 39.206925) (xy 97.640483 39.200919) (xy 97.741771 39.14931)
				(xy 97.757496 39.137885) (xy 97.837885 39.057496) (xy 97.84931 39.041771) (xy 97.900919 38.940483)
				(xy 97.906925 38.921998) (xy 97.924709 38.809718) (xy 97.924709 38.790281) (xy 97.906925 38.678001)
				(xy 97.900919 38.659516) (xy 97.84931 38.558228) (xy 97.837885 38.542503) (xy 97.757496 38.462114)
				(xy 97.741771 38.450689) (xy 97.640483 38.39908) (xy 97.621998 38.393074) (xy 97.509719 38.375291)
				(xy 97.490281 38.375291) (xy 97.378001 38.393074) (xy 97.359516 38.39908) (xy 97.258228 38.450689)
				(xy 97.242503 38.462114) (xy 97.162114 38.542503) (xy 97.150689 38.558228) (xy 97.09908 38.659516)
				(xy 97.093074 38.678001) (xy 97.075291 38.790281) (xy 91.624709 38.790281) (xy 91.606925 38.678001)
				(xy 91.600919 38.659516) (xy 91.54931 38.558228) (xy 91.537885 38.542503) (xy 91.457496 38.462114)
				(xy 91.441771 38.450689) (xy 91.340483 38.39908) (xy 91.321998 38.393074) (xy 91.209719 38.375291)
				(xy 91.190281 38.375291) (xy 91.078001 38.393074) (xy 91.059516 38.39908) (xy 90.958228 38.450689)
				(xy 90.942503 38.462114) (xy 90.862112 38.542505) (xy 90.857744 38.548518) (xy 90.821355 38.582154)
				(xy 90.774864 38.599304) (xy 90.72535 38.597357) (xy 90.680349 38.576611) (xy 90.646713 38.540222)
				(xy 90.629563 38.493731) (xy 90.628 38.473867) (xy 90.628 37.626133) (xy 90.637667 37.577532) (xy 90.665197 37.53633)
				(xy 90.706399 37.5088) (xy 90.755 37.499133) (xy 90.803601 37.5088) (xy 90.844803 37.53633) (xy 90.857744 37.551482)
				(xy 90.862112 37.557494) (xy 90.942503 37.637885) (xy 90.958228 37.64931) (xy 91.059516 37.700919)
				(xy 91.078001 37.706925) (xy 91.190281 37.724709) (xy 91.209719 37.724709) (xy 91.321998 37.706925)
				(xy 91.340483 37.700919) (xy 91.441771 37.64931) (xy 91.457496 37.637885) (xy 91.537885 37.557496)
				(xy 91.54931 37.541771) (xy 91.600919 37.440483) (xy 91.606925 37.421998) (xy 91.624709 37.309718)
				(xy 91.624709 37.290281) (xy 97.075291 37.290281) (xy 97.075291 37.309718) (xy 97.093074 37.421998)
				(xy 97.09908 37.440483) (xy 97.150689 37.541771) (xy 97.162114 37.557496) (xy 97.242503 37.637885)
				(xy 97.258228 37.64931) (xy 97.359516 37.700919) (xy 97.378001 37.706925) (xy 97.490281 37.724709)
				(xy 97.509719 37.724709) (xy 97.621998 37.706925) (xy 97.640483 37.700919) (xy 97.741771 37.64931)
				(xy 97.757496 37.637885) (xy 97.837885 37.557496) (xy 97.84931 37.541771) (xy 97.900919 37.440483)
				(xy 97.906925 37.421998) (xy 97.924709 37.309718) (xy 97.924709 37.290281) (xy 97.906925 37.178001)
				(xy 97.900919 37.159516) (xy 97.84931 37.058228) (xy 97.837885 37.042503) (xy 97.757496 36.962114)
				(xy 97.741771 36.950689) (xy 97.640483 36.89908) (xy 97.621998 36.893074) (xy 97.509719 36.875291)
				(xy 97.490281 36.875291) (xy 97.378001 36.893074) (xy 97.359516 36.89908) (xy 97.258228 36.950689)
				(xy 97.242503 36.962114) (xy 97.162114 37.042503) (xy 97.150689 37.058228) (xy 97.09908 37.159516)
				(xy 97.093074 37.178001) (xy 97.075291 37.290281) (xy 91.624709 37.290281) (xy 91.606925 37.178001)
				(xy 91.600919 37.159516) (xy 91.54931 37.058228) (xy 91.537885 37.042503) (xy 91.457496 36.962114)
				(xy 91.441771 36.950689) (xy 91.340483 36.89908) (xy 91.321998 36.893074) (xy 91.209719 36.875291)
				(xy 91.190281 36.875291) (xy 91.078001 36.893074) (xy 91.059516 36.89908) (xy 90.958228 36.950689)
				(xy 90.942503 36.962114) (xy 90.862112 37.042505) (xy 90.857744 37.048518) (xy 90.821355 37.082154)
				(xy 90.774864 37.099304) (xy 90.72535 37.097357) (xy 90.680349 37.076611) (xy 90.646713 37.040222)
				(xy 90.629563 36.993731) (xy 90.628 36.973867) (xy 90.628 35.790281) (xy 100.075291 35.790281) (xy 100.075291 35.809718)
				(xy 100.093074 35.921998) (xy 100.09908 35.940483) (xy 100.150689 36.041771) (xy 100.162114 36.057496)
				(xy 100.242503 36.137885) (xy 100.258228 36.14931) (xy 100.359516 36.200919) (xy 100.378001 36.206925)
				(xy 100.490281 36.224709) (xy 100.509719 36.224709) (xy 100.621998 36.206925) (xy 100.640483 36.200919)
				(xy 100.741771 36.14931) (xy 100.757496 36.137885) (xy 100.837885 36.057496) (xy 100.84931 36.041771)
				(xy 100.900919 35.940483) (xy 100.906925 35.921998) (xy 100.924709 35.809718) (xy 100.924709 35.790281)
				(xy 100.906925 35.678001) (xy 100.900919 35.659516) (xy 100.84931 35.558228) (xy 100.837885 35.542503)
				(xy 100.757496 35.462114) (xy 100.741771 35.450689) (xy 100.640483 35.39908) (xy 100.621998 35.393074)
				(xy 100.509719 35.375291) (xy 100.490281 35.375291) (xy 100.378001 35.393074) (xy 100.359516 35.39908)
				(xy 100.258228 35.450689) (xy 100.242503 35.462114) (xy 100.162114 35.542503) (xy 100.150689 35.558228)
				(xy 100.09908 35.659516) (xy 100.093074 35.678001) (xy 100.075291 35.790281) (xy 90.628 35.790281)
				(xy 90.628 34.548871) (xy 92.110764 34.548871) (xy 92.110764 34.565108) (xy 92.127562 34.69271)
				(xy 92.131766 34.708397) (xy 92.181017 34.827299) (xy 92.18914 34.84137) (xy 92.267481 34.943466)
				(xy 92.278967 34.954951) (xy 92.381074 35.033301) (xy 92.395138 35.041421) (xy 92.514041 35.090672)
				(xy 92.529728 35.094876) (xy 92.657331 35.111675) (xy 92.673567 35.111675) (xy 92.801169 35.094876)
				(xy 92.816856 35.090672) (xy 92.935759 35.041421) (xy 92.949823 35.033301) (xy 93.05193 34.954951)
				(xy 93.063413 34.943469) (xy 93.07016 34.934677) (xy 93.107416 34.902005) (xy 93.154339 34.886077)
				(xy 93.170916 34.88499) (xy 95.863772 34.88499) (xy 95.874841 34.885473) (xy 95.901764 34.887828)
				(xy 95.923221 34.885951) (xy 95.949326 34.878957) (xy 95.96014 34.87656) (xy 95.986772 34.871863)
				(xy 96.010484 34.863233) (xy 96.018094 34.859685) (xy 96.066227 34.847907) (xy 96.088343 34.848872)
				(xy 96.1325 34.854685) (xy 96.148746 34.854685) (xy 96.159729 34.853239) (xy 96.209176 34.856479)
				(xy 96.253619 34.878396) (xy 96.266109 34.889349) (xy 96.294641 34.917881) (xy 96.302126 34.92605)
				(xy 96.319505 34.946762) (xy 96.336002 34.960604) (xy 96.359422 34.974126) (xy 96.368768 34.98008)
				(xy 96.390907 34.995582) (xy 96.413775 35.006246) (xy 96.437475 35.014872) (xy 96.464109 35.019568)
				(xy 96.47493 35.021967) (xy 96.501033 35.028961) (xy 96.522481 35.030838) (xy 96.549409 35.028483)
				(xy 96.560478 35.028) (xy 98.894533 35.028) (xy 98.943134 35.037667) (xy 98.984336 35.065197) (xy 98.995289 35.077687)
				(xy 99.002035 35.086479) (xy 99.013518 35.097961) (xy 99.115625 35.176311) (xy 99.129689 35.184431)
				(xy 99.248592 35.233682) (xy 99.264279 35.237886) (xy 99.391882 35.254685) (xy 99.408118 35.254685)
				(xy 99.53572 35.237886) (xy 99.551407 35.233682) (xy 99.67031 35.184431) (xy 99.684374 35.176311)
				(xy 99.786481 35.097961) (xy 99.797967 35.086476) (xy 99.876308 34.98438) (xy 99.884431 34.970309)
				(xy 99.933682 34.851407) (xy 99.937886 34.83572) (xy 99.954685 34.708118) (xy 99.954685 34.691881)
				(xy 99.937886 34.564279) (xy 99.933682 34.548592) (xy 99.884431 34.42969) (xy 99.876308 34.415619)
				(xy 99.797967 34.313523) (xy 99.786481 34.302038) (xy 99.684374 34.223688) (xy 99.67031 34.215568)
				(xy 99.551407 34.166317) (xy 99.53572 34.162113) (xy 99.408118 34.145315) (xy 99.391882 34.145315)
				(xy 99.264279 34.162113) (xy 99.248592 34.166317) (xy 99.12969 34.215568) (xy 99.115619 34.223691)
				(xy 99.013527 34.302029) (xy 99.002033 34.313523) (xy 98.995289 34.322313) (xy 98.958033 34.354985)
				(xy 98.91111 34.370913) (xy 98.894533 34.372) (xy 96.817229 34.372) (xy 96.768628 34.362333) (xy 96.727426 34.334803)
				(xy 96.699896 34.293601) (xy 96.691315 34.261576) (xy 96.678506 34.164279) (xy 96.674302 34.148592)
				(xy 96.625051 34.02969) (xy 96.616928 34.015619) (xy 96.538587 33.913523) (xy 96.527101 33.902038)
				(xy 96.424994 33.823688) (xy 96.41093 33.815568) (xy 96.292027 33.766317) (xy 96.27634 33.762113)
				(xy 96.148738 33.745315) (xy 96.132502 33.745315) (xy 96.004899 33.762113) (xy 95.989212 33.766317)
				(xy 95.87031 33.815568) (xy 95.856239 33.823691) (xy 95.754147 33.902029) (xy 95.742649 33.913527)
				(xy 95.664311 34.015619) (xy 95.656184 34.029696) (xy 95.606108 34.150591) (xy 95.578578 34.191793)
				(xy 95.537376 34.219323) (xy 95.488776 34.22899) (xy 93.170916 34.22899) (xy 93.122315 34.219323)
				(xy 93.081113 34.191793) (xy 93.07016 34.179303) (xy 93.063413 34.17051) (xy 93.05193 34.159028)
				(xy 92.949823 34.080678) (xy 92.935759 34.072558) (xy 92.816856 34.023307) (xy 92.801169 34.019103)
				(xy 92.673567 34.002305) (xy 92.657331 34.002305) (xy 92.529728 34.019103) (xy 92.514041 34.023307)
				(xy 92.395139 34.072558) (xy 92.381068 34.080681) (xy 92.278976 34.159019) (xy 92.267478 34.170517)
				(xy 92.18914 34.272609) (xy 92.181017 34.28668) (xy 92.131766 34.405582) (xy 92.127562 34.421269)
				(xy 92.110764 34.548871) (xy 90.628 34.548871) (xy 90.628 33.116533) (xy 98.711495 33.116533) (xy 98.711495 33.13597)
				(xy 98.729278 33.24825) (xy 98.735284 33.266735) (xy 98.786893 33.368023) (xy 98.798318 33.383748)
				(xy 98.878707 33.464137) (xy 98.894432 33.475562) (xy 98.99572 33.527171) (xy 99.014205 33.533177)
				(xy 99.126485 33.550961) (xy 99.145923 33.550961) (xy 99.258202 33.533177) (xy 99.276687 33.527171)
				(xy 99.377975 33.475562) (xy 99.3937 33.464137) (xy 99.474089 33.383748) (xy 99.485514 33.368023)
				(xy 99.537123 33.266735) (xy 99.543129 33.24825) (xy 99.560913 33.13597) (xy 99.560913 33.116533)
				(xy 99.543129 33.004253) (xy 99.537123 32.985768) (xy 99.485514 32.88448) (xy 99.474089 32.868755)
				(xy 99.3937 32.788366) (xy 99.377975 32.776941) (xy 99.276687 32.725332) (xy 99.258202 32.719326)
				(xy 99.145923 32.701543) (xy 99.126485 32.701543) (xy 99.014205 32.719326) (xy 98.99572 32.725332)
				(xy 98.894432 32.776941) (xy 98.878707 32.788366) (xy 98.798318 32.868755) (xy 98.786893 32.88448)
				(xy 98.735284 32.985768) (xy 98.729278 33.004253) (xy 98.711495 33.116533) (xy 90.628 33.116533)
				(xy 90.628 32.391881) (xy 99.745315 32.391881) (xy 99.745315 32.408118) (xy 99.762113 32.53572)
				(xy 99.766317 32.551407) (xy 99.815568 32.670309) (xy 99.823691 32.68438) (xy 99.902032 32.786476)
				(xy 99.913518 32.797961) (xy 100.015625 32.876311) (xy 100.029689 32.884431) (xy 100.148592 32.933682)
				(xy 100.164279 32.937886) (xy 100.291882 32.954685) (xy 100.308118 32.954685) (xy 100.43572 32.937886)
				(xy 100.451407 32.933682) (xy 100.57031 32.884431) (xy 100.584374 32.876311) (xy 100.686481 32.797961)
				(xy 100.697967 32.786476) (xy 100.776308 32.68438) (xy 100.784431 32.670309) (xy 100.833682 32.551407)
				(xy 100.837886 32.53572) (xy 100.854685 32.408118) (xy 100.854685 32.391874) (xy 100.853239 32.380891)
				(xy 100.856479 32.331444) (xy 100.878396 32.287001) (xy 100.889349 32.274511) (xy 101.417881 31.745979)
				(xy 101.42605 31.738494) (xy 101.446762 31.721114) (xy 101.460604 31.704617) (xy 101.474126 31.681198)
				(xy 101.48008 31.671852) (xy 101.495582 31.649712) (xy 101.506246 31.626844) (xy 101.514872 31.603144)
				(xy 101.519568 31.576511) (xy 101.521967 31.56569) (xy 101.528961 31.539586) (xy 101.530839 31.518137)
				(xy 101.528483 31.491208) (xy 101.528 31.48014) (xy 101.528 28.505467) (xy 101.537667 28.456866)
				(xy 101.565197 28.415664) (xy 101.577687 28.404711) (xy 101.586476 28.397966) (xy 101.59797 28.386472)
				(xy 101.676308 28.28438) (xy 101.684431 28.270309) (xy 101.733682 28.151407) (xy 101.737886 28.13572)
				(xy 101.754685 28.008118) (xy 101.754685 27.991881) (xy 101.737886 27.864279) (xy 101.733682 27.848592)
				(xy 101.684431 27.72969) (xy 101.676308 27.715619) (xy 101.597967 27.613523) (xy 101.586481 27.602038)
				(xy 101.484374 27.523688) (xy 101.47031 27.515568) (xy 101.351407 27.466317) (xy 101.33572 27.462113)
				(xy 101.208118 27.445315) (xy 101.191882 27.445315) (xy 101.064279 27.462113) (xy 101.048592 27.466317)
				(xy 100.92969 27.515568) (xy 100.915619 27.523691) (xy 100.813527 27.602029) (xy 100.802029 27.613527)
				(xy 100.723691 27.715619) (xy 100.715568 27.72969) (xy 100.666317 27.848592) (xy 100.662113 27.864279)
				(xy 100.645315 27.991881) (xy 100.645315 28.008118) (xy 100.662113 28.13572) (xy 100.666317 28.151407)
				(xy 100.715568 28.270309) (xy 100.723691 28.28438) (xy 100.802029 28.386472) (xy 100.813523 28.397966)
				(xy 100.822313 28.404711) (xy 100.854985 28.441967) (xy 100.870913 28.48889) (xy 100.872 28.505467)
				(xy 100.872 31.311534) (xy 100.862333 31.360135) (xy 100.834803 31.401337) (xy 100.425489 31.810651)
				(xy 100.384287 31.838181) (xy 100.335686 31.847848) (xy 100.319109 31.846761) (xy 100.308126 31.845315)
				(xy 100.291882 31.845315) (xy 100.164279 31.862113) (xy 100.148592 31.866317) (xy 100.02969 31.915568)
				(xy 100.015619 31.923691) (xy 99.913527 32.002029) (xy 99.902029 32.013527) (xy 99.823691 32.115619)
				(xy 99.815568 32.12969) (xy 99.766317 32.248592) (xy 99.762113 32.264279) (xy 99.745315 32.391881)
				(xy 90.628 32.391881) (xy 90.628 31.793492) (xy 90.637667 31.744891) (xy 90.665197 31.703689) (xy 91.936998 30.431888)
				(xy 91.9782 30.404358) (xy 92.026801 30.394691) (xy 92.075402 30.404358) (xy 92.116604 30.431888)
				(xy 92.144134 30.47309) (xy 92.152237 30.501821) (xy 92.154193 30.51417) (xy 92.160198 30.532653)
				(xy 92.211807 30.633941) (xy 92.223232 30.649666) (xy 92.303621 30.730055) (xy 92.319346 30.74148)
				(xy 92.420634 30.793089) (xy 92.439119 30.799095) (xy 92.551399 30.816879) (xy 92.570837 30.816879)
				(xy 92.683116 30.799095) (xy 92.701601 30.793089) (xy 92.802889 30.74148) (xy 92.818614 30.730055)
				(xy 92.899003 30.649666) (xy 92.910428 30.633941) (xy 92.962037 30.532653) (xy 92.968043 30.514168)
				(xy 92.985827 30.401888) (xy 92.985827 30.382451) (xy 92.968043 30.270171) (xy 92.962037 30.251686)
				(xy 92.910428 30.150398) (xy 92.899003 30.134673) (xy 92.818611 30.054281) (xy 92.808018 30.046585)
				(xy 92.774382 30.010197) (xy 92.757231 29.963707) (xy 92.759176 29.914192) (xy 92.779922 29.869191)
				(xy 92.81631 29.835555) (xy 92.8628 29.818404) (xy 92.912315 29.820349) (xy 92.931267 29.826507)
				(xy 92.948589 29.833682) (xy 92.964279 29.837886) (xy 93.091882 29.854685) (xy 93.108118 29.854685)
				(xy 93.23572 29.837886) (xy 93.251407 29.833682) (xy 93.37031 29.784431) (xy 93.384374 29.776311)
				(xy 93.486481 29.697961) (xy 93.497967 29.686476) (xy 93.576308 29.58438) (xy 93.584431 29.570309)
				(xy 93.617488 29.490502) (xy 97.480102 29.490502) (xy 97.480102 29.509939) (xy 97.497885 29.622219)
				(xy 97.503891 29.640704) (xy 97.5555 29.741992) (xy 97.566925 29.757717) (xy 97.647314 29.838106)
				(xy 97.663039 29.849531) (xy 97.764327 29.90114) (xy 97.782812 29.907146) (xy 97.895092 29.92493)
				(xy 97.91453 29.92493) (xy 98.026809 29.907146) (xy 98.045294 29.90114) (xy 98.146582 29.849531)
				(xy 98.162307 29.838106) (xy 98.242696 29.757717) (xy 98.254121 29.741992) (xy 98.30573 29.640704)
				(xy 98.311736 29.622219) (xy 98.32952 29.509939) (xy 98.32952 29.490502) (xy 98.311736 29.378222)
				(xy 98.30573 29.359737) (xy 98.254121 29.258449) (xy 98.242696 29.242724) (xy 98.162307 29.162335)
				(xy 98.146582 29.15091) (xy 98.045294 29.099301) (xy 98.026809 29.093295) (xy 97.91453 29.075512)
				(xy 97.895092 29.075512) (xy 97.782812 29.093295) (xy 97.764327 29.099301) (xy 97.663039 29.15091)
				(xy 97.647314 29.162335) (xy 97.566925 29.242724) (xy 97.5555 29.258449) (xy 97.503891 29.359737)
				(xy 97.497885 29.378222) (xy 97.480102 29.490502) (xy 93.617488 29.490502) (xy 93.633683 29.451405)
				(xy 93.634692 29.447642) (xy 93.634692 29.44764) (xy 93.637886 29.435718) (xy 93.654685 29.308118)
				(xy 93.654685 29.291881) (xy 93.637886 29.164279) (xy 93.633682 29.148592) (xy 93.584431 29.02969)
				(xy 93.576308 29.015619) (xy 93.497967 28.913523) (xy 93.486481 28.902038) (xy 93.384374 28.823688)
				(xy 93.37031 28.815568) (xy 93.251407 28.766317) (xy 93.23572 28.762113) (xy 93.108118 28.745315)
				(xy 93.091882 28.745315) (xy 92.964279 28.762113) (xy 92.948592 28.766317) (xy 92.82969 28.815568)
				(xy 92.815619 28.823691) (xy 92.713527 28.902029) (xy 92.702028 28.913528) (xy 92.697197 28.919825)
				(xy 92.659941 28.952498) (xy 92.613018 28.968426) (xy 92.589426 28.968939) (xy 92.565434 28.971038)
				(xy 92.53933 28.978033) (xy 92.528516 28.98043) (xy 92.501883 28.985126) (xy 92.478166 28.993758)
				(xy 92.455311 29.004416) (xy 92.433154 29.019931) (xy 92.423811 29.025883) (xy 92.400412 29.039392)
				(xy 92.383907 29.053241) (xy 92.366531 29.07395) (xy 92.359047 29.082119) (xy 90.082119 31.359047)
				(xy 90.07395 31.366532) (xy 90.053236 31.383912) (xy 90.039394 31.400409) (xy 90.025883 31.423811)
				(xy 90.019931 31.433154) (xy 90.004416 31.455311) (xy 89.993758 31.478166) (xy 89.985126 31.501883)
				(xy 89.98043 31.528516) (xy 89.978033 31.53933) (xy 89.971038 31.565434) (xy 89.969161 31.586891)
				(xy 87.799 31.586891) (xy 87.799 28.290281) (xy 88.375291 28.290281) (xy 88.375291 28.309718) (xy 88.393074 28.421998)
				(xy 88.39908 28.440483) (xy 88.450689 28.541771) (xy 88.462114 28.557496) (xy 88.542503 28.637885)
				(xy 88.558228 28.64931) (xy 88.631889 28.686842) (xy 88.670804 28.71752) (xy 88.695017 28.760754)
				(xy 88.700842 28.809964) (xy 88.687391 28.857656) (xy 88.656713 28.896571) (xy 88.631889 28.913158)
				(xy 88.558228 28.950689) (xy 88.542503 28.962114) (xy 88.462114 29.042503) (xy 88.450689 29.058228)
				(xy 88.39908 29.159516) (xy 88.393074 29.178001) (xy 88.375291 29.290281) (xy 88.375291 29.309718)
				(xy 88.393074 29.421998) (xy 88.39908 29.440483) (xy 88.450689 29.541771) (xy 88.462114 29.557496)
				(xy 88.542507 29.637889) (xy 88.555399 29.647256) (xy 88.589035 29.683644) (xy 88.606186 29.730134)
				(xy 88.60424 29.779649) (xy 88.583493 29.82465) (xy 88.555399 29.852744) (xy 88.542507 29.86211)
				(xy 88.462114 29.942503) (xy 88.450689 29.958228) (xy 88.39908 30.059516) (xy 88.393074 30.078001)
				(xy 88.375291 30.190281) (xy 88.375291 30.209718) (xy 88.393074 30.321998) (xy 88.39908 30.340483)
				(xy 88.450689 30.441771) (xy 88.462114 30.457496) (xy 88.542507 30.537889) (xy 88.555399 30.547256)
				(xy 88.589035 30.583644) (xy 88.606186 30.630134) (xy 88.60424 30.679649) (xy 88.583493 30.72465)
				(xy 88.555399 30.752744) (xy 88.542507 30.76211) (xy 88.462114 30.842503) (xy 88.450689 30.858228)
				(xy 88.39908 30.959516) (xy 88.393074 30.978001) (xy 88.375291 31.090281) (xy 88.375291 31.109718)
				(xy 88.393074 31.221998) (xy 88.39908 31.240483) (xy 88.450689 31.341771) (xy 88.462114 31.357496)
				(xy 88.542503 31.437885) (xy 88.558228 31.44931) (xy 88.659516 31.500919) (xy 88.678001 31.506925)
				(xy 88.790281 31.524709) (xy 88.809719 31.524709) (xy 88.921998 31.506925) (xy 88.94048 31.500919)
				(xy 88.993842 31.473731) (xy 88.993843 31.473731) (xy 89.041771 31.44931) (xy 89.057496 31.437885)
				(xy 89.137885 31.357496) (xy 89.14931 31.341771) (xy 89.200919 31.240483) (xy 89.206925 31.221998)
				(xy 89.224709 31.109718) (xy 89.224709 31.090281) (xy 89.206925 30.978001) (xy 89.200918 30.959514)
				(xy 89.178546 30.915607) (xy 89.165095 30.867915) (xy 89.170919 30.818706) (xy 89.195131 30.775471)
				(xy 89.234046 30.744793) (xy 89.281738 30.731342) (xy 89.330947 30.737166) (xy 89.349359 30.744792)
				(xy 89.459516 30.800918) (xy 89.478001 30.806925) (xy 89.590281 30.824709) (xy 89.609719 30.824709)
				(xy 89.721998 30.806925) (xy 89.740483 30.800919) (xy 89.841771 30.74931) (xy 89.857496 30.737885)
				(xy 89.937885 30.657496) (xy 89.94931 30.641771) (xy 90.000919 30.540483) (xy 90.006925 30.521998)
				(xy 90.024709 30.409718) (xy 90.024709 30.390281) (xy 90.006925 30.278001) (xy 90.000919 30.259516)
				(xy 89.94931 30.158228) (xy 89.937885 30.142503) (xy 89.857496 30.062114) (xy 89.841771 30.050689)
				(xy 89.740483 29.99908) (xy 89.721998 29.993074) (xy 89.609719 29.975291) (xy 89.590281 29.975291)
				(xy 89.478001 29.993074) (xy 89.459516 29.99908) (xy 89.349317 30.05523) (xy 89.348896 30.054404)
				(xy 89.315436 30.06983) (xy 89.265921 30.071775) (xy 89.219431 30.054624) (xy 89.183043 30.020988)
				(xy 89.17263 30.003996) (xy 89.149309 29.958226) (xy 89.137885 29.942503) (xy 89.057492 29.86211)
				(xy 89.044601 29.852744) (xy 89.010965 29.816356) (xy 88.993814 29.769866) (xy 88.99576 29.720351)
				(xy 89.016507 29.67535) (xy 89.044601 29.647256) (xy 89.057492 29.637889) (xy 89.137885 29.557496)
				(xy 89.14931 29.541771) (xy 89.200919 29.440483) (xy 89.206925 29.421998) (xy 89.224709 29.309718)
				(xy 89.224709 29.280289) (xy 89.22621 29.280289) (xy 89.227542 29.246374) (xy 89.248288 29.201373)
				(xy 89.284675 29.167736) (xy 89.331165 29.150585) (xy 89.38068 29.15253) (xy 89.425681 29.173276)
				(xy 89.440836 29.186218) (xy 89.492503 29.237885) (xy 89.508228 29.24931) (xy 89.609516 29.300919)
				(xy 89.628001 29.306925) (xy 89.740281 29.324709) (xy 89.759719 29.324709) (xy 89.871998 29.306925)
				(xy 89.890483 29.300919) (xy 89.991771 29.24931) (xy 90.007496 29.237885) (xy 90.087885 29.157496)
				(xy 90.09931 29.141771) (xy 90.150919 29.040483) (xy 90.156925 29.021998) (xy 90.174709 28.909718)
				(xy 90.174709 28.890281) (xy 90.156925 28.777999) (xy 90.151609 28.76164) (xy 90.145783 28.71243)
				(xy 90.159233 28.664738) (xy 90.18991 28.625822) (xy 90.233144 28.601609) (xy 90.282354 28.595783)
				(xy 90.31164 28.601609) (xy 90.327999 28.606925) (xy 90.440281 28.624709) (xy 90.459719 28.624709)
				(xy 90.571998 28.606925) (xy 90.590483 28.600919) (xy 90.691771 28.54931) (xy 90.707496 28.537885)
				(xy 90.7851 28.460281) (xy 95.335291 28.460281) (xy 95.335291 28.479718) (xy 95.353074 28.591998)
				(xy 95.35908 28.610483) (xy 95.410689 28.711771) (xy 95.422114 28.727496) (xy 95.502503 28.807885)
				(xy 95.518228 28.81931) (xy 95.619516 28.870919) (xy 95.638001 28.876925) (xy 95.750281 28.894709)
				(xy 95.769719 28.894709) (xy 95.881998 28.876925) (xy 95.900483 28.870919) (xy 96.001771 28.81931)
				(xy 96.017496 28.807885) (xy 96.097885 28.727496) (xy 96.10931 28.711771) (xy 96.160919 28.610483)
				(xy 96.166925 28.591998) (xy 96.184709 28.479718) (xy 96.184709 28.460281) (xy 96.166925 28.348001)
				(xy 96.160919 28.329516) (xy 96.10931 28.228228) (xy 96.097885 28.212503) (xy 96.017496 28.132114)
				(xy 96.001771 28.120689) (xy 95.900483 28.06908) (xy 95.881998 28.063074) (xy 95.769719 28.045291)
				(xy 95.750281 28.045291) (xy 95.638001 28.063074) (xy 95.619516 28.06908) (xy 95.518228 28.120689)
				(xy 95.502503 28.132114) (xy 95.422114 28.212503) (xy 95.410689 28.228228) (xy 95.35908 28.329516)
				(xy 95.353074 28.348001) (xy 95.335291 28.460281) (xy 90.7851 28.460281) (xy 90.787885 28.457496)
				(xy 90.799308 28.441773) (xy 90.824348 28.392632) (xy 90.850918 28.340483) (xy 90.856925 28.321998)
				(xy 90.874709 28.209718) (xy 90.874709 28.190281) (xy 90.856925 28.078001) (xy 90.850919 28.059516)
				(xy 90.79931 27.958228) (xy 90.787885 27.942503) (xy 90.707496 27.862114) (xy 90.691771 27.850689)
				(xy 90.590483 27.79908) (xy 90.571998 27.793074) (xy 90.459719 27.775291) (xy 90.440281 27.775291)
				(xy 90.328001 27.793074) (xy 90.309516 27.79908) (xy 90.208228 27.850689) (xy 90.192503 27.862114)
				(xy 90.112114 27.942503) (xy 90.100689 27.958228) (xy 90.04908 28.059516) (xy 90.043074 28.078001)
				(xy 90.025291 28.190281) (xy 90.025291 28.209718) (xy 90.043074 28.322) (xy 90.048391 28.33836)
				(xy 90.054217 28.38757) (xy 90.040767 28.435262) (xy 90.01009 28.474178) (xy 89.966856 28.498391)
				(xy 89.917646 28.504217) (xy 89.88836 28.498391) (xy 89.872 28.493074) (xy 89.759719 28.475291)
				(xy 89.740281 28.475291) (xy 89.628001 28.493074) (xy 89.609516 28.49908) (xy 89.508228 28.550689)
				(xy 89.492503 28.562114) (xy 89.412114 28.642503) (xy 89.400689 28.658228) (xy 89.34908 28.759516)
				(xy 89.343074 28.778001) (xy 89.325291 28.890281) (xy 89.325291 28.919711) (xy 89.32379 28.919711)
				(xy 89.322458 28.953626) (xy 89.301712 28.998627) (xy 89.265325 29.032264) (xy 89.218835 29.049415)
				(xy 89.16932 29.04747) (xy 89.124319 29.026724) (xy 89.109164 29.013782) (xy 89.057496 28.962114)
				(xy 89.041771 28.950689) (xy 88.968111 28.913158) (xy 88.929196 28.88248) (xy 88.904983 28.839246)
				(xy 88.899158 28.790036) (xy 88.912609 28.742344) (xy 88.943287 28.703429) (xy 88.968111 28.686842)
				(xy 89.041771 28.64931) (xy 89.057496 28.637885) (xy 89.137885 28.557496) (xy 89.14931 28.541771)
				(xy 89.200919 28.440483) (xy 89.206925 28.421998) (xy 89.224709 28.309718) (xy 89.224709 28.290281)
				(xy 89.206925 28.178001) (xy 89.200919 28.159516) (xy 89.14931 28.058228) (xy 89.137885 28.042503)
				(xy 89.057496 27.962114) (xy 89.041771 27.950689) (xy 88.940483 27.89908) (xy 88.921998 27.893074)
				(xy 88.810634 27.875436) (xy 88.801908 27.872217) (xy 88.799146 27.873491) (xy 88.789366 27.875436)
				(xy 88.678001 27.893074) (xy 88.659516 27.89908) (xy 88.558228 27.950689) (xy 88.542503 27.962114)
				(xy 88.462114 28.042503) (xy 88.450689 28.058228) (xy 88.39908 28.159516) (xy 88.393074 28.178001)
				(xy 88.375291 28.290281) (xy 87.799 28.290281) (xy 87.799 23.835033) (xy 87.808667 23.786432) (xy 87.836197 23.74523)
				(xy 88.1712 23.410227) (xy 88.212402 23.382697) (xy 88.261003 23.37303) (xy 88.309604 23.382697)
				(xy 88.350806 23.410227) (xy 88.378336 23.451429) (xy 88.388003 23.50003) (xy 88.386439 23.519898)
				(xy 88.375291 23.590282) (xy 88.375291 23.609718) (xy 88.393074 23.721998) (xy 88.39908 23.740483)
				(xy 88.450689 23.841771) (xy 88.462114 23.857496) (xy 88.542507 23.937889) (xy 88.555399 23.947256)
				(xy 88.589035 23.983644) (xy 88.606186 24.030134) (xy 88.60424 24.079649) (xy 88.583493 24.12465)
				(xy 88.555399 24.152744) (xy 88.542507 24.16211) (xy 88.462114 24.242503) (xy 88.450689 24.258228)
				(xy 88.39908 24.359516) (xy 88.393074 24.378001) (xy 88.375291 24.490281) (xy 88.375291 24.509718)
				(xy 88.393074 24.621998) (xy 88.39908 24.640483) (xy 88.450689 24.741771) (xy 88.462114 24.757496)
				(xy 88.514815 24.810197) (xy 88.542345 24.851399) (xy 88.552012 24.9) (xy 88.542345 24.948601) (xy 88.514815 24.989803)
				(xy 88.462114 25.042503) (xy 88.450689 25.058228) (xy 88.39908 25.159516) (xy 88.393074 25.178001)
				(xy 88.375291 25.290281) (xy 88.375291 25.309718) (xy 88.393074 25.421998) (xy 88.39908 25.440483)
				(xy 88.450689 25.541771) (xy 88.462114 25.557496) (xy 88.542507 25.637889) (xy 88.555399 25.647256)
				(xy 88.589035 25.683644) (xy 88.606186 25.730134) (xy 88.60424 25.779649) (xy 88.583493 25.82465)
				(xy 88.555399 25.852744) (xy 88.542507 25.86211) (xy 88.462114 25.942503) (xy 88.450689 25.958228)
				(xy 88.39908 26.059516) (xy 88.393074 26.078001) (xy 88.375291 26.190281) (xy 88.375291 26.209718)
				(xy 88.393074 26.321998) (xy 88.39908 26.340483) (xy 88.450689 26.441771) (xy 88.462114 26.457496)
				(xy 88.542503 26.537885) (xy 88.558228 26.54931) (xy 88.631889 26.586842) (xy 88.670804 26.61752)
				(xy 88.695017 26.660754) (xy 88.700842 26.709964) (xy 88.687391 26.757656) (xy 88.656713 26.796571)
				(xy 88.631889 26.813158) (xy 88.558228 26.850689) (xy 88.542503 26.862114) (xy 88.462114 26.942503)
				(xy 88.450689 26.958228) (xy 88.39908 27.059516) (xy 88.393074 27.078001) (xy 88.375291 27.190281)
				(xy 88.375291 27.209718) (xy 88.393074 27.321998) (xy 88.39908 27.340483) (xy 88.450689 27.441771)
				(xy 88.462114 27.457496) (xy 88.542503 27.537885) (xy 88.558228 27.54931) (xy 88.659516 27.600919)
				(xy 88.678001 27.606925) (xy 88.789366 27.624564) (xy 88.798091 27.627782) (xy 88.800854 27.626509)
				(xy 88.810634 27.624564) (xy 88.921998 27.606925) (xy 88.940483 27.600919) (xy 89.041771 27.54931)
				(xy 89.057496 27.537885) (xy 89.137885 27.457496) (xy 89.14931 27.441771) (xy 89.200919 27.340483)
				(xy 89.206925 27.321998) (xy 89.224709 27.209718) (xy 89.224709 27.190281) (xy 89.206925 27.078001)
				(xy 89.200919 27.059516) (xy 89.14931 26.958228) (xy 89.137885 26.942503) (xy 89.057496 26.862114)
				(xy 89.041771 26.850689) (xy 88.968111 26.813158) (xy 88.929196 26.78248) (xy 88.904983 26.739246)
				(xy 88.899158 26.690036) (xy 88.912609 26.642344) (xy 88.943287 26.603429) (xy 88.968111 26.586842)
				(xy 89.041771 26.54931) (xy 89.057496 26.537885) (xy 89.137885 26.457496) (xy 89.14931 26.441771)
				(xy 89.200919 26.340483) (xy 89.206925 26.321998) (xy 89.224709 26.209718) (xy 89.224709 26.190281)
				(xy 89.210684 26.101732) (xy 89.207537 26.081865) (xy 95.263713 26.081865) (xy 95.271887 26.17531)
				(xy 95.277461 26.19611) (xy 95.317101 26.28112) (xy 95.329455 26.298763) (xy 95.395788 26.365096)
				(xy 95.413428 26.377448) (xy 95.498442 26.417089) (xy 95.520184 26.422916) (xy 95.564626 26.444833)
				(xy 95.588068 26.468276) (xy 95.60203 26.486472) (xy 95.613523 26.497965) (xy 95.715625 26.576311)
				(xy 95.729689 26.584431) (xy 95.848592 26.633682) (xy 95.864279 26.637886) (xy 95.991882 26.654685)
				(xy 96.008118 26.654685) (xy 96.13572 26.637886) (xy 96.151407 26.633682) (xy 96.256187 26.590281)
				(xy 97.175291 26.590281) (xy 97.175291 26.609718) (xy 97.193074 26.721998) (xy 97.19908 26.740483)
				(xy 97.250689 26.841771) (xy 97.262114 26.857496) (xy 97.342503 26.937885) (xy 97.358228 26.94931)
				(xy 97.459516 27.000919) (xy 97.478001 27.006925) (xy 97.590281 27.024709) (xy 97.609719 27.024709)
				(xy 97.721998 27.006925) (xy 97.740483 27.000919) (xy 97.841771 26.94931) (xy 97.857496 26.937885)
				(xy 97.937885 26.857496) (xy 97.94931 26.841771) (xy 98.000919 26.740483) (xy 98.006925 26.721998)
				(xy 98.024709 26.609718) (xy 98.024709 26.590281) (xy 98.006925 26.478001) (xy 98.000919 26.459516)
				(xy 97.94931 26.358228) (xy 97.937885 26.342503) (xy 97.857496 26.262114) (xy 97.841771 26.250689)
				(xy 97.740483 26.19908) (xy 97.721998 26.193074) (xy 97.609719 26.175291) (xy 97.590281 26.175291)
				(xy 97.478001 26.193074) (xy 97.459516 26.19908) (xy 97.358228 26.250689) (xy 97.342503 26.262114)
				(xy 97.262114 26.342503) (xy 97.250689 26.358228) (xy 97.19908 26.459516) (xy 97.193074 26.478001)
				(xy 97.175291 26.590281) (xy 96.256187 26.590281) (xy 96.27031 26.584431) (xy 96.284374 26.576311)
				(xy 96.386482 26.497961) (xy 96.393957 26.490486) (xy 96.39797 26.486472) (xy 96.476308 26.38438)
				(xy 96.484431 26.370309) (xy 96.533682 26.251407) (xy 96.537886 26.23572) (xy 96.554685 26.108118)
				(xy 96.554685 26.091881) (xy 96.537886 25.964279) (xy 96.533682 25.948592) (xy 96.484431 25.82969)
				(xy 96.476308 25.815619) (xy 96.397967 25.713523) (xy 96.386481 25.702038) (xy 96.284374 25.623688)
				(xy 96.27031 25.615568) (xy 96.151407 25.566317) (xy 96.13572 25.562113) (xy 96.008118 25.545315)
				(xy 95.991882 25.545315) (xy 95.864279 25.562113) (xy 95.848592 25.566317) (xy 95.72969 25.615568)
				(xy 95.715619 25.623691) (xy 95.613523 25.702032) (xy 95.602037 25.713519) (xy 95.588759 25.730824)
				(xy 95.551503 25.763496) (xy 95.520873 25.776184) (xy 95.469804 25.789867) (xy 95.450283 25.79897)
				(xy 95.392833 25.839197) (xy 95.384508 25.846183) (xy 95.346179 25.884512) (xy 95.339202 25.892825)
				(xy 95.298969 25.950283) (xy 95.289867 25.969804) (xy 95.26559 26.060409) (xy 95.263713 26.081865)
				(xy 89.207537 26.081865) (xy 89.206925 26.078) (xy 89.200919 26.059516) (xy 89.14931 25.958228)
				(xy 89.137885 25.942503) (xy 89.057492 25.86211) (xy 89.044601 25.852744) (xy 89.010965 25.816356)
				(xy 88.993814 25.769866) (xy 88.99576 25.720351) (xy 89.016507 25.67535) (xy 89.044601 25.647256)
				(xy 89.057492 25.637889) (xy 89.137885 25.557496) (xy 89.14931 25.541771) (xy 89.200919 25.440483)
				(xy 89.206925 25.421998) (xy 89.224709 25.309718) (xy 89.224709 25.290281) (xy 89.206925 25.178001)
				(xy 89.200919 25.159516) (xy 89.14931 25.058228) (xy 89.137885 25.042503) (xy 89.085185 24.989803)
				(xy 89.057655 24.948601) (xy 89.047988 24.9) (xy 89.057655 24.851399) (xy 89.085185 24.810197) (xy 89.137885 24.757496)
				(xy 89.14931 24.741771) (xy 89.200919 24.640483) (xy 89.206925 24.621998) (xy 89.224709 24.509718)
				(xy 89.224709 24.490281) (xy 89.20887 24.390281) (xy 100.620481 24.390281) (xy 100.620481 24.409718)
				(xy 100.638264 24.521998) (xy 100.64427 24.540483) (xy 100.695879 24.641771) (xy 100.707304 24.657496)
				(xy 100.787693 24.737885) (xy 100.803418 24.74931) (xy 100.904706 24.800919) (xy 100.923191 24.806925)
				(xy 101.035471 24.824709) (xy 101.054909 24.824709) (xy 101.167188 24.806925) (xy 101.185673 24.800919)
				(xy 101.286961 24.74931) (xy 101.302686 24.737885) (xy 101.383075 24.657496) (xy 101.3945 24.641771)
				(xy 101.446109 24.540483) (xy 101.452115 24.521998) (xy 101.469899 24.409718) (xy 101.469899 24.390281)
				(xy 101.452115 24.278001) (xy 101.446109 24.259516) (xy 101.3945 24.158228) (xy 101.383075 24.142503)
				(xy 101.38278 24.142208) (xy 101.381854 24.140823) (xy 101.377201 24.134418) (xy 101.377452 24.134235)
				(xy 101.35525 24.101006) (xy 101.345583 24.052405) (xy 101.35525 24.003804) (xy 101.38278 23.962602)
				(xy 101.387885 23.957496) (xy 101.39931 23.941771) (xy 101.450919 23.840483) (xy 101.456925 23.821998)
				(xy 101.474709 23.709718) (xy 101.474709 23.690281) (xy 101.456925 23.578001) (xy 101.450919 23.559516)
				(xy 101.39931 23.458228) (xy 101.387885 23.442503) (xy 101.307496 23.362114) (xy 101.291771 23.350689)
				(xy 101.190483 23.29908) (xy 101.171998 23.293074) (xy 101.059719 23.275291) (xy 101.040281 23.275291)
				(xy 100.928001 23.293074) (xy 100.909516 23.29908) (xy 100.808228 23.350689) (xy 100.792503 23.362114)
				(xy 100.712114 23.442503) (xy 100.700689 23.458228) (xy 100.64908 23.559516) (xy 100.643074 23.578001)
				(xy 100.625291 23.690281) (xy 100.625291 23.709718) (xy 100.643074 23.821998) (xy 100.64908 23.840483)
				(xy 100.700689 23.941771) (xy 100.712114 23.957496) (xy 100.71241 23.957792) (xy 100.713335 23.959176)
				(xy 100.717989 23.965582) (xy 100.717737 23.965764) (xy 100.73994 23.998994) (xy 100.749607 24.047595)
				(xy 100.73994 24.096196) (xy 100.71241 24.137398) (xy 100.707304 24.142503) (xy 100.695879 24.158228)
				(xy 100.64427 24.259516) (xy 100.638264 24.278001) (xy 100.620481 24.390281) (xy 89.20887 24.390281)
				(xy 89.206925 24.378001) (xy 89.200919 24.359516) (xy 89.14931 24.258228) (xy 89.137885 24.242503)
				(xy 89.057492 24.16211) (xy 89.044601 24.152744) (xy 89.010965 24.116356) (xy 88.993814 24.069866)
				(xy 88.99576 24.020351) (xy 89.016507 23.97535) (xy 89.044601 23.947256) (xy 89.057492 23.937889)
				(xy 89.1051 23.890281) (xy 91.425291 23.890281) (xy 91.425291 23.909718) (xy 91.443074 24.021998)
				(xy 91.44908 24.040483) (xy 91.500689 24.141771) (xy 91.512114 24.157496) (xy 91.592503 24.237885)
				(xy 91.608228 24.24931) (xy 91.709516 24.300919) (xy 91.728001 24.306925) (xy 91.840281 24.324709)
				(xy 91.859719 24.324709) (xy 91.971998 24.306925) (xy 91.990483 24.300919) (xy 92.056796 24.267131)
				(xy 92.091773 24.249308) (xy 92.107496 24.237885) (xy 92.187885 24.157496) (xy 92.19931 24.141771)
				(xy 92.250919 24.040483) (xy 92.256925 24.021998) (xy 92.274709 23.909718) (xy 92.274709 23.890281)
				(xy 92.256925 23.778001) (xy 92.250919 23.759516) (xy 92.19931 23.658228) (xy 92.187885 23.642503)
				(xy 92.107496 23.562114) (xy 92.091771 23.550689) (xy 92.052002 23.530426) (xy 91.990483 23.49908)
				(xy 91.971998 23.493074) (xy 91.859719 23.475291) (xy 91.840281 23.475291) (xy 91.728001 23.493074)
				(xy 91.709516 23.49908) (xy 91.608228 23.550689) (xy 91.592503 23.562114) (xy 91.512114 23.642503)
				(xy 91.500689 23.658228) (xy 91.44908 23.759516) (xy 91.443074 23.778001) (xy 91.425291 23.890281)
				(xy 89.1051 23.890281) (xy 89.137885 23.857496) (xy 89.14931 23.841771) (xy 89.159062 23.822633)
				(xy 89.200919 23.740483) (xy 89.206925 23.721998) (xy 89.224709 23.609718) (xy 89.224709 23.590281)
				(xy 89.206925 23.478001) (xy 89.200919 23.459516) (xy 89.184484 23.42726) (xy 95.438312 23.42726)
				(xy 95.438312 23.446697) (xy 95.456095 23.558977) (xy 95.462101 23.577462) (xy 95.51371 23.67875)
				(xy 95.525135 23.694475) (xy 95.605524 23.774864) (xy 95.621249 23.786289) (xy 95.722537 23.837898)
				(xy 95.741022 23.843904) (xy 95.853302 23.861688) (xy 95.87274 23.861688) (xy 95.985019 23.843904)
				(xy 96.003504 23.837898) (xy 96.104792 23.786289) (xy 96.120517 23.774864) (xy 96.200906 23.694475)
				(xy 96.212331 23.67875) (xy 96.26394 23.577462) (xy 96.269946 23.558977) (xy 96.28773 23.446697)
				(xy 96.28773 23.42726) (xy 96.269946 23.31498) (xy 96.26394 23.296498) (xy 96.234107 23.237946)
				(xy 96.234107 23.237945) (xy 96.212331 23.195207) (xy 96.208752 23.190281) (xy 99.538101 23.190281)
				(xy 99.538101 23.209718) (xy 99.555884 23.321998) (xy 99.56189 23.340483) (xy 99.613499 23.441771)
				(xy 99.624924 23.457496) (xy 99.705313 23.537885) (xy 99.721038 23.54931) (xy 99.822326 23.600919)
				(xy 99.840811 23.606925) (xy 99.953091 23.624709) (xy 99.972529 23.624709) (xy 100.084808 23.606925)
				(xy 100.103293 23.600919) (xy 100.204581 23.54931) (xy 100.220306 23.537885) (xy 100.300695 23.457496)
				(xy 100.31212 23.441771) (xy 100.363729 23.340483) (xy 100.369735 23.321998) (xy 100.387519 23.209718)
				(xy 100.387519 23.190281) (xy 100.369735 23.078001) (xy 100.363729 23.059516) (xy 100.31212 22.958228)
				(xy 100.300695 22.942503) (xy 100.220306 22.862114) (xy 100.204581 22.850689) (xy 100.103293 22.79908)
				(xy 100.084808 22.793074) (xy 99.972529 22.775291) (xy 99.953091 22.775291) (xy 99.840811 22.793074)
				(xy 99.822326 22.79908) (xy 99.721038 22.850689) (xy 99.705313 22.862114) (xy 99.624924 22.942503)
				(xy 99.613499 22.958228) (xy 99.56189 23.059516) (xy 99.555884 23.078001) (xy 99.538101 23.190281)
				(xy 96.208752 23.190281) (xy 96.200906 23.179482) (xy 96.120517 23.099093) (xy 96.104792 23.087668)
				(xy 96.003504 23.036059) (xy 95.985019 23.030053) (xy 95.87274 23.01227) (xy 95.853302 23.01227)
				(xy 95.741022 23.030053) (xy 95.722537 23.036059) (xy 95.621249 23.087668) (xy 95.605524 23.099093)
				(xy 95.525135 23.179482) (xy 95.51371 23.195207) (xy 95.462101 23.296495) (xy 95.456095 23.31498)
				(xy 95.438312 23.42726) (xy 89.184484 23.42726) (xy 89.14931 23.358228) (xy 89.137885 23.342503)
				(xy 89.057496 23.262114) (xy 89.041771 23.250689) (xy 88.940483 23.19908) (xy 88.921998 23.193074)
				(xy 88.809719 23.175291) (xy 88.790282 23.175291) (xy 88.719898 23.186439) (xy 88.670383 23.184494)
				(xy 88.625382 23.163748) (xy 88.591745 23.127361) (xy 88.574594 23.080871) (xy 88.576539 23.031356)
				(xy 88.597285 22.986355) (xy 88.610227 22.9712) (xy 89.691146 21.890281) (xy 90.375291 21.890281)
				(xy 90.375291 21.909718) (xy 90.393074 22.021998) (xy 90.39908 22.040483) (xy 90.450689 22.141771)
				(xy 90.462114 22.157496) (xy 90.542503 22.237885) (xy 90.558228 22.24931) (xy 90.659516 22.300919)
				(xy 90.678001 22.306925) (xy 90.790281 22.324709) (xy 90.809719 22.324709) (xy 90.921998 22.306925)
				(xy 90.940483 22.300919) (xy 91.041771 22.24931) (xy 91.057496 22.237885) (xy 91.137885 22.157496)
				(xy 91.14931 22.141771) (xy 91.186842 22.068111) (xy 91.21752 22.029196) (xy 91.260754 22.004983)
				(xy 91.309964 21.999158) (xy 91.357656 22.012609) (xy 91.396571 22.043287) (xy 91.413158 22.068111)
				(xy 91.450689 22.141771) (xy 91.462114 22.157496) (xy 91.542503 22.237885) (xy 91.558228 22.24931)
				(xy 91.659516 22.300919) (xy 91.678001 22.306925) (xy 91.790281 22.324709) (xy 91.809719 22.324709)
				(xy 91.921998 22.306925) (xy 91.940483 22.300919) (xy 92.041771 22.24931) (xy 92.057496 22.237885)
				(xy 92.137885 22.157496) (xy 92.14931 22.141771) (xy 92.200919 22.040483) (xy 92.206925 22.021998)
				(xy 92.224709 21.909718) (xy 92.224709 21.890281) (xy 92.206925 21.778001) (xy 92.200919 21.759516)
				(xy 92.149307 21.658221) (xy 92.146922 21.654939) (xy 92.146922 21.65494) (xy 92.137887 21.642505)
				(xy 92.085663 21.590281) (xy 95.175291 21.590281) (xy 95.175291 21.609718) (xy 95.193074 21.721998)
				(xy 95.19908 21.740483) (xy 95.250689 21.841771) (xy 95.262114 21.857496) (xy 95.342503 21.937885)
				(xy 95.358228 21.94931) (xy 95.459516 22.000919) (xy 95.478001 22.006925) (xy 95.590281 22.024709)
				(xy 95.609719 22.024709) (xy 95.721998 22.006925) (xy 95.740483 22.000919) (xy 95.841771 21.94931)
				(xy 95.857496 21.937885) (xy 95.937885 21.857496) (xy 95.94931 21.841771) (xy 96.000919 21.740483)
				(xy 96.006925 21.721998) (xy 96.024709 21.609718) (xy 96.024709 21.590281) (xy 96.006925 21.478001)
				(xy 96.000919 21.459516) (xy 95.94931 21.358228) (xy 95.937885 21.342503) (xy 95.857496 21.262114)
				(xy 95.841771 21.250689) (xy 95.740483 21.19908) (xy 95.721998 21.193074) (xy 95.609719 21.175291)
				(xy 95.590281 21.175291) (xy 95.478001 21.193074) (xy 95.459516 21.19908) (xy 95.358228 21.250689)
				(xy 95.342503 21.262114) (xy 95.262114 21.342503) (xy 95.250689 21.358228) (xy 95.19908 21.459516)
				(xy 95.193074 21.478001) (xy 95.175291 21.590281) (xy 92.085663 21.590281) (xy 92.057496 21.562114)
				(xy 92.041771 21.550689) (xy 91.940483 21.49908) (xy 91.921998 21.493074) (xy 91.809719 21.475291)
				(xy 91.790281 21.475291) (xy 91.678001 21.493074) (xy 91.659516 21.49908) (xy 91.558228 21.550689)
				(xy 91.542503 21.562114) (xy 91.462114 21.642503) (xy 91.450689 21.658228) (xy 91.413158 21.731889)
				(xy 91.38248 21.770804) (xy 91.339246 21.795017) (xy 91.290036 21.800842) (xy 91.242344 21.787391)
				(xy 91.203429 21.756713) (xy 91.186842 21.731889) (xy 91.14931 21.658228) (xy 91.137885 21.642503)
				(xy 91.057496 21.562114) (xy 91.041771 21.550689) (xy 90.940483 21.49908) (xy 90.921998 21.493074)
				(xy 90.809719 21.475291) (xy 90.790281 21.475291) (xy 90.678001 21.493074) (xy 90.659516 21.49908)
				(xy 90.558228 21.550689) (xy 90.542503 21.562114) (xy 90.462114 21.642503) (xy 90.450689 21.658228)
				(xy 90.39908 21.759516) (xy 90.393074 21.778001) (xy 90.375291 21.890281) (xy 89.691146 21.890281)
				(xy 91.515204 20.066223) (xy 91.525946 20.056636) (xy 91.535855 20.048753) (xy 91.544157 20.038373)
				(xy 91.54996 20.031886) (xy 91.562321 20.01639) (xy 91.566981 20.009832) (xy 91.574979 19.99983)
				(xy 91.58083 19.987727) (xy 91.585466 19.979355) (xy 91.587126 19.975358) (xy 91.589782 19.966172)
				(xy 91.594226 19.95348) (xy 91.595665 19.940755) (xy 91.597071 19.932523) (xy 91.599321 19.912702)
				(xy 91.599804 19.904143) (xy 91.601263 19.891235) (xy 91.599791 19.878111) (xy 91.599 19.863954)
				(xy 91.599 16.405876) (xy 91.599032 16.368852) (xy 91.591798 16.337011) (xy 91.589314 16.329898)
				(xy 91.572735 16.29545) (xy 91.5693 16.289974) (xy 91.54551 16.260067) (xy 91.540189 16.254737)
				(xy 91.510308 16.230883) (xy 91.50485 16.227446) (xy 91.470436 16.210809) (xy 91.463328 16.208313)
				(xy 91.426059 16.199777) (xy 91.419647 16.199046) (xy 91.37901 16.198976) (xy 91.369267 16.198584)
				(xy 91.190799 16.184538) (xy 91.171115 16.181421) (xy 90.976783 16.134766) (xy 90.95783 16.128608)
				(xy 90.773193 16.052129) (xy 90.755436 16.043081) (xy 90.585033 15.938657) (xy 90.568911 15.926943)
				(xy 90.416942 15.797149) (xy 90.402851 15.783058) (xy 90.273057 15.631089) (xy 90.261343 15.614967)
				(xy 90.156919 15.444564) (xy 90.147871 15.426806) (xy 90.107313 15.32889) (xy 90.09132 15.290281)
				(xy 91.275291 15.290281) (xy 91.275291 15.309718) (xy 91.293074 15.421998) (xy 91.29908 15.440483)
				(xy 91.350689 15.541771) (xy 91.362114 15.557496) (xy 91.442503 15.637885) (xy 91.458228 15.64931)
				(xy 91.559516 15.700919) (xy 91.578001 15.706925) (xy 91.690281 15.724709) (xy 91.709719 15.724709)
				(xy 91.821998 15.706925) (xy 91.840483 15.700919) (xy 91.941771 15.64931) (xy 91.957496 15.637885)
				(xy 92.037885 15.557496) (xy 92.04931 15.541771) (xy 92.100919 15.440483) (xy 92.106925 15.421998)
				(xy 92.124709 15.309718) (xy 92.124709 15.290281) (xy 92.106925 15.178001) (xy 92.100919 15.159516)
				(xy 92.04931 15.058228) (xy 92.037885 15.042503) (xy 91.957496 14.962114) (xy 91.941771 14.950689)
				(xy 91.840483 14.89908) (xy 91.821998 14.893074) (xy 91.709719 14.875291) (xy 91.690281 14.875291)
				(xy 91.578001 14.893074) (xy 91.559516 14.89908) (xy 91.458228 14.950689) (xy 91.442503 14.962114)
				(xy 91.362114 15.042503) (xy 91.350689 15.058228) (xy 91.29908 15.159516) (xy 91.293074 15.178001)
				(xy 91.275291 15.290281) (xy 90.09132 15.290281) (xy 90.071392 15.24217) (xy 90.065234 15.223217)
				(xy 90.018579 15.028885) (xy 90.015461 15.009201) (xy 89.999781 14.809964) (xy 89.999781 14.790036)
				(xy 90.015461 14.590799) (xy 90.018579 14.571115) (xy 90.065234 14.376783) (xy 90.071392 14.35783)
				(xy 90.147871 14.173193) (xy 90.156919 14.155436) (xy 90.196846 14.090281) (xy 91.275291 14.090281)
				(xy 91.275291 14.109718) (xy 91.293074 14.221998) (xy 91.29908 14.240483) (xy 91.350689 14.341771)
				(xy 91.362114 14.357496) (xy 91.442503 14.437885) (xy 91.458228 14.44931) (xy 91.559516 14.500919)
				(xy 91.578001 14.506925) (xy 91.690281 14.524709) (xy 91.709719 14.524709) (xy 91.821998 14.506925)
				(xy 91.840483 14.500919) (xy 91.941771 14.44931) (xy 91.957496 14.437885) (xy 92.037885 14.357496)
				(xy 92.04931 14.341771) (xy 92.100919 14.240483) (xy 92.106925 14.221998) (xy 92.124709 14.109718)
				(xy 92.124709 14.090281) (xy 92.106925 13.978001) (xy 92.100919 13.959516) (xy 92.04931 13.858228)
				(xy 92.037885 13.842503) (xy 91.957496 13.762114) (xy 91.941771 13.750689) (xy 91.840483 13.69908)
				(xy 91.821998 13.693074) (xy 91.709719 13.675291) (xy 91.690281 13.675291) (xy 91.578001 13.693074)
				(xy 91.559516 13.69908) (xy 91.458228 13.750689) (xy 91.442503 13.762114) (xy 91.362114 13.842503)
				(xy 91.350689 13.858228) (xy 91.29908 13.959516) (xy 91.293074 13.978001) (xy 91.275291 14.090281)
				(xy 90.196846 14.090281) (xy 90.243634 14.013931) (xy 90.261343 13.985033) (xy 90.273057 13.968911)
				(xy 90.402851 13.816942) (xy 90.416942 13.802851) (xy 90.568911 13.673057) (xy 90.585033 13.661343)
				(xy 90.755436 13.556919) (xy 90.773193 13.547871) (xy 90.95783 13.471392) (xy 90.976783 13.465234)
				(xy 91.171115 13.418579) (xy 91.190798 13.415462) (xy 91.370179 13.401344) (xy 91.38037 13.400952)
				(xy 91.41837 13.401019) (xy 91.436448 13.399) (xy 99.162976 13.399)
			)
		)
	)
	(embedded_fonts no)
)
