(kicad_sch (version 20221004) (generator eeschema)

  (uuid 0c9c3a92-6a4e-4a5b-b607-2cfe0f3c4b2e)

  (paper "A4")

  (lib_symbols
    (symbol "Device:C" (pin_numbers hide) (pin_names (offset 0.254)) (in_bom yes) (on_board yes)
      (property "Reference" "C" (at 0.635 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "C" (at 0.635 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0.9652 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "cap capacitor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Unpolarized capacitor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "C_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "C_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy 2.032 -0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
      )
      (symbol "C_1_1"
        (pin passive line (at 0 3.81 270) (length 2.794)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 2.794)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:D" (pin_numbers hide) (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "D" (at 0 2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "D" (at 0 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "diode" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Diode" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "TO-???* *_Diode_* *SingleDiode* D_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "D_0_1"
        (polyline
          (pts
            (xy -1.27 1.27)
            (xy -1.27 -1.27)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 0)
            (xy -1.27 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 1.27)
            (xy 1.27 -1.27)
            (xy -1.27 0)
            (xy 1.27 1.27)
          )
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "D_1_1"
        (pin passive line (at -3.81 0 0) (length 2.54)
          (name "K" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 3.81 0 180) (length 2.54)
          (name "A" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VSIN" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VSIN" (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "sin(0 1 1k)" (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, sinusoidal" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VSIN_0_0"
        (arc (start 0 0) (mid -0.635 0.6323) (end -1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0) (mid 0.635 -0.6323) (end 1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VSIN_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VSIN_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 155.575 96.52) (diameter 0) (color 0 0 0 0)
    (uuid 90298255-a103-4111-b99f-6839e4ed2aa3)
  )

  (wire (pts (xy 155.575 96.52) (xy 165.1 96.52))
    (stroke (width 0) (type default))
    (uuid 938ac8bd-5d33-45d4-a671-e203531936df)
  )
  (wire (pts (xy 134.62 96.52) (xy 147.955 96.52))
    (stroke (width 0) (type default))
    (uuid f208d8ac-c356-4e7f-8843-f10eed82b140)
  )

  (text ".tran 1u 10m" (at 134.62 88.265 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid b0eb3c42-1695-4c39-a9fd-fa13617eced3)
  )

  (label "in" (at 134.62 96.52 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 0638f2a4-6aba-46f5-93c4-8c70a83aa927)
  )
  (label "out" (at 165.1 96.52 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 534a5091-dddc-4654-962b-04a61e3980a0)
  )

  (symbol (lib_id "power:GND") (at 134.62 106.68 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 125bcfd6-fc91-456c-8753-fecb0425cb16)
    (property "Reference" "#PWR0103" (at 134.62 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 134.62 111.76 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 134.62 106.68 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 134.62 106.68 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 2e7c6b87-8ca0-4b4f-b706-9b46d00816ac))
    (instances
      (project "rectifier"
        (path "/0c9c3a92-6a4e-4a5b-b607-2cfe0f3c4b2e"
          (reference "#PWR0103") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 165.1 104.14 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 2c87bb46-80f5-4e71-b72e-b1b229611c8f)
    (property "Reference" "#PWR0101" (at 165.1 110.49 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 165.1 109.22 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 165.1 104.14 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 165.1 104.14 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 280fc63a-0d9f-48b6-95f7-c5e61dae283d))
    (instances
      (project "rectifier"
        (path "/0c9c3a92-6a4e-4a5b-b607-2cfe0f3c4b2e"
          (reference "#PWR0101") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VSIN") (at 134.62 101.6 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 2f4ce84c-7613-4135-9bc6-7662b55052cf)
    (property "Reference" "V1" (at 119.38 99.06 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "${Sim.Device} ${Sim.Type}" (at 119.38 101.6 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 134.62 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 134.62 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 134.62 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Type" "SIN" (at 134.62 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "ampl=5 f=1k" (at 124.46 104.14 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid a29d8f63-65fa-4d69-b0d4-cfa5c00fc5ab))
    (pin "2" (uuid 8bfe05fb-6889-4435-80ba-ab2128b151d8))
    (instances
      (project "rectifier"
        (path "/0c9c3a92-6a4e-4a5b-b607-2cfe0f3c4b2e"
          (reference "V1") (unit 1) (value "${Sim.Device} ${Sim.Type}") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 155.575 100.33 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 36c45a8c-57fb-4d16-8113-60be934d3024)
    (property "Reference" "C1" (at 159.385 99.695 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "10u" (at 159.385 102.235 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 156.5402 104.14 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 155.575 100.33 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "C" (at 155.575 100.33 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 0f0333bf-611a-47ec-bd68-52c7b166c571))
    (pin "2" (uuid b71649f6-4e0d-4ac7-a45e-e31d214c664b))
    (instances
      (project "rectifier"
        (path "/0c9c3a92-6a4e-4a5b-b607-2cfe0f3c4b2e"
          (reference "C1") (unit 1) (value "10u") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:D") (at 151.765 96.52 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 37eb4620-c4e1-45b7-92da-1117151d60cc)
    (property "Reference" "D1" (at 151.765 91.44 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "D" (at 151.765 93.98 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 151.765 96.52 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 151.765 96.52 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "D" (at 151.765 96.52 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Name" "DIODE1" (at 151.765 96.52 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Library" "diode.lib" (at 151.765 96.52 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Pins" "2=A 1=K" (at 151.765 96.52 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid cb1986eb-8b78-41be-ab05-5001090a2a65))
    (pin "2" (uuid edfbe61b-6d1d-45a6-bfef-e48a694e79db))
    (instances
      (project "rectifier"
        (path "/0c9c3a92-6a4e-4a5b-b607-2cfe0f3c4b2e"
          (reference "D1") (unit 1) (value "D") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 165.1 100.33 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 7ae05049-58f1-4d14-9a95-35ba665c7986)
    (property "Reference" "R1" (at 167.64 99.695 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "10K" (at 167.64 102.235 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 163.322 100.33 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 165.1 100.33 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 165.1 100.33 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 908dc772-f335-42cf-8fc9-474d99c97d6b))
    (pin "2" (uuid 8284393b-9c35-4de1-912f-0588e9c9883b))
    (instances
      (project "rectifier"
        (path "/0c9c3a92-6a4e-4a5b-b607-2cfe0f3c4b2e"
          (reference "R1") (unit 1) (value "10K") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 155.575 104.14 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid c5967cbe-0914-4e7b-92aa-1bc61c11d83b)
    (property "Reference" "#PWR0102" (at 155.575 110.49 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 155.575 109.22 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 155.575 104.14 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 155.575 104.14 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7706233c-3e24-4ca2-898e-587d4e9865df))
    (instances
      (project "rectifier"
        (path "/0c9c3a92-6a4e-4a5b-b607-2cfe0f3c4b2e"
          (reference "#PWR0102") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (sheet_instances
    (path "/0c9c3a92-6a4e-4a5b-b607-2cfe0f3c4b2e" (page "1"))
  )
)
