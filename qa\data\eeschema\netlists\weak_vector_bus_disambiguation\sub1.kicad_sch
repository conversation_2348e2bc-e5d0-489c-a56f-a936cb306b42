(kicad_sch (version 20200828) (generator eeschema)

  (page 2 4)

  (paper "A4")

  (lib_symbols
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254)) (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )


  (bus_entry (at 146.05 78.74) (size 2.54 2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 146.05 88.9) (size 2.54 2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )

  (wire (pts (xy 148.59 81.28) (xy 163.83 81.28))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 148.59 91.44) (xy 163.83 91.44))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 163.83 81.28) (xy 163.83 82.55))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 163.83 90.17) (xy 163.83 91.44))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 127 76.2) (xy 146.05 76.2))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 146.05 76.2) (xy 146.05 78.74))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 146.05 78.74) (xy 146.05 88.9))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 146.05 88.9) (xy 146.05 96.52))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )

  (label "B1" (at 151.13 81.28 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "B2" (at 151.13 91.44 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )

  (hierarchical_label "B[10..0]" (shape input) (at 127 76.2 180)
    (effects (font (size 1.27 1.27)) (justify right))
  )

  (symbol (lib_id "Device:R") (at 163.83 86.36 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid "549fed29-2fe3-4199-8781-d64e3d55522a")
    (property "Reference" "R1" (id 0) (at 165.608 85.217 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 165.6081 87.5093 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 162.052 86.36 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 163.83 86.36 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )
)
