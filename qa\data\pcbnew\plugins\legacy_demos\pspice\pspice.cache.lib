EESchema-LIBRARY Version  18/6/2008-08:34:30
#
#
# +12V
#
DEF +12V #PWR 0 0 N Y 1 F N
F0 "#PWR" 0 200 40 H I C C
F1 "+12V" 0 150 40 H V C C
DRAW
P 2 0 1 0  0 60  0 0 N
C 0 80 20 0 1 0 N
X +12V 1 0 0 0 R 40 40 0 0 W N
ENDDRAW
ENDDEF
#
# 0
#
DEF 0 #GND 0 0 Y Y 1 F N
F0 "#GND" 0 -100 40 H I C C
F1 "0" 0 -70 40 H V C C
DRAW
P 4 0 1 0  -50 0  0 -50  50 0  -50 0 N
X 0 1 0 0 0 R 40 40 1 1 W N
ENDDRAW
ENDDEF
#
# CAP
#
DEF CAP C 0 10 Y Y 1 F N
F0 "C" 100 150 50 V V C C
F1 "CAP" 100 -150 50 V V C C
ALIAS C
DRAW
P 2 0 1 0  -150 -50  150 -50 N
P 2 0 1 0  -150 50  150 50 N
X ~ 1 0 250 200 D 40 40 1 1 P
X ~ 2 0 -250 200 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# INDUCTOR
#
DEF INDUCTOR L 0 0 N Y 0 F N
F0 "L" 0 100 70 H V C C
F1 "INDUCTOR" 0 -100 70 H V C C
DRAW
A -300 0 100 0 1800 0 0 0 N -200 0 -400 0
A -100 0 100 0 1800 0 0 0 N 0 0 -200 0
A 100 0 100 0 1800 0 0 0 N 200 0 0 0
A 300 0 100 0 1800 0 0 0 N 400 0 200 0
X 2 2 700 0 300 L 70 70 1 1 I
X 1 1 -700 0 300 R 70 70 1 1 I
ENDDRAW
ENDDEF
#
# QNPN
#
DEF QNPN Q 0 0 Y Y 0 F N
F0 "Q" -100 300 50 H V C C
F1 "QNPN" -100 200 50 H V C C
DRAW
P 4 0 0 0  150 -150  150 -50  50 -150  150 -150 F
P 4 0 1 0  -100 -150  0 -150  0 -150  0 -150 N
P 2 0 0 0  0 0  150 -150 N
P 2 0 1 0  0 0  150 150 N
P 2 0 1 0  0 -150  0 150 N
X C 1 150 350 200 D 40 40 1 1 P
X B 2 -300 0 300 R 40 40 1 1 I
X Substrat 4 -100 -350 200 U 50 20 1 1 I
X E 3 150 -350 200 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# R
#
DEF R R 0 0 N Y 0 F N
F0 "R" 80 0 50 V V C C
F1 "R" 0 0 50 V V C C
DRAW
S -40 150 40 -150 0 1 0 N
X ~ 1 0 250 100 D 60 60 1 1 P
X ~ 2 0 -250 100 U 60 60 1 1 P
ENDDRAW
ENDDEF
#
# VSOURCE
#
DEF VSOURCE V 0 40 Y Y 1 F N
F0 "V" 0 -100 60 H V C C
F1 "VSOURCE" 0 100 60 H V C C
DRAW
P 3 0 1 0  -300 150  -250 250  -200 150 F
P 2 0 1 0  -250 -250  -250 150 F
C 0 0 400 0 1 0 N
T 0 -320 -10 100 0 0 1 V
X E1 1 0 700 300 D 60 60 1 1 I
X E2 2 0 -700 300 U 60 60 1 1 I
ENDDRAW
ENDDEF
#
#EndLibrary
