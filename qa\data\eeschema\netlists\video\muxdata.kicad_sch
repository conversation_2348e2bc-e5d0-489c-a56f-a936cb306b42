(kicad_sch (version 20200512) (host eeschema "(5.99.0-1735-ga49fde5ce-dirty)")

  (page 7 8)

  (paper "A4")

  (title_block
    (title "Video")
    (date "Sun 22 Mar 2015")
    (rev "2.0B")
    (company "Kicad EDA")
  )

  (lib_symbols
    (symbol "video_schlib:74LS245" (pin_names (offset 0.254))
      (property "Reference" "U" (id 0) (at 2.54 14.605 0)
        (effects (font (size 1.27 1.27)) (justify left bottom))
      )
      (property "Value" "74LS245" (id 1) (at 1.27 -14.605 0)
        (effects (font (size 1.27 1.27)) (justify left top))
      )
      (property "Footprint" "" (id 2) (at 1.27 -16.51 0)
        (effects (font (size 0.762 0.762)) (justify left))
      )
      (property "Datasheet" "" (id 3) (at 2.54 14.605 0)
        (effects (font (size 0.762 0.762)))
      )
      (symbol "74LS245_0_1"
        (rectangle (start -10.16 13.97) (end 10.16 -13.97))
        (polyline
          (pts (xy 1.27 2.54) (xy 0 -2.54) (xy -2.54 -2.54))
        )
        (polyline
          (pts (xy 2.54 2.54) (xy -1.27 2.54) (xy -2.54 -2.54) (xy -3.81 -2.54))
        )
      )
      (symbol "74LS245_0_0"
        (pin power_in line (at 0 -13.97 90) (length 0)
          (name "GND" (effects (font (size 1.524 1.524))))
          (number "10" (effects (font (size 1.524 1.524))))
           hide        )
        (pin power_in line (at 0 13.97 270) (length 0)
          (name "VCC" (effects (font (size 1.524 1.524))))
          (number "20" (effects (font (size 1.524 1.524))))
           hide        )
      )
      (symbol "74LS245_1_1"
        (pin input line (at -17.78 -10.16 0) (length 7.62)
          (name "A->B" (effects (font (size 1.524 1.524))))
          (number "1" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at 17.78 -5.08 180) (length 7.62)
          (name "B7" (effects (font (size 1.524 1.524))))
          (number "11" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at 17.78 -2.54 180) (length 7.62)
          (name "B6" (effects (font (size 1.524 1.524))))
          (number "12" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at 17.78 0 180) (length 7.62)
          (name "B5" (effects (font (size 1.524 1.524))))
          (number "13" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at 17.78 2.54 180) (length 7.62)
          (name "B4" (effects (font (size 1.524 1.524))))
          (number "14" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at 17.78 5.08 180) (length 7.62)
          (name "B3" (effects (font (size 1.524 1.524))))
          (number "15" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at 17.78 7.62 180) (length 7.62)
          (name "B2" (effects (font (size 1.524 1.524))))
          (number "16" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at 17.78 10.16 180) (length 7.62)
          (name "B1" (effects (font (size 1.524 1.524))))
          (number "17" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at 17.78 12.7 180) (length 7.62)
          (name "B0" (effects (font (size 1.524 1.524))))
          (number "18" (effects (font (size 1.524 1.524))))
        )
        (pin input inverted (at -17.78 -12.7 0) (length 7.62)
          (name "CE" (effects (font (size 1.524 1.524))))
          (number "19" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at -17.78 12.7 0) (length 7.62)
          (name "A0" (effects (font (size 1.524 1.524))))
          (number "2" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at -17.78 10.16 0) (length 7.62)
          (name "A1" (effects (font (size 1.524 1.524))))
          (number "3" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at -17.78 7.62 0) (length 7.62)
          (name "A2" (effects (font (size 1.524 1.524))))
          (number "4" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at -17.78 5.08 0) (length 7.62)
          (name "A3" (effects (font (size 1.524 1.524))))
          (number "5" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at -17.78 2.54 0) (length 7.62)
          (name "A4" (effects (font (size 1.524 1.524))))
          (number "6" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at -17.78 0 0) (length 7.62)
          (name "A5" (effects (font (size 1.524 1.524))))
          (number "7" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at -17.78 -2.54 0) (length 7.62)
          (name "A6" (effects (font (size 1.524 1.524))))
          (number "8" (effects (font (size 1.524 1.524))))
        )
        (pin tri_state line (at -17.78 -5.08 0) (length 7.62)
          (name "A7" (effects (font (size 1.524 1.524))))
          (number "9" (effects (font (size 1.524 1.524))))
        )
      )
    )
    (symbol "video_schlib:VCC" (power) (pin_names (offset 0))
      (property "Reference" "#PWR" (id 0) (at 0 2.54 0)
        (effects (font (size 0.762 0.762)) hide)
      )
      (property "Value" "VCC" (id 1) (at 0 2.54 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "VCC_0_1"
        (circle (center 0 1.27) (radius 0.508))
        (polyline
          (pts (xy 0 0) (xy 0 0.762) (xy 0 0.762))
        )
      )
      (symbol "VCC_0_0"
        (pin power_in line (at 0 0 90) (length 0)
          (name "VCC" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
           hide        )
      )
    )
    (symbol "video_schlib:XC4003-VQ100"
      (property "Reference" "U" (id 0) (at 0 2.54 0)
        (effects (font (size 1.778 1.778)))
      )
      (property "Value" "XC4003-VQ100" (id 1) (at 0 -2.54 0)
        (effects (font (size 1.778 1.778)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "XC4003-VQ100_0_1"
        (rectangle (start -16.51 -59.69) (end 16.51 59.69))
      )
      (symbol "XC4003-VQ100_1_1"
        (pin power_in line (at -13.97 -59.69 90) (length 0)
          (name "GND" (effects (font (size 1.524 1.524))))
          (number "1" (effects (font (size 1.524 1.524))))
           hide        )
        (pin passive line (at -24.13 8.89 0) (length 7.62)
          (name "P/A4" (effects (font (size 1.524 1.524))))
          (number "10" (effects (font (size 1.524 1.524))))
        )
        (pin power_in line (at 3.81 59.69 270) (length 0)
          (name "VCC" (effects (font (size 1.524 1.524))))
          (number "100" (effects (font (size 1.524 1.524))))
           hide        )
        (pin power_in line (at -11.43 -59.69 90) (length 0)
          (name "GND" (effects (font (size 1.524 1.524))))
          (number "11" (effects (font (size 1.524 1.524))))
           hide        )
        (pin power_in line (at -13.97 59.69 270) (length 0)
          (name "VCC" (effects (font (size 1.524 1.524))))
          (number "12" (effects (font (size 1.524 1.524))))
           hide        )
        (pin bidirectional line (at -24.13 3.81 0) (length 7.62)
          (name "P13" (effects (font (size 1.524 1.524))))
          (number "13" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 1.27 0) (length 7.62)
          (name "P14" (effects (font (size 1.524 1.524))))
          (number "14" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -1.27 0) (length 7.62)
          (name "P15" (effects (font (size 1.524 1.524))))
          (number "15" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -3.81 0) (length 7.62)
          (name "P16" (effects (font (size 1.524 1.524))))
          (number "16" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -6.35 0) (length 7.62)
          (name "P17" (effects (font (size 1.524 1.524))))
          (number "17" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -8.89 0) (length 7.62)
          (name "P18" (effects (font (size 1.524 1.524))))
          (number "18" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -11.43 0) (length 7.62)
          (name "P19" (effects (font (size 1.524 1.524))))
          (number "19" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 29.21 0) (length 7.62)
          (name "PGCK1" (effects (font (size 1.524 1.524))))
          (number "2" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -13.97 0) (length 7.62)
          (name "P20" (effects (font (size 1.524 1.524))))
          (number "20" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -16.51 0) (length 7.62)
          (name "SGCK2" (effects (font (size 1.524 1.524))))
          (number "21" (effects (font (size 1.524 1.524))))
        )
        (pin input line (at -24.13 54.61 0) (length 7.62)
          (name "M1/RD" (effects (font (size 1.524 1.524))))
          (number "22" (effects (font (size 1.524 1.524))))
        )
        (pin power_in line (at -8.89 -59.69 90) (length 0)
          (name "GND" (effects (font (size 1.524 1.524))))
          (number "23" (effects (font (size 1.524 1.524))))
           hide        )
        (pin input line (at -24.13 57.15 0) (length 7.62)
          (name "M0/RT" (effects (font (size 1.524 1.524))))
          (number "24" (effects (font (size 1.524 1.524))))
        )
        (pin power_in line (at -11.43 59.69 270) (length 0)
          (name "VCC" (effects (font (size 1.524 1.524))))
          (number "25" (effects (font (size 1.524 1.524))))
           hide        )
        (pin passive line (at -24.13 52.07 0) (length 7.62)
          (name "M2" (effects (font (size 1.524 1.524))))
          (number "26" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -24.13 0) (length 7.62)
          (name "PGCK2" (effects (font (size 1.524 1.524))))
          (number "27" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 34.29 0) (length 7.62)
          (name "P/HDC" (effects (font (size 1.524 1.524))))
          (number "28" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -29.21 0) (length 7.62)
          (name "P29" (effects (font (size 1.524 1.524))))
          (number "29" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 26.67 0) (length 7.62)
          (name "P/A17" (effects (font (size 1.524 1.524))))
          (number "3" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 31.75 0) (length 7.62)
          (name "P/LDC" (effects (font (size 1.524 1.524))))
          (number "30" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -34.29 0) (length 7.62)
          (name "P31" (effects (font (size 1.524 1.524))))
          (number "31" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -36.83 0) (length 7.62)
          (name "P32" (effects (font (size 1.524 1.524))))
          (number "32" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -39.37 0) (length 7.62)
          (name "P33" (effects (font (size 1.524 1.524))))
          (number "33" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -41.91 0) (length 7.62)
          (name "P34" (effects (font (size 1.524 1.524))))
          (number "34" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -44.45 0) (length 7.62)
          (name "P35" (effects (font (size 1.524 1.524))))
          (number "35" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -46.99 0) (length 7.62)
          (name "P36/INIT" (effects (font (size 1.524 1.524))))
          (number "36" (effects (font (size 1.524 1.524))))
        )
        (pin power_in line (at -8.89 59.69 270) (length 0)
          (name "VCC" (effects (font (size 1.524 1.524))))
          (number "37" (effects (font (size 1.524 1.524))))
           hide        )
        (pin power_in line (at -6.35 -59.69 90) (length 0)
          (name "GND" (effects (font (size 1.524 1.524))))
          (number "38" (effects (font (size 1.524 1.524))))
           hide        )
        (pin passive line (at -24.13 -52.07 0) (length 7.62)
          (name "P39" (effects (font (size 1.524 1.524))))
          (number "39" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 24.13 0) (length 7.62)
          (name "P/TDI" (effects (font (size 1.524 1.524))))
          (number "4" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 -54.61 0) (length 7.62)
          (name "P40" (effects (font (size 1.524 1.524))))
          (number "40" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -57.15 180) (length 7.62)
          (name "P41" (effects (font (size 1.524 1.524))))
          (number "41" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -54.61 180) (length 7.62)
          (name "P42" (effects (font (size 1.524 1.524))))
          (number "42" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -52.07 180) (length 7.62)
          (name "P43" (effects (font (size 1.524 1.524))))
          (number "43" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -49.53 180) (length 7.62)
          (name "P44" (effects (font (size 1.524 1.524))))
          (number "44" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -46.99 180) (length 7.62)
          (name "P45" (effects (font (size 1.524 1.524))))
          (number "45" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -44.45 180) (length 7.62)
          (name "P46" (effects (font (size 1.524 1.524))))
          (number "46" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -41.91 180) (length 7.62)
          (name "P47" (effects (font (size 1.524 1.524))))
          (number "47" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -39.37 180) (length 7.62)
          (name "SGCK3" (effects (font (size 1.524 1.524))))
          (number "48" (effects (font (size 1.524 1.524))))
        )
        (pin power_in line (at -3.81 -59.69 90) (length 0)
          (name "GND" (effects (font (size 1.524 1.524))))
          (number "49" (effects (font (size 1.524 1.524))))
           hide        )
        (pin passive line (at -24.13 21.59 0) (length 7.62)
          (name "P/TCK" (effects (font (size 1.524 1.524))))
          (number "5" (effects (font (size 1.524 1.524))))
        )
        (pin open_collector line (at -24.13 44.45 0) (length 7.62)
          (name "DONE" (effects (font (size 1.524 1.524))))
          (number "50" (effects (font (size 1.524 1.524))))
        )
        (pin power_in line (at -6.35 59.69 270) (length 0)
          (name "VCC" (effects (font (size 1.524 1.524))))
          (number "51" (effects (font (size 1.524 1.524))))
           hide        )
        (pin input inverted (at -24.13 46.99 0) (length 7.62)
          (name "PROG" (effects (font (size 1.524 1.524))))
          (number "52" (effects (font (size 1.524 1.524))))
        )
        (pin bidirectional line (at 24.13 -36.83 180) (length 7.62)
          (name "P53" (effects (font (size 1.524 1.524))))
          (number "53" (effects (font (size 1.524 1.524))))
        )
        (pin bidirectional line (at 24.13 -34.29 180) (length 7.62)
          (name "PGCK3" (effects (font (size 1.524 1.524))))
          (number "54" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -31.75 180) (length 7.62)
          (name "P55" (effects (font (size 1.524 1.524))))
          (number "55" (effects (font (size 1.524 1.524))))
        )
        (pin bidirectional line (at 24.13 -29.21 180) (length 7.62)
          (name "P56" (effects (font (size 1.524 1.524))))
          (number "56" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -26.67 180) (length 7.62)
          (name "P57" (effects (font (size 1.524 1.524))))
          (number "57" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -24.13 180) (length 7.62)
          (name "P58" (effects (font (size 1.524 1.524))))
          (number "58" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -21.59 180) (length 7.62)
          (name "P59" (effects (font (size 1.524 1.524))))
          (number "59" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 19.05 0) (length 7.62)
          (name "P/A3" (effects (font (size 1.524 1.524))))
          (number "6" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -19.05 180) (length 7.62)
          (name "P60" (effects (font (size 1.524 1.524))))
          (number "60" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -16.51 180) (length 7.62)
          (name "P61" (effects (font (size 1.524 1.524))))
          (number "61" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -13.97 180) (length 7.62)
          (name "P62" (effects (font (size 1.524 1.524))))
          (number "62" (effects (font (size 1.524 1.524))))
        )
        (pin power_in line (at -3.81 59.69 270) (length 0)
          (name "VCC" (effects (font (size 1.524 1.524))))
          (number "63" (effects (font (size 1.524 1.524))))
           hide        )
        (pin power_in line (at -1.27 -59.69 90) (length 0)
          (name "GND" (effects (font (size 1.524 1.524))))
          (number "64" (effects (font (size 1.524 1.524))))
           hide        )
        (pin passive line (at 24.13 -11.43 180) (length 7.62)
          (name "P65" (effects (font (size 1.524 1.524))))
          (number "65" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -8.89 180) (length 7.62)
          (name "P66" (effects (font (size 1.524 1.524))))
          (number "66" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -6.35 180) (length 7.62)
          (name "P67" (effects (font (size 1.524 1.524))))
          (number "67" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -3.81 180) (length 7.62)
          (name "P68" (effects (font (size 1.524 1.524))))
          (number "68" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 -1.27 180) (length 7.62)
          (name "P69" (effects (font (size 1.524 1.524))))
          (number "69" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 16.51 0) (length 7.62)
          (name "P7" (effects (font (size 1.524 1.524))))
          (number "7" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 1.27 180) (length 7.62)
          (name "P70" (effects (font (size 1.524 1.524))))
          (number "70" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 3.81 180) (length 7.62)
          (name "P71/RDY" (effects (font (size 1.524 1.524))))
          (number "71" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 41.91 0) (length 7.62)
          (name "DIN" (effects (font (size 1.524 1.524))))
          (number "72" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 39.37 0) (length 7.62)
          (name "DOUT/SGCK4" (effects (font (size 1.524 1.524))))
          (number "73" (effects (font (size 1.524 1.524))))
        )
        (pin input clock (at -24.13 36.83 0) (length 7.62)
          (name "CCLK" (effects (font (size 1.524 1.524))))
          (number "74" (effects (font (size 1.524 1.524))))
        )
        (pin power_in line (at -1.27 59.69 270) (length 0)
          (name "VCC" (effects (font (size 1.524 1.524))))
          (number "75" (effects (font (size 1.524 1.524))))
           hide        )
        (pin output line (at 24.13 6.35 180) (length 7.62)
          (name "TDO" (effects (font (size 1.524 1.524))))
          (number "76" (effects (font (size 1.524 1.524))))
        )
        (pin power_in line (at 1.27 -59.69 90) (length 0)
          (name "GND" (effects (font (size 1.524 1.524))))
          (number "77" (effects (font (size 1.524 1.524))))
           hide        )
        (pin passive line (at 24.13 8.89 180) (length 7.62)
          (name "P78" (effects (font (size 1.524 1.524))))
          (number "78" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 11.43 180) (length 7.62)
          (name "PGCK4" (effects (font (size 1.524 1.524))))
          (number "79" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at -24.13 13.97 0) (length 7.62)
          (name "P8" (effects (font (size 1.524 1.524))))
          (number "8" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 13.97 180) (length 7.62)
          (name "P80" (effects (font (size 1.524 1.524))))
          (number "80" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 16.51 180) (length 7.62)
          (name "P81" (effects (font (size 1.524 1.524))))
          (number "81" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 19.05 180) (length 7.62)
          (name "P82" (effects (font (size 1.524 1.524))))
          (number "82" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 21.59 180) (length 7.62)
          (name "P83" (effects (font (size 1.524 1.524))))
          (number "83" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 24.13 180) (length 7.62)
          (name "P84" (effects (font (size 1.524 1.524))))
          (number "84" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 26.67 180) (length 7.62)
          (name "P85" (effects (font (size 1.524 1.524))))
          (number "85" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 29.21 180) (length 7.62)
          (name "P86" (effects (font (size 1.524 1.524))))
          (number "86" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 31.75 180) (length 7.62)
          (name "P87" (effects (font (size 1.524 1.524))))
          (number "87" (effects (font (size 1.524 1.524))))
        )
        (pin power_in line (at 3.81 -59.69 90) (length 0)
          (name "GND" (effects (font (size 1.524 1.524))))
          (number "88" (effects (font (size 1.524 1.524))))
           hide        )
        (pin power_in line (at 1.27 59.69 270) (length 0)
          (name "VCC" (effects (font (size 1.524 1.524))))
          (number "89" (effects (font (size 1.524 1.524))))
           hide        )
        (pin passive line (at -24.13 11.43 0) (length 7.62)
          (name "P/A15" (effects (font (size 1.524 1.524))))
          (number "9" (effects (font (size 1.524 1.524))))
        )
        (pin bidirectional line (at 24.13 34.29 180) (length 7.62)
          (name "P90" (effects (font (size 1.524 1.524))))
          (number "90" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 36.83 180) (length 7.62)
          (name "P91" (effects (font (size 1.524 1.524))))
          (number "91" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 39.37 180) (length 7.62)
          (name "P92" (effects (font (size 1.524 1.524))))
          (number "92" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 41.91 180) (length 7.62)
          (name "P93" (effects (font (size 1.524 1.524))))
          (number "93" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 44.45 180) (length 7.62)
          (name "P94" (effects (font (size 1.524 1.524))))
          (number "94" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 46.99 180) (length 7.62)
          (name "P91" (effects (font (size 1.524 1.524))))
          (number "95" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 49.53 180) (length 7.62)
          (name "P96" (effects (font (size 1.524 1.524))))
          (number "96" (effects (font (size 1.524 1.524))))
        )
        (pin passive line (at 24.13 52.07 180) (length 7.62)
          (name "P97" (effects (font (size 1.524 1.524))))
          (number "97" (effects (font (size 1.524 1.524))))
        )
        (pin bidirectional line (at 24.13 54.61 180) (length 7.62)
          (name "P98" (effects (font (size 1.524 1.524))))
          (number "98" (effects (font (size 1.524 1.524))))
        )
        (pin bidirectional line (at 24.13 57.15 180) (length 7.62)
          (name "SGCK1" (effects (font (size 1.524 1.524))))
          (number "99" (effects (font (size 1.524 1.524))))
        )
      )
    )
  )

  (junction (at 63.5 81.28))
  (junction (at 63.5 111.76))
  (junction (at 63.5 142.24))
  (junction (at 64.77 83.82))
  (junction (at 64.77 114.3))
  (junction (at 64.77 144.78))
  (junction (at 154.94 40.64))
  (junction (at 154.94 43.18))

  (no_connect (at 154.94 73.66))
  (no_connect (at 154.94 66.04))
  (no_connect (at 203.2 106.68))
  (no_connect (at 203.2 109.22))
  (no_connect (at 154.94 99.06))
  (no_connect (at 154.94 58.42))
  (no_connect (at 154.94 71.12))
  (no_connect (at 154.94 101.6))
  (no_connect (at 154.94 88.9))
  (no_connect (at 203.2 91.44))
  (no_connect (at 154.94 121.92))
  (no_connect (at 154.94 96.52))
  (no_connect (at 154.94 78.74))
  (no_connect (at 154.94 76.2))

  (wire (pts (xy 60.96 142.24) (xy 63.5 142.24)))
  (wire (pts (xy 63.5 50.8) (xy 66.04 50.8)))
  (wire (pts (xy 63.5 81.28) (xy 63.5 50.8)))
  (wire (pts (xy 63.5 111.76) (xy 63.5 81.28)))
  (wire (pts (xy 63.5 142.24) (xy 63.5 111.76)))
  (wire (pts (xy 63.5 142.24) (xy 66.04 142.24)))
  (wire (pts (xy 64.77 53.34) (xy 66.04 53.34)))
  (wire (pts (xy 64.77 83.82) (xy 64.77 53.34)))
  (wire (pts (xy 64.77 114.3) (xy 64.77 83.82)))
  (wire (pts (xy 64.77 144.78) (xy 60.96 144.78)))
  (wire (pts (xy 64.77 144.78) (xy 64.77 114.3)))
  (wire (pts (xy 66.04 27.94) (xy 52.07 27.94)))
  (wire (pts (xy 66.04 30.48) (xy 52.07 30.48)))
  (wire (pts (xy 66.04 33.02) (xy 52.07 33.02)))
  (wire (pts (xy 66.04 35.56) (xy 52.07 35.56)))
  (wire (pts (xy 66.04 38.1) (xy 52.07 38.1)))
  (wire (pts (xy 66.04 40.64) (xy 52.07 40.64)))
  (wire (pts (xy 66.04 43.18) (xy 52.07 43.18)))
  (wire (pts (xy 66.04 45.72) (xy 52.07 45.72)))
  (wire (pts (xy 66.04 58.42) (xy 52.07 58.42)))
  (wire (pts (xy 66.04 60.96) (xy 52.07 60.96)))
  (wire (pts (xy 66.04 63.5) (xy 52.07 63.5)))
  (wire (pts (xy 66.04 66.04) (xy 52.07 66.04)))
  (wire (pts (xy 66.04 68.58) (xy 52.07 68.58)))
  (wire (pts (xy 66.04 71.12) (xy 52.07 71.12)))
  (wire (pts (xy 66.04 73.66) (xy 52.07 73.66)))
  (wire (pts (xy 66.04 76.2) (xy 52.07 76.2)))
  (wire (pts (xy 66.04 81.28) (xy 63.5 81.28)))
  (wire (pts (xy 66.04 83.82) (xy 64.77 83.82)))
  (wire (pts (xy 66.04 88.9) (xy 52.07 88.9)))
  (wire (pts (xy 66.04 91.44) (xy 52.07 91.44)))
  (wire (pts (xy 66.04 93.98) (xy 52.07 93.98)))
  (wire (pts (xy 66.04 96.52) (xy 52.07 96.52)))
  (wire (pts (xy 66.04 99.06) (xy 52.07 99.06)))
  (wire (pts (xy 66.04 101.6) (xy 52.07 101.6)))
  (wire (pts (xy 66.04 104.14) (xy 52.07 104.14)))
  (wire (pts (xy 66.04 106.68) (xy 52.07 106.68)))
  (wire (pts (xy 66.04 111.76) (xy 63.5 111.76)))
  (wire (pts (xy 66.04 114.3) (xy 64.77 114.3)))
  (wire (pts (xy 66.04 119.38) (xy 52.07 119.38)))
  (wire (pts (xy 66.04 121.92) (xy 52.07 121.92)))
  (wire (pts (xy 66.04 124.46) (xy 52.07 124.46)))
  (wire (pts (xy 66.04 127) (xy 52.07 127)))
  (wire (pts (xy 66.04 129.54) (xy 52.07 129.54)))
  (wire (pts (xy 66.04 132.08) (xy 52.07 132.08)))
  (wire (pts (xy 66.04 134.62) (xy 52.07 134.62)))
  (wire (pts (xy 66.04 137.16) (xy 52.07 137.16)))
  (wire (pts (xy 66.04 144.78) (xy 64.77 144.78)))
  (wire (pts (xy 101.6 27.94) (xy 115.57 27.94)))
  (wire (pts (xy 101.6 30.48) (xy 115.57 30.48)))
  (wire (pts (xy 101.6 33.02) (xy 115.57 33.02)))
  (wire (pts (xy 101.6 35.56) (xy 115.57 35.56)))
  (wire (pts (xy 101.6 38.1) (xy 115.57 38.1)))
  (wire (pts (xy 101.6 40.64) (xy 115.57 40.64)))
  (wire (pts (xy 101.6 43.18) (xy 115.57 43.18)))
  (wire (pts (xy 101.6 45.72) (xy 115.57 45.72)))
  (wire (pts (xy 101.6 58.42) (xy 115.57 58.42)))
  (wire (pts (xy 101.6 60.96) (xy 115.57 60.96)))
  (wire (pts (xy 101.6 63.5) (xy 115.57 63.5)))
  (wire (pts (xy 101.6 66.04) (xy 115.57 66.04)))
  (wire (pts (xy 101.6 68.58) (xy 115.57 68.58)))
  (wire (pts (xy 101.6 71.12) (xy 115.57 71.12)))
  (wire (pts (xy 101.6 73.66) (xy 115.57 73.66)))
  (wire (pts (xy 101.6 76.2) (xy 115.57 76.2)))
  (wire (pts (xy 101.6 88.9) (xy 115.57 88.9)))
  (wire (pts (xy 101.6 91.44) (xy 115.57 91.44)))
  (wire (pts (xy 101.6 93.98) (xy 115.57 93.98)))
  (wire (pts (xy 101.6 96.52) (xy 115.57 96.52)))
  (wire (pts (xy 101.6 99.06) (xy 115.57 99.06)))
  (wire (pts (xy 101.6 101.6) (xy 115.57 101.6)))
  (wire (pts (xy 101.6 104.14) (xy 115.57 104.14)))
  (wire (pts (xy 101.6 106.68) (xy 115.57 106.68)))
  (wire (pts (xy 101.6 119.38) (xy 115.57 119.38)))
  (wire (pts (xy 101.6 121.92) (xy 115.57 121.92)))
  (wire (pts (xy 101.6 124.46) (xy 115.57 124.46)))
  (wire (pts (xy 101.6 127) (xy 115.57 127)))
  (wire (pts (xy 101.6 129.54) (xy 115.57 129.54)))
  (wire (pts (xy 101.6 132.08) (xy 115.57 132.08)))
  (wire (pts (xy 101.6 134.62) (xy 115.57 134.62)))
  (wire (pts (xy 101.6 137.16) (xy 115.57 137.16)))
  (wire (pts (xy 143.51 63.5) (xy 154.94 63.5)))
  (wire (pts (xy 143.51 104.14) (xy 154.94 104.14)))
  (wire (pts (xy 143.51 106.68) (xy 154.94 106.68)))
  (wire (pts (xy 143.51 109.22) (xy 154.94 109.22)))
  (wire (pts (xy 143.51 111.76) (xy 154.94 111.76)))
  (wire (pts (xy 143.51 127) (xy 154.94 127)))
  (wire (pts (xy 143.51 137.16) (xy 154.94 137.16)))
  (wire (pts (xy 143.51 139.7) (xy 154.94 139.7)))
  (wire (pts (xy 143.51 142.24) (xy 154.94 142.24)))
  (wire (pts (xy 143.51 144.78) (xy 154.94 144.78)))
  (wire (pts (xy 143.51 149.86) (xy 154.94 149.86)))
  (wire (pts (xy 143.51 152.4) (xy 154.94 152.4)))
  (wire (pts (xy 154.94 39.37) (xy 154.94 40.64)))
  (wire (pts (xy 154.94 40.64) (xy 154.94 43.18)))
  (wire (pts (xy 154.94 43.18) (xy 154.94 45.72)))
  (wire (pts (xy 154.94 60.96) (xy 142.24 60.96)))
  (wire (pts (xy 154.94 114.3) (xy 135.89 114.3)))
  (wire (pts (xy 154.94 132.08) (xy 143.51 132.08)))
  (wire (pts (xy 154.94 134.62) (xy 143.51 134.62)))
  (wire (pts (xy 203.2 45.72) (xy 214.63 45.72)))
  (wire (pts (xy 203.2 48.26) (xy 214.63 48.26)))
  (wire (pts (xy 203.2 50.8) (xy 214.63 50.8)))
  (wire (pts (xy 203.2 53.34) (xy 214.63 53.34)))
  (wire (pts (xy 203.2 55.88) (xy 214.63 55.88)))
  (wire (pts (xy 203.2 58.42) (xy 214.63 58.42)))
  (wire (pts (xy 203.2 66.04) (xy 214.63 66.04)))
  (wire (pts (xy 203.2 68.58) (xy 214.63 68.58)))
  (wire (pts (xy 203.2 71.12) (xy 214.63 71.12)))
  (wire (pts (xy 203.2 73.66) (xy 214.63 73.66)))
  (wire (pts (xy 203.2 76.2) (xy 214.63 76.2)))
  (wire (pts (xy 203.2 78.74) (xy 214.63 78.74)))
  (wire (pts (xy 203.2 86.36) (xy 215.9 86.36)))
  (wire (pts (xy 203.2 88.9) (xy 214.63 88.9)))
  (wire (pts (xy 203.2 93.98) (xy 214.63 93.98)))
  (wire (pts (xy 203.2 96.52) (xy 214.63 96.52)))
  (wire (pts (xy 203.2 99.06) (xy 214.63 99.06)))
  (wire (pts (xy 203.2 101.6) (xy 214.63 101.6)))
  (wire (pts (xy 203.2 104.14) (xy 214.63 104.14)))
  (wire (pts (xy 203.2 119.38) (xy 214.63 119.38)))
  (wire (pts (xy 203.2 121.92) (xy 214.63 121.92)))
  (wire (pts (xy 203.2 124.46) (xy 214.63 124.46)))
  (wire (pts (xy 203.2 127) (xy 214.63 127)))
  (wire (pts (xy 203.2 129.54) (xy 214.63 129.54)))
  (wire (pts (xy 203.2 137.16) (xy 214.63 137.16)))
  (wire (pts (xy 203.2 139.7) (xy 214.63 139.7)))
  (wire (pts (xy 203.2 142.24) (xy 214.63 142.24)))
  (wire (pts (xy 203.2 144.78) (xy 214.63 144.78)))
  (wire (pts (xy 203.2 147.32) (xy 214.63 147.32)))
  (wire (pts (xy 203.2 149.86) (xy 214.63 149.86)))
  (wire (pts (xy 218.44 40.64) (xy 203.2 40.64)))
  (wire (pts (xy 218.44 43.18) (xy 203.2 43.18)))
  (wire (pts (xy 218.44 60.96) (xy 203.2 60.96)))
  (wire (pts (xy 218.44 63.5) (xy 203.2 63.5)))
  (wire (pts (xy 218.44 81.28) (xy 203.2 81.28)))
  (wire (pts (xy 218.44 83.82) (xy 203.2 83.82)))
  (wire (pts (xy 218.44 111.76) (xy 203.2 111.76)))
  (wire (pts (xy 218.44 114.3) (xy 203.2 114.3)))
  (wire (pts (xy 218.44 116.84) (xy 203.2 116.84)))
  (wire (pts (xy 218.44 132.08) (xy 203.2 132.08)))
  (wire (pts (xy 218.44 134.62) (xy 203.2 134.62)))
  (wire (pts (xy 218.44 152.4) (xy 203.2 152.4)))
  (wire (pts (xy 218.44 154.94) (xy 203.2 154.94)))

  (label "DPC0" (at 53.34 27.94 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC1" (at 53.34 30.48 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC2" (at 53.34 33.02 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC3" (at 53.34 35.56 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC4" (at 53.34 38.1 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC5" (at 53.34 40.64 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC6" (at 53.34 43.18 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC7" (at 53.34 45.72 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC8" (at 53.34 58.42 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC9" (at 53.34 60.96 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC10" (at 53.34 63.5 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC11" (at 53.34 66.04 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC12" (at 53.34 68.58 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC13" (at 53.34 71.12 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC14" (at 53.34 73.66 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC15" (at 53.34 76.2 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC16" (at 53.34 88.9 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC17" (at 53.34 91.44 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC18" (at 53.34 93.98 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC19" (at 53.34 96.52 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC20" (at 53.34 99.06 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC21" (at 53.34 101.6 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC22" (at 53.34 104.14 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC23" (at 53.34 106.68 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC24" (at 53.34 119.38 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC25" (at 53.34 121.92 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC26" (at 53.34 124.46 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC27" (at 53.34 127 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC28" (at 53.34 129.54 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC29" (at 53.34 132.08 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC30" (at 53.34 134.62 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "DPC31" (at 53.34 137.16 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM0" (at 104.14 27.94 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM1" (at 104.14 30.48 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM2" (at 104.14 33.02 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM3" (at 104.14 35.56 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM4" (at 104.14 38.1 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM5" (at 104.14 40.64 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM6" (at 104.14 43.18 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM7" (at 104.14 45.72 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM8" (at 104.14 58.42 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM9" (at 104.14 60.96 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM10" (at 104.14 63.5 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM11" (at 104.14 66.04 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM12" (at 104.14 68.58 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM13" (at 104.14 71.12 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM14" (at 104.14 73.66 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM15" (at 104.14 76.2 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM16" (at 104.14 88.9 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM17" (at 104.14 91.44 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM18" (at 104.14 93.98 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM19" (at 104.14 96.52 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM20" (at 104.14 99.06 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM21" (at 104.14 101.6 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM22" (at 104.14 104.14 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM23" (at 104.14 106.68 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM24" (at 104.14 119.38 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM25" (at 104.14 121.92 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM26" (at 104.14 124.46 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM27" (at 104.14 127 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM28" (at 104.14 129.54 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM29" (at 104.14 132.08 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM30" (at 104.14 134.62 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM31" (at 104.14 137.16 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "ACCES_RAM-" (at 135.89 114.3 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM6" (at 143.51 132.08 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM7" (at 143.51 134.62 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM4" (at 144.78 63.5 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM0" (at 144.78 104.14 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM3" (at 144.78 106.68 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM1" (at 144.78 109.22 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM2" (at 144.78 111.76 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM5" (at 144.78 127 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM8" (at 144.78 137.16 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM9" (at 144.78 139.7 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM10" (at 144.78 142.24 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM11" (at 144.78 144.78 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM12" (at 144.78 149.86 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM13" (at 144.78 152.4 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM28" (at 204.47 119.38 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM27" (at 204.47 121.92 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM26" (at 204.47 124.46 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM25" (at 204.47 127 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM24" (at 204.47 129.54 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM21" (at 204.47 137.16 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM20" (at 204.47 139.7 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM19" (at 204.47 142.24 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM18" (at 204.47 144.78 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM17" (at 204.47 147.32 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM16" (at 204.47 149.86 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVB5" (at 205.74 45.72 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVB4" (at 205.74 48.26 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVB3" (at 205.74 50.8 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVB2" (at 205.74 53.34 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVB1" (at 205.74 55.88 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVB0" (at 205.74 58.42 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVR5" (at 205.74 66.04 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVR4" (at 205.74 68.58 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVR3" (at 205.74 71.12 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVR2" (at 205.74 73.66 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVR1" (at 205.74 76.2 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVR0" (at 205.74 78.74 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVG5" (at 205.74 88.9 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVG4" (at 205.74 93.98 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVG3" (at 205.74 96.52 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVG2" (at 205.74 99.06 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVG1" (at 205.74 101.6 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVG0" (at 205.74 104.14 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM31" (at 205.74 111.76 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM23" (at 205.74 132.08 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM15" (at 205.74 152.4 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVB7" (at 207.01 40.64 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVB6" (at 207.01 43.18 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVR7" (at 207.01 60.96 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVR6" (at 207.01 63.5 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVG7" (at 207.01 81.28 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "TVG6" (at 207.01 83.82 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM30" (at 207.01 114.3 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM29" (at 207.01 116.84 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM22" (at 207.01 134.62 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )
  (label "VRAM14" (at 207.01 154.94 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
  )

  (hierarchical_label "DPC[0..31]" (shape bidirectional) (at 59.69 22.86 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "DATA_WR" (shape input) (at 60.96 142.24 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "ACCES_RAM-" (shape input) (at 60.96 144.78 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "X_CLK" (shape input) (at 142.24 60.96 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "X_PROG-" (shape input) (at 154.94 50.8 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "X_DONE" (shape output) (at 154.94 53.34 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "X_DIN" (shape input) (at 154.94 55.88 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "CLKCAD" (shape input) (at 154.94 68.58 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "CLAMP" (shape input) (at 154.94 81.28 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "BLANK-" (shape input) (at 154.94 83.82 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "CSYNC-OUT" (shape input) (at 154.94 86.36 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "ACQ_ON" (shape input) (at 154.94 93.98 180)
    (effects (font (size 1.524 1.524)) (justify right))
  )
  (hierarchical_label "CLKCDA" (shape input) (at 215.9 86.36 0)
    (effects (font (size 1.524 1.524)) (justify left))
  )
  (hierarchical_label "VRAM[0..31]" (shape bidirectional) (at 241.3 25.4 0)
    (effects (font (size 1.524 1.524)) (justify left))
  )
  (hierarchical_label "TVR[0..7]" (shape bidirectional) (at 241.3 27.94 0)
    (effects (font (size 1.524 1.524)) (justify left))
  )
  (hierarchical_label "TVG[0..7]" (shape bidirectional) (at 241.3 30.48 0)
    (effects (font (size 1.524 1.524)) (justify left))
  )
  (hierarchical_label "TVB[0..7]" (shape bidirectional) (at 241.3 33.02 0)
    (effects (font (size 1.524 1.524)) (justify left))
  )

  (symbol (lib_id "video_schlib:VCC") (at 154.94 39.37 0) (unit 1)
    (uuid "00000000-0000-0000-0000-000033a567e7")
    (property "Reference" "#PWR06" (id 0) (at 154.94 34.29 0)
      (effects (font (size 1.016 1.016)) hide)
    )
    (property "Value" "VCC" (id 1) (at 154.94 35.56 0)
      (effects (font (size 1.016 1.016)))
    )
    (property "Footprint" "" (id 2) (at 154.94 39.37 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Datasheet" "" (id 3) (at 154.94 39.37 0)
      (effects (font (size 1.524 1.524)) hide)
    )
  )

  (symbol (lib_id "video_schlib:74LS245") (at 83.82 40.64 0) (unit 1)
    (uuid "00000000-0000-0000-0000-000033a7e303")
    (property "Reference" "U3" (id 0) (at 83.82 45.72 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Value" "74LS245" (id 1) (at 83.82 48.26 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Footprint" "Package_SO:SOIC-20W_7.5x12.8mm_P1.27mm" (id 2) (at 83.82 40.64 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Datasheet" "" (id 3) (at 83.82 40.64 0)
      (effects (font (size 1.524 1.524)) hide)
    )
  )

  (symbol (lib_id "video_schlib:74LS245") (at 83.82 71.12 0) (unit 1)
    (uuid "00000000-0000-0000-0000-00004bf036d7")
    (property "Reference" "U4" (id 0) (at 83.82 76.2 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Value" "74LS245" (id 1) (at 83.82 78.74 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Footprint" "Package_SO:SOIC-20W_7.5x12.8mm_P1.27mm" (id 2) (at 83.82 71.12 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Datasheet" "" (id 3) (at 83.82 71.12 0)
      (effects (font (size 1.524 1.524)) hide)
    )
  )

  (symbol (lib_id "video_schlib:74LS245") (at 83.82 101.6 0) (unit 1)
    (uuid "00000000-0000-0000-0000-00004bf036d6")
    (property "Reference" "U5" (id 0) (at 83.82 106.68 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Value" "74LS245" (id 1) (at 83.82 109.22 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Footprint" "Package_SO:SOIC-20W_7.5x12.8mm_P1.27mm" (id 2) (at 83.82 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Datasheet" "" (id 3) (at 83.82 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
  )

  (symbol (lib_id "video_schlib:74LS245") (at 83.82 132.08 0) (unit 1)
    (uuid "00000000-0000-0000-0000-00004bf036d5")
    (property "Reference" "U6" (id 0) (at 83.82 137.16 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Value" "74LS245" (id 1) (at 83.82 139.7 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Footprint" "Package_SO:SOIC-20W_7.5x12.8mm_P1.27mm" (id 2) (at 83.82 132.08 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Datasheet" "" (id 3) (at 83.82 132.08 0)
      (effects (font (size 1.524 1.524)) hide)
    )
  )

  (symbol (lib_id "video_schlib:XC4003-VQ100") (at 179.07 97.79 0) (unit 1)
    (uuid "00000000-0000-0000-0000-000033a567b8")
    (property "Reference" "U22" (id 0) (at 179.07 95.25 0)
      (effects (font (size 1.778 1.778)))
    )
    (property "Value" "XC4003-VQ100" (id 1) (at 179.07 100.33 0)
      (effects (font (size 1.778 1.778)))
    )
    (property "Footprint" "Package_QFP:TQFP-100_14x14mm_P0.5mm" (id 2) (at 179.07 97.79 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Datasheet" "" (id 3) (at 179.07 97.79 0)
      (effects (font (size 1.524 1.524)) hide)
    )
  )
)
