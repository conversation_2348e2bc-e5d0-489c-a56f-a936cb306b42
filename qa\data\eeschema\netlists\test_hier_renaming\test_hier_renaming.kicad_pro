{"board": {"design_settings": {"defaults": {"board_outline_line_width": 0.09999999999999999, "copper_line_width": 0.19999999999999998, "copper_text_italic": false, "copper_text_size_h": 1.5, "copper_text_size_v": 1.5, "copper_text_thickness": 0.3, "copper_text_upright": true, "courtyard_line_width": 0.049999999999999996, "dimension_precision": 1, "dimension_units": 0, "dimensions": {"arrow_length": 1270000, "extension_offset": 500000, "keep_text_aligned": true, "suppress_zeroes": false, "text_position": 0, "units_format": 1}, "fab_line_width": 0.09999999999999999, "fab_text_italic": false, "fab_text_size_h": 1.0, "fab_text_size_v": 1.0, "fab_text_thickness": 0.15, "fab_text_upright": true, "other_line_width": 0.09999999999999999, "other_text_italic": false, "other_text_size_h": 1.0, "other_text_size_v": 1.0, "other_text_thickness": 0.15, "other_text_upright": true, "pads": {"drill": 0.762, "height": 1.524, "width": 1.524}, "silk_line_width": 0.15, "silk_text_italic": false, "silk_text_size_h": 1.0, "silk_text_size_v": 1.0, "silk_text_thickness": 0.15, "silk_text_upright": true, "zones": {"45_degree_only": false, "min_clearance": 0.19999999999999998}}, "diff_pair_dimensions": [{"gap": 0.0, "via_gap": 0.0, "width": 0.0}], "drc_exclusions": [], "meta": {"version": 1}, "rule_severities": {"annular_width": "error", "clearance": "error", "copper_edge_clearance": "error", "courtyards_overlap": "error", "diff_pair_gap_out_of_range": "error", "diff_pair_uncoupled_length_too_long": "error", "drill_too_small": "error", "duplicate_footprints": "warning", "extra_footprint": "warning", "hole_clearance": "error", "hole_near_hole": "error", "invalid_outline": "error", "item_on_disabled_layer": "error", "items_not_allowed": "error", "length_out_of_range": "error", "malformed_courtyard": "error", "microvia_drill_too_small": "error", "missing_courtyard": "ignore", "missing_footprint": "warning", "net_conflict": "warning", "npth_inside_courtyard": "ignore", "padstack": "error", "pth_inside_courtyard": "ignore", "shorting_items": "error", "silk_over_copper": "error", "silk_overlap": "error", "skew_out_of_range": "error", "too_many_vias": "error", "track_dangling": "warning", "track_width": "error", "tracks_crossing": "error", "unconnected_items": "error", "unresolved_variable": "error", "via_dangling": "warning", "zone_has_empty_net": "error", "zones_intersect": "error"}, "rules": {"allow_blind_buried_vias": false, "allow_microvias": false, "max_error": 0.005, "min_clearance": 0.15, "min_copper_edge_clearance": 0.3, "min_hole_clearance": 0.0, "min_hole_to_hole": 0.25, "min_microvia_diameter": 0.19999999999999998, "min_microvia_drill": 0.09999999999999999, "min_silk_clearance": 0.0, "min_through_hole_diameter": 0.3, "min_track_width": 0.15, "min_via_annular_width": 0.049999999999999996, "min_via_annulus": 0.15, "min_via_diameter": 0.3, "solder_mask_clearance": 0.0, "solder_mask_min_width": 0.0, "solder_paste_clearance": 0.0, "solder_paste_margin_ratio": -0.0}, "track_widths": [0.0, 0.2, 0.4], "via_dimensions": [{"diameter": 0.0, "drill": 0.0}, {"diameter": 0.6, "drill": 0.3}, {"diameter": 0.8, "drill": 0.4}], "zones_allow_external_fillets": false, "zones_use_no_outline": false}, "layer_presets": []}, "boards": [], "cvpcb": {"equivalence_files": []}, "erc": {"erc_exclusions": [], "meta": {"version": 0}, "pin_map": [[0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 0, 1, 0, 1, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 2], [0, 1, 0, 0, 0, 1, 1, 2, 1, 1, 2], [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], [0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 2], [0, 2, 1, 2, 0, 1, 0, 2, 2, 2, 2], [0, 2, 0, 1, 0, 1, 0, 2, 0, 0, 2], [0, 2, 1, 1, 0, 1, 0, 2, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "rule_severities": {"bus_definition_conflict": "error", "bus_label_syntax": "error", "bus_to_bus_conflict": "error", "bus_to_net_conflict": "error", "different_unit_footprint": "error", "different_unit_net": "error", "duplicate_sheet_names": "error", "global_label_dangling": "error", "hier_label_mismatch": "error", "label_dangling": "error", "lib_symbol_issues": "warning", "multiple_net_names": "error", "net_not_bus_member": "error", "no_connect_connected": "error", "no_connect_dangling": "error", "pin_not_connected": "error", "pin_not_driven": "error", "pin_to_pin": "warning", "power_pin_not_driven": "error", "similar_labels": "error", "unresolved_variable": "error", "wire_dangling": "error"}}, "libraries": {"pinned_footprint_libs": [], "pinned_symbol_libs": []}, "meta": {"filename": "test_hier_renaming.kicad_pro", "version": 1}, "net_settings": {"classes": [{"bus_width": 6.0, "clearance": 0.2, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "<PERSON><PERSON><PERSON>", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.25, "via_diameter": 0.8, "via_drill": 0.4, "wire_width": 6.0}], "meta": {"version": 0}, "net_colors": {"Net-(DS1-Pad1)": "rgb(255, 255, 0)"}}, "pcbnew": {"last_paths": {"gencad": "", "idf": "", "netlist": "", "specctra_dsn": "", "step": "", "vmrl": "", "vrml": ""}, "page_layout_descr_file": ""}, "schematic": {"drawing": {"default_bus_thickness": 12.0, "default_junction_size": 40.0, "default_line_thickness": 6.0, "default_text_size": 50.0, "default_wire_thickness": 6.0, "field_names": [], "intersheets_ref_prefix": "", "intersheets_ref_short": false, "intersheets_ref_show": false, "intersheets_ref_suffix": "", "junction_size_choice": 3, "pin_symbol_size": 25.0, "text_offset_ratio": 0.3}, "legacy_lib_dir": "", "legacy_lib_list": [], "meta": {"version": 0}, "net_format_name": "Pcbnew", "page_layout_descr_file": "", "plot_directory": "", "spice_adjust_passive_values": false, "spice_external_command": "spice \"%I\"", "subpart_first_id": 65, "subpart_id_separator": 0}, "sheets": [["0c777b0f-2f12-4510-99ee-a28b103b057a", ""], ["0c777b0f-2f12-4510-99ee-a28b103b057a", "matrixes1"], ["8fe7e2b2-e224-440a-ae87-ac0912bb533a", "matrixes2"]], "text_variables": {}}