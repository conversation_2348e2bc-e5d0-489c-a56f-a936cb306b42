(footprint "Reverb_BTDR-1V"
	(version 20231007)
	(generator pcbnew)
	(layer "F.Cu")
	(descr "Digital Reverberation Unit, http://www.belton.co.kr/inc/downfile.php?seq=17&file=pdf (footprint from http://www.uk-electronic.de/PDF/BTDR-1.pdf)")
	(tags "audio belton reverb")
	(property "Reference" "REF**"
		(at -11.5 0 180)
		(layer "F.SilkS")
		(tstamp 23fd8612-0682-410f-bf8d-c4874ad45f3d)
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Value" "Reverb_BTDR-1V"
		(at 27.62 -2.75 180)
		(layer "F.Fab")
		(tstamp 84aa1724-7810-4bd1-982b-bc3da110a2d1)
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(property "Footprint" ""
		(at 0 0 0 unlocked)
		(layer "F.Fab") hide
		(tstamp 8ed64581-2b83-4ebe-8edd-cfd9b8d4cf5e)
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(property "Datasheet" ""
		(at 0 0 0 unlocked)
		(layer "F.Fab") hide
		(tstamp b2b0cf58-a054-47f5-a1a7-d365c87f1a14)
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(property "Description" ""
		(at 0 0 0 unlocked)
		(layer "F.Fab") hide
		(tstamp a92072d5-74bf-49b6-8a42-08692319d2f4)
		(effects
			(font
				(size 1.27 1.27)
			)
		)
	)
	(attr through_hole)
	(fp_line
		(start -13.88 1.3)
		(end 69.12 1.3)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(tstamp 81a7dfa1-c278-425c-a935-a8b9a29946a6)
	)
	(fp_line
		(start -13.88 4.3)
		(end -13.88 1.3)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(tstamp bda1223a-e7ea-4c51-9249-a21d389f3710)
	)
	(fp_line
		(start -6.38 4.3)
		(end -13.88 4.3)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(tstamp 690bfa6b-b0f9-4317-ad57-06b3802ac7b0)
	)
	(fp_line
		(start -6.38 11.1)
		(end -6.38 4.3)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(tstamp 97cadbec-a55e-41ed-92d5-eb3214b34afd)
	)
	(fp_line
		(start 61.62 4.3)
		(end 61.62 11.1)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(tstamp c5e6a983-b7b3-4b12-9d4a-dd295e782cfe)
	)
	(fp_line
		(start 61.62 11.1)
		(end -6.38 11.1)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(tstamp 2d8f87c0-9dd2-41d3-b1cd-9d71e4644960)
	)
	(fp_line
		(start 69.12 1.3)
		(end 69.12 4.3)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(tstamp be83e42e-9ffb-493c-ad07-45c77841473d)
	)
	(fp_line
		(start 69.12 4.3)
		(end 61.62 4.3)
		(stroke
			(width 0.12)
			(type solid)
		)
		(layer "F.SilkS")
		(tstamp f4ce1fd3-d6c1-4abb-b989-ae90ad642a07)
	)
	(fp_line
		(start -14.13 -1.25)
		(end 69.37 -1.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(tstamp 712967cb-b7b3-4f31-95b5-a3ae7821d1b9)
	)
	(fp_line
		(start -14.13 11.35)
		(end -14.13 -1.25)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(tstamp 0662dc2c-544d-47e4-b9a9-078d346b7b8b)
	)
	(fp_line
		(start -6.63 11.35)
		(end -14.13 11.35)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(tstamp 6b2036dd-604c-4667-9cd8-889990119aba)
	)
	(fp_line
		(start 61.62 11.35)
		(end -6.63 11.35)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(tstamp 48f58059-e5ec-4db5-97c0-741c62b975e6)
	)
	(fp_line
		(start 69.37 -1.25)
		(end 69.37 11.35)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(tstamp 12d7a82e-b0c6-40b8-a78c-2d112ffab686)
	)
	(fp_line
		(start 69.37 11.35)
		(end 61.62 11.35)
		(stroke
			(width 0.05)
			(type solid)
		)
		(layer "F.CrtYd")
		(tstamp 665f55d3-1930-4c50-8ac6-a50812f0ebb7)
	)
	(fp_line
		(start -13.88 1.3)
		(end 69.12 1.3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(tstamp 40e6edcf-2dca-4414-8c68-54b5cb516af0)
	)
	(fp_line
		(start -13.88 4.3)
		(end -13.88 1.3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(tstamp 69e81b15-c24f-4180-a1e2-ddeb5ca6447d)
	)
	(fp_line
		(start -6.38 4.3)
		(end -13.88 4.3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(tstamp f4d1a664-c87b-4415-947e-80bf18cfb575)
	)
	(fp_line
		(start -6.38 11.1)
		(end -6.38 4.3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(tstamp e0811244-7b68-4fcc-9992-3c461271d12c)
	)
	(fp_line
		(start 61.62 4.3)
		(end 61.62 11.1)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(tstamp 1c90e511-a13d-4a19-bb0c-341a540211ff)
	)
	(fp_line
		(start 61.62 11.1)
		(end -6.38 11.1)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(tstamp b8985526-ec53-47c3-9430-ddff43dd0717)
	)
	(fp_line
		(start 69.12 1.3)
		(end 69.12 4.3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(tstamp 422ec1ee-934d-458a-a428-dae9f64f0e7c)
	)
	(fp_line
		(start 69.12 4.3)
		(end 61.62 4.3)
		(stroke
			(width 0.1)
			(type solid)
		)
		(layer "F.Fab")
		(tstamp c32cd034-9d5f-4f99-a118-06e174e115a6)
	)
	(fp_text user "${REFERENCE}"
		(at 27.62 6.2 180)
		(layer "F.Fab")
		(tstamp 2c2c1e88-d6ac-4cc4-8e09-80f372bca783)
		(effects
			(font
				(size 1 1)
				(thickness 0.15)
			)
		)
	)
	(pad "1" thru_hole rect
		(at 0 0)
		(size 2 2)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(tstamp 6b54a4cf-1c47-48bd-9076-9d2c1ed1dc0b)
	)
	(pad "2" thru_hole circle
		(at 2.54 0)
		(size 2 2)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(tstamp 1b5d7c94-aaa1-488b-9352-7b5770314a42)
	)
	(pad "3" thru_hole circle
		(at 5.08 0)
		(size 2 2)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(tstamp 254b0c32-5f01-4308-b5dd-0a4bc380a715)
	)
	(pad "4" thru_hole circle
		(at 7.62 0)
		(size 2 2)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(tstamp bc6a6a8f-3b8f-413f-88e7-4957bd99351b)
	)
	(pad "5" thru_hole circle
		(at 50.62 0)
		(size 2 2)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(tstamp 35698302-6de0-4184-aefe-e2cbdd76c272)
	)
	(pad "6" thru_hole circle
		(at 53.16 0)
		(size 2 2)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(tstamp c86ea0ec-de68-4d29-839c-dfca7c7d64b3)
	)
	(pad "7" thru_hole circle
		(at 55.7 0)
		(size 2 2)
		(drill 1)
		(layers "*.Cu" "*.Mask")
		(tstamp 5fc56d94-ca72-44e4-babd-0986bd43161c)
	)
	(model "${KICAD6_3DMODEL_DIR}/Audio_Module.3dshapes/Reverb_BTDR-1V.wrl"
		(offset
			(xyz 0 0 0)
		)
		(scale
			(xyz 1 1 1)
		)
		(rotate
			(xyz 0 0 0)
		)
	)
)
