{"board": {"3dviewports": [], "design_settings": {"defaults": {"apply_defaults_to_fp_fields": false, "apply_defaults_to_fp_shapes": false, "apply_defaults_to_fp_text": false, "board_outline_line_width": 0.1, "copper_line_width": 0.2, "copper_text_italic": false, "copper_text_size_h": 0.5, "copper_text_size_v": 0.5, "copper_text_thickness": 0.3, "copper_text_upright": false, "courtyard_line_width": 0.05, "dimension_precision": 4, "dimension_units": 3, "dimensions": {"arrow_length": 1270000, "extension_offset": 500000, "keep_text_aligned": true, "suppress_zeroes": false, "text_position": 0, "units_format": 1}, "fab_line_width": 0.1, "fab_text_italic": false, "fab_text_size_h": 0.5, "fab_text_size_v": 0.5, "fab_text_thickness": 0.15, "fab_text_upright": false, "other_line_width": 0.15, "other_text_italic": false, "other_text_size_h": 0.5, "other_text_size_v": 0.5, "other_text_thickness": 0.15, "other_text_upright": false, "pads": {"drill": 0.0, "height": 1.0, "width": 1.0}, "silk_line_width": 0.15, "silk_text_italic": false, "silk_text_size_h": 0.5, "silk_text_size_v": 0.5, "silk_text_thickness": 0.15, "silk_text_upright": false, "zones": {"min_clearance": 0.1}}, "diff_pair_dimensions": [{"gap": 0.0, "via_gap": 0.0, "width": 0.0}], "drc_exclusions": [], "meta": {"version": 2}, "rule_severities": {"annular_width": "error", "clearance": "error", "connection_width": "warning", "copper_edge_clearance": "error", "copper_sliver": "warning", "courtyards_overlap": "error", "diff_pair_gap_out_of_range": "error", "diff_pair_uncoupled_length_too_long": "error", "drill_out_of_range": "error", "duplicate_footprints": "warning", "extra_footprint": "warning", "footprint": "error", "footprint_symbol_mismatch": "warning", "footprint_type_mismatch": "ignore", "hole_clearance": "error", "hole_near_hole": "error", "hole_to_hole": "warning", "holes_co_located": "warning", "invalid_outline": "error", "isolated_copper": "warning", "item_on_disabled_layer": "error", "items_not_allowed": "error", "length_out_of_range": "error", "lib_footprint_issues": "warning", "lib_footprint_mismatch": "warning", "malformed_courtyard": "error", "microvia_drill_out_of_range": "error", "missing_courtyard": "ignore", "missing_footprint": "warning", "net_conflict": "warning", "npth_inside_courtyard": "ignore", "padstack": "warning", "pth_inside_courtyard": "ignore", "shorting_items": "error", "silk_edge_clearance": "ignore", "silk_over_copper": "ignore", "silk_overlap": "ignore", "skew_out_of_range": "error", "solder_mask_bridge": "error", "starved_thermal": "error", "text_height": "warning", "text_thickness": "warning", "through_hole_pad_without_hole": "error", "too_many_vias": "error", "track_dangling": "ignore", "track_width": "error", "tracks_crossing": "error", "unconnected_items": "error", "unresolved_variable": "error", "via_dangling": "ignore", "zones_intersect": "error"}, "rules": {"max_error": 0.005, "min_clearance": 0.07, "min_connection": 0.05, "min_copper_edge_clearance": 0.2, "min_hole_clearance": 0.1, "min_hole_to_hole": 0.1, "min_microvia_diameter": 0.2, "min_microvia_drill": 0.1, "min_resolved_spokes": 2, "min_silk_clearance": 0.1, "min_text_height": 0.3, "min_text_thickness": 0.075, "min_through_hole_diameter": 0.15, "min_track_width": 0.1, "min_via_annular_width": 0.06, "min_via_diameter": 0.2, "solder_mask_clearance": 0.0, "solder_mask_min_width": 0.0, "solder_mask_to_copper_clearance": 0.0, "use_height_for_length_calcs": true}, "teardrop_options": [{"td_onpadsmd": true, "td_onroundshapesonly": false, "td_ontrackend": false, "td_onviapad": true}], "teardrop_parameters": [{"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.2, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_round_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_rect_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_track_end", "td_width_to_size_filter_ratio": 0.9}], "track_widths": [0.0, 0.1, 0.14, 0.15, 0.2, 0.25, 0.29, 0.3, 0.4, 0.5, 0.6, 0.7, 0.75, 0.8, 0.9, 1.0], "tuning_pattern_settings": {"diff_pair_defaults": {"corner_radius_percentage": 80, "corner_style": 1, "max_amplitude": 1.0, "min_amplitude": 0.2, "single_sided": false, "spacing": 1.0}, "diff_pair_skew_defaults": {"corner_radius_percentage": 80, "corner_style": 1, "max_amplitude": 1.0, "min_amplitude": 0.2, "single_sided": false, "spacing": 0.6}, "single_track_defaults": {"corner_radius_percentage": 80, "corner_style": 1, "max_amplitude": 1.0, "min_amplitude": 0.2, "single_sided": false, "spacing": 0.6}}, "via_dimensions": [{"diameter": 0.0, "drill": 0.0}], "zones_allow_external_fillets": false}, "ipc2581": {"dist": "", "distpn": "", "internal_id": "", "mfg": "", "mpn": ""}, "layer_presets": [], "viewports": []}, "boards": [], "cvpcb": {"equivalence_files": []}, "erc": {"erc_exclusions": [], "meta": {"version": 0}, "pin_map": [[0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 0, 1, 0, 0, 1, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 2], [0, 1, 0, 0, 0, 0, 1, 1, 2, 1, 1, 2], [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 2], [0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 1, 2, 0, 0, 1, 0, 2, 2, 2, 2], [0, 2, 0, 1, 0, 0, 1, 0, 2, 0, 0, 2], [0, 2, 1, 1, 0, 0, 1, 0, 2, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "rule_severities": {"bus_definition_conflict": "error", "bus_entry_needed": "error", "bus_to_bus_conflict": "error", "bus_to_net_conflict": "error", "conflicting_netclasses": "error", "different_unit_footprint": "error", "different_unit_net": "error", "duplicate_reference": "error", "duplicate_sheet_names": "error", "endpoint_off_grid": "ignore", "extra_units": "error", "global_label_dangling": "warning", "hier_label_mismatch": "error", "label_dangling": "error", "lib_symbol_issues": "warning", "missing_bidi_pin": "warning", "missing_input_pin": "warning", "missing_power_pin": "error", "missing_unit": "warning", "multiple_net_names": "warning", "net_not_bus_member": "warning", "no_connect_connected": "warning", "no_connect_dangling": "warning", "pin_not_connected": "error", "pin_not_driven": "error", "pin_to_pin": "error", "power_pin_not_driven": "warning", "similar_labels": "warning", "simulation_model_issue": "ignore", "unannotated": "error", "unit_value_mismatch": "error", "unresolved_variable": "error", "wire_dangling": "error"}}, "libraries": {"pinned_footprint_libs": ["epy"], "pinned_symbol_libs": ["epy"]}, "meta": {"filename": "optimistic-proteus.kicad_pro", "version": 1}, "net_settings": {"classes": [{"bus_width": 12, "clearance": 0.1, "diff_pair_gap": 0.1, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.1, "line_style": 0, "microvia_diameter": 0.225, "microvia_drill": 0.1, "name": "<PERSON><PERSON><PERSON>", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.1, "via_diameter": 0.3, "via_drill": 0.15, "wire_width": 6}, {"bus_width": 12, "clearance": 0.1, "diff_pair_gap": 0.1, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.225, "microvia_drill": 0.1, "name": "NFC", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgb(0, 0, 132)", "track_width": 0.2, "via_diameter": 0.3, "via_drill": 0.15, "wire_width": 12}, {"bus_width": 12, "clearance": 0.1, "diff_pair_gap": 0.2, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.225, "microvia_drill": 0.1, "name": "Power", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.2, "via_diameter": 0.3, "via_drill": 0.15, "wire_width": 18}, {"bus_width": 12, "clearance": 0.14, "diff_pair_gap": 0.1, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.225, "microvia_drill": 0.1, "name": "RF", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgb(0, 132, 132)", "track_width": 0.14, "via_diameter": 0.3, "via_drill": 0.15, "wire_width": 12}, {"bus_width": 12, "clearance": 0.1, "diff_pair_gap": 0.1, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.1, "line_style": 0, "microvia_diameter": 0.225, "microvia_drill": 0.1, "name": "Sensitive signal", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgb(204, 102, 0)", "track_width": 0.1, "via_diameter": 0.3, "via_drill": 0.15, "wire_width": 12}], "meta": {"version": 3}, "net_colors": null, "netclass_assignments": {"/BPA": "Sensitive signal", "/BPC": "Sensitive signal", "/IC1A": "Sensitive signal", "/IC1C": "Sensitive signal", "/IC2A": "Sensitive signal", "/IC2C": "Sensitive signal", "/PD4A": "Sensitive signal", "/PD4C": "Sensitive signal", "/WP_COIL+": "Power", "/WP_COIL-": "Power", "/core/Power Management/VLDO_AUDA": "Sensitive signal", "/core/wireless_communication/NFC/VDD": "Power", "Net-(C108-Pad1)": "Power", "Net-(C3-Pad2)": "RF", "Net-(U8-BATTCHGR)": "Power", "VDDAUDA": "Sensitive signal", "VNFC+": "Power"}, "netclass_patterns": [{"netclass": "Power", "pattern": "VCHGIN"}, {"netclass": "Power", "pattern": "VSYS"}, {"netclass": "Power", "pattern": "/core/Power Management/SBB0_0"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/core/Power Management/LDO0_0"}, {"netclass": "Power", "pattern": "VSBB0"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/core/Power Management/LDO0_1"}, {"netclass": "Power", "pattern": "VLDO0"}, {"netclass": "Power", "pattern": "VLDO1"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/core/Power Management/LDO1_1"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/core/Power Management/LDO1_0"}, {"netclass": "Power", "pattern": "VSBB2"}, {"netclass": "Power", "pattern": "VCORE"}, {"netclass": "Power", "pattern": "/core/Power Management/SBB2_0"}, {"netclass": "Power", "pattern": "/core/Power Management/SBB1_0"}, {"netclass": "Power", "pattern": "/core/Power Management/VRECT"}, {"netclass": "Power", "pattern": "/core/Power Management/AC1_C"}, {"netclass": "Power", "pattern": "/core/Power Management/AC2"}, {"netclass": "Power", "pattern": "/core/Power Management/AC1"}, {"netclass": "Power", "pattern": "/core/Power Management/CLMAP1"}, {"netclass": "Power", "pattern": "/core/Power Management/COMM1"}, {"netclass": "Power", "pattern": "/core/Power Management/BST1"}, {"netclass": "Power", "pattern": "/core/Power Management/CLAMP2"}, {"netclass": "Power", "pattern": "/core/Power Management/COMM2"}, {"netclass": "Power", "pattern": "/core/Power Management/BST2"}, {"netclass": "Power", "pattern": "/core/Power Management/V1P8_WC"}, {"netclass": "Power", "pattern": "/core/Power Management/VCC_WC"}, {"netclass": "Power", "pattern": "/core/Power Management/V1P8"}, {"netclass": "Power", "pattern": "/core/Power Management/VCC"}, {"netclass": "Power", "pattern": "/core/Power Management/ADCREF"}, {"netclass": "Power", "pattern": "/core/Power Management/VDD_WC"}, {"netclass": "Power", "pattern": "/core/Power Management/REG"}, {"netclass": "Power", "pattern": "/core/Power Management/VIO_PIN"}, {"netclass": "Power", "pattern": "/core/Power Management/VL"}, {"netclass": "Power", "pattern": "/core/Power Management/LXB"}, {"netclass": "Power", "pattern": "/core/Power Management/LXA"}, {"netclass": "Power", "pattern": "/core/Power Management/BST"}, {"netclass": "Power", "pattern": "/core/Power Management/BATT*"}, {"netclass": "RF", "pattern": "/core/RFIOP"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/core/wireless_communication/NFC/RF_B"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/ANT_SEL"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/GNSS_RF"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/ANT"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/FILT_OUT"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/FILT_IN"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/SW_IN"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/GNSS_S"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/SW_OUT2"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/SW_OUT1"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/SW_OUT2_0"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/GPS*"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/RFWS*_RF*"}, {"netclass": "Power", "pattern": "VCC_NAND*"}, {"netclass": "RF", "pattern": "/core/wireless_communication/LTE/OUT1_GPS"}, {"netclass": "Power", "pattern": "/core/PGND"}, {"netclass": "Power", "pattern": "/core/on_board_sensors/PGND"}, {"netclass": "Power", "pattern": "VNFC*"}, {"netclass": "Power", "pattern": "VLTE*"}, {"netclass": "Power", "pattern": "VLRA*"}, {"netclass": "Power", "pattern": "VLED*"}, {"netclass": "Power", "pattern": "VCHGIN*"}, {"netclass": "Power", "pattern": "/LRA*"}, {"netclass": "Sensitive signal", "pattern": "/core/on_board_sensors/PD*_IN"}, {"netclass": "Sensitive signal", "pattern": "/core/on_board_sensors/ECG*"}, {"netclass": "NFC", "pattern": "/core/wireless_communication/NFC/RF(O|_).*"}, {"netclass": "NFC", "pattern": "/NFC_COIL*"}, {"netclass": "Power", "pattern": "/core/Power Management/VSBB1"}, {"netclass": "Power", "pattern": "VBATT*"}, {"netclass": "<PERSON><PERSON><PERSON>", "pattern": "/core/wireless_communication/NFC/RFI*"}, {"netclass": "Power", "pattern": "VSBB1"}, {"netclass": "RF", "pattern": "/core/FEED*"}, {"netclass": "Power", "pattern": "VDD_LSW_HUM"}, {"netclass": "Power", "pattern": "VDD_LSW_TEMP"}]}, "pcbnew": {"last_paths": {"gencad": "", "idf": "", "netlist": "", "plot": "", "pos_files": "", "specctra_dsn": "", "step": "../../../../../../Downloads/optimistic-proteus.step", "svg": "", "vrml": ""}, "page_layout_descr_file": ""}, "schematic": {"annotate_start_num": 0, "bom_fmt_presets": [], "bom_fmt_settings": {"field_delimiter": ",", "keep_line_breaks": false, "keep_tabs": false, "name": "CSV", "ref_delimiter": ",", "ref_range_delimiter": "", "string_delimiter": "\""}, "bom_presets": [], "bom_settings": {"exclude_dnp": false, "fields_ordered": [{"group_by": false, "label": "Reference", "name": "Reference", "show": true}, {"group_by": true, "label": "Value", "name": "Value", "show": true}, {"group_by": false, "label": "Datasheet", "name": "Datasheet", "show": true}, {"group_by": false, "label": "Footprint", "name": "Footprint", "show": true}, {"group_by": false, "label": "Qty", "name": "${QUANTITY}", "show": true}, {"group_by": true, "label": "DNP", "name": "${DNP}", "show": true}], "filter_string": "", "group_symbols": true, "name": "Grouped By Value", "sort_asc": true, "sort_field": "Reference"}, "connection_grid_size": 50.0, "drawing": {"dashed_lines_dash_length_ratio": 12.0, "dashed_lines_gap_length_ratio": 3.0, "default_line_thickness": 6.0, "default_text_size": 50.0, "field_names": [], "intersheets_ref_own_page": false, "intersheets_ref_prefix": "", "intersheets_ref_short": false, "intersheets_ref_show": false, "intersheets_ref_suffix": "", "junction_size_choice": 3, "label_size_ratio": 0.375, "operating_point_overlay_i_precision": 3, "operating_point_overlay_i_range": "~A", "operating_point_overlay_v_precision": 3, "operating_point_overlay_v_range": "~V", "overbar_offset_ratio": 1.23, "pin_symbol_size": 25.0, "text_offset_ratio": 0.15}, "legacy_lib_dir": "", "legacy_lib_list": [], "meta": {"version": 1}, "net_format_name": "", "ngspice": {"fix_include_paths": true, "fix_passive_vals": false, "meta": {"version": 0}, "model_mode": 0, "workbook_filename": ""}, "page_layout_descr_file": "", "plot_directory": "", "spice_adjust_passive_values": false, "spice_current_sheet_as_root": false, "spice_external_command": "spice \"%I\"", "spice_model_current_sheet_as_root": true, "spice_save_all_currents": false, "spice_save_all_dissipations": false, "spice_save_all_voltages": false, "subpart_first_id": 65, "subpart_id_separator": 0}, "sheets": [["9ab3088e-c554-48d6-9f0c-cc72d0121e39", "Root"], ["85bc8598-5916-4d08-b650-0e94702a1fe7", "core"], ["0939167a-161e-4056-a0a9-261158957311", "Power Management"], ["9be20d24-870f-4e13-b610-c0566d5cb639", "on_board_sensors"], ["e7b67602-ef6f-4d24-a4d6-bbefdb8019e6", "wireless_communication"], ["fc7f0b05-01a5-4a28-9530-e06b15ab695a", "LTE"], ["cc76319b-6642-4992-94ef-a98ad082fce6", "NFC"]], "text_variables": {}}