{"board": {"3dviewports": [], "design_settings": {"defaults": {"board_outline_line_width": 0.15239999999999998, "copper_line_width": 0.15239999999999998, "copper_text_italic": false, "copper_text_size_h": 0.8128, "copper_text_size_v": 0.8128, "copper_text_thickness": 0.15239999999999998, "copper_text_upright": false, "courtyard_line_width": 0.049999999999999996, "dimension_precision": 1, "dimension_units": 0, "dimensions": {"arrow_length": 1270000, "extension_offset": 500000, "keep_text_aligned": true, "suppress_zeroes": false, "text_position": 0, "units_format": 1}, "fab_line_width": 0.15239999999999998, "fab_text_italic": false, "fab_text_size_h": 1.0, "fab_text_size_v": 1.0, "fab_text_thickness": 0.15, "fab_text_upright": false, "other_line_width": 0.15239999999999998, "other_text_italic": false, "other_text_size_h": 1.0, "other_text_size_v": 1.0, "other_text_thickness": 0.15, "other_text_upright": false, "pads": {"drill": 2.7, "height": 5.0, "width": 5.0}, "silk_line_width": 0.15239999999999998, "silk_text_italic": false, "silk_text_size_h": 0.8128, "silk_text_size_v": 0.8128, "silk_text_thickness": 0.15239999999999998, "silk_text_upright": false, "zones": {"45_degree_only": false, "min_clearance": 0.2032}}, "diff_pair_dimensions": [{"gap": 0.0, "via_gap": 0.0, "width": 0.0}], "drc_exclusions": [], "meta": {"version": 2}, "rule_severities": {"annular_width": "error", "clearance": "error", "connection_width": "warning", "copper_edge_clearance": "error", "copper_sliver": "warning", "courtyards_overlap": "error", "diff_pair_gap_out_of_range": "error", "diff_pair_uncoupled_length_too_long": "error", "drill_out_of_range": "error", "duplicate_footprints": "warning", "extra_footprint": "warning", "footprint": "error", "footprint_type_mismatch": "ignore", "hole_clearance": "error", "hole_near_hole": "error", "invalid_outline": "error", "isolated_copper": "warning", "item_on_disabled_layer": "error", "items_not_allowed": "error", "length_out_of_range": "error", "lib_footprint_issues": "ignore", "lib_footprint_mismatch": "ignore", "malformed_courtyard": "error", "microvia_drill_out_of_range": "error", "missing_courtyard": "ignore", "missing_footprint": "warning", "net_conflict": "warning", "npth_inside_courtyard": "ignore", "padstack": "error", "pth_inside_courtyard": "ignore", "shorting_items": "error", "silk_edge_clearance": "warning", "silk_over_copper": "ignore", "silk_overlap": "error", "skew_out_of_range": "error", "solder_mask_bridge": "ignore", "starved_thermal": "error", "text_height": "warning", "text_thickness": "warning", "through_hole_pad_without_hole": "error", "too_many_vias": "error", "track_dangling": "warning", "track_width": "error", "tracks_crossing": "error", "unconnected_items": "error", "unresolved_variable": "error", "via_dangling": "warning", "zone_has_empty_net": "error", "zones_intersect": "error"}, "rules": {"allow_blind_buried_vias": false, "allow_microvias": false, "max_error": 0.005, "min_clearance": 0.2032, "min_connection": 0.0, "min_copper_edge_clearance": 0.3, "min_hole_clearance": 0.0, "min_hole_to_hole": 0.25, "min_microvia_diameter": 0.39877999999999997, "min_microvia_drill": 0.29972, "min_resolved_spokes": 2, "min_silk_clearance": 0.0, "min_text_height": 0.7999999999999999, "min_text_thickness": 0.12, "min_through_hole_diameter": 0.3, "min_track_width": 0.2032, "min_via_annular_width": 0.049999999999999996, "min_via_annulus": 0.15, "min_via_diameter": 0.6, "solder_mask_to_copper_clearance": 0.0, "use_height_for_length_calcs": true}, "teardrop_options": [{"td_onpadsmd": true, "td_onroundshapesonly": false, "td_ontrackend": false, "td_onviapad": true}], "teardrop_parameters": [{"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_round_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_rect_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_track_end", "td_width_to_size_filter_ratio": 0.9}], "track_widths": [0.0, 0.254, 0.3048, 0.4064, 0.508, 0.6096, 0.8128, 1.016, 1.27, 1.524, 1.778, 2.032], "via_dimensions": [{"diameter": 0.0, "drill": 0.0}, {"diameter": 0.6, "drill": 0.3}, {"diameter": 0.7, "drill": 0.4}, {"diameter": 0.8, "drill": 0.5}, {"diameter": 0.9, "drill": 0.6}, {"diameter": 1.0, "drill": 0.7}], "zones_allow_external_fillets": false, "zones_use_no_outline": false}, "layer_presets": [], "viewports": []}, "boards": [], "cvpcb": {"equivalence_files": []}, "erc": {"meta": {"version": 0}, "pin_map": [[0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 0, 1, 0, 1, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 2], [0, 1, 0, 0, 0, 1, 1, 2, 1, 1, 2], [0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 2], [0, 0, 0, 1, 0, 1, 0, 0, 0, 0, 2], [0, 2, 1, 2, 0, 1, 0, 2, 2, 2, 2], [0, 2, 0, 1, 0, 1, 0, 2, 0, 0, 2], [0, 2, 1, 1, 0, 1, 0, 2, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "rule_severities": {"bus_definition_conflict": "error", "bus_label_syntax": "error", "bus_to_bus_conflict": "error", "bus_to_net_conflict": "error", "different_unit_footprint": "error", "different_unit_net": "error", "duplicate_sheet_names": "error", "global_label_dangling": "error", "hier_label_mismatch": "error", "label_dangling": "error", "lib_symbol_issues": "warning", "multiple_net_names": "error", "net_not_bus_member": "error", "no_connect_connected": "error", "no_connect_dangling": "error", "pin_not_connected": "error", "pin_not_driven": "error", "pin_to_pin": "warning", "similar_labels": "error", "unresolved_variable": "error", "wire_dangling": "error"}}, "libraries": {"pinned_footprint_libs": [], "pinned_symbol_libs": []}, "meta": {"filename": "issue5990.kicad_pro", "version": 1}, "net_settings": {"classes": [{"bus_width": 12, "clearance": 0.2, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "<PERSON><PERSON><PERSON>", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.254, "via_diameter": 0.6, "via_drill": 0.3, "wire_width": 6}, {"bus_width": 6, "clearance": 0.4, "diff_pair_gap": 0.2032, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2032, "line_style": 0, "microvia_diameter": 0.6, "microvia_drill": 0.3, "name": "100V", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.254, "via_diameter": 0.6, "via_drill": 0.3, "wire_width": 6}, {"bus_width": 6, "clearance": 0.8, "diff_pair_gap": 0.508, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.508, "line_style": 0, "microvia_diameter": 0.6, "microvia_drill": 0.3, "name": "170V", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.4064, "via_diameter": 0.6, "via_drill": 0.3, "wire_width": 6}], "hidden_nets": [], "meta": {"version": 3}, "net_colors": null, "netclass_assignments": null, "netclass_patterns": []}, "pcbnew": {"last_paths": {"gencad": "", "idf": "", "netlist": "", "plot": "", "pos_files": "", "specctra_dsn": "", "step": "../../Mech/001.04.010.1020.step", "svg": "", "vmrl": "../../Mech/001.04.010.0620.wrl", "vrml": ""}, "page_layout_descr_file": ""}, "schematic": {"drawing": {"default_bus_thickness": 12.0, "default_junction_size": 20.0, "default_line_thickness": 6.0, "default_text_size": 40.0, "default_wire_thickness": 6.0, "field_names": [], "intersheets_ref_prefix": "[", "intersheets_ref_short": false, "intersheets_ref_show": false, "intersheets_ref_suffix": "]", "pin_symbol_size": 25.0, "text_offset_ratio": 0.3}, "legacy_lib_dir": "", "legacy_lib_list": [], "meta": {"version": 0}, "net_format_name": "", "page_layout_descr_file": "", "plot_directory": "", "spice_adjust_passive_values": false, "spice_external_command": "spice \"%I\"", "subpart_first_id": 65, "subpart_id_separator": 0}, "sheets": [["8fbdd1bf-c6bc-48ff-8b55-861d7eb93f67", ""]], "text_variables": {}}