(kicad_pcb (version 20221018) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.Adhes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
    (50 "User.1" user)
    (51 "User.2" user)
    (52 "User.3" user)
    (53 "User.4" user)
    (54 "User.5" user)
    (55 "User.6" user)
    (56 "User.7" user)
    (57 "User.8" user)
    (58 "User.9" user)
  )

  (setup
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 4)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "Net-(J1-Pin_1)")

  (footprint "Connector_PinHeader_2.54mm:PinHeader_1x02_P2.54mm_Vertical" (layer "F.Cu")
    (tstamp 68bc9f46-85a1-4162-a5c3-834b38cb9df1)
    (at 130 62.46)
    (descr "Through hole straight pin header, 1x02, 2.54mm pitch, single row")
    (tags "Through hole pin header THT 1x02 2.54mm single row")
    (property "Sheetfile" "drc_test.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "Generic connector, single row, 01x02, script generated (kicad-library-utils/schlib/autogen/connector/)")
    (property "ki_keywords" "connector")
    (path "/b069c179-b0e0-42a3-b403-2b7c752f320d")
    (attr through_hole)
    (fp_text reference "J1" (at 0 -2.33) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 2e4968c6-6be4-4e33-aa0d-c33bead056c1)
    )
    (fp_text value "Conn_01x02" (at 0 4.87) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 392d1358-fe9c-4107-b5bd-c53b0cbd2dfc)
    )
    (fp_text user "${REFERENCE}" (at 0 1.27 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 7186d18f-72ac-40ed-af23-5411fa618e42)
    )
    (fp_line (start -1.33 -1.33) (end 0 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp fe204db4-d6a9-4861-9e60-6b7cbc12232b))
    (fp_line (start -1.33 0) (end -1.33 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp de55de8c-87fd-4a6d-9ac4-aadfb00c3741))
    (fp_line (start -1.33 1.27) (end -1.33 3.87)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0ac6dadb-2c9b-4260-86ee-10c9f4005f67))
    (fp_line (start -1.33 1.27) (end 1.33 1.27)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f2b7276f-cd57-4a9e-9cf4-5353b8905df5))
    (fp_line (start -1.33 3.87) (end 1.33 3.87)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6abba294-db45-4375-82fb-e19654c1da48))
    (fp_line (start 1.33 1.27) (end 1.33 3.87)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b880dff7-048e-41c1-b179-87c3a6f48b02))
    (fp_line (start -1.8 -1.8) (end -1.8 4.35)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 593a9103-8809-4d41-a4d0-e7e08c576de2))
    (fp_line (start -1.8 4.35) (end 1.8 4.35)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 9abd1955-64d4-49db-9268-fdd86db93d13))
    (fp_line (start 1.8 -1.8) (end -1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 33ee2868-9e9c-4b07-9729-f19f823c592d))
    (fp_line (start 1.8 4.35) (end 1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 62278c55-4736-4459-8183-bf8f34c2404b))
    (fp_line (start -1.27 -0.635) (end -0.635 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp db6b0bcf-ecd5-4ef9-8e8c-9286dd4e6e7e))
    (fp_line (start -1.27 3.81) (end -1.27 -0.635)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b709afe9-6615-458f-9996-52c8c10a8447))
    (fp_line (start -0.635 -1.27) (end 1.27 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9507ba34-3989-4283-bdad-08c0174ed286))
    (fp_line (start 1.27 -1.27) (end 1.27 3.81)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f168ab76-be7a-44e5-b324-0fede1cb97cc))
    (fp_line (start 1.27 3.81) (end -1.27 3.81)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d63a17a1-dd57-4f5f-a5f9-01c563f19034))
    (pad "1" thru_hole rect (at 0 0) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 1 "Net-(J1-Pin_1)") (pinfunction "Pin_1") (pintype "passive") (tstamp 69f942b8-65af-40b0-b6de-f6b7aec43de6))
    (pad "2" thru_hole oval (at 0 2.54) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 1 "Net-(J1-Pin_1)") (pinfunction "Pin_2") (pintype "passive") (tstamp 82a572b9-d886-4709-8c47-30c7476f1065))
    (model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x02_P2.54mm_Vertical.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_line (start 137.5 55) (end 122.5 55)
    (stroke (width 0.1) (type default)) (layer "Edge.Cuts") (tstamp 0729a4eb-2a78-47a1-a132-03bb32f0a616))
  (gr_line (start 122.5 55) (end 122.5 72.5)
    (stroke (width 0.1) (type default)) (layer "Edge.Cuts") (tstamp 36984778-a4cc-4023-b701-c6e818544244))
  (gr_line (start 137.5 72.5) (end 137.5 55)
    (stroke (width 0.1) (type default)) (layer "Edge.Cuts") (tstamp 41faf862-7eb4-4d81-8181-73323b62ded0))
  (gr_line (start 122.5 72.5) (end 137.5 72.5)
    (stroke (width 0.1) (type default)) (layer "Edge.Cuts") (tstamp f51d9d01-9882-4633-9a11-1824eba2e257))

  (zone (net 0) (net_name "") (layer "F.Cu") (tstamp 324ad930-1eba-45f6-9808-c8b615e087db) (hatch edge 0.5)
    (connect_pads (clearance 0))
    (min_thickness 0.25) (filled_areas_thickness no)
    (keepout (tracks allowed) (vias allowed) (pads allowed) (copperpour not_allowed) (footprints allowed))
    (fill (thermal_gap 0.5) (thermal_bridge_width 0.5))
    (polygon
      (pts
        (xy 127.499318 60.26828)
        (xy 132.499318 60.26828)
        (xy 132.499318 61.26828)
        (xy 128.999318 61.26828)
        (xy 128.999318 63.25)
        (xy 130.25 63.25)
        (xy 130.25 64.25)
        (xy 128.999318 64.25)
        (xy 128.999318 67.01828)
        (xy 131.249318 67.01828)
        (xy 131.249318 65.5)
        (xy 132.499318 65.5)
        (xy 132.499321 68.01828)
        (xy 127.499318 68.01828)
      )
    )
  )
  (zone (net 1) (net_name "Net-(J1-Pin_1)") (layer "F.Cu") (tstamp d4e487fd-a8a8-45f4-bf48-98c28fc9d9d1) (hatch edge 0.5)
    (connect_pads (clearance 0.5))
    (min_thickness 0.25) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.5) (thermal_bridge_width 0.5))
    (polygon
      (pts
        (xy 120 52.5)
        (xy 140 52.5)
        (xy 140 75)
        (xy 120 75)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 130.25 66.330633)
        (xy 130.463483 66.273433)
        (xy 130.463492 66.273429)
        (xy 130.677578 66.1736)
        (xy 130.871082 66.038105)
        (xy 131.037636 65.871551)
        (xy 131.098959 65.838066)
        (xy 131.16865 65.84305)
        (xy 131.224584 65.884921)
        (xy 131.249001 65.950386)
        (xy 131.249317 65.959232)
        (xy 131.249318 66.89428)
        (xy 131.229634 66.961319)
        (xy 131.17683 67.007074)
        (xy 131.125318 67.01828)
        (xy 129.123318 67.01828)
        (xy 129.056279 66.998595)
        (xy 129.010524 66.945791)
        (xy 128.999318 66.89428)
        (xy 128.999318 66.185557)
        (xy 129.019001 66.118522)
        (xy 129.071805 66.072767)
        (xy 129.140964 66.062823)
        (xy 129.194441 66.083987)
        (xy 129.322414 66.173595)
        (xy 129.32242 66.173599)
        (xy 129.536507 66.273429)
        (xy 129.536516 66.273433)
        (xy 129.75 66.330634)
        (xy 129.75 65.435501)
        (xy 129.857685 65.48468)
        (xy 129.964237 65.5)
        (xy 130.035763 65.5)
        (xy 130.142315 65.48468)
        (xy 130.25 65.435501)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 130.193039 64.269685)
        (xy 130.238794 64.322489)
        (xy 130.25 64.374)
        (xy 130.25 64.564498)
        (xy 130.142315 64.51532)
        (xy 130.035763 64.5)
        (xy 129.964237 64.5)
        (xy 129.857685 64.51532)
        (xy 129.75 64.564498)
        (xy 129.75 64.374)
        (xy 129.769685 64.306961)
        (xy 129.822489 64.261206)
        (xy 129.874 64.25)
        (xy 130.126 64.25)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 130.25 63.126)
        (xy 130.230315 63.193039)
        (xy 130.177511 63.238794)
        (xy 130.126 63.25)
        (xy 129.874 63.25)
        (xy 129.806961 63.230315)
        (xy 129.761206 63.177511)
        (xy 129.75 63.126)
        (xy 129.749999 62.895501)
        (xy 129.857685 62.94468)
        (xy 129.964237 62.96)
        (xy 130.035763 62.96)
        (xy 130.142315 62.94468)
        (xy 130.25 62.895501)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 137.442539 55.020185)
        (xy 137.488294 55.072989)
        (xy 137.4995 55.1245)
        (xy 137.4995 72.3755)
        (xy 137.479815 72.442539)
        (xy 137.427011 72.488294)
        (xy 137.3755 72.4995)
        (xy 122.6245 72.4995)
        (xy 122.557461 72.479815)
        (xy 122.511706 72.427011)
        (xy 122.5005 72.3755)
        (xy 122.5005 60.26828)
        (xy 127.499318 60.26828)
        (xy 127.499318 68.01828)
        (xy 132.49932 68.01828)
        (xy 132.499321 68.01828)
        (xy 132.499318 65.5)
        (xy 131.425249 65.5)
        (xy 131.35821 65.480315)
        (xy 131.312455 65.427511)
        (xy 131.302511 65.358353)
        (xy 131.305474 65.343906)
        (xy 131.330636 65.25)
        (xy 130.433686 65.25)
        (xy 130.459493 65.209844)
        (xy 130.5 65.071889)
        (xy 130.5 64.928111)
        (xy 130.459493 64.790156)
        (xy 130.433686 64.75)
        (xy 131.330636 64.75)
        (xy 131.330635 64.749999)
        (xy 131.273432 64.536513)
        (xy 131.273429 64.536507)
        (xy 131.1736 64.322422)
        (xy 131.173599 64.32242)
        (xy 131.038113 64.128926)
        (xy 131.038108 64.12892)
        (xy 130.915665 64.006477)
        (xy 130.88218 63.945154)
        (xy 130.887164 63.875462)
        (xy 130.929036 63.819529)
        (xy 130.960013 63.802614)
        (xy 131.092086 63.753354)
        (xy 131.092093 63.75335)
        (xy 131.207187 63.66719)
        (xy 131.20719 63.667187)
        (xy 131.29335 63.552093)
        (xy 131.293354 63.552086)
        (xy 131.343596 63.417379)
        (xy 131.343598 63.417372)
        (xy 131.349999 63.357844)
        (xy 131.35 63.357827)
        (xy 131.35 62.71)
        (xy 130.433686 62.71)
        (xy 130.459493 62.669844)
        (xy 130.5 62.531889)
        (xy 130.5 62.388111)
        (xy 130.459493 62.250156)
        (xy 130.433686 62.21)
        (xy 131.35 62.21)
        (xy 131.349999 62.209999)
        (xy 131.35 61.562172)
        (xy 131.349999 61.562155)
        (xy 131.343598 61.502627)
        (xy 131.343596 61.502619)
        (xy 131.318605 61.435613)
        (xy 131.313621 61.365921)
        (xy 131.347106 61.304598)
        (xy 131.40843 61.271114)
        (xy 131.434787 61.26828)
        (xy 132.499318 61.26828)
        (xy 132.499318 60.26828)
        (xy 127.499318 60.26828)
        (xy 122.5005 60.26828)
        (xy 122.5005 55.1245)
        (xy 122.520185 55.057461)
        (xy 122.572989 55.011706)
        (xy 122.6245 55.0005)
        (xy 137.3755 55.0005)
      )
    )
  )
)
