(kicad_pcb (version 20231007) (generator pcbnew)

  (general
    (thickness 1.6062)
  )

  (paper "A4")
  (title_block
    (title "grblPANEL")
    (date "2023-04-28")
    (rev "B")
    (company "dresco")
  )

  (layers
    (0 "F.Cu" signal)
    (1 "In1.Cu" power)
    (2 "In2.Cu" power)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.<PERSON>" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
    (50 "User.1" user)
    (51 "User.2" user)
    (52 "User.3" user)
    (53 "User.4" user)
    (54 "User.5" user)
    (55 "User.6" user)
    (56 "User.7" user)
    (57 "User.8" user)
    (58 "User.9" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (color "Black") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "prepreg") (thickness 0.2104) (material "FR4") (epsilon_r 4.6) (loss_tangent 0.02))
      (layer "In1.Cu" (type "copper") (thickness 0.0152))
      (layer "dielectric 2" (type "core") (thickness 1.065) (material "FR4") (epsilon_r 4.6) (loss_tangent 0.02))
      (layer "In2.Cu" (type "copper") (thickness 0.0152))
      (layer "dielectric 3" (type "prepreg") (thickness 0.2104) (material "FR4") (epsilon_r 4.6) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (color "Black") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "ENIG")
      (dielectric_constraints yes)
    )
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions true)
      (usegerberattributes true)
      (usegerberadvancedattributes false)
      (creategerberjobfile false)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 6)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (pdf_front_fp_property_popups true)
      (pdf_back_fp_property_popups true)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue false)
      (plotfptext true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk true)
      (outputformat 1)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "manufacturing/")
    )
  )

  (net 0 "")
  (net 1 "GND")
  (net 2 "/Power/VIN_PI_OUT")
  (net 3 "+3V3")
  (net 4 "/MCU/~{RST}")
  (net 5 "/Power/PWR_LED_K")
  (net 6 "DISPLAY_D{slash}C")
  (net 7 "DISPLAY_SDA")
  (net 8 "Net-(JP401-B)")
  (net 9 "Net-(R202-Pad1)")
  (net 10 "/MCU/LED_K")
  (net 11 "/MCU/SWCLK{slash}BOOT0")
  (net 12 "MODBUS_RX")
  (net 13 "MODBUS_TX")
  (net 14 "/Modbus/CONN_RS485_A")
  (net 15 "/Modbus/CONN_RS485_B")
  (net 16 "/Modbus/RS485_B")
  (net 17 "/Modbus/RS485_A")
  (net 18 "/Modbus/RS485_MODE_SELECT")
  (net 19 "/Display/CONN_SCL")

  (footprint "TestPoint:TestPoint_THTPad_D2.0mm_Drill1.0mm" (layer "F.Cu")
    (tstamp 0000468b-72e2-4a41-a1c6-bb64ad8b1463)
    (at 161 100.5)
    (descr "THT pad as test Point, diameter 2.0mm, hole diameter 1.0mm")
    (tags "test point THT pad")
    (property "Reference" "TP502" (at 0 -1.998 0) (layer "F.SilkS") hide (tstamp ddbec07b-201e-4d15-a60d-bbce6f0c43ae)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "TestPoint" (at 0 2.05 0) (layer "F.Fab") hide (tstamp 4ec50636-4d6d-4b7a-b9f0-f300fd1ec630)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp cd22f57a-80b4-4e5d-b089-638fda4b0cd0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp f6d91ea7-a43c-4f9b-87f7-120f4f2361a6)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "test point" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 73417084-20b5-4131-b3f7-9aae1dc01d30)
      (effects (font (size 1.27 1.27)))
    )
    (path "/2c60fe6e-45f9-484b-a2a4-9c1dd728d5af/2818bd06-857c-4f88-99f8-f837cac02934")
    (sheetname "Display")
    (sheetfile "grblPANEL_display.kicad_sch")
    (fp_circle (center 0 0) (end 0 1.2)
      (stroke (width 0.12) (type solid)) (fill none) (layer "F.SilkS") (tstamp 640e8dd2-e448-4bd4-a4b3-36c648d34ddf))
    (fp_circle (center 0 0) (end 1.5 0)
      (stroke (width 0.05) (type solid)) (fill none) (layer "F.CrtYd") (tstamp 97529426-5de3-4bd9-a960-3269c4cc6671))
    (fp_text user "${REFERENCE}" (at -0.2 -2.3 0) (layer "F.Fab") (tstamp 33636c75-7ee5-4255-9280-48d307f9d202)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad "1" thru_hole circle (at 0 0) (size 2 2) (drill 1) (layers "*.Cu" "*.Mask")
      (net 19 "/Display/CONN_SCL") (pinfunction "1") (pintype "passive")
      (tstamp 71a135f7-7135-4f66-b1e5-f4c9e06c7f85)
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp 03a5680f-1a43-4752-b5bb-6a2b3cff277e)
    (at 178.3 107 90)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R202" (at 0 -1.43 90) (layer "F.SilkS") hide (tstamp ed2d8701-6848-4dc8-a8ff-012a5d7a0cab)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "200" (at 0 1.43 90) (layer "F.Fab") (tstamp 9b51937e-d9e8-472f-8dd3-2b53299ec04f)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 90 unlocked) (layer "F.Fab") hide (tstamp 70073732-4982-467b-a572-dc4f07caeacf)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 90 unlocked) (layer "F.Fab") hide (tstamp a22ca0ba-121b-4928-b5e8-8b12a9ebc753)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Resistor" (at 0 0 90 unlocked) (layer "F.Fab") hide (tstamp b6c1fbe1-9cb3-4c71-a3ea-756250b7b4ba)
      (effects (font (size 1.27 1.27)))
    )
    (path "/437f2ceb-712b-41a0-9e79-c6c2becba629/df571d19-3631-45b0-a40b-90b7c04354a7")
    (sheetname "MCU")
    (sheetfile "grblPANEL_mcu.kicad_sch")
    (attr smd)
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 269852ac-9956-430b-ad10-9b090bc3b4cf))
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c1066901-3f09-4318-ac08-11181ce66834))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6cbd71b5-2c8e-4b35-b942-8ad3a69fea35))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 959e287e-1236-4f3b-bf58-cd1aff0f6f52))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp a78073d7-5dbd-43dd-a118-ba4d95b622ec))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 8e3129b7-8289-46d2-9661-9c629957d2b1))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5701eaed-ed41-4ad5-850a-c11f4bdf249f))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 62d01180-2d69-4136-acd2-28ed5d49397d))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 24d9b70c-7616-47d8-be16-7c32350ca55e))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0f166b18-0565-4ec8-9a3f-bc1326ec6e01))
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab") (tstamp f5bf3cbe-1c59-45c1-8070-a9c796eb80c8)
      (effects (font (size 0.4 0.4) (thickness 0.06)))
    )
    (pad "1" smd roundrect (at -0.825 0 90) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 9 "Net-(R202-Pad1)") (pintype "passive")
      (tstamp 97f2fdf3-a80c-4a1b-9acc-097bca187fe7)
    )
    (pad "2" smd roundrect (at 0.825 0 90) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pintype "passive")
      (tstamp 3f6497ad-9438-4ee4-bd4d-4039f66e12fc)
    )
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "TestPoint:TestPoint_THTPad_D2.0mm_Drill1.0mm" (layer "F.Cu")
    (tstamp 1d83d55e-536a-4294-bde0-a90c94fe37f8)
    (at 161 97)
    (descr "THT pad as test Point, diameter 2.0mm, hole diameter 1.0mm")
    (tags "test point THT pad")
    (property "Reference" "TP504" (at 0 -1.998 0) (layer "F.SilkS") hide (tstamp 430e5f04-5057-476b-ad11-b581f8d55a32)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "TestPoint" (at 0 2.05 0) (layer "F.Fab") hide (tstamp d8655c3a-daa1-4143-ab78-0c82c29d33d9)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp fb259b99-6a72-427d-bf88-e85c12b7acb1)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 2ca2ac47-c274-4064-a225-427e0bfda38d)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "test point" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 2f7d1754-c716-40da-a9af-27fb4ed613a8)
      (effects (font (size 1.27 1.27)))
    )
    (path "/2c60fe6e-45f9-484b-a2a4-9c1dd728d5af/9bb6ccac-a2c0-49f0-8823-dccefb3cd5bd")
    (sheetname "Display")
    (sheetfile "grblPANEL_display.kicad_sch")
    (fp_circle (center 0 0) (end 0 1.2)
      (stroke (width 0.12) (type solid)) (fill none) (layer "F.SilkS") (tstamp 8164a8aa-8f8d-4e54-af24-881822d5d0a8))
    (fp_circle (center 0 0) (end 1.5 0)
      (stroke (width 0.05) (type solid)) (fill none) (layer "F.CrtYd") (tstamp 667ae065-d8bc-41aa-8a95-f1920fb540a8))
    (fp_text user "${REFERENCE}" (at 0.1 2.2 0) (layer "F.Fab") (tstamp b7cc1a39-a3c3-4ed4-9768-1e63cf503897)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad "1" thru_hole circle (at 0 0) (size 2 2) (drill 1) (layers "*.Cu" "*.Mask")
      (net 6 "DISPLAY_D{slash}C") (pinfunction "1") (pintype "passive")
      (tstamp 56162aae-2ca3-4728-8b50-de5444fe193a)
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp 52a9768b-a6f7-4175-81e4-2c8223f7ca82)
    (at 167.4 94.8 180)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R402" (at 0 -1.43 0) (layer "F.SilkS") hide (tstamp 8aaaa541-8852-4a9f-af7c-e9214cdca706)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "10k" (at 0 1.43 0) (layer "F.Fab") (tstamp 0fbe507e-84a6-40d8-9ad0-ea40a77f58e0)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp f08a5ce9-1eaf-4334-aa41-0904c51c7739)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 2c5bcf5a-65c3-43bc-8772-7f715ca2a97b)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Resistor" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 9e4ec15a-8d14-4ccf-b680-43d602929581)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/ea3222be-09e2-44a8-80f3-7057a7d842ed")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr smd)
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 530604ff-ea01-4f2f-bd0d-bebbc80d0e4f))
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 535f0521-3fa8-4c20-beea-3bfc1be70d8c))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2f19f199-f624-48f0-8085-8fe29b4bb07e))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 126e009c-0ff5-4fc7-8271-b52ab6f67a39))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp ba6a729b-f962-4e2d-ac5c-328fef563e83))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp faf5bda7-1c9e-4106-8eee-14b4c607edd5))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 49eece06-0de3-4edc-a77d-e0c5b91ce090))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 68be6fe7-d158-41c8-acb5-6d848b733b29))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9b0a70b1-4c3d-48b6-91a5-6a199a321b2c))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cf5a589c-54d6-4ff3-9445-1613235d7081))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 35a2ba87-be66-4fd1-ba5f-d7e6eaefcb4e)
      (effects (font (size 0.4 0.4) (thickness 0.06)))
    )
    (pad "1" smd roundrect (at -0.825 0 180) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pintype "passive")
      (tstamp e0578f4f-ba11-46b7-9ebd-ba22c77ed930)
    )
    (pad "2" smd roundrect (at 0.825 0 180) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 12 "MODBUS_RX") (pintype "passive")
      (tstamp 7bc0d97c-c2b1-4603-883c-f10513f8cf70)
    )
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Package_SO:SOIC-8_3.9x4.9mm_P1.27mm" (layer "F.Cu")
    (tstamp 5b205737-a0d6-4bdc-9b7f-f81b83838ea7)
    (at 169.5 98.5)
    (descr "SOIC, 8 Pin (JEDEC MS-012AA, https://www.analog.com/media/en/package-pcb-resources/package/pkg_pdf/soic_narrow-r/r_8.pdf), generated with kicad-footprint-generator ipc_gullwing_generator.py")
    (tags "SOIC SO")
    (property "Reference" "U401" (at 0 -3.4 0) (layer "F.SilkS") hide (tstamp 91812611-1919-4909-bde9-7ae5cff3bd92)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "THVD1406" (at 0.5 1.5 0) (layer "F.Fab") (tstamp 6a23dd1b-bf0b-45c3-b133-2da4434254cf)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 913691b8-5a8d-4f94-9a99-7d1ffd059657)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 7e207e46-d75d-42fd-a529-496dd86db397)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 10c65bf1-0600-4d75-8c14-3dbc86bca941)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/72eb354b-fa6b-411c-b8d5-f1b865e88ee1")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr smd)
    (fp_line (start 0 -2.56) (end -3.45 -2.56)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 41a9e236-af0f-4b1a-96c4-3d03ca2b1566))
    (fp_line (start 0 -2.56) (end 1.95 -2.56)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 82889758-7bdc-48f8-afc1-385fc806cd42))
    (fp_line (start 0 2.56) (end -1.95 2.56)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp fbcae9eb-9ff9-41bf-bc4e-15b712c6d9fa))
    (fp_line (start 0 2.56) (end 1.95 2.56)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 8f1346b8-2177-4e64-ba8e-de2fbbd78ccf))
    (fp_line (start -3.7 -2.7) (end -3.7 2.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 38bca8f8-14d5-462d-b1ff-b8fa11863040))
    (fp_line (start -3.7 2.7) (end 3.7 2.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 47e603f6-1af9-4ada-be46-d88069cb6a4e))
    (fp_line (start 3.7 -2.7) (end -3.7 -2.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e709ab3f-3f7e-4b4f-9dab-d161b3c2cf9a))
    (fp_line (start 3.7 2.7) (end 3.7 -2.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6931d80c-01fa-4d3c-bf80-a649e3626829))
    (fp_line (start -1.95 -1.475) (end -0.975 -2.45)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b93aaf01-793f-45e3-a3e6-3023a4f397dd))
    (fp_line (start -1.95 2.45) (end -1.95 -1.475)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a9de8011-2ec3-488a-9f18-5358e7ae5545))
    (fp_line (start -0.975 -2.45) (end 1.95 -2.45)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 43510ed1-af92-440b-933c-afab2846c956))
    (fp_line (start 1.95 -2.45) (end 1.95 2.45)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f223fbed-5ea4-4a97-87b2-debee1295804))
    (fp_line (start 1.95 2.45) (end -1.95 2.45)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 20357ac9-31e3-4f87-a716-cdaf579fd155))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 4e4a7c4b-1a52-40ab-bc58-f85cd567368a)
      (effects (font (size 0.98 0.98) (thickness 0.15)))
    )
    (pad "1" smd roundrect (at -2.475 -1.905) (size 1.95 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 12 "MODBUS_RX") (pinfunction "R") (pintype "output")
      (tstamp 2bfd11ae-83e9-4833-975e-38f66e22ef7d)
    )
    (pad "2" smd roundrect (at -2.475 -0.635) (size 1.95 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 18 "/Modbus/RS485_MODE_SELECT") (pinfunction "~{RE}") (pintype "input")
      (tstamp 6d7e3adf-1380-4d9d-8dc5-a4744558e571)
    )
    (pad "3" smd roundrect (at -2.475 0.635) (size 1.95 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 18 "/Modbus/RS485_MODE_SELECT") (pinfunction "~{SHDN}") (pintype "input")
      (tstamp 066b8f89-1715-4308-91bf-d772d116fda8)
    )
    (pad "4" smd roundrect (at -2.475 1.905) (size 1.95 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 13 "MODBUS_TX") (pinfunction "D") (pintype "input")
      (tstamp f1fbf684-ca64-43c0-84a0-5c666f80e23c)
    )
    (pad "5" smd roundrect (at 2.475 1.905) (size 1.95 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "GND") (pintype "power_in")
      (tstamp 8c001da0-850b-40c2-8414-54a50de3a40e)
    )
    (pad "6" smd roundrect (at 2.475 0.635) (size 1.95 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 17 "/Modbus/RS485_A") (pinfunction "A") (pintype "bidirectional")
      (tstamp 267fafc6-6934-49cb-b18f-790a45b865ef)
    )
    (pad "7" smd roundrect (at 2.475 -0.635) (size 1.95 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 16 "/Modbus/RS485_B") (pinfunction "B") (pintype "bidirectional")
      (tstamp d39650d2-00d3-4051-bb1d-0964554a9556)
    )
    (pad "8" smd roundrect (at 2.475 -1.905) (size 1.95 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pinfunction "Vcc") (pintype "power_in")
      (tstamp de7894b8-4195-4a7e-bd8e-30bd8451076a)
    )
    (model "${KICAD6_3DMODEL_DIR}/Package_SO.3dshapes/SOIC-8_3.9x4.9mm_P1.27mm.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp 5b601c37-1955-4a10-908e-99861adf593f)
    (at 183.5 105.5)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R114" (at 0 2 0) (layer "F.SilkS") hide (tstamp 1fff870a-f6b2-4904-b4cc-8a412fc45a57)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "4k7" (at 0 1.43 0) (layer "F.Fab") (tstamp cd5273f2-e1ad-43a9-a619-7e358e4191f9)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 565eaa25-18d4-4732-8188-5ca73b4b92fa)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp ae195bfd-25c9-4ebf-9490-c0f65c704bb8)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Resistor" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 38ebd934-7ca9-45d2-ae84-f380e8140bd6)
      (effects (font (size 1.27 1.27)))
    )
    (path "/d3c757f4-85a2-4c64-b768-7bb57e3daca0/d50859d2-5b5b-4a45-af68-faab342ab65b")
    (sheetname "Power")
    (sheetfile "grblPANEL_power.kicad_sch")
    (attr smd)
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 70c100a5-e0d4-432f-b1c5-a0a8339f473a))
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c2754980-4fb8-48d0-a685-4fc18b813832))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 60df720b-37e4-432b-a1a4-aa3ca477120d))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 909bbee9-c6bc-4e36-b766-f71326ca0d38))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 04a35d52-06a1-41c3-a95a-3274110b8f55))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 38c0c370-217e-4389-bced-c7992a7051d1))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7a2e5a4c-083a-46ca-a824-40cabfff004a))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1253a765-735c-43f0-ab41-adcb29268b66))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cdda5d63-ef50-48aa-8344-c3bb9e016f3c))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7124ff69-af16-477b-ad2b-7a5afb92f3ff))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 75192606-6fb8-43e2-94b9-3d6a2701aec2)
      (effects (font (size 0.4 0.4) (thickness 0.06)))
    )
    (pad "1" smd roundrect (at -0.825 0) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "/Power/PWR_LED_K") (pintype "passive")
      (tstamp fe13aee8-4d3f-4d2b-91fa-572e7b9cf35f)
    )
    (pad "2" smd roundrect (at 0.825 0) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive")
      (tstamp 951f3ba0-cccb-4349-a692-1424a6b55d27)
    )
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "TestPoint:TestPoint_THTPad_D2.0mm_Drill1.0mm" (layer "F.Cu")
    (tstamp 61692664-d6b9-41fd-bb38-7d86449f9a3c)
    (at 161 89.5)
    (descr "THT pad as test Point, diameter 2.0mm, hole diameter 1.0mm")
    (tags "test point THT pad")
    (property "Reference" "TP401" (at 0 -1.998 0) (layer "F.SilkS") hide (tstamp ca54138d-778e-4e7c-ac90-29f2805abdc0)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "TestPoint" (at 0 2.05 0) (layer "F.Fab") hide (tstamp 3028023c-1c6f-42c4-8ebe-ec6f209ede14)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp ed258358-f65b-443c-80c4-9b234b1395dc)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 08d42848-bc94-476d-ba92-34bc00ddb639)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "test point" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 4fced291-746c-4bc0-bd4b-5939a270d9d6)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/b49c1632-c0fa-4884-ad05-f94341e1d2e4")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (fp_circle (center 0 0) (end 0 1.2)
      (stroke (width 0.12) (type solid)) (fill none) (layer "F.SilkS") (tstamp f0873454-9fb0-4f62-96b1-d9fd73899e07))
    (fp_circle (center 0 0) (end 1.5 0)
      (stroke (width 0.05) (type solid)) (fill none) (layer "F.CrtYd") (tstamp 399d6edc-6af7-48d1-b362-0e07426045d8))
    (fp_text user "${REFERENCE}" (at 0 -2 0) (layer "F.Fab") (tstamp b9f2b5e9-df14-4348-bf2e-cb758633d066)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad "1" thru_hole circle (at 0 0) (size 2 2) (drill 1) (layers "*.Cu" "*.Mask")
      (net 12 "MODBUS_RX") (pinfunction "1") (pintype "passive")
      (tstamp 88dcfcfe-ae4a-4b5c-a90b-0de98fe6fd3d)
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp 622f3622-d8db-43f0-8859-b3671bb65391)
    (at 176.7 101.5 90)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R404" (at 0 -1.5 90) (layer "F.SilkS") hide (tstamp b495b3a5-0e1e-45e6-8021-27e5cde93212)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "DNP" (at -2.5 0.3 90) (layer "F.Fab") (tstamp 44cb7419-0a61-4157-b95f-c5b41a294500)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 90 unlocked) (layer "F.Fab") hide (tstamp 04ab4641-c8b4-4467-b0b0-569109177667)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 90 unlocked) (layer "F.Fab") hide (tstamp 496e01a9-bb06-47c5-ae32-ebf537bd4dcb)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Resistor" (at 0 0 90 unlocked) (layer "F.Fab") hide (tstamp 565a5bd8-9284-4f95-a116-80c46da587b9)
      (effects (font (size 1.27 1.27)))
    )
    (property "dnp" "" (at 0 0 0) (layer "F.Fab") hide (tstamp d7bdb6b5-c6d2-45bb-a7cf-dd5e2b57f8ae)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/21081a96-bc0e-418d-8648-7aa600383e35")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr smd)
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c1b90f28-92ba-43cd-a1b9-70cb00e64afb))
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 32b3cc5d-61d8-4e0d-b3b1-69df532a067d))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 33af8346-3a49-4c80-a4a1-87fdbb0e2ea3))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 174d8118-1582-4837-9e1e-76e213b7a04a))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 37649d50-a3d3-4f4f-8738-700b8b5e0f9f))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 19bf39af-68c8-48a2-9dab-4ffae3c9b466))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b242f28a-2c88-44c3-bb2b-50dfd389faa8))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0ae5d55e-6d2b-4f69-8487-9bdbcc55f925))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp eb1805c8-b3ba-4c1c-a052-ffd59cb480c7))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp df3e68e9-7f16-43dc-a6d2-8e275e2e96ca))
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab") (tstamp 9d4442c9-8906-4607-9d9b-37435bc0e9e8)
      (effects (font (size 0.4 0.4) (thickness 0.06)))
    )
    (pad "1" smd roundrect (at -0.825 0 90) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pintype "passive")
      (tstamp e1760934-cf5a-4398-a7cd-9a44284ec279)
    )
    (pad "2" smd roundrect (at 0.825 0 90) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 17 "/Modbus/RS485_A") (pintype "passive")
      (tstamp d3105f33-35ad-4089-b419-0bc297d49fd9)
    )
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_1206_3216Metric" (layer "F.Cu")
    (tstamp 6535d353-65c3-4da5-ba70-fbca778cad70)
    (at 180 102)
    (descr "Resistor SMD 1206 (3216 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R406" (at 0 2 0) (layer "F.SilkS") hide (tstamp 241a6430-bb9c-4462-8d54-5916b5c4e767)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "10" (at 0 1.82 0) (layer "F.Fab") (tstamp 224f2c73-ca0d-487a-af4d-3cff9ad17de3)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 9e454c28-1f07-4fde-83f3-87ebffdaa252)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 293941bd-51cb-4245-9ba4-68426b350ed4)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Resistor" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp edff2811-c07c-4f42-97f7-8ba296ea129b)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/a7f70079-89a4-442b-8737-f29bf6c60f2e")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr smd)
    (fp_line (start -0.727064 -0.91) (end 0.727064 -0.91)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cc3bcbaf-b707-485b-b1bf-3bbf6001a3c0))
    (fp_line (start -0.727064 0.91) (end 0.727064 0.91)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 13d1bad6-9bb1-41ec-9bab-1f64d0316eed))
    (fp_line (start -2.28 -1.12) (end 2.28 -1.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 4d2dcc6d-af83-4d01-98e2-f18312772a4b))
    (fp_line (start -2.28 1.12) (end -2.28 -1.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2bfbd9be-ea45-4e23-a0b0-e2719ed8cda5))
    (fp_line (start 2.28 -1.12) (end 2.28 1.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp da6dcc52-01d0-41f9-a743-241cb73f4377))
    (fp_line (start 2.28 1.12) (end -2.28 1.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 02e82016-271f-496e-91d4-736f6c593089))
    (fp_line (start -1.6 -0.8) (end 1.6 -0.8)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0fee3df6-a8d4-4be5-87be-c77ceabf9224))
    (fp_line (start -1.6 0.8) (end -1.6 -0.8)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 957c929e-3a79-4913-95a1-209b9c6476ca))
    (fp_line (start 1.6 -0.8) (end 1.6 0.8)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bdd96fcc-b819-440f-b226-e2790a6a3170))
    (fp_line (start 1.6 0.8) (end -1.6 0.8)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2fb23996-f56e-4baa-93fb-17f1c56b8776))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 3b9bd94d-fb3a-41bd-8dc2-48e69ca4a355)
      (effects (font (size 0.8 0.8) (thickness 0.12)))
    )
    (pad "1" smd roundrect (at -1.4625 0) (size 1.125 1.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.2222222222)
      (net 17 "/Modbus/RS485_A") (pintype "passive")
      (tstamp 4ecc18fe-a97b-4d2c-ab18-e31063891796)
    )
    (pad "2" smd roundrect (at 1.4625 0) (size 1.125 1.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.2222222222)
      (net 14 "/Modbus/CONN_RS485_A") (pintype "passive")
      (tstamp 4938e286-4b23-426e-9865-815231bb1aab)
    )
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_1206_3216Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_1206_3216Metric" (layer "F.Cu")
    (tstamp 69a44f65-aa67-4f6a-a10c-089e8ae2d2ed)
    (at 180 95)
    (descr "Resistor SMD 1206 (3216 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R407" (at 0 -2 0) (layer "F.SilkS") hide (tstamp 66fd91c7-3791-48cd-8784-d91a98f0832a)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "10" (at 0 -2 0) (layer "F.Fab") (tstamp bd442a49-0bc2-4f03-9b5c-288f293dc5ca)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 83716f15-a324-4af8-918b-8824444beb5e)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 408549bd-54f0-4ce7-8034-c54592914fd9)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Resistor" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 0c2489de-f250-4ec8-9134-1ed3f7a57736)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/2acd2bd3-05fd-47b2-bc78-4871ae1e86d6")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr smd)
    (fp_line (start -0.727064 -0.91) (end 0.727064 -0.91)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp be0d4e2a-68a4-48b6-a457-f671bf9d245a))
    (fp_line (start -0.727064 0.91) (end 0.727064 0.91)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp fccc2b76-8b0c-4332-a129-4e0c9f06d0f7))
    (fp_line (start -2.28 -1.12) (end 2.28 -1.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 16f9eaa7-2468-49f7-9a72-a2d803259e39))
    (fp_line (start -2.28 1.12) (end -2.28 -1.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b745619d-8b32-48b3-a4f5-105b2046426a))
    (fp_line (start 2.28 -1.12) (end 2.28 1.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 94989046-7bef-4a76-8c28-48fadfafc7d7))
    (fp_line (start 2.28 1.12) (end -2.28 1.12)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp cf74678a-b6cb-41d4-9985-ebfdf2b5e7ec))
    (fp_line (start -1.6 -0.8) (end 1.6 -0.8)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp fb641c0b-167e-4311-a617-550a5533374f))
    (fp_line (start -1.6 0.8) (end -1.6 -0.8)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 88536ad3-18b8-4bad-9d3b-8f6f784f5ced))
    (fp_line (start 1.6 -0.8) (end 1.6 0.8)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7e100a98-767d-4f01-95b0-5f8941292736))
    (fp_line (start 1.6 0.8) (end -1.6 0.8)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f51d0263-e43c-4bc4-a1d9-730848daa527))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 72b75589-543d-4028-8239-0bbd4c653b2d)
      (effects (font (size 0.8 0.8) (thickness 0.12)))
    )
    (pad "1" smd roundrect (at -1.4625 0) (size 1.125 1.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.2222222222)
      (net 16 "/Modbus/RS485_B") (pintype "passive")
      (tstamp 2e9e86b5-a6d9-4dd8-9bce-4df490dc5bc1)
    )
    (pad "2" smd roundrect (at 1.4625 0) (size 1.125 1.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.2222222222)
      (net 15 "/Modbus/CONN_RS485_B") (pintype "passive")
      (tstamp 71cbe040-7894-428f-a2d4-088b95a39486)
    )
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_1206_3216Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp 6d428dd9-cff7-44f2-9a13-8cc5c826565c)
    (at 176.7 95.5 -90)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R405" (at 0 -1.5 90) (layer "F.SilkS") hide (tstamp 48f33cc4-6c1e-477e-a968-68a457ee7780)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "DNP" (at -2.5 -0.3 90) (layer "F.Fab") (tstamp 81c21475-c233-48e5-aa50-a1b464531b02)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 70e317cf-93a7-4718-8cfa-4329ccad835c)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp f25116b4-08ae-48b8-87cb-bb7bdcb8041b)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Resistor" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 44e666b4-8c37-4c7e-982b-8e352917ff48)
      (effects (font (size 1.27 1.27)))
    )
    (property "dnp" "" (at 0 0 0) (layer "F.Fab") hide (tstamp 8b5773ac-fbb1-4ea9-95c8-b82f82fb83ac)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/7741c217-6ef6-4909-afdb-f2541e5a202b")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr smd)
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ee7b5ff7-1bc2-4005-9f2c-1bcf7daf85de))
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0aa8b815-83b0-4ca4-81b2-768a15b2277b))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 297970b5-8b3e-4e13-97e8-6077c551c0ee))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 240a8ff2-83d9-4260-ac5a-455a3d957591))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp bd07661d-fbe4-450e-934c-de4663c5b849))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 13a69352-c653-4c20-b764-f6efd1ab6459))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 380852a1-8c6f-4f82-ae83-31661b0bb18f))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7b720c00-06cf-4a02-9bad-f7021a48f4b6))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2d99e0ad-7fc6-460c-9bcc-da88b3ebeecd))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0059fb78-0815-4dbb-97e2-aa9262937e7d))
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab") (tstamp 8da15f8f-870d-4c64-9c90-588a743a880a)
      (effects (font (size 0.4 0.4) (thickness 0.06)))
    )
    (pad "1" smd roundrect (at -0.825 0 270) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 16 "/Modbus/RS485_B") (pintype "passive")
      (tstamp 8599e393-6809-4e74-b9ba-fa6441670844)
    )
    (pad "2" smd roundrect (at 0.825 0 270) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive")
      (tstamp 8a724f74-a86e-4cc7-81a1-30362ff9bf69)
    )
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp 7b90f0e2-bf4d-465d-881c-98746ce61b56)
    (at 175 95.5 90)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R403" (at 0 1.5 90) (layer "F.SilkS") hide (tstamp a84017f6-046b-4d78-a266-8283f48286f1)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "120" (at 2.5 0 90) (layer "F.Fab") (tstamp 1271d19d-d8d5-4525-ad16-5ac963b08472)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 90 unlocked) (layer "F.Fab") hide (tstamp da3e29ad-5bb6-45a2-8851-9999133a66da)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 90 unlocked) (layer "F.Fab") hide (tstamp 12db8661-27bd-4838-ac74-fb9dd4735b58)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Resistor" (at 0 0 90 unlocked) (layer "F.Fab") hide (tstamp dfeaa040-3efb-4f90-a249-b69f3619f91c)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/7662ee8b-dfe0-4bc0-b2c4-ef0f120195d5")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr smd)
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp bc871fa2-f5d7-441a-be37-0039c1c67514))
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 45853a63-90d9-4a0f-8616-32f05dfc2789))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 8933f593-0156-43de-834c-1c0af1671e03))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 41f4652f-515d-426d-bad6-e7b5d29a5474))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3d128164-739b-4c14-8659-5910e35e420a))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 2f2811b0-ee0c-4dbe-8392-762866ce8010))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2a1f45f3-b16d-4789-930f-f951d237f05c))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp aa51fe87-b374-4e68-acf4-16159673f23a))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4859db18-25c5-43b0-af1e-eff103e0493c))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7ec9e865-b625-4d01-8e40-30cc5f254706))
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab") (tstamp 973a4da8-054a-4f9e-bf32-4bc29775cf31)
      (effects (font (size 0.4 0.4) (thickness 0.06)))
    )
    (pad "1" smd roundrect (at -0.825 0 90) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 8 "Net-(JP401-B)") (pintype "passive")
      (tstamp 2858feee-70bc-4ee4-b407-8dc2ce85a814)
    )
    (pad "2" smd roundrect (at 0.825 0 90) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 16 "/Modbus/RS485_B") (pintype "passive")
      (tstamp b469e3ca-3146-4220-a1f1-0fa9f6954667)
    )
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0603_1608Metric" (layer "F.Cu")
    (tstamp ae366deb-f8b9-4ceb-aae6-585118f3ccc0)
    (at 169.46 102.2 180)
    (descr "Resistor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R401" (at 0 1.5 0) (layer "F.SilkS") hide (tstamp c9483382-0427-4402-95b9-ff3c407710db)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "10k" (at -1.54 -1.3 0) (layer "F.Fab") (tstamp e1358eca-f31a-4a90-a5b6-fc1716ecbbf2)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp d15490f6-8d37-494a-aa16-a0068f9a22db)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 18697b3a-4790-4f1a-908d-5b97a4bed069)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Resistor" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 379a43a6-2dc5-4be8-88e4-000dcaf83281)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/367b2bad-5cb5-41e2-995b-498579799c9d")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr smd)
    (fp_line (start -0.237258 0.5225) (end 0.237258 0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0ea330a3-9ae3-4a76-80b4-3674f7502d75))
    (fp_line (start -0.237258 -0.5225) (end 0.237258 -0.5225)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cc06d0f9-746d-47e0-b149-8a27ee034144))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e319b979-d4cb-4bed-b4f3-52083dadf2be))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e53a3fe3-819a-4efa-9c12-fe974f416fa1))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 30a7168d-f4e4-4521-a53a-c9de5e2537f6))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 66c70553-c97e-4171-acd5-4cce5faa75e7))
    (fp_line (start 0.8 0.4125) (end -0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b873fc85-7fa8-49ec-876f-ff112d81930a))
    (fp_line (start 0.8 -0.4125) (end 0.8 0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 271fbec2-cd92-4a68-a823-cb694a484798))
    (fp_line (start -0.8 0.4125) (end -0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 90d635b7-8da0-4123-a50f-723baea6483d))
    (fp_line (start -0.8 -0.4125) (end 0.8 -0.4125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a6eebef4-dc46-4be3-8e23-91e521dc8981))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp a633ccf1-391a-426c-a1db-0c94d6397bfa)
      (effects (font (size 0.4 0.4) (thickness 0.06)))
    )
    (pad "1" smd roundrect (at -0.825 0 180) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pintype "passive")
      (tstamp e83dd601-5054-4ac3-a3e2-7ce79e4165ca)
    )
    (pad "2" smd roundrect (at 0.825 0 180) (size 0.8 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 18 "/Modbus/RS485_MODE_SELECT") (pintype "passive")
      (tstamp bc96ee36-512b-4107-946c-f32bce9b4d7a)
    )
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "TestPoint:TestPoint_THTPad_D2.0mm_Drill1.0mm" (layer "F.Cu")
    (tstamp aefaa96b-2358-4123-90b5-d61b2e71f2f9)
    (at 161 93)
    (descr "THT pad as test Point, diameter 2.0mm, hole diameter 1.0mm")
    (tags "test point THT pad")
    (property "Reference" "TP402" (at 0 -1.998 0) (layer "F.SilkS") hide (tstamp e05fe356-d850-433c-955d-f809b20983be)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "TestPoint" (at 0 2.05 0) (layer "F.Fab") hide (tstamp eec4ce36-4c6a-4593-9a0d-3f758a430850)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 4e452ba9-4635-4de3-8034-3c4c37435b13)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp f56f539f-57b4-4f6e-a495-e67a7e84f965)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "test point" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 836e1b53-9e11-4e1d-b4ee-f3e53c4989f1)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/b04450d8-68aa-429a-a073-74fbb1db6dc1")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (fp_circle (center 0 0) (end 0 1.2)
      (stroke (width 0.12) (type solid)) (fill none) (layer "F.SilkS") (tstamp 85cfcca7-8901-4fa5-86f7-7255dbde66db))
    (fp_circle (center 0 0) (end 1.5 0)
      (stroke (width 0.05) (type solid)) (fill none) (layer "F.CrtYd") (tstamp a02fbe20-9820-4025-8910-bcde557fc475))
    (fp_text user "${REFERENCE}" (at 0 2.1 0) (layer "F.Fab") (tstamp 14659b3e-228f-47ac-a6fd-7b326e4dd5d7)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad "1" thru_hole circle (at 0 0) (size 2 2) (drill 1) (layers "*.Cu" "*.Mask")
      (net 13 "MODBUS_TX") (pinfunction "1") (pintype "passive")
      (tstamp c44411d7-ae56-42fd-963f-552646a5a7f7)
    )
  )

  (footprint "Package_TO_SOT_SMD:SOT-223-3_TabPin2" (layer "F.Cu")
    (tstamp c5b6c4fa-6c9b-4715-8795-9516aca0f565)
    (at 169.9 112.9)
    (descr "module CMS SOT223 4 pins")
    (tags "CMS SOT")
    (property "Reference" "U103" (at 0 -4.5 0) (layer "F.SilkS") hide (tstamp 24b1315d-34b9-4b28-8cc5-4a23f8f76016)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "AZ1117-3.3" (at 0.1 -4.4 180) (layer "F.Fab") (tstamp 2db165eb-756d-4255-bb88-808dba7dc41d)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 8a5d14b1-934f-4ac6-9a64-b681dfe633c2)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 6c1ec463-fd81-44d7-97c2-a2706a7021a1)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "1A 20V Fixed LDO Linear Regulator, 3.3V, SOT-89/SOT-223/TO-220/TO-252/TO-263" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 770755c3-ae59-4aad-80e4-5896309542c5)
      (effects (font (size 1.27 1.27)))
    )
    (path "/d3c757f4-85a2-4c64-b768-7bb57e3daca0/40613080-d6fb-4046-b5af-9c87ac091ddf")
    (sheetname "Power")
    (sheetfile "grblPANEL_power.kicad_sch")
    (attr smd)
    (fp_line (start -4.1 -3.41) (end 1.91 -3.41)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0c2101de-7c30-4da2-83c3-1f0f5f006222))
    (fp_line (start -1.85 3.41) (end 1.91 3.41)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c184768c-072f-4cea-af43-c8b8e3dd6c87))
    (fp_line (start 1.91 -3.41) (end 1.91 -2.15)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b5087100-e60f-4c3b-8b36-8f0d56d93667))
    (fp_line (start 1.91 3.41) (end 1.91 2.15)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 32bd11bd-d003-4d45-9fc2-0fe314c63b2c))
    (fp_line (start -4.4 -3.6) (end -4.4 3.6)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 292f0f6f-9492-453c-9bd5-351c59fc3e49))
    (fp_line (start -4.4 3.6) (end 4.4 3.6)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp bf754ff4-1d54-4124-a148-2c3cb39eaf75))
    (fp_line (start 4.4 -3.6) (end -4.4 -3.6)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp dbfc15c7-3c45-4c14-9779-6822a8991ec5))
    (fp_line (start 4.4 3.6) (end 4.4 -3.6)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 99395e6e-e4cf-43a4-a7af-c43b5d150e67))
    (fp_line (start -1.85 -2.35) (end -1.85 3.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f44ce608-cc27-4ae9-8f36-c6b439b81391))
    (fp_line (start -1.85 -2.35) (end -0.85 -3.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 551dd222-cb93-4665-8bb8-736e08982fd6))
    (fp_line (start -1.85 3.35) (end 1.85 3.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1ebdaccf-7a98-4fb6-b5ce-83fbd6d29a10))
    (fp_line (start -0.85 -3.35) (end 1.85 -3.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 61692b12-1863-412d-ab44-7c652bb327b4))
    (fp_line (start 1.85 -3.35) (end 1.85 3.35)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 81ab91c1-bb62-4a5d-b114-9cb1bba3f281))
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab") (tstamp 2efc870d-8aef-4fa0-b71d-d7a89fc9ff04)
      (effects (font (size 0.8 0.8) (thickness 0.12)))
    )
    (pad "1" smd rect (at -3.15 -2.3) (size 2 1.5) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 1 "GND") (pinfunction "GND") (pintype "power_in")
      (tstamp 13ea49f5-c6de-4856-8323-7dc269bc6d40)
    )
    (pad "2" smd rect (at -3.15 0) (size 2 1.5) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 3 "+3V3") (pinfunction "VO") (pintype "power_out")
      (tstamp 1395deb4-902d-42ad-af31-b08906a9c5b2)
    )
    (pad "2" smd rect (at 3.15 0) (size 2 3.8) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 3 "+3V3") (pinfunction "VO") (pintype "power_out")
      (tstamp 64165286-5205-49d5-a726-484ed01bebd6)
    )
    (pad "3" smd rect (at -3.15 2.3) (size 2 1.5) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 2 "/Power/VIN_PI_OUT") (pinfunction "VI") (pintype "power_in")
      (tstamp 06246136-ea09-410b-ae73-5bdd3001bb8c)
    )
    (model "${KICAD6_3DMODEL_DIR}/Package_TO_SOT_SMD.3dshapes/SOT-223.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Package_TO_SOT_SMD:SOT-23" (layer "F.Cu")
    (tstamp d2079c23-1a02-42c0-a23b-b8d3b0ef6783)
    (at 180 98.5)
    (descr "SOT, 3 Pin (https://www.jedec.org/system/files/docs/to-236h.pdf variant AB), generated with kicad-footprint-generator ipc_gullwing_generator.py")
    (tags "SOT TO_SOT_SMD")
    (property "Reference" "D401" (at 0 -2.4 0) (layer "F.SilkS") hide (tstamp e77c61bf-2664-4b95-bb04-ea4ddbe2b673)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "CDSOT23-SM712" (at 1.5 0 90) (layer "F.Fab") (tstamp 2d755058-56c7-4ce8-978b-77e5a75595bd)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 0f1291f9-b978-4fc3-a6c9-b613e2700bd4)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp a1c78266-4419-476c-b5d8-9c4087763de1)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Bidirectional dual transient-voltage-suppression diode, center on pin 3" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp ee185396-31f8-4f3f-aaa5-c4b4e1c5869e)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/bca927cd-d8d2-4f3d-8984-04acb4026919")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr smd)
    (fp_line (start 0 -1.56) (end -1.675 -1.56)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp fc9dc507-8640-4e13-a6f2-06ffcccf5c94))
    (fp_line (start 0 -1.56) (end 0.65 -1.56)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp d0941c47-a0ab-4617-bc70-02af2603ad04))
    (fp_line (start 0 1.56) (end -0.65 1.56)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6802b20f-e520-47cc-b22a-1af58b328971))
    (fp_line (start 0 1.56) (end 0.65 1.56)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2388badd-06e3-4f2f-887f-8569dbe4ae33))
    (fp_line (start -1.92 -1.7) (end -1.92 1.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 04f4c02b-80e6-472b-b7b1-387b9be22b1b))
    (fp_line (start -1.92 1.7) (end 1.92 1.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 347fe186-c78a-4f4a-b23c-efb5eade977d))
    (fp_line (start 1.92 -1.7) (end -1.92 -1.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 631d99e8-98b3-496c-a7ee-d08e628616d8))
    (fp_line (start 1.92 1.7) (end 1.92 -1.7)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b49a2091-969e-4d7f-984a-6f073539cdae))
    (fp_line (start -0.65 -1.125) (end -0.325 -1.45)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ee3aa3a6-2915-46b8-bc09-f3bc44c9e8d8))
    (fp_line (start -0.65 1.45) (end -0.65 -1.125)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4f64be06-5377-4e69-ae43-b492071235e0))
    (fp_line (start -0.325 -1.45) (end 0.65 -1.45)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 896973b2-8337-4d3f-a454-73cdbea3cedb))
    (fp_line (start 0.65 -1.45) (end 0.65 1.45)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f83395ee-5e79-467d-aa4c-c33fd4cfa294))
    (fp_line (start 0.65 1.45) (end -0.65 1.45)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d13ebf42-b1b9-4440-8205-6ab3793a725b))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 58b424af-b5dc-40f4-9c27-91952042580d)
      (effects (font (size 0.32 0.32) (thickness 0.05)))
    )
    (pad "1" smd roundrect (at -0.9375 -0.95) (size 1.475 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 15 "/Modbus/CONN_RS485_B") (pinfunction "A1") (pintype "passive")
      (tstamp 9454dc35-371a-45db-9d35-e5ea6bbaf2cd)
    )
    (pad "2" smd roundrect (at -0.9375 0.95) (size 1.475 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 14 "/Modbus/CONN_RS485_A") (pinfunction "A2") (pintype "passive")
      (tstamp 968277a4-cbf3-4df6-8e80-3f74f0648a0c)
    )
    (pad "3" smd roundrect (at 0.9375 0) (size 1.475 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "common") (pintype "input")
      (tstamp f62a5cf2-4447-485a-8156-093fa958b1b5)
    )
    (model "${KICAD6_3DMODEL_DIR}/Package_TO_SOT_SMD.3dshapes/SOT-23.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0603_1608Metric" (layer "F.Cu")
    (tstamp f292e717-c996-40f8-865d-24ad6d720f94)
    (at 171.6 94.8)
    (descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Reference" "C401" (at 0 -1.43 0) (layer "F.SilkS") hide (tstamp 74d71da2-44dc-4a98-85ac-436dcf90cd14)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "100n" (at 0 -1.3 0) (layer "F.Fab") (tstamp 9c98f468-cb4e-49ff-b874-ea99f3805d07)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 252bea3b-0211-4b06-aecb-8918c3a4b0ad)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp b4d2e914-2b87-4874-9251-1c231c07c5ff)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Unpolarized capacitor" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp b943cbd3-2989-4a4e-821a-fa46e536d38f)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/058eb096-3e23-4bb8-ad61-dcfc93da82af")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr smd)
    (fp_line (start -0.14058 -0.51) (end 0.14058 -0.51)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e269543c-9b15-4792-85d8-ba28a3962207))
    (fp_line (start -0.14058 0.51) (end 0.14058 0.51)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 73b8bc79-ad63-48c7-b9ee-919bfaa39c75))
    (fp_line (start -1.48 -0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 7d536a83-64b5-4f2d-8adb-dbcbb8cb5753))
    (fp_line (start -1.48 0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3084da38-1837-432a-bb14-e522a4d7c668))
    (fp_line (start 1.48 -0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp ecf3a037-77aa-4925-aaa7-0d5f71beebbf))
    (fp_line (start 1.48 0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6bb1007f-d3cc-4d76-a962-57439d4be9b8))
    (fp_line (start -0.8 -0.4) (end 0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp abd171fe-4329-453f-b159-bd27e1961ed3))
    (fp_line (start -0.8 0.4) (end -0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3b67f1e0-483e-4b01-a0e8-3aa21d9b0056))
    (fp_line (start 0.8 -0.4) (end 0.8 0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7a2a6d3e-0035-45f8-abac-d1d9fbd1d4c0))
    (fp_line (start 0.8 0.4) (end -0.8 0.4)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2a79cd77-f095-4afc-bc1e-0e070464b260))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 7074677d-5e95-4f59-8f2c-812f5aed116f)
      (effects (font (size 0.4 0.4) (thickness 0.06)))
    )
    (pad "1" smd roundrect (at -0.775 0) (size 0.9 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pintype "passive")
      (tstamp 59d73f10-245f-4b0f-ac06-b55c3268f7d0)
    )
    (pad "2" smd roundrect (at 0.775 0) (size 0.9 0.95) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pintype "passive")
      (tstamp 74e4ee64-c099-41b5-933a-27f1024cd168)
    )
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "TestPoint:TestPoint_THTPad_D2.0mm_Drill1.0mm" (layer "F.Cu")
    (tstamp f897ddb7-0dc0-4873-81d7-19ef88e62caf)
    (at 161 104)
    (descr "THT pad as test Point, diameter 2.0mm, hole diameter 1.0mm")
    (tags "test point THT pad")
    (property "Reference" "TP503" (at 0 -1.998 0) (layer "F.SilkS") hide (tstamp e01badf8-3311-4cd2-92b5-896b7d355a4b)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "TestPoint" (at 0 2.05 0) (layer "F.Fab") hide (tstamp 7e78b6fb-31a1-4a06-84a0-cc4ccda11ef7)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 1873c18f-77c3-452b-b2cb-6ba461bec774)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 69f175c2-4a97-4a20-80ad-11cc2e721f93)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "test point" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 0a949367-842f-4667-9e47-1b68af58bc5d)
      (effects (font (size 1.27 1.27)))
    )
    (path "/2c60fe6e-45f9-484b-a2a4-9c1dd728d5af/63445544-5d26-41f5-9ff0-4cd9d4ec2a4f")
    (sheetname "Display")
    (sheetfile "grblPANEL_display.kicad_sch")
    (fp_circle (center 0 0) (end 0 1.2)
      (stroke (width 0.12) (type solid)) (fill none) (layer "F.SilkS") (tstamp 1d871e2c-fb4d-424a-97c4-cf135db253f5))
    (fp_circle (center 0 0) (end 1.5 0)
      (stroke (width 0.05) (type solid)) (fill none) (layer "F.CrtYd") (tstamp 2efc1cb0-552b-48c1-abac-48b42c5cb151))
    (fp_text user "${REFERENCE}" (at -0.5 2.2 0) (layer "F.Fab") (tstamp 53e8e15d-f5a6-4294-97e7-c0a9c36b169e)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad "1" thru_hole circle (at 0 0) (size 2 2) (drill 1) (layers "*.Cu" "*.Mask")
      (net 7 "DISPLAY_SDA") (pinfunction "1") (pintype "passive")
      (tstamp b0097a1a-8d9d-4def-96ac-fd7c6bb318b0)
    )
  )

  (footprint "LED_SMD:LED_0603_1608Metric" (layer "B.Cu")
    (tstamp 2249b586-a90f-4806-bfd0-399dbd7a20bc)
    (at 183.5 107.5)
    (descr "LED SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: http://www.tortai-tech.com/upload/download/2011102023233369053.pdf), generated with kicad-footprint-generator")
    (tags "LED")
    (property "Reference" "D106" (at 0 1.43 0) (layer "B.SilkS") hide (tstamp f5098179-3085-4311-9cfb-62c8c0fd1047)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Value" "LED" (at 0 -1.43 0) (layer "B.Fab") (tstamp 55055b2c-a3a2-421b-89b1-d82e9454d6d9)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 7da78c92-b7ed-410c-815f-6a4518b22083)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp bdc388f8-58d3-48d8-aea0-8220e2dc506c)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Light emitting diode" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp fc0a0a3a-c8dd-4c08-9e3c-c23fc24f5b6b)
      (effects (font (size 1.27 1.27)))
    )
    (path "/d3c757f4-85a2-4c64-b768-7bb57e3daca0/cf7b08ba-d3f7-41f2-b810-bac49b9aa643")
    (sheetname "Power")
    (sheetfile "grblPANEL_power.kicad_sch")
    (attr smd)
    (fp_line (start -1.485 -0.735) (end 0.8 -0.735)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 80b7fb3a-c69b-4b27-b211-7a8388f51df1))
    (fp_line (start -1.485 0.735) (end -1.485 -0.735)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 6e070ab9-96b3-4014-9454-9f0bac30409a))
    (fp_line (start 0.8 0.735) (end -1.485 0.735)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp b43f245f-09f4-4de8-9712-8a5940894b59))
    (fp_line (start -1.48 -0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 293ac568-7cfc-4bee-9a5a-54e560517c4e))
    (fp_line (start -1.48 0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp d458c978-186a-4b28-a478-0cdcd97213cb))
    (fp_line (start 1.48 -0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 2f13e658-6fdb-43ab-ae54-9c339a4a3813))
    (fp_line (start 1.48 0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp a3f4f3f3-2e4e-4cc4-9a16-b5a5bcf01c85))
    (fp_line (start -0.8 -0.4) (end 0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp bb044bb1-426e-4ed0-99ed-ae0c4fcb7b36))
    (fp_line (start -0.8 0.1) (end -0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp f7a09223-9eba-4fb7-bc12-160500ccc3bb))
    (fp_line (start -0.5 0.4) (end -0.8 0.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp c7ef7f16-ceee-4e40-9314-d41a8becf04b))
    (fp_line (start 0.8 -0.4) (end 0.8 0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp caeafa12-3954-4865-95f5-5482e4743466))
    (fp_line (start 0.8 0.4) (end -0.5 0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 10272262-9997-46ba-a274-f4645df6b6b3))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "B.Fab") (tstamp b1568c74-2137-4d61-9126-a3a88aea1365)
      (effects (font (size 0.4 0.4) (thickness 0.06)) (justify mirror))
    )
    (pad "1" smd roundrect (at -0.7875 0) (size 0.875 0.95) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 5 "/Power/PWR_LED_K") (pinfunction "K") (pintype "passive")
      (tstamp 1a0a4bcb-71bd-43aa-b256-9dbc2e3e3674)
    )
    (pad "2" smd roundrect (at 0.7875 0) (size 0.875 0.95) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pinfunction "A") (pintype "passive")
      (tstamp 4e9ce799-74fb-42c8-85e9-e8509e37c3a8)
    )
    (model "${KICAD6_3DMODEL_DIR}/LED_SMD.3dshapes/LED_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Connector_PinHeader_2.54mm:PinHeader_1x02_P2.54mm_Vertical" (layer "B.Cu")
    (tstamp 44f6d973-96e8-4806-b805-a9c2eb195746)
    (at 174.71 98.5 -90)
    (descr "Through hole straight pin header, 1x02, 2.54mm pitch, single row")
    (tags "Through hole pin header THT 1x02 2.54mm single row")
    (property "Reference" "JP401" (at -2.5 -1.27 0) (layer "B.SilkS") hide (tstamp 99485b19-6d5d-4643-99d9-0b2993130e54)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Value" "Jumper_2_Bridged" (at 0 -4.87 90) (layer "B.Fab") hide (tstamp c318ce2e-80a9-474f-9cad-3c1d22e56572)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Footprint" "" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 738c4953-d579-4af8-9c0c-e6295d1cc813)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 9e351bfa-2f8e-4f42-b48c-d5e0776f0a22)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Jumper, 2-pole, closed/bridged" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 6213c1f3-849c-4eb6-84b5-66586aac430d)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/ed1cb14f-c92c-4d76-8936-cc3208583852")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr through_hole)
    (fp_line (start -1.33 1.33) (end 0 1.33)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 296ae4e6-4a9b-4ed7-85a5-7de766719d71))
    (fp_line (start -1.33 0) (end -1.33 1.33)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 5e506f20-4bd0-485a-8fd6-dc6b43d3567e))
    (fp_line (start -1.33 -1.27) (end 1.33 -1.27)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 2c8c7ed6-a6b9-4051-85db-6f5635f792be))
    (fp_line (start -1.33 -1.27) (end -1.33 -3.87)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 92df36d1-7b17-4787-b568-026c239448c3))
    (fp_line (start 1.33 -1.27) (end 1.33 -3.87)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 133fe8ca-689c-4e44-b3e1-9c1b6ff9cf58))
    (fp_line (start -1.33 -3.87) (end 1.33 -3.87)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp c1d672a7-be96-499b-9110-db0c132358fb))
    (fp_line (start -1.8 1.8) (end -1.8 -4.35)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp d0e777cc-2da5-4598-a9a6-da3a8cfad48f))
    (fp_line (start 1.8 1.8) (end -1.8 1.8)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp cd180a4b-4061-4be1-b0e3-fa6388343071))
    (fp_line (start -1.8 -4.35) (end 1.8 -4.35)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 23b7f3e8-e73f-4be8-ba1b-16618c76ba0c))
    (fp_line (start 1.8 -4.35) (end 1.8 1.8)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 0749ee43-4392-454d-a2fa-477ce8d73c35))
    (fp_line (start -0.635 1.27) (end 1.27 1.27)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp fe4c2c5b-69b6-48d1-8f85-1867b7e7af52))
    (fp_line (start 1.27 1.27) (end 1.27 -3.81)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 498df9c3-35f9-4b41-96e6-a87ebded9a6c))
    (fp_line (start -1.27 0.635) (end -0.635 1.27)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp aaf2aa8e-bbc8-4238-b50f-42f2c7632289))
    (fp_line (start -1.27 -3.81) (end -1.27 0.635)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 71d10e5c-e64a-47ea-97dc-65b7a15cc581))
    (fp_line (start 1.27 -3.81) (end -1.27 -3.81)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 6ebb09a1-0507-4d4b-977a-d7e42581d4c6))
    (fp_text user "${REFERENCE}" (at 0 -1.27 0) (layer "B.Fab") (tstamp ff9fc11f-051e-47dd-b932-8670e237f1c4)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (pad "1" thru_hole rect (at 0 0 270) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 17 "/Modbus/RS485_A") (pinfunction "A") (pintype "passive")
      (tstamp 3ce46bd4-8120-4112-8067-6a74d9213285)
    )
    (pad "2" thru_hole oval (at 0 -2.54 270) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 8 "Net-(JP401-B)") (pinfunction "B") (pintype "passive")
      (tstamp 03316b89-c2cc-45f5-a54c-2707a9ad2be7)
    )
    (model "${KICAD6_3DMODEL_DIR}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x02_P2.54mm_Vertical.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Button_Switch_SMD:SW_SPST_PTS810" (layer "B.Cu")
    (tstamp 4c7b3914-a884-4bd5-8c52-500c40b03e7a)
    (at 174.6 112.3 180)
    (descr "C&K Components, PTS 810 Series, Microminiature SMT Top Actuated, http://www.ckswitches.com/media/1476/pts810.pdf")
    (tags "SPST Button Switch")
    (property "Reference" "SW202" (at 0 2.6 0) (layer "B.SilkS") hide (tstamp acfa7542-5f36-4ae6-9e19-17ef3bb653dd)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Value" "SW_Push" (at 0 -2.6 0) (layer "B.Fab") (tstamp 66cf80c5-09fa-4e9e-a3b0-dd69b4f55dc8)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Footprint" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 0e67e22e-a36e-48b1-9328-e883ab7f19c8)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 9c584ec6-974f-4d2c-8167-39bbf4fa0901)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Push button switch, generic, two pins" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 8be44753-fb97-45b7-87b8-77802cf984f6)
      (effects (font (size 1.27 1.27)))
    )
    (path "/437f2ceb-712b-41a0-9e79-c6c2becba629/0172b5a2-a1cb-43e3-9544-457799cbb77c")
    (sheetname "MCU")
    (sheetfile "grblPANEL_mcu.kicad_sch")
    (attr smd)
    (fp_line (start 2.2 1.7) (end -2.2 1.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 8acc3ed9-046a-45c0-adb5-21a104bcc79d))
    (fp_line (start 2.2 1.58) (end 2.2 1.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp c76cb627-56dc-47e7-847b-168e517badd2))
    (fp_line (start 2.2 -0.57) (end 2.2 0.57)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 8056c927-6181-4a00-b817-e48b9776a562))
    (fp_line (start 2.2 -1.7) (end 2.2 -1.58)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp df58c039-ce9c-4901-a5a4-f587797e52b3))
    (fp_line (start -2.2 1.7) (end -2.2 1.58)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp f263334d-22c5-4bd7-839c-8121468ef9b4))
    (fp_line (start -2.2 0.57) (end -2.2 -0.57)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 440cbdb9-4338-4a41-8a76-f1d11bfbd0b9))
    (fp_line (start -2.2 -1.58) (end -2.2 -1.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp b24691f5-b2bd-4f5e-9397-de78f5a32b49))
    (fp_line (start -2.2 -1.7) (end 2.2 -1.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp a823d3c1-3640-423f-99fc-e073c7190d9f))
    (fp_line (start 2.85 1.85) (end 2.85 -1.85)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 7945443c-7fcf-4b5e-b7bf-0133e75673b1))
    (fp_line (start 2.85 -1.85) (end -2.85 -1.85)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 1cb806df-af18-4e9b-8365-878c02f7a14e))
    (fp_line (start -2.85 1.85) (end 2.85 1.85)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 4bbe4606-0e1a-497f-88e1-f5ad75186828))
    (fp_line (start -2.85 -1.85) (end -2.85 1.85)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 8f8ed5e5-219c-408b-8c8b-fdd298676127))
    (fp_line (start 2.1 1.6) (end -2.1 1.6)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 3efd96b0-060e-4542-b093-54a8f4a3982c))
    (fp_line (start 2.1 -1.6) (end 2.1 1.6)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp c65cfd61-31b1-4c39-884c-905524bd248f))
    (fp_line (start 0.4 -1.1) (end -0.4 -1.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp bffbdc79-2f17-4f57-8214-dda5b4eb4c1d))
    (fp_line (start -0.4 1.1) (end 0.4 1.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp a5f1e677-ac29-4148-8633-984fe511211c))
    (fp_line (start -2.1 1.6) (end -2.1 -1.6)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp e6307870-b089-4b1b-b680-c7b10e1bcbc7))
    (fp_line (start -2.1 -1.6) (end 2.1 -1.6)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp ca43e9dc-9110-4e14-a5c3-1a84ea099341))
    (fp_arc (start 0.4 -1.1) (mid 1.5 0) (end 0.4 1.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp a7f61d13-b9aa-42d7-980a-e771b39a5dca))
    (fp_arc (start -0.4 1.1) (mid -1.5 0) (end -0.4 -1.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 8f795cb9-0b4c-42eb-b502-0a3b270c73b0))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "B.Fab") (tstamp 9150596b-f783-40e2-85de-92e7859e455c)
      (effects (font (size 0.6 0.6) (thickness 0.09)) (justify mirror))
    )
    (pad "1" smd rect (at -2.075 1.075 180) (size 1.05 0.65) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 4 "/MCU/~{RST}") (pinfunction "1") (pintype "passive")
      (tstamp 8fb1d370-ee96-4181-8df5-05be9ea67f37)
    )
    (pad "1" smd rect (at 2.075 1.075 180) (size 1.05 0.65) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 4 "/MCU/~{RST}") (pinfunction "1") (pintype "passive")
      (tstamp 4de828b4-4a80-4258-a6d1-ed01b17785c5)
    )
    (pad "2" smd rect (at -2.075 -1.075 180) (size 1.05 0.65) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 1 "GND") (pinfunction "2") (pintype "passive")
      (tstamp 96d72b51-0804-4140-bd1a-55010b75b65e)
    )
    (pad "2" smd rect (at 2.075 -1.075 180) (size 1.05 0.65) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 1 "GND") (pinfunction "2") (pintype "passive")
      (tstamp b5939446-ab92-4d8a-ad49-ebeba9a512c8)
    )
    (model "${KICAD6_3DMODEL_DIR}/Button_Switch_SMD.3dshapes/SW_SPST_PTS810.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
    (model "/opt/KiCad/3d/CK_PTS810.step"
      (offset (xyz 0 0 1.27))
      (scale (xyz 1 1 1))
      (rotate (xyz -90 0 0))
    )
  )

  (footprint "dresco:OSHW_LOGO_5MM" (layer "B.Cu")
    (tstamp 53f8ecb7-c616-40ff-9d25-b654a95b7de8)
    (at 175.7 117.9 180)
    (property "Reference" "G***" (at 0 -2.95148 0) (layer "B.SilkS") hide (tstamp 08e2349c-776b-45ec-b9ab-c4ad06ec6c74)
      (effects (font (size 0.254 0.254) (thickness 0.0508)) (justify mirror))
    )
    (property "Value" "OSHW_LOGO_5MM" (at 0 2.95148 0) (layer "B.SilkS") hide (tstamp 1c6a9ebc-9bef-41f9-9b34-26cf942550fe)
      (effects (font (size 0.254 0.254) (thickness 0.0508)) (justify mirror))
    )
    (property "Footprint" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 0421d930-270c-459e-8c69-08240095bddf)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 132ffb58-cfc7-4aea-ad62-49826ebe6420)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 2f01a778-60c4-4420-9fea-0a7f29b287b5)
      (effects (font (size 1.27 1.27)))
    )
    (attr board_only)
    (fp_poly
      (pts
        (xy -1.68656 -2.49936)
        (xy -1.65608 -2.48412)
        (xy -1.59258 -2.44348)
        (xy -1.4986 -2.38252)
        (xy -1.38938 -2.30886)
        (xy -1.27762 -2.23266)
        (xy -1.18872 -2.1717)
        (xy -1.12522 -2.13106)
        (xy -1.09728 -2.11836)
        (xy -1.08458 -2.12344)
        (xy -1.03124 -2.14884)
        (xy -0.95504 -2.18694)
        (xy -0.91186 -2.2098)
        (xy -0.84074 -2.24028)
        (xy -0.80772 -2.24536)
        (xy -0.8001 -2.23774)
        (xy -0.7747 -2.1844)
        (xy -0.7366 -2.09296)
        (xy -0.68326 -1.97104)
        (xy -0.6223 -1.83134)
        (xy -0.5588 -1.67894)
        (xy -0.49276 -1.524)
        (xy -0.4318 -1.37414)
        (xy -0.37846 -1.24206)
        (xy -0.33528 -1.13284)
        (xy -0.3048 -1.05918)
        (xy -0.29464 -1.02616)
        (xy -0.29718 -1.01854)
        (xy -0.33274 -0.98552)
        (xy -0.3937 -0.9398)
        (xy -0.52578 -0.83312)
        (xy -0.65532 -0.67056)
        (xy -0.73406 -0.48514)
        (xy -0.762 -0.28194)
        (xy -0.73914 -0.09144)
        (xy -0.66294 0.09144)
        (xy -0.53594 0.254)
        (xy -0.38354 0.37592)
        (xy -0.2032 0.45466)
        (xy 0 0.47752)
        (xy 0.19304 0.4572)
        (xy 0.37846 0.38354)
        (xy 0.54356 0.25908)
        (xy 0.61214 0.1778)
        (xy 0.70866 0.0127)
        (xy 0.762 -0.1651)
        (xy 0.76962 -0.21082)
        (xy 0.75946 -0.4064)
        (xy 0.70358 -0.59182)
        (xy 0.59944 -0.75946)
        (xy 0.4572 -0.89662)
        (xy 0.43942 -0.91186)
        (xy 0.37084 -0.96012)
        (xy 0.32766 -0.99568)
        (xy 0.2921 -1.02362)
        (xy 0.54102 -1.62306)
        (xy 0.58166 -1.71704)
        (xy 0.65024 -1.88214)
        (xy 0.70866 -2.02184)
        (xy 0.75692 -2.1336)
        (xy 0.78994 -2.2098)
        (xy 0.80518 -2.24028)
        (xy 0.80772 -2.24028)
        (xy 0.82804 -2.24536)
        (xy 0.87376 -2.22758)
        (xy 0.95758 -2.18694)
        (xy 1.01346 -2.159)
        (xy 1.07696 -2.12852)
        (xy 1.1049 -2.11836)
        (xy 1.1303 -2.13106)
        (xy 1.19126 -2.16916)
        (xy 1.28016 -2.23012)
        (xy 1.38684 -2.30378)
        (xy 1.48844 -2.37236)
        (xy 1.58242 -2.43332)
        (xy 1.651 -2.4765)
        (xy 1.68402 -2.49428)
        (xy 1.6891 -2.49428)
        (xy 1.71958 -2.47904)
        (xy 1.77292 -2.43332)
        (xy 1.8542 -2.35712)
        (xy 1.97104 -2.24282)
        (xy 1.98882 -2.22504)
        (xy 2.0828 -2.12852)
        (xy 2.159 -2.04724)
        (xy 2.21234 -1.99136)
        (xy 2.23012 -1.96342)
        (xy 2.21234 -1.9304)
        (xy 2.16916 -1.86436)
        (xy 2.1082 -1.76784)
        (xy 2.032 -1.65608)
        (xy 1.83388 -1.36906)
        (xy 1.9431 -1.09728)
        (xy 1.97612 -1.01346)
        (xy 2.0193 -0.91186)
        (xy 2.04978 -0.84074)
        (xy 2.06502 -0.80772)
        (xy 2.0955 -0.79756)
        (xy 2.16916 -0.77978)
        (xy 2.27838 -0.75692)
        (xy 2.40792 -0.73406)
        (xy 2.52984 -0.7112)
        (xy 2.63906 -0.69088)
        (xy 2.72034 -0.67564)
        (xy 2.7559 -0.66802)
        (xy 2.76606 -0.66294)
        (xy 2.77114 -0.64516)
        (xy 2.77622 -0.60706)
        (xy 2.77876 -0.54102)
        (xy 2.7813 -0.43434)
        (xy 2.7813 -0.28194)
        (xy 2.7813 -0.26416)
        (xy 2.77876 -0.11684)
        (xy 2.77622 -0.00254)
        (xy 2.77368 0.07366)
        (xy 2.7686 0.10414)
        (xy 2.73304 0.11176)
        (xy 2.6543 0.12954)
        (xy 2.54508 0.14986)
        (xy 2.413 0.17526)
        (xy 2.40284 0.1778)
        (xy 2.2733 0.2032)
        (xy 2.16154 0.22606)
        (xy 2.08534 0.24384)
        (xy 2.05232 0.254)
        (xy 2.0447 0.26416)
        (xy 2.0193 0.31496)
        (xy 1.9812 0.39624)
        (xy 1.93802 0.4953)
        (xy 1.89484 0.59944)
        (xy 1.85674 0.69088)
        (xy 1.83134 0.762)
        (xy 1.82372 0.79248)
        (xy 1.82626 0.79248)
        (xy 1.84404 0.8255)
        (xy 1.88976 0.89408)
        (xy 1.95326 0.98806)
        (xy 2.032 1.09982)
        (xy 2.03708 1.10744)
        (xy 2.11328 1.2192)
        (xy 2.17424 1.31318)
        (xy 2.21488 1.38176)
        (xy 2.23012 1.41224)
        (xy 2.20472 1.44526)
        (xy 2.14884 1.50876)
        (xy 2.06756 1.59512)
        (xy 1.9685 1.69164)
        (xy 1.93802 1.72212)
        (xy 1.83134 1.8288)
        (xy 1.75514 1.89738)
        (xy 1.70942 1.93548)
        (xy 1.68656 1.9431)
        (xy 1.68402 1.9431)
        (xy 1.651 1.92278)
        (xy 1.58242 1.87706)
        (xy 1.4859 1.81102)
        (xy 1.37414 1.73482)
        (xy 1.36652 1.72974)
        (xy 1.25476 1.65354)
        (xy 1.16078 1.59004)
        (xy 1.09474 1.54686)
        (xy 1.0668 1.52908)
        (xy 1.06172 1.52908)
        (xy 1.016 1.54432)
        (xy 0.93726 1.57226)
        (xy 0.84074 1.60782)
        (xy 0.7366 1.651)
        (xy 0.64516 1.6891)
        (xy 0.57404 1.72212)
        (xy 0.54102 1.7399)
        (xy 0.54102 1.74244)
        (xy 0.52832 1.78054)
        (xy 0.508 1.86436)
        (xy 0.48514 1.97866)
        (xy 0.45974 2.11582)
        (xy 0.45466 2.13868)
        (xy 0.42926 2.27076)
        (xy 0.40894 2.37998)
        (xy 0.3937 2.45618)
        (xy 0.38608 2.48666)
        (xy 0.36576 2.49174)
        (xy 0.30226 2.49682)
        (xy 0.2032 2.49936)
        (xy 0.08382 2.49936)
        (xy -0.04064 2.49936)
        (xy -0.1651 2.49682)
        (xy -0.26924 2.49174)
        (xy -0.34544 2.48666)
        (xy -0.37592 2.48158)
        (xy -0.37592 2.47904)
        (xy -0.38862 2.4384)
        (xy -0.4064 2.35458)
        (xy -0.42926 2.24028)
        (xy -0.4572 2.10312)
        (xy -0.46228 2.07772)
        (xy -0.48514 1.94564)
        (xy -0.508 1.83896)
        (xy -0.52324 1.76276)
        (xy -0.5334 1.73482)
        (xy -0.54356 1.7272)
        (xy -0.59944 1.70434)
        (xy -0.68834 1.66624)
        (xy -0.79756 1.62306)
        (xy -1.05156 1.51892)
        (xy -1.36398 1.73482)
        (xy -1.39446 1.7526)
        (xy -1.50622 1.8288)
        (xy -1.59766 1.8923)
        (xy -1.66116 1.93294)
        (xy -1.6891 1.94818)
        (xy -1.69164 1.94564)
        (xy -1.72212 1.92024)
        (xy -1.78308 1.86182)
        (xy -1.86944 1.778)
        (xy -1.96596 1.68148)
        (xy -2.03962 1.60782)
        (xy -2.12598 1.52146)
        (xy -2.17932 1.46304)
        (xy -2.2098 1.42494)
        (xy -2.21996 1.40208)
        (xy -2.21742 1.38684)
        (xy -2.1971 1.35382)
        (xy -2.15138 1.28524)
        (xy -2.08788 1.19126)
        (xy -2.01168 1.0795)
        (xy -1.94818 0.98806)
        (xy -1.88214 0.88392)
        (xy -1.83642 0.80772)
        (xy -1.82118 0.77216)
        (xy -1.82626 0.75692)
        (xy -1.84658 0.69596)
        (xy -1.88468 0.60198)
        (xy -1.9304 0.49276)
        (xy -2.03962 0.24638)
        (xy -2.20218 0.21336)
        (xy -2.30124 0.19558)
        (xy -2.4384 0.17018)
        (xy -2.56794 0.14478)
        (xy -2.77368 0.10414)
        (xy -2.7813 -0.6477)
        (xy -2.75082 -0.66294)
        (xy -2.72034 -0.67056)
        (xy -2.64414 -0.68834)
        (xy -2.53492 -0.70866)
        (xy -2.40792 -0.73406)
        (xy -2.2987 -0.75438)
        (xy -2.18694 -0.7747)
        (xy -2.1082 -0.78994)
        (xy -2.07264 -0.79756)
        (xy -2.06502 -0.80772)
        (xy -2.03708 -0.86106)
        (xy -1.99898 -0.94742)
        (xy -1.9558 -1.04902)
        (xy -1.91008 -1.15316)
        (xy -1.87198 -1.25222)
        (xy -1.84404 -1.32588)
        (xy -1.83388 -1.36398)
        (xy -1.84912 -1.39192)
        (xy -1.8923 -1.45796)
        (xy -1.95326 -1.5494)
        (xy -2.02692 -1.65862)
        (xy -2.10058 -1.76784)
        (xy -2.16408 -1.86182)
        (xy -2.20726 -1.92786)
        (xy -2.22758 -1.95834)
        (xy -2.21742 -1.9812)
        (xy -2.17424 -2.032)
        (xy -2.09042 -2.11836)
        (xy -1.9685 -2.24028)
        (xy -1.94818 -2.25806)
        (xy -1.85166 -2.35204)
        (xy -1.76784 -2.42824)
        (xy -1.71196 -2.47904)
        (xy -1.68656 -2.49936)
      )
      (stroke (width 0.00254) (type solid)) (fill solid) (layer "B.SilkS") (tstamp 8cd02d43-39a9-48dd-a657-5dc6737bd6d7))
  )

  (footprint "LED_SMD:LED_0603_1608Metric" (layer "B.Cu")
    (tstamp 5d4f7979-f2c1-4eb7-bb10-2f94cd3ba725)
    (at 183.5 110.5)
    (descr "LED SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: http://www.tortai-tech.com/upload/download/2011102023233369053.pdf), generated with kicad-footprint-generator")
    (tags "LED")
    (property "Reference" "D201" (at 0 1.43 0) (layer "B.SilkS") hide (tstamp 5d361b5c-9e14-43b7-8e84-5be19a9dd7ea)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Value" "LED" (at 0 -1.43 0) (layer "B.Fab") (tstamp bf01304e-cce9-4d2f-b34a-283988c16541)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 7e27d144-45f8-468d-bad6-3d6480736595)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp f6cd821a-c060-40e7-8391-30b8e65af9d0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Light emitting diode" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 3879ec8a-4a92-4035-a114-f4589ee6c1ef)
      (effects (font (size 1.27 1.27)))
    )
    (path "/437f2ceb-712b-41a0-9e79-c6c2becba629/709b6c30-3991-4f2a-9b6e-c128b57d2732")
    (sheetname "MCU")
    (sheetfile "grblPANEL_mcu.kicad_sch")
    (attr smd)
    (fp_line (start -1.485 -0.735) (end 0.8 -0.735)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 303a2908-10a0-44c2-8df5-5ca0276028cd))
    (fp_line (start -1.485 0.735) (end -1.485 -0.735)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp abf16ea6-3adf-4fee-8bd0-8a423c84f2e3))
    (fp_line (start 0.8 0.735) (end -1.485 0.735)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp db6496f6-4922-49af-bfd6-6c96b2679d50))
    (fp_line (start -1.48 -0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp ea0fd68e-06f3-45fc-8064-0cc66548581b))
    (fp_line (start -1.48 0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 805dda84-ee9b-49a1-9f06-576daeed5032))
    (fp_line (start 1.48 -0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp ce46f0e8-e625-41df-ace8-1c04bb99d0cb))
    (fp_line (start 1.48 0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 7f60e700-c2eb-4661-8cf8-bcd8b9a4ef56))
    (fp_line (start -0.8 -0.4) (end 0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 3456a317-37fc-419d-81a0-ef23c179dd9a))
    (fp_line (start -0.8 0.1) (end -0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 1d79df03-2ecb-4343-a000-0b4a37b609c2))
    (fp_line (start -0.5 0.4) (end -0.8 0.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp c8f80f89-d52e-4ade-867e-c122336493c3))
    (fp_line (start 0.8 -0.4) (end 0.8 0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 500bb8a2-eede-46f0-8497-2c36e62e9566))
    (fp_line (start 0.8 0.4) (end -0.5 0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 0c117467-80b9-425d-840c-44f18759e970))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "B.Fab") (tstamp 4acd175b-c0fb-4591-ac60-6c23f2b687e6)
      (effects (font (size 0.4 0.4) (thickness 0.06)) (justify mirror))
    )
    (pad "1" smd roundrect (at -0.7875 0) (size 0.875 0.95) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 10 "/MCU/LED_K") (pinfunction "K") (pintype "passive")
      (tstamp a4e107ef-99bd-444f-b8f3-5a0eab5ce0a9)
    )
    (pad "2" smd roundrect (at 0.7875 0) (size 0.875 0.95) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pinfunction "A") (pintype "passive")
      (tstamp a0397133-d462-4a09-a51c-39fd0fc63ce9)
    )
    (model "${KICAD6_3DMODEL_DIR}/LED_SMD.3dshapes/LED_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Button_Switch_SMD:SW_SPST_PTS810" (layer "B.Cu")
    (tstamp cc7b892e-2384-4dc8-9463-c1e9ebb1e5ee)
    (at 174.6 107.8 180)
    (descr "C&K Components, PTS 810 Series, Microminiature SMT Top Actuated, http://www.ckswitches.com/media/1476/pts810.pdf")
    (tags "SPST Button Switch")
    (property "Reference" "SW201" (at 0 2.6 0) (layer "B.SilkS") hide (tstamp 84524a6c-e20e-452b-afab-80d126273b4c)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Value" "SW_Push" (at 0 2.8 0) (layer "B.Fab") (tstamp 1c3c3bc2-39d8-4cf6-91e5-449375b61ced)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Footprint" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 8fc5e04f-8175-4c4b-b272-e545641bd7a2)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp f7519f4e-48ec-4147-ac31-ed5b856c8034)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Push button switch, generic, two pins" (at 0 0 180 unlocked) (layer "F.Fab") hide (tstamp 7aa529ea-e023-47b7-8579-d634f028e83b)
      (effects (font (size 1.27 1.27)))
    )
    (path "/437f2ceb-712b-41a0-9e79-c6c2becba629/ce40af38-6eca-4492-8fde-6e9a33cb1c65")
    (sheetname "MCU")
    (sheetfile "grblPANEL_mcu.kicad_sch")
    (attr smd)
    (fp_line (start 2.2 1.7) (end -2.2 1.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 0525d7f0-3821-43bc-8541-13915cb91766))
    (fp_line (start 2.2 1.58) (end 2.2 1.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp bf9e4a3b-29f4-4147-9e04-8ec69d861043))
    (fp_line (start 2.2 -0.57) (end 2.2 0.57)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 0b96f6bb-f08d-4470-9a9b-db1e1159eb0e))
    (fp_line (start 2.2 -1.7) (end 2.2 -1.58)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp a0249605-d3ad-4f50-8394-28777821ce72))
    (fp_line (start -2.2 1.7) (end -2.2 1.58)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 4f686ef6-93d8-4449-9353-9ffb88f8e862))
    (fp_line (start -2.2 0.57) (end -2.2 -0.57)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp fe9faae0-5b8a-46aa-8b02-cdaba6e7c393))
    (fp_line (start -2.2 -1.58) (end -2.2 -1.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp bf0fb811-e5f1-46ad-bfa6-129d1a8e36b8))
    (fp_line (start -2.2 -1.7) (end 2.2 -1.7)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 27e2662d-b561-4d08-95de-9020549f19fc))
    (fp_line (start 2.85 1.85) (end 2.85 -1.85)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 20988937-d259-40bc-917e-d1eec665df2f))
    (fp_line (start 2.85 -1.85) (end -2.85 -1.85)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 32979203-cbcc-42ec-9430-994bf8ac6e0a))
    (fp_line (start -2.85 1.85) (end 2.85 1.85)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 01ac45e5-0b7b-4d3b-9762-a901eb9dd1e3))
    (fp_line (start -2.85 -1.85) (end -2.85 1.85)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 57ad2665-e739-411c-845a-41c8483876f8))
    (fp_line (start 2.1 1.6) (end -2.1 1.6)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 22a518fb-f4b9-41ec-a448-82396fad8616))
    (fp_line (start 2.1 -1.6) (end 2.1 1.6)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp fc069abc-9c50-428f-8beb-a50258787e16))
    (fp_line (start 0.4 -1.1) (end -0.4 -1.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 2735c3c4-045d-4970-8e2b-f90b24eb8acb))
    (fp_line (start -0.4 1.1) (end 0.4 1.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 59eee27c-5d5b-4a25-b8c5-53c14367600e))
    (fp_line (start -2.1 1.6) (end -2.1 -1.6)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 11f921ef-4e39-43ce-afb1-e22158607d94))
    (fp_line (start -2.1 -1.6) (end 2.1 -1.6)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 561af2a7-fc20-4ed2-ae6f-7e15c693719e))
    (fp_arc (start 0.4 -1.1) (mid 1.5 0) (end 0.4 1.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 1c92ea41-f905-4435-95a0-06d5bbdf0702))
    (fp_arc (start -0.4 1.1) (mid -1.5 0) (end -0.4 -1.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp ac846f8e-1d12-40a9-9444-26a5aa8d0ad2))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "B.Fab") (tstamp 3b158572-9c15-4684-9ef6-947539f15247)
      (effects (font (size 0.6 0.6) (thickness 0.09)) (justify mirror))
    )
    (pad "1" smd rect (at -2.075 1.075 180) (size 1.05 0.65) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 11 "/MCU/SWCLK{slash}BOOT0") (pinfunction "1") (pintype "passive")
      (tstamp 827805c3-19ec-41f2-b155-11bfd67a0a84)
    )
    (pad "1" smd rect (at 2.075 1.075 180) (size 1.05 0.65) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 11 "/MCU/SWCLK{slash}BOOT0") (pinfunction "1") (pintype "passive")
      (tstamp 1b491b5b-2476-4605-ab1e-05c331905716)
    )
    (pad "2" smd rect (at -2.075 -1.075 180) (size 1.05 0.65) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 9 "Net-(R202-Pad1)") (pinfunction "2") (pintype "passive")
      (tstamp 4f6dc94f-1b61-4de5-b30a-f6ef16e85486)
    )
    (pad "2" smd rect (at 2.075 -1.075 180) (size 1.05 0.65) (layers "B.Cu" "B.Paste" "B.Mask")
      (net 9 "Net-(R202-Pad1)") (pinfunction "2") (pintype "passive")
      (tstamp b9cbe1d4-d697-4f1c-9ba3-dee4099e1589)
    )
    (model "${KICAD6_3DMODEL_DIR}/Button_Switch_SMD.3dshapes/SW_SPST_PTS810.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
    (model "/opt/KiCad/3d/CK_PTS810.step"
      (offset (xyz 0 0 1.27))
      (scale (xyz 1 1 1))
      (rotate (xyz -90 0 0))
    )
  )

  (footprint "TerminalBlock_Phoenix:TerminalBlock_Phoenix_PT-1,5-2-3.5-H_1x02_P3.50mm_Horizontal" (layer "B.Cu")
    (tstamp e40066be-dd01-407d-89ff-1282c8be4104)
    (at 184 96.75 -90)
    (descr "Terminal Block Phoenix PT-1,5-2-3.5-H, 2 pins, pitch 3.5mm, size 7x7.6mm^2, drill diamater 1.2mm, pad diameter 2.4mm, see , script-generated using https://github.com/pointhi/kicad-footprint-generator/scripts/TerminalBlock_Phoenix")
    (tags "THT Terminal Block Phoenix PT-1,5-2-3.5-H pitch 3.5mm size 7x7.6mm^2 drill 1.2mm pad 2.4mm")
    (property "Reference" "J401" (at 1.75 4.16 90) (layer "B.SilkS") hide (tstamp c9efaf0e-f01a-4f33-b450-5ae2085c05a1)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Value" "Conn_01x02" (at 1.75 -5.56 90) (layer "B.Fab") (tstamp cf5c62e7-2e2b-46fa-a099-ad7de6b4978e)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (property "Footprint" "" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 3e7552bd-f5a8-463c-a5d9-9d474b29ffd6)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 8c1a5e11-5a42-408d-b373-ee611c0969eb)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Generic connector, single row, 01x02, script generated (kicad-library-utils/schlib/autogen/connector/)" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 3dbf7ba6-0a4e-4ec4-8edb-48e70efb8fb4)
      (effects (font (size 1.27 1.27)))
    )
    (path "/4da24ca5-d3e5-4dcb-8fe8-efef99f12779/19854203-6c4a-4ad0-91aa-0aaa13e9917c")
    (sheetname "Modbus")
    (sheetfile "grblPANEL_modbus.kicad_sch")
    (attr through_hole)
    (fp_line (start -1.81 3.16) (end 5.31 3.16)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 4cb9b9a3-9a04-4b15-99cf-6c13054b63d6))
    (fp_line (start -1.81 3.16) (end -1.81 -4.56)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 36c5484f-3973-48f8-8f85-5964b496d4d0))
    (fp_line (start 5.31 3.16) (end 5.31 -4.56)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp d67b19c0-7465-4ad8-b632-18f7ee50965f))
    (fp_line (start 4.57 1.275) (end 4.476 1.181)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp b1e37c7b-b994-47b7-9193-8dc1f1939d39))
    (fp_line (start 4.775 1.069) (end 4.646 0.941)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp c2175e1f-e477-404b-972d-a0276df5e651))
    (fp_line (start 2.355 -0.941) (end 2.226 -1.069)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp ff3c7464-2300-4a62-b087-8561becb1369))
    (fp_line (start 2.525 -1.181) (end 2.431 -1.274)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 591bdd6f-f3de-40f1-9e74-607276f0622c))
    (fp_line (start -1.81 -3) (end 5.31 -3)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp d87431d2-a615-41ba-8a7b-04532e3d0678))
    (fp_line (start -1.81 -4.1) (end 5.31 -4.1)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 8567d7f1-1eab-4453-a11f-18a2dd8ab9b0))
    (fp_line (start -2.05 -4.16) (end -2.05 -4.8)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 5f1df69e-1a4a-45e4-93c9-5dca3c753095))
    (fp_line (start -1.81 -4.56) (end 5.31 -4.56)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 8b95f981-de52-494f-b486-16ebdf73ff1f))
    (fp_line (start -2.05 -4.8) (end -1.65 -4.8)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 0a327f84-cc0a-48dd-a30a-bb9bb57f8d8a))
    (fp_arc (start 0.866 1.44) (mid -0.014012 1.680286) (end -0.889894 1.425358)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 5c348ed8-638f-43b3-97d8-de3678e68ae8))
    (fp_arc (start -1.44 0.866) (mid -1.680286 -0.014012) (end -1.425358 -0.889894)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp d79e1e60-70b6-48d3-a25d-55400fc0aacb))
    (fp_arc (start 1.425 -0.891) (mid 1.680626 -0.000476) (end 1.425504 0.890193)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 2e878742-1eba-4046-b68d-139d436c3ec8))
    (fp_arc (start -0.866 -1.44) (mid -0.435535 -1.622918) (end 0.028674 -1.680099)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 044600c0-90e4-4f17-8b19-72d6be41eed3))
    (fp_arc (start 0 -1.68) (mid 0.463071 -1.61492) (end 0.890264 -1.424721)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 5bc4be66-0f00-4419-9717-06579fbb315c))
    (fp_circle (center 3.5 0) (end 5.18 0)
      (stroke (width 0.12) (type solid)) (fill none) (layer "B.SilkS") (tstamp c0393292-a753-45b9-8836-f7c4332b6f77))
    (fp_line (start -2.25 3.6) (end -2.25 -5)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 3b6ad009-df45-4577-93b8-ac6d93da16c1))
    (fp_line (start 5.75 3.6) (end -2.25 3.6)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp e12fc8da-ad36-4872-a180-f056a7f729c0))
    (fp_line (start -2.25 -5) (end 5.75 -5)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 92a53fe1-4e52-4f4b-b8db-5d92f5c55935))
    (fp_line (start 5.75 -5) (end 5.75 3.6)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp f26f6d65-ec06-4b21-93db-e0523a2ed222))
    (fp_line (start -1.75 3.1) (end 5.25 3.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp a91d1601-d8b4-4500-a90c-b8881fa590d7))
    (fp_line (start 5.25 3.1) (end 5.25 -4.5)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 9c3421bc-a38f-468c-9a57-a27a8aec18ad))
    (fp_line (start 0.955 1.138) (end -1.138 -0.955)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 9b2efabb-529c-46a1-9eee-f5c3870d9dba))
    (fp_line (start 4.455 1.138) (end 2.363 -0.955)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp a9bc2386-974d-4841-ae86-ba828c2fe0da))
    (fp_line (start 1.138 0.955) (end -0.955 -1.138)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp cde6c25e-ef64-4775-a2c8-0364767e158a))
    (fp_line (start 4.638 0.955) (end 2.546 -1.138)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp afe97be4-5bd7-411b-a7e0-82ee2b7d66f7))
    (fp_line (start -1.75 -3) (end 5.25 -3)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 44161e8c-f071-4ef6-8eb4-209811908330))
    (fp_line (start -1.75 -4.1) (end -1.75 3.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 806f605f-9f06-41c6-be7e-52cb17ea7a71))
    (fp_line (start -1.75 -4.1) (end 5.25 -4.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 1f7bee54-ca10-4356-ae93-74f6e67714bc))
    (fp_line (start -1.35 -4.5) (end -1.75 -4.1)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp c5e49e10-81e4-482b-8880-3ff2a1cc6310))
    (fp_line (start 5.25 -4.5) (end -1.35 -4.5)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 66ffde22-c822-4f5a-8704-776fe12d77f5))
    (fp_circle (center 0 0) (end 1.5 0)
      (stroke (width 0.1) (type solid)) (fill none) (layer "B.Fab") (tstamp 8039941d-2e35-4564-a59b-d9ff6af7d317))
    (fp_circle (center 3.5 0) (end 5 0)
      (stroke (width 0.1) (type solid)) (fill none) (layer "B.Fab") (tstamp df07a518-b54f-47aa-ae4c-79d37ab09828))
    (fp_text user "${REFERENCE}" (at 1.75 -2.4 90) (layer "B.Fab") (tstamp 767c0bcd-8834-40a9-bb0d-97ccbaf3debc)
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
    )
    (pad "1" thru_hole rect (at 0 0 270) (size 2.4 2.4) (drill 1.2) (layers "*.Cu" "*.Mask")
      (net 14 "/Modbus/CONN_RS485_A") (pinfunction "Pin_1") (pintype "passive")
      (tstamp e71502aa-c31c-4e76-a3a2-bf05757b7675)
    )
    (pad "2" thru_hole circle (at 3.5 0 270) (size 2.4 2.4) (drill 1.2) (layers "*.Cu" "*.Mask")
      (net 15 "/Modbus/CONN_RS485_B") (pinfunction "Pin_2") (pintype "passive")
      (tstamp ceae58bb-47e3-4113-a0ee-340276fee8fc)
    )
    (model "${KICAD6_3DMODEL_DIR}/TerminalBlock_Phoenix.3dshapes/TerminalBlock_Phoenix_PT-1,5-2-3.5-H_1x02_P3.50mm_Horizontal.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_rect (start 166.5 96) (end 179.5 101)
    (stroke (width 0.15) (type default)) (fill none) (layer "B.SilkS") (tstamp 001248c9-0219-4732-8743-6290967a4a49))
  (gr_rect (start 152 79.8) (end 158 86.2)
    (stroke (width 0.15) (type default)) (fill none) (layer "B.SilkS") (tstamp 0332eaed-0e52-405f-90b5-e3b535b6747e))
  (gr_line (start 113.8 102.9) (end 113.8 100.3)
    (stroke (width 0.4) (type default)) (layer "B.SilkS") (tstamp 0de947c5-fb51-440a-b6c6-6af02d7a1bde))
  (gr_rect (start 115.25 89.5) (end 121.35 96)
    (stroke (width 0.15) (type default)) (fill none) (layer "B.SilkS") (tstamp 1333880d-1504-4fc9-8af3-1ebe041a300b))
  (gr_line (start 161.8 117.4) (end 105.7 117.4)
    (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp 20a95b0a-4330-4f88-926a-51a366454dd8))
  (gr_line (start 147.7 121.3) (end 147.7 117.8)
    (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp 25b625a7-0210-446e-8eed-e6a01f499f88))
  (gr_line (start 105.7 121.3) (end 105.7 117.8)
    (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp 386ee220-a1a8-4af6-8728-527d81eb25cc))
  (gr_rect (start 158.3 88) (end 164.1 94.5)
    (stroke (width 0.15) (type default)) (fill none) (layer "B.SilkS") (tstamp 4925651f-20db-4e79-a911-674ff107398c))
  (gr_line (start 156 79.8) (end 156 86.2)
    (stroke (width 0.15) (type default)) (layer "B.SilkS") (tstamp 53d78553-6416-4859-b9df-0166842f2671))
  (gr_line (start 119.4 89.5) (end 119.4 96)
    (stroke (width 0.15) (type default)) (layer "B.SilkS") (tstamp 6b0bb9ad-2665-49bc-8dfa-5e94011eb4b9))
  (gr_line (start 110.5 106.25) (end 110.5 113.25)
    (stroke (width 0.15) (type default)) (layer "B.SilkS") (tstamp 73382f9a-a4ad-4293-908e-46d9b54ad709))
  (gr_line (start 162.2 95.4) (end 162.2 105.5)
    (stroke (width 0.15) (type default)) (layer "B.SilkS") (tstamp 9d0276b3-01d4-443d-86bd-629979303ce0))
  (gr_line (start 119.7 121.3) (end 119.7 117.8)
    (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp a10505e5-8cad-4dc3-be35-4ec8c4977f0c))
  (gr_rect (start 105.75 106.25) (end 112.5 113.25)
    (stroke (width 0.15) (type default)) (fill none) (layer "B.SilkS") (tstamp a6da5638-8cf8-4334-b884-934ab87a3648))
  (gr_line (start 161.8 121.3) (end 161.8 117.8)
    (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp a746f3b8-5c4f-463b-ac3c-8e1522d95127))
  (gr_rect (start 158.3 95.4) (end 164.1 105.5)
    (stroke (width 0.15) (type default)) (fill none) (layer "B.SilkS") (tstamp d396a20c-703d-4847-ada9-e45ccdfca157))
  (gr_line (start 162.2 88) (end 162.2 94.5)
    (stroke (width 0.15) (type default)) (layer "B.SilkS") (tstamp d6b0efae-2511-4613-b0de-cbcec805275f))
  (gr_rect (start 167 80.5) (end 179.5 86)
    (stroke (width 0.15) (type default)) (fill none) (layer "B.SilkS") (tstamp f5d2483f-76cc-4ee8-a74c-f48c6f68885c))
  (gr_line (start 133.8 121.3) (end 133.8 117.8)
    (stroke (width 0.15) (type solid)) (layer "B.SilkS") (tstamp fa3d8ae2-749e-46a2-bcbc-c22ec35aa5a7))
  (gr_circle (center 165.3 95.65) (end 165.5 95.65)
    (stroke (width 0.2) (type solid)) (fill none) (layer "F.SilkS") (tstamp cf9cb875-f061-4e23-90ca-19da7a07a32a))
  (gr_rect (start 105 75) (end 189 130)
    (stroke (width 0.2) (type solid)) (fill none) (layer "Edge.Cuts") (tstamp 23f96a5b-b73f-4af7-81e9-fae98791b224))
  (gr_poly
    (pts
      (xy 189 130)
      (xy 105 130)
      (xy 105 126)
      (xy 107 126)
      (xy 107 128)
      (xy 187 128)
      (xy 187 77)
      (xy 107 77)
      (xy 107 85)
      (xy 105 85)
      (xy 105 75)
      (xy 189 75)
    )
    (stroke (width 0.15) (type solid)) (fill none) (layer "User.2") (tstamp d54ca7a9-0700-4810-ac92-fdada7168199))
  (gr_rect (start 118.7 76.5) (end 144.7 130.5)
    (stroke (width 0.15) (type solid)) (fill none) (layer "User.3") (tstamp 992af31a-667b-4c2f-978d-a05ad0e7d1f2))
  (gr_rect (start 104 92) (end 152 112)
    (stroke (width 0.15) (type solid)) (fill none) (layer "User.3") (tstamp a5bcbb3f-acd5-4063-b8cf-f4a49f5c6f48))
  (gr_text "SWD" (at 110.1 94 0) (layer "B.SilkS") (tstamp 01f2f6da-1ed0-4a51-a2db-291632116884)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "B" (at 128.5 120.5 0) (layer "B.SilkS") (tstamp 0219884f-f8ee-4372-aafd-ba17d75f788e)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "CAN H" (at 185 78.45 0) (layer "B.SilkS") (tstamp 12ecce64-c283-4ffd-be6f-e1cf2be5b8e8)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "CAN L" (at 185.181 88.45 0) (layer "B.SilkS") (tstamp 13a01027-97ec-4082-a22f-07c8e2d9fac3)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "B" (at 142.5 120.5 0) (layer "B.SilkS") (tstamp 14f6c8d8-6e06-4445-be76-14cce3ee8746)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "ENCODER 2" (at 140.75 118.5 0) (layer "B.SilkS") (tstamp 18afd804-29b4-48ca-aed1-7e004eaac2c5)
    (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
  )
  (gr_text "CC-BY-SA" (at 175.7 121.7 0) (layer "B.SilkS") (tstamp 1cea958c-261a-404f-8dc9-c463be20e814)
    (effects (font (size 1.5 1.2) (thickness 0.2)) (justify mirror))
  )
  (gr_text "MODBUS" (at 163.2 91.25 -90) (layer "B.SilkS") (tstamp 1d986cb2-6606-4645-9173-43dd2a43945d)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "Vcc" (at 153 120.514367 0) (layer "B.SilkS") (tstamp 1ea815ec-d140-40e7-b262-97ad972254fc)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "RS485 B-" (at 183.5 103.5 0) (layer "B.SilkS") (tstamp 20bd6a38-476a-4f6d-8da7-fdf4a2a551a3)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "ENCODER 3" (at 126.8 118.460723 0) (layer "B.SilkS") (tstamp 21caedbd-bc9c-4bac-a500-9eba6796b724)
    (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
  )
  (gr_text "GND" (at 107.5 120.5 0) (layer "B.SilkS") (tstamp 232aaaf6-f920-4fc2-bdf6-006556907bd2)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "GND" (at 184 126 0) (layer "B.SilkS") (tstamp 27f980dc-5796-4040-b73c-97fd0b41d169)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "RX" (at 159.2 89.5 -90) (layer "B.SilkS") (tstamp 3525f842-f29c-48ff-8a78-eae3809bd984)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "GND" (at 135.5 120.5 0) (layer "B.SilkS") (tstamp 369c4d09-dc22-4984-bfd7-158c7a246148)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "DISPLAY" (at 163.2 100.5 270) (layer "B.SilkS") (tstamp 4307081f-1bd5-4733-9964-27e542dfeeb3)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "LINK" (at 185 110.5 0) (layer "B.SilkS") (tstamp 44ea0da5-06d4-452c-84d4-3f3a4514627c)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify right mirror))
  )
  (gr_text "A" (at 132 120.5 0) (layer "B.SilkS") (tstamp 44f18f33-dd71-4f6d-9c85-f79e2d2a1ea1)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "UVOV" (at 152.75 116 0) (layer "B.SilkS") (tstamp 47429a85-4948-4664-afc4-141f760b5a9f)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify right mirror))
  )
  (gr_text "CLK" (at 116 94.5 -90) (layer "B.SilkS") (tstamp 47f9c0c0-38eb-45d5-8a0d-d7e4cca9eaae)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "Vcc" (at 111 120.514367 0) (layer "B.SilkS") (tstamp 4a1d76d1-7e9f-41a4-9607-0e2c4b2b58c7)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "FLAG" (at 152.75 113.25 0) (layer "B.SilkS") (tstamp 4d163c44-409d-4b1a-8564-6b77bd712559)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify right mirror))
  )
  (gr_text "Vcc" (at 125 120.514367 0) (layer "B.SilkS") (tstamp 53d5cba3-d312-4a4b-9525-8d3f4ca2e1e2)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "CONSOLE" (at 111.5 109.75 270) (layer "B.SilkS") (tstamp 585f9d2f-7908-4d4e-a543-cb713bf07ff9)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "DC" (at 159.2 97 270) (layer "B.SilkS") (tstamp 58f24128-7a36-4328-8fe1-e97d71c70eca)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "github.com/dresco/grblPANEL" (at 166.5 78.3 0) (layer "B.SilkS") (tstamp 67f5d422-56b0-4b4b-b6d7-29b0598a002e)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "Vcc" (at 139 120.514367 0) (layer "B.SilkS") (tstamp 698e73bd-2e11-4e6e-af6a-900bde703a94)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "RS485 A+" (at 183.5 93.5 0) (layer "B.SilkS") (tstamp 6c479a01-3c14-41a9-8e8d-5369b22c8232)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "CAN" (at 157 81.5 270) (layer "B.SilkS") (tstamp 6dfd7608-447a-4400-b945-a4799e24dae0)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify right mirror))
  )
  (gr_text "CAN\nTERM" (at 170 83.2 0) (layer "B.SilkS") (tstamp 7096afba-be80-4d2b-91ac-63f41bfddde0)
    (effects (font (size 1.5 1.3) (thickness 0.2)) (justify mirror))
  )
  (gr_text "A" (at 160 120.531419 0) (layer "B.SilkS") (tstamp 7132ab60-f784-485b-a076-99636e80d5b3)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "B" (at 114.5 120.5 0) (layer "B.SilkS") (tstamp 72c32afe-df67-47a2-8dc4-db6cfc5126fb)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "A" (at 146 120.5 0) (layer "B.SilkS") (tstamp 7337b10f-708f-436c-9e27-cad508364633)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "A" (at 118 120.5 0) (layer "B.SilkS") (tstamp 7d86672d-4f1b-4ba1-b528-c56bd380590b)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "RS485\nTERM" (at 170 98.5 0) (layer "B.SilkS") (tstamp 81783a3d-d8ef-4e69-8923-b640080ef316)
    (effects (font (size 1.5 1.3) (thickness 0.2)) (justify mirror))
  )
  (gr_text "KEYPAD" (at 120.4 92.75 270) (layer "B.SilkS") (tstamp 83b60f7f-941a-4eeb-9a21-07104c630b08)
    (effects (font (size 1.2 1) (thickness 0.2) bold) (justify mirror))
  )
  (gr_text "B" (at 156.5 120.528734 0) (layer "B.SilkS") (tstamp 92adfa63-1c49-4028-bf66-7531bc9c1eda)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "TX" (at 109.5 111.5 270) (layer "B.SilkS") (tstamp 957ddf14-054c-4e91-afba-d0c41c606c9b)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "RX" (at 153 84 -90) (layer "B.SilkS") (tstamp 97416a7b-4dca-4787-8252-62a27242c099)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify right mirror))
  )
  (gr_text "USB" (at 107.75 79.05 0) (layer "B.SilkS") (tstamp 99114daa-d187-4eb5-bdbe-13cd799f8919)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "SDA" (at 159.2 104 -90) (layer "B.SilkS") (tstamp 9ebe19f5-14d4-436a-b46e-20fd7d06381f)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "GND" (at 121.5 120.5 0) (layer "B.SilkS") (tstamp a23ac560-21c6-4e00-8b5a-289933d54f17)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "ENCODER 1" (at 154.75 118.5 0) (layer "B.SilkS") (tstamp a5f0041b-083e-4147-b222-c171fa331866)
    (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
  )
  (gr_text "TX" (at 153 80.5 270) (layer "B.SilkS") (tstamp aca20ea8-7e6b-4b09-8384-19e0af20e493)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify right mirror))
  )
  (gr_text "Rev B\n04/23" (at 166.3 120.8 0) (layer "B.SilkS") (tstamp b2627901-e06a-4510-9793-224725311c3a)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "GND" (at 149.5 120.5 0) (layer "B.SilkS") (tstamp bd89d851-8f04-462f-a348-73f20e4cbce5)
    (effects (font (size 1.5 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "RESET" (at 168.9 112.3 0) (layer "B.SilkS") (tstamp bffd07e2-558e-44ef-9985-9bd954fa4231)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "DC IN" (at 184.1 113.8 0) (layer "B.SilkS") (tstamp c1572d5a-00c9-4975-bff0-adb9761fcd51)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "SCL" (at 159.2 100.5 -90) (layer "B.SilkS") (tstamp c1e1df6f-5cb2-416a-ac3b-2e22d0e9252c)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "TX" (at 159.2 93 -90) (layer "B.SilkS") (tstamp c42cca01-4697-4a37-a8f1-dbf7fd1619e0)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "BOOT" (at 169.3 107.8 0) (layer "B.SilkS") (tstamp c464c200-007b-4a8c-b414-c97731e53a08)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "RX" (at 109.5 108 270) (layer "B.SilkS") (tstamp d0fa5bfa-e74f-4a76-8a0f-456316efd0bd)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "5-12V" (at 184 116 0) (layer "B.SilkS") (tstamp d17e1d56-b480-4510-b781-70799fd719d9)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "KEYPAD" (at 131.9 86 0) (layer "B.SilkS") (tstamp dfb852c1-5478-421c-a347-dca103c1011e)
    (effects (font (size 1.5 1.3) (thickness 0.3)) (justify mirror))
  )
  (gr_text "grblPANEL" (at 171.55 126.4 0) (layer "B.SilkS") (tstamp e6026ffd-4c52-4dd6-8d91-4b656a7a85af)
    (effects (font (size 3 2.2) (thickness 0.5)) (justify mirror))
  )
  (gr_text "PWR" (at 185 107.5 0) (layer "B.SilkS") (tstamp ead8a5d4-b33a-4258-9eef-e691c2ac9b40)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify right mirror))
  )
  (gr_text "ENCODER 4" (at 112.75 118.5 0) (layer "B.SilkS") (tstamp f35c0360-282d-4bbc-b041-98773a615d61)
    (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
  )
  (gr_text "SDA" (at 116 91 -90) (layer "B.SilkS") (tstamp f5998f2e-148b-41d9-aefa-43d4d46f9b5d)
    (effects (font (size 1.2 1) (thickness 0.2)) (justify mirror))
  )
  (gr_text "Display panel:\n83.98 x 54.53mm\n\nEdge cuts:\n84.00 x 55.00mm" (at 192.8 83.4 0) (layer "User.1") (tstamp 6dc2b79d-50f3-41b8-b151-616385419fb6)
    (effects (font (size 2 2) (thickness 0.15)) (justify left))
  )

  (segment (start 184.325 105.5) (end 185.5 105.5) (width 0.3) (layer "F.Cu") (net 1) (tstamp 0d2df83d-27dd-48e9-bcde-5ce2833cd25f))
  (segment (start 173.605 100.405) (end 173.61 100.4) (width 0.5) (layer "F.Cu") (net 1) (tstamp 144e7364-45a2-4c33-b99d-05d7cc041dc6))
  (segment (start 177.8 96.7) (end 177.425 96.325) (width 0.3) (layer "F.Cu") (net 1) (tstamp 34bb7c52-1f2b-4d6c-8431-158e1354a171))
  (segment (start 180.9375 98.5) (end 179 98.5) (width 0.5) (layer "F.Cu") (net 1) (tstamp 3d2b337a-a034-47cc-ab43-b61a14c81a69))
  (segment (start 173.2 94.8) (end 173.6 94.4) (width 0.5) (layer "F.Cu") (net 1) (tstamp 416a6b80-0124-47e1-9aa7-c9d1e79ef571))
  (segment (start 172.375 94.8) (end 173.2 94.8) (width 0.5) (layer "F.Cu") (net 1) (tstamp 9afd5ff0-5870-4e14-a1dd-51e907f876dc))
  (segment (start 171.975 100.405) (end 173.605 100.405) (width 0.5) (layer "F.Cu") (net 1) (tstamp cdac7d31-d998-4049-806f-5cf2dddc94fa))
  (segment (start 177.425 96.325) (end 176.7 96.325) (width 0.3) (layer "F.Cu") (net 1) (tstamp da23af1f-3946-4ff1-80b1-23626129fb3e))
  (segment (start 180.1 105) (end 180.1 105.5) (width 0.5) (layer "F.Cu") (net 3) (tstamp 143042d7-1f78-46a8-a032-40f7d71e67f4))
  (segment (start 180.1 108.9) (end 180 109) (width 0.5) (layer "F.Cu") (net 3) (tstamp 274c5252-9228-4910-9bd3-ab909c59208f))
  (segment (start 172.3 105.6) (end 176.2 109.5) (width 0.5) (layer "F.Cu") (net 3) (tstamp 2d1b473b-a1c8-4271-8e55-30b69646746d))
  (segment (start 170.825 95.825) (end 170.825 94.8) (width 0.3) (layer "F.Cu") (net 3) (tstamp 2e15961d-e8d4-4619-be24-2e88ebab1c94))
  (segment (start 170.8 92) (end 170.8 94.775) (width 0.5) (layer "F.Cu") (net 3) (tstamp 4e3d0868-8c12-4d54-be08-79743e3ecd5e))
  (segment (start 184.25 109) (end 186 109) (width 0.5) (layer "F.Cu") (net 3) (tstamp 4ec3a508-50ba-41ce-bcf5-ffa9888dc36e))
  (segment (start 176.7 103.4) (end 177.7 104.4) (width 0.5) (layer "F.Cu") (net 3) (tstamp 50443ec8-a904-4c79-8fd4-9f9aa7b477b0))
  (segment (start 171.975 96.145) (end 171.79 95.96) (width 0.3) (layer "F.Cu") (net 3) (tstamp 51f9b674-dd90-45f2-b323-8dd3856f7a54))
  (segment (start 164.3 86.1) (end 167.2 89) (width 0.5) (layer "F.Cu") (net 3) (tstamp 52edb161-0f14-4c88-8017-c1649cf56c21))
  (segment (start 178.3 106.175) (end 180.025 106.175) (width 0.5) (layer "F.Cu") (net 3) (tstamp 5598122b-cc13-4968-9228-9f7b89ed9fc0))
  (segment (start 171.975 96.595) (end 170.875 96.595) (width 0.3) (layer "F.Cu") (net 3) (tstamp 63d10c89-3881-4690-8936-1acbe93bc884))
  (segment (start 180 109) (end 176.2 112.8) (width 0.5) (layer "F.Cu") (net 3) (tstamp 67ba7972-0051-45bb-962f-7bcd1d537aca))
  (segment (start 176.2 109.5) (end 176.2 113.55) (width 0.5) (layer "F.Cu") (net 3) (tstamp 70250cb2-99cd-408b-9af0-859bb077f559))
  (segment (start 172.2 90.6) (end 170.8 92) (width 0.5) (layer "F.Cu") (net 3) (tstamp 7409ade3-a01b-4502-96e8-82b8b29f7c57))
  (segment (start 170.875 96.595) (end 170.285 97.185) (width 0.3) (layer "F.Cu") (net 3) (tstamp 8103a261-2199-4a59-a3c1-f076e14faa77))
  (segment (start 188 107) (end 188 95) (width 0.5) (layer "F.Cu") (net 3) (tstamp 8f2ff9b6-74b0-457c-811a-af0df1992749))
  (segment (start 180.025 106.175) (end 180.1 106.1) (width 0.5) (layer "F.Cu") (net 3) (tstamp 958a88a8-bd97-4da4-aebd-1717bdf035bf))
  (segment (start 184.25 109) (end 180 109) (width 0.5) (layer "F.Cu") (net 3) (tstamp 959faf6e-91e1-45f7-afa5-43b50d353dd0))
  (segment (start 188 111) (end 186 109) (width 0.5) (layer "F.Cu") (net 3) (tstamp 9779cc85-70bd-4a55-ac02-92be5e479f59))
  (segment (start 170.285 97.185) (end 170.285 102.2) (width 0.3) (layer "F.Cu") (net 3) (tstamp a3a1cfc6-8850-4d5e-9e8d-7a1b960f2800))
  (segment (start 180.1 105.5) (end 180.1 108.9) (width 0.5) (layer "F.Cu") (net 3) (tstamp a417a783-95ed-4ba4-84b8-00fd6344a843))
  (segment (start 158.1 105.6) (end 172.3 105.6) (width 0.5) (layer "F.Cu") (net 3) (tstamp a53ac72f-f020-4801-865e-5cdcdd8c1cba))
  (segment (start 168.225 94.8) (end 170.825 94.8) (width 0.3) (layer "F.Cu") (net 3) (tstamp aec09e62-5c9a-451a-87fb-e6d9ab2461cc))
  (segment (start 183.6 90.6) (end 172.2 90.6) (width 0.5) (layer "F.Cu") (net 3) (tstamp b1330a2a-8ee3-491e-a4b3-d2d311ec8cc0))
  (segment (start 180.1 105.5) (end 180.1 106.1) (width 0.5) (layer "F.Cu") (net 3) (tstamp b58ec8f4-cba9-4d93-9c71-e3e3f399f0eb))
  (segment (start 171.79 95.96) (end 170.96 95.96) (width 0.3) (layer "F.Cu") (net 3) (tstamp b857114c-5702-4b66-8552-523a038b5c8f))
  (segment (start 179.5 104.4) (end 180.1 105) (width 0.5) (layer "F.Cu") (net 3) (tstamp bccad212-43b5-473c-a6a9-b193c873633c))
  (segment (start 188 95) (end 183.6 90.6) (width 0.5) (layer "F.Cu") (net 3) (tstamp bee62160-0243-43a0-a121-bab5dd81ba05))
  (segment (start 177.7 104.4) (end 179.5 104.4) (width 0.5) (layer "F.Cu") (net 3) (tstamp c3d6b7eb-e6c2-4934-9430-accc0fdf0b52))
  (segment (start 176.7 102.325) (end 176.7 103.4) (width 0.5) (layer "F.Cu") (net 3) (tstamp c77b952d-d009-4ede-850a-45cef9241957))
  (segment (start 167.2 89) (end 167.8 89) (width 0.5) (layer "F.Cu") (net 3) (tstamp d137c8ae-fa68-4e5b-bf6b-213e2c2f069d))
  (segment (start 171.975 96.595) (end 171.975 96.145) (width 0.3) (layer "F.Cu") (net 3) (tstamp d3845b23-9594-4f3a-bc34-a6f0e15ee4fc))
  (segment (start 170.96 95.96) (end 170.825 95.825) (width 0.3) (layer "F.Cu") (net 3) (tstamp e319148b-37bd-4dcc-b650-3bc1ced534ed))
  (segment (start 167.8 89) (end 170.8 92) (width 0.5) (layer "F.Cu") (net 3) (tstamp e5a5873c-5f0c-4d4e-98d8-b60535a88930))
  (segment (start 186 109) (end 188 107) (width 0.5) (layer "F.Cu") (net 3) (tstamp f65e698c-02ed-4463-bbb0-4c9ad1b03895))
  (segment (start 184.2875 109.0375) (end 184.25 109) (width 0.3) (layer "B.Cu") (net 3) (tstamp 1b32b853-7f33-4e0d-8b3e-1346e82c62ce))
  (segment (start 184.2875 108.9625) (end 184.25 109) (width 0.3) (layer "B.Cu") (net 3) (tstamp 4b41e856-5a9c-4c77-81e9-fe283a2c467e))
  (segment (start 184.2875 107.5) (end 184.2875 108.9625) (width 0.3) (layer "B.Cu") (net 3) (tstamp 50213ccc-5d79-4c15-8995-194f436f2831))
  (segment (start 184.2875 110.5) (end 184.2875 109.0375) (width 0.3) (layer "B.Cu") (net 3) (tstamp f459ae0f-3625-462b-939a-42601922b850))
  (segment (start 170.6 108.6) (end 170.6 109.2) (width 0.3) (layer "F.Cu") (net 4) (tstamp 53ec9bd3-6fdb-4660-b5ba-1b6b89e89abd))
  (segment (start 158.78995 108.2) (end 170.2 108.2) (width 0.3) (layer "F.Cu") (net 4) (tstamp c8e88aee-c08a-4f6a-af8b-26464257b8f9))
  (segment (start 170.2 108.2) (end 170.6 108.6) (width 0.3) (layer "F.Cu") (net 4) (tstamp fac2cf8e-1fd5-452c-a9ad-e0ce2f8d949e))
  (segment (start 170.6 110.1) (end 171.7 111.2) (width 0.3) (layer "B.Cu") (net 4) (tstamp 5e4c874c-0ca0-479b-a6d4-d323e0355c46))
  (segment (start 170.6 109.2) (end 170.6 110.1) (width 0.3) (layer "B.Cu") (net 4) (tstamp cec66086-67a4-4b39-b200-8d3ec7724e23))
  (segment (start 182.75 106.5) (end 182.75 105.575) (width 0.3) (layer "F.Cu") (net 5) (tstamp 07437e30-de40-419c-a9c7-5e9e943558b2))
  (segment (start 182.75 105.575) (end 182.675 105.5) (width 0.3) (layer "F.Cu") (net 5) (tstamp 8117deeb-1ae0-496e-a7f8-264d027446c3))
  (segment (start 182.7125 107.5) (end 182.7125 106.5375) (width 0.3) (layer "B.Cu") (net 5) (tstamp 5bdac9ca-2f2f-465a-b410-9bc71cb585b7))
  (segment (start 182.7125 106.5375) (end 182.75 106.5) (width 0.3) (layer "B.Cu") (net 5) (tstamp cfeb9cb9-f3e1-4ebb-9be0-2824a9f4bac2))
  (segment (start 175.875 96.575) (end 175.875 97.125) (width 0.3) (layer "F.Cu") (net 8) (tstamp 09248401-f518-4e3b-8e45-e93cf155babd))
  (segment (start 175.875 97.125) (end 177.25 98.5) (width 0.3) (layer "F.Cu") (net 8) (tstamp 5fb5f196-2f5d-4106-a7d4-92a7d249d4f8))
  (segment (start 175.625 96.325) (end 175.875 96.575) (width 0.3) (layer "F.Cu") (net 8) (tstamp 78c4d2b3-21af-4aaf-adcd-90df83840212))
  (segment (start 175 96.325) (end 175.625 96.325) (width 0.3) (layer "F.Cu") (net 8) (tstamp ba871ba7-a4af-445c-aa4c-ba8b6bb6f037))
  (segment (start 178.3 107.825) (end 178.3 108.8) (width 0.3) (layer "F.Cu") (net 9) (tstamp a586eacb-d0e5-4b95-825e-bd179ca46f24))
  (segment (start 172.525 108.875) (end 176.675 108.875) (width 0.3) (layer "B.Cu") (net 9) (tstamp 0ad228d1-b7f8-4b5d-ae12-aa54c1b4eeb0))
  (segment (start 176.675 108.875) (end 178.225 108.875) (width 0.3) (layer "B.Cu") (net 9) (tstamp 94b60368-49db-4889-b155-aeeef4c0a799))
  (segment (start 178.225 108.875) (end 178.3 108.8) (width 0.3) (layer "B.Cu") (net 9) (tstamp e28e980c-b1bd-42f5-a233-0df1963d47e5))
  (segment (start 182.7125 110.5) (end 182.7125 111.4625) (width 0.3) (layer "B.Cu") (net 10) (tstamp a7a1f3f2-5cb7-40bc-a51b-71356dce4ebd))
  (segment (start 158.08995 106.6) (end 171 106.6) (width 0.3) (layer "F.Cu") (net 11) (tstamp 55fb0025-65f7-4032-805e-8a1b7ced7852))
  (segment (start 172.525 106.725) (end 176.675 106.725) (width 0.3) (layer "B.Cu") (net 11) (tstamp 12e65464-77bf-4086-a2e0-024d7acb651d))
  (segment (start 171.1 106.7) (end 172.5 106.7) (width 0.3) (layer "B.Cu") (net 11) (tstamp 2e16601c-2e3b-498d-9364-28a337d0b40e))
  (segment (start 171 106.6) (end 171.1 106.7) (width 0.3) (layer "B.Cu") (net 11) (tstamp 35bc2913-3d9a-419c-ba40-847a57de7eb8))
  (segment (start 164 92) (end 164 93.3) (width 0.3) (layer "F.Cu") (net 12) (tstamp 35279739-ac09-4523-9e3d-3da6fbebbe5d))
  (segment (start 161.5 89.5) (end 164 92) (width 0.3) (layer "F.Cu") (net 12) (tstamp 74eef225-0876-4ca2-b933-38f6158c7c25))
  (segment (start 166.575 94.8) (end 166.575 95.515) (width 0.3) (layer "F.Cu") (net 12) (tstamp 868e5b13-6be3-43be-b3a2-59eb41c7bab9))
  (segment (start 166.575 95.515) (end 167.025 95.965) (width 0.3) (layer "F.Cu") (net 12) (tstamp 8a1262ca-706f-469c-aa15-84fc955c949a))
  (segment (start 164 93.3) (end 165.5 94.8) (width 0.3) (layer "F.Cu") (net 12) (tstamp bd52245e-9de8-4a61-b412-668af380e12a))
  (segment (start 165.5 94.8) (end 166.575 94.8) (width 0.3) (layer "F.Cu") (net 12) (tstamp be517472-61ce-4efe-bfb6-d7e850e7a6f3))
  (segment (start 167.025 95.965) (end 167.025 96.595) (width 0.3) (layer "F.Cu") (net 12) (tstamp e679b1b6-95ba-4208-b58d-d0e4f83da049))
  (segment (start 157.5 90.4) (end 164 96.9) (width 0.3) (layer "F.Cu") (net 13) (tstamp 6754b849-93da-40b4-b4e0-aad30ec27a4a))
  (segment (start 166 100.5) (end 166.93 100.5) (width 0.3) (layer "F.Cu") (net 13) (tstamp 9348263a-09a3-42c3-8f6b-98692b0ba020))
  (segment (start 164 96.9) (end 164 98.5) (width 0.3) (layer "F.Cu") (net 13) (tstamp a9026cbb-cd5d-4ac4-9ed0-590c58bc8020))
  (segment (start 164 98.5) (end 166 100.5) (width 0.3) (layer "F.Cu") (net 13) (tstamp eb52d1a5-5a0f-4446-8600-1b80c724c564))
  (segment (start 181.4625 102) (end 181.4625 101.0125) (width 0.3) (layer "F.Cu") (net 14) (tstamp 0a255ec4-4467-4fa2-a1d8-eee5fbb816a1))
  (segment (start 179.0625 100.2225) (end 179.0625 99.45) (width 0.3) (layer "F.Cu") (net 14) (tstamp 69852ba5-d238-475f-96f4-0789e9ac1795))
  (segment (start 180.95 100.5) (end 179.34 100.5) (width 0.3) (layer "F.Cu") (net 14) (tstamp 6e929d87-db4a-472e-9388-4d859d9b0e14))
  (segment (start 181.5005 99.487174) (end 181.463326 99.45) (width 0.3) (layer "F.Cu") (net 14) (tstamp 7bdb1093-9c10-4472-9e22-d257212a38a8))
  (segment (start 181.463326 99.45) (end 179.0625 99.45) (width 0.3) (layer "F.Cu") (net 14) (tstamp 8308e19d-6f1e-4c0c-b1f2-78d460aad72e))
  (segment (start 181.4625 101.0125) (end 180.95 100.5) (width 0.3) (layer "F.Cu") (net 14) (tstamp d0fc0b46-4bc4-480c-9775-4f6ad48efd9b))
  (segment (start 179.34 100.5) (end 179.0625 100.2225) (width 0.3) (layer "F.Cu") (net 14) (tstamp e75ebdbd-38cf-4ee3-aa33-a8c8e3cdacd6))
  (segment (start 182.2 97.2) (end 182.65 96.75) (width 0.3) (layer "B.Cu") (net 14) (tstamp 4733d9f1-cabc-469f-9f83-b20488b46f60))
  (segment (start 181.5005 99.487174) (end 182.2 98.787674) (width 0.3) (layer "B.Cu") (net 14) (tstamp a2a580ce-44e1-473f-a706-ff5c164f86a7))
  (segment (start 182.65 96.75) (end 184 96.75) (width 0.3) (layer "B.Cu") (net 14) (tstamp db8d436e-989d-4240-b19c-efa0948bf091))
  (segment (start 182.2 98.787674) (end 182.2 97.2) (width 0.3) (layer "B.Cu") (net 14) (tstamp f758f1e4-0ebd-4554-8173-080d99e8d9a0))
  (segment (start 179.0625 97.55) (end 181.85 97.55) (width 0.3) (layer "F.Cu") (net 15) (tstamp 5650b0a4-ca42-4f21-b4d3-c9cbf4fd600e))
  (segment (start 182.2 99.8) (end 182.2 97.9) (width 0.3) (layer "F.Cu") (net 15) (tstamp 585adb43-5357-4ec3-93a8-bae654208022))
  (segment (start 182.65 100.25) (end 182.2 99.8) (width 0.3) (layer "F.Cu") (net 15) (tstamp 5c1ede68-2dfd-47a6-a9f6-c6da938ecfe2))
  (segment (start 181.4625 96.2375) (end 181.2 96.5) (width 0.3) (layer "F.Cu") (net 15) (tstamp 8dfccdc4-cfa7-4cb1-9457-40f62dd1fae7))
  (segment (start 184 100.25) (end 182.65 100.25) (width 0.3) (layer "F.Cu") (net 15) (tstamp a3a462c4-e839-404a-b54b-f46dc7b2f272))
  (segment (start 179.34 96.5) (end 179.0625 96.7775) (width 0.3) (layer "F.Cu") (net 15) (tstamp aa0fd8c7-25c1-4e5c-89ef-5851cbb95808))
  (segment (start 181.4625 94.7) (end 181.4625 96.2375) (width 0.3) (layer "F.Cu") (net 15) (tstamp aa14ae7b-5db6-4b58-af15-e002331b75b0))
  (segment (start 181.2 96.5) (end 179.34 96.5) (width 0.3) (layer "F.Cu") (net 15) (tstamp c7ac1f4e-acf2-4ed6-8747-8f77c31b3744))
  (segment (start 182.2 97.9) (end 181.85 97.55) (width 0.3) (layer "F.Cu") (net 15) (tstamp cc35df3a-ab39-4dea-944a-be1b6ed60e10))
  (segment (start 179.0625 96.7775) (end 179.0625 97.55) (width 0.3) (layer "F.Cu") (net 15) (tstamp fd8b9f1e-b99c-4a37-9aa3-c27379d5e460))
  (segment (start 174.325 94.675) (end 175 94.675) (width 0.3) (layer "F.Cu") (net 16) (tstamp 1ac71e7f-1278-406f-a430-92f6304fe006))
  (segment (start 171.975 97.865) (end 173.135 97.865) (width 0.3) (layer "F.Cu") (net 16) (tstamp 1bf897a9-2de4-4c8c-8023-fa9e15eea06a))
  (segment (start 173.135 97.865) (end 173.7 97.3) (width 0.3) (layer "F.Cu") (net 16) (tstamp 923133c9-8b18-47fb-9f50-4e0121793654))
  (segment (start 175 94.675) (end 178.2125 94.675) (width 0.3) (layer "F.Cu") (net 16) (tstamp 9bac6529-9856-42f9-88ba-ece4b13ad192))
  (segment (start 173.7 95.3) (end 174.325 94.675) (width 0.3) (layer "F.Cu") (net 16) (tstamp 9e283541-3b58-4a7c-9bed-c753f28831ad))
  (segment (start 173.7 97.3) (end 173.7 95.3) (width 0.3) (layer "F.Cu") (net 16) (tstamp ac4210d2-2648-4276-a52c-a8b6b356789e))
  (segment (start 174 98.565) (end 174.065 98.5) (width 0.3) (layer "F.Cu") (net 17) (tstamp 05bb8afc-3c68-489b-a744-b358a88ec27a))
  (segment (start 173.065 99.135) (end 173.7 98.5) (width 0.3) (layer "F.Cu") (net 17) (tstamp 14f13366-563c-4154-a272-cc9f1baf4c6a))
  (segment (start 178.5375 100.9375) (end 178.5375 102) (width 0.3) (layer "F.Cu") (net 17) (tstamp 66452dc9-d143-46d9-8d41-41f16adc0c71))
  (segment (start 176.7 100.675) (end 178.275 100.675) (width 0.3) (layer "F.Cu") (net 17) (tstamp 6bfb3c34-d9ad-48ab-82df-7d706b8a0173))
  (segment (start 174.71 98.5) (end 174.71 100.11) (width 0.3) (layer "F.Cu") (net 17) (tstamp 70e73f70-04a7-4fe4-bbbd-94bc92b6dfb3))
  (segment (start 171.975 99.135) (end 173.065 99.135) (width 0.3) (layer "F.Cu") (net 17) (tstamp 899ec12a-c5b8-456e-bf21-b4c401d9c0e8))
  (segment (start 175.275 100.675) (end 176.7 100.675) (width 0.3) (layer "F.Cu") (net 17) (tstamp a8a44b2d-6da2-4133-a45d-5c3e5c7a6ad0))
  (segment (start 174.71 100.11) (end 175.275 100.675) (width 0.3) (layer "F.Cu") (net 17) (tstamp b7bea19c-f012-44f9-816a-f202209b6a88))
  (segment (start 173.7 98.5) (end 174.71 98.5) (width 0.3) (layer "F.Cu") (net 17) (tstamp c0173ee0-a304-4977-8043-d73aa3796245))
  (segment (start 178.275 100.675) (end 178.5375 100.9375) (width 0.3) (layer "F.Cu") (net 17) (tstamp d445610a-2e1d-4287-a960-a081e6970fdb))
  (segment (start 168.66 97.9) (end 168.66 99.1) (width 0.3) (layer "F.Cu") (net 18) (tstamp 282995e4-682a-4817-b725-6e32c31e13dd))
  (segment (start 167.025 99.135) (end 168.625 99.135) (width 0.3) (layer "F.Cu") (net 18) (tstamp 9b416dd9-42ed-473d-953b-65c0baca33bd))
  (segment (start 168.66 99.1) (end 168.66 102.175) (width 0.3) (layer "F.Cu") (net 18) (tstamp cb1209a7-9402-49ac-bba4-5846be3f6555))
  (segment (start 167.025 97.865) (end 168.625 97.865) (width 0.3) (layer "F.Cu") (net 18) (tstamp de7b0b08-13e7-42a7-822f-cdba9d9521ba))
  (segment (start 168.625 99.135) (end 168.66 99.1) (width 0.3) (layer "F.Cu") (net 18) (tstamp f854eeed-6024-4b36-a112-98360d6831c6))
  (segment (start 168.625 97.865) (end 168.66 97.9) (width 0.3) (layer "F.Cu") (net 18) (tstamp faa3b7a6-7e01-4d0c-a0a4-50bccb021910))

  (zone (net 3) (net_name "+3V3") (layer "F.Cu") (tstamp 7e3ff057-891f-4bc8-807c-50f0be7a64a6) (hatch edge 0.508)
    (priority 5)
    (connect_pads yes (clearance 0.3))
    (min_thickness 0.254) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.508) (thermal_bridge_width 0.508) (smoothing chamfer) (radius 0.2))
    (polygon
      (pts
        (xy 171.4 95.47)
        (xy 171.7 95.74)
        (xy 173.14 95.74)
        (xy 173.14 96.9)
        (xy 170.59 96.91)
        (xy 170.59 95.99)
        (xy 170.1 95.5)
        (xy 167.8 95.5)
        (xy 167.8 94)
        (xy 171.4 94)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 171.215931 94.020002)
        (xy 171.236905 94.036905)
        (xy 171.363095 94.163095)
        (xy 171.397121 94.225407)
        (xy 171.4 94.25219)
        (xy 171.4 95.47)
        (xy 171.548659 95.603793)
        (xy 171.548661 95.603795)
        (xy 171.551338 95.606205)
        (xy 171.551341 95.606207)
        (xy 171.7 95.74)
        (xy 172.88781 95.74)
        (xy 172.955931 95.760002)
        (xy 172.976905 95.776905)
        (xy 173.103095 95.903095)
        (xy 173.137121 95.965407)
        (xy 173.14 95.99219)
        (xy 173.14 96.647953)
        (xy 173.119998 96.716074)
        (xy 173.10327 96.736874)
        (xy 172.976731 96.86391)
        (xy 172.914485 96.898057)
        (xy 172.887955 96.900988)
        (xy 171.170005 96.907725)
        (xy 170.842332 96.909009)
        (xy 170.774134 96.889275)
        (xy 170.752918 96.872281)
        (xy 170.627079 96.746934)
        (xy 170.592932 96.684688)
        (xy 170.59 96.657664)
        (xy 170.59 96.189999)
        (xy 170.59 95.99)
        (xy 170.448579 95.848579)
        (xy 170.448576 95.848575)
        (xy 170.241424 95.641423)
        (xy 170.24142 95.64142)
        (xy 170.1 95.5)
        (xy 169.9 95.5)
        (xy 168.05219 95.5)
        (xy 167.984069 95.479998)
        (xy 167.963095 95.463095)
        (xy 167.836905 95.336905)
        (xy 167.802879 95.274593)
        (xy 167.8 95.24781)
        (xy 167.8 94.25219)
        (xy 167.820002 94.184069)
        (xy 167.836905 94.163095)
        (xy 167.963095 94.036905)
        (xy 168.025407 94.002879)
        (xy 168.05219 94)
        (xy 171.14781 94)
      )
    )
  )
  (zone (net 1) (net_name "GND") (layer "F.Cu") (tstamp 93ed6141-d0e6-4281-806c-6cdd736933b2) (name "outer_pour") (hatch edge 0.508)
    (priority 1)
    (connect_pads (clearance 0.508))
    (min_thickness 0.508) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 189 130)
        (xy 162.9 130)
        (xy 162.9 75)
        (xy 189 75)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 172.022577 106.269758)
        (xy 172.104656 106.324602)
        (xy 175.475398 109.695344)
        (xy 175.530242 109.777423)
        (xy 175.5495 109.874242)
        (xy 175.5495 113.614069)
        (xy 175.562916 113.681512)
        (xy 175.574499 113.739746)
        (xy 175.623533 113.858125)
        (xy 175.623536 113.85813)
        (xy 175.69472 113.964665)
        (xy 175.785334 114.055279)
        (xy 175.891869 114.126463)
        (xy 175.891874 114.126466)
        (xy 176.010253 114.1755)
        (xy 176.010254 114.1755)
        (xy 176.010256 114.175501)
        (xy 176.135931 114.2005)
        (xy 176.135934 114.2005)
        (xy 176.264066 114.2005)
        (xy 176.264069 114.2005)
        (xy 176.389744 114.175501)
        (xy 176.508127 114.126465)
        (xy 176.534174 114.109061)
        (xy 176.614665 114.055279)
        (xy 176.614665 114.055278)
        (xy 176.614669 114.055276)
        (xy 176.705276 113.964669)
        (xy 176.747621 113.901296)
        (xy 176.776463 113.85813)
        (xy 176.776466 113.858125)
        (xy 176.81077 113.775307)
        (xy 176.825501 113.739744)
        (xy 176.8505 113.614069)
        (xy 176.8505 113.174242)
        (xy 176.869758 113.077423)
        (xy 176.924602 112.995344)
        (xy 180.195344 109.724602)
        (xy 180.277423 109.669758)
        (xy 180.374242 109.6505)
        (xy 184.185931 109.6505)
        (xy 185.625758 109.6505)
        (xy 185.722577 109.669758)
        (xy 185.804656 109.724602)
        (xy 187.585334 111.505279)
        (xy 187.691869 111.576463)
        (xy 187.691874 111.576466)
        (xy 187.81025 111.625499)
        (xy 187.810253 111.6255)
        (xy 187.810254 111.6255)
        (xy 187.810256 111.625501)
        (xy 187.935931 111.650499)
        (xy 188.064069 111.650499)
        (xy 188.189744 111.625501)
        (xy 188.26105 111.595965)
        (xy 188.308125 111.576466)
        (xy 188.308125 111.576465)
        (xy 188.308127 111.576465)
        (xy 188.348311 111.549614)
        (xy 188.355941 111.544517)
        (xy 188.447143 111.50674)
        (xy 188.545859 111.50674)
        (xy 188.63706 111.544518)
        (xy 188.706862 111.61432)
        (xy 188.744639 111.705522)
        (xy 188.7495 111.754879)
        (xy 188.7495 129.4965)
        (xy 188.730242 129.593319)
        (xy 188.675398 129.675398)
        (xy 188.593319 129.730242)
        (xy 188.4965 129.7495)
        (xy 163.153 129.7495)
        (xy 163.056181 129.730242)
        (xy 162.974102 129.675398)
        (xy 162.919258 129.593319)
        (xy 162.9 129.4965)
        (xy 162.9 112.4)
        (xy 164.5 112.4)
        (xy 165.363053 111.680788)
        (xy 165.373683 111.694987)
        (xy 165.416288 111.784035)
        (xy 165.421572 111.88261)
        (xy 165.396572 111.961464)
        (xy 165.364354 112.024694)
        (xy 165.364354 112.024695)
        (xy 165.3495 112.118479)
        (xy 165.3495 113.681512)
        (xy 165.3495 113.681515)
        (xy 165.349501 113.681518)
        (xy 165.358723 113.739746)
        (xy 165.364354 113.775303)
        (xy 165.364355 113.775308)
        (xy 165.421952 113.888346)
        (xy 165.43136 113.901296)
        (xy 165.472686 113.990945)
        (xy 165.476559 114.089584)
        (xy 165.44239 114.182198)
        (xy 165.43136 114.198704)
        (xy 165.42195 114.211655)
        (xy 165.364355 114.324692)
        (xy 165.364354 114.324695)
        (xy 165.3495 114.418479)
        (xy 165.3495 115.981512)
        (xy 165.3495 115.981515)
        (xy 165.349501 115.981518)
        (xy 165.364354 116.075303)
        (xy 165.364355 116.075308)
        (xy 165.421949 116.18834)
        (xy 165.421953 116.188346)
        (xy 165.511653 116.278046)
        (xy 165.511661 116.278052)
        (xy 165.624692 116.335644)
        (xy 165.624696 116.335646)
        (xy 165.718481 116.3505)
        (xy 167.781518 116.350499)
        (xy 167.875304 116.335646)
        (xy 167.988342 116.27805)
        (xy 168.07805 116.188342)
        (xy 168.135646 116.075304)
        (xy 168.1505 115.981519)
        (xy 168.150499 114.831514)
        (xy 171.6495 114.831514)
        (xy 171.649501 114.831518)
        (xy 171.664354 114.925303)
        (xy 171.664355 114.925308)
        (xy 171.721949 115.03834)
        (xy 171.721953 115.038346)
        (xy 171.811653 115.128046)
        (xy 171.811661 115.128052)
        (xy 171.924692 115.185644)
        (xy 171.924696 115.185646)
        (xy 172.018481 115.2005)
        (xy 174.081518 115.200499)
        (xy 174.175304 115.185646)
        (xy 174.288342 115.12805)
        (xy 174.37805 115.038342)
        (xy 174.435646 114.925304)
        (xy 174.4505 114.831519)
        (xy 174.450499 110.968482)
        (xy 174.435646 110.874696)
        (xy 174.435644 110.874692)
        (xy 174.435644 110.874691)
        (xy 174.396392 110.797656)
        (xy 174.37805 110.761658)
        (xy 174.378048 110.761656)
        (xy 174.378046 110.761653)
        (xy 174.288346 110.671953)
        (xy 174.288338 110.671947)
        (xy 174.175307 110.614355)
        (xy 174.175304 110.614354)
        (xy 174.081519 110.5995)
        (xy 174.081518 110.5995)
        (xy 172.018488 110.5995)
        (xy 172.018484 110.5995)
        (xy 172.018482 110.599501)
        (xy 171.924696 110.614354)
        (xy 171.924691 110.614355)
        (xy 171.811659 110.671949)
        (xy 171.811653 110.671953)
        (xy 171.721953 110.761653)
        (xy 171.721947 110.761661)
        (xy 171.664355 110.874692)
        (xy 171.664354 110.874695)
        (xy 171.6495 110.968479)
        (xy 171.6495 114.831511)
        (xy 171.6495 114.831514)
        (xy 168.150499 114.831514)
        (xy 168.150499 114.418482)
        (xy 168.135646 114.324696)
        (xy 168.135644 114.324692)
        (xy 168.135644 114.324691)
        (xy 168.07805 114.211658)
        (xy 168.068642 114.198709)
        (xy 168.027314 114.109061)
        (xy 168.023439 114.010421)
        (xy 168.057606 113.917807)
        (xy 168.068642 113.901291)
        (xy 168.078045 113.888346)
        (xy 168.07805 113.888342)
        (xy 168.135646 113.775304)
        (xy 168.1505 113.681519)
        (xy 168.150499 112.118482)
        (xy 168.135646 112.024696)
        (xy 168.103428 111.961464)
        (xy 168.076632 111.866458)
        (xy 168.088234 111.768427)
        (xy 168.126317 111.694986)
        (xy 168.200443 111.595966)
        (xy 168.200447 111.595959)
        (xy 168.251494 111.459096)
        (xy 168.251496 111.459089)
        (xy 168.257999 111.398605)
        (xy 168.258 111.398587)
        (xy 168.258 110.854)
        (xy 167.8 110.854)
        (xy 167.8 110.346)
        (xy 168.258 110.346)
        (xy 168.258 109.801412)
        (xy 168.257999 109.801394)
        (xy 168.251496 109.74091)
        (xy 168.251494 109.740903)
        (xy 168.200447 109.60404)
        (xy 168.200444 109.604034)
        (xy 168.112905 109.487097)
        (xy 168.112902 109.487094)
        (xy 167.995965 109.399555)
        (xy 167.995959 109.399552)
        (xy 167.859096 109.348505)
        (xy 167.859089 109.348503)
        (xy 167.798605 109.342)
        (xy 167.004 109.342)
        (xy 167.004 109.8)
        (xy 166.496 109.8)
        (xy 166.496 109.342)
        (xy 165.701394 109.342)
        (xy 165.64091 109.348503)
        (xy 165.640903 109.348505)
        (xy 165.50404 109.399552)
        (xy 165.504034 109.399555)
        (xy 165.387097 109.487094)
        (xy 165.387094 109.487097)
        (xy 165.299555 109.604034)
        (xy 165.299552 109.60404)
        (xy 165.248505 109.740903)
        (xy 165.248503 109.74091)
        (xy 165.24215 109.8)
        (xy 164.5 109.8)
        (xy 162.9 111.3)
        (xy 162.9 109.0035)
        (xy 162.919258 108.906681)
        (xy 162.974102 108.824602)
        (xy 163.056181 108.769758)
        (xy 163.153 108.7505)
        (xy 169.7965 108.7505)
        (xy 169.893319 108.769758)
        (xy 169.975398 108.824602)
        (xy 170.030242 108.906681)
        (xy 170.0495 109.0035)
        (xy 170.0495 109.272475)
        (xy 170.068129 109.342)
        (xy 170.087016 109.412486)
        (xy 170.130093 109.487097)
        (xy 170.159491 109.538015)
        (xy 170.261985 109.640509)
        (xy 170.387515 109.712984)
        (xy 170.527525 109.7505)
        (xy 170.672475 109.7505)
        (xy 170.812485 109.712984)
        (xy 170.938015 109.640509)
        (xy 171.040509 109.538015)
        (xy 171.112984 109.412485)
        (xy 171.1505 109.272475)
        (xy 171.1505 108.527525)
        (xy 171.112984 108.387515)
        (xy 171.091036 108.3495)
        (xy 171.048254 108.275398)
        (xy 171.040511 108.261986)
        (xy 170.538012 107.759487)
        (xy 170.412489 107.687018)
        (xy 170.412487 107.687017)
        (xy 170.412485 107.687016)
        (xy 170.272475 107.6495)
        (xy 170.266425 107.647879)
        (xy 170.17789 107.604218)
        (xy 170.112802 107.529999)
        (xy 170.081071 107.436522)
        (xy 170.087528 107.338018)
        (xy 170.131189 107.249483)
        (xy 170.205408 107.184395)
        (xy 170.298885 107.152664)
        (xy 170.331907 107.1505)
        (xy 171.072475 107.1505)
        (xy 171.212485 107.112984)
        (xy 171.338015 107.040509)
        (xy 171.440509 106.938015)
        (xy 171.512984 106.812485)
        (xy 171.5505 106.672475)
        (xy 171.5505 106.527525)
        (xy 171.5505 106.510942)
        (xy 171.553362 106.510942)
        (xy 171.558139 106.438027)
        (xy 171.601797 106.34949)
        (xy 171.676013 106.2844)
        (xy 171.769489 106.252666)
        (xy 171.80252 106.2505)
        (xy 171.925758 106.2505)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 163.249819 96.955579)
        (xy 163.331898 97.010423)
        (xy 163.375398 97.053923)
        (xy 163.430242 97.136002)
        (xy 163.4495 97.232821)
        (xy 163.4495 98.572475)
        (xy 163.47411 98.664317)
        (xy 163.487016 98.712484)
        (xy 163.487018 98.712489)
        (xy 163.559487 98.838012)
        (xy 165.661987 100.940512)
        (xy 165.728408 100.978859)
        (xy 165.787515 101.012984)
        (xy 165.927525 101.0505)
        (xy 165.927526 101.0505)
        (xy 165.92753 101.0505)
        (xy 165.939279 101.052047)
        (xy 165.999069 101.067522)
        (xy 166.068436 101.094877)
        (xy 166.156898 101.1055)
        (xy 166.156904 101.1055)
        (xy 167.793877 101.1055)
        (xy 167.890696 101.124758)
        (xy 167.972775 101.179602)
        (xy 168.027619 101.261681)
        (xy 168.046877 101.3585)
        (xy 168.027619 101.455319)
        (xy 167.994596 101.512516)
        (xy 167.910463 101.622159)
        (xy 167.849956 101.768237)
        (xy 167.849954 101.768243)
        (xy 167.8345 101.885625)
        (xy 167.8345 102.514356)
        (xy 167.834501 102.514367)
        (xy 167.849955 102.631763)
        (xy 167.910462 102.777838)
        (xy 167.910463 102.77784)
        (xy 167.910464 102.777841)
        (xy 168.006718 102.903282)
        (xy 168.132159 102.999536)
        (xy 168.278238 103.060044)
        (xy 168.278241 103.060044)
        (xy 168.278243 103.060045)
        (xy 168.395625 103.075499)
        (xy 168.395631 103.075499)
        (xy 168.395639 103.0755)
        (xy 168.87436 103.075499)
        (xy 168.918223 103.069724)
        (xy 168.991763 103.060044)
        (xy 169.083877 103.021888)
        (xy 169.137841 102.999536)
        (xy 169.263282 102.903282)
        (xy 169.263285 102.903277)
        (xy 169.275008 102.891556)
        (xy 169.276685 102.893233)
        (xy 169.333498 102.843409)
        (xy 169.426975 102.811677)
        (xy 169.525479 102.818132)
        (xy 169.614015 102.861792)
        (xy 169.644385 102.892162)
        (xy 169.644992 102.891556)
        (xy 169.656717 102.903281)
        (xy 169.656718 102.903282)
        (xy 169.782159 102.999536)
        (xy 169.928238 103.060044)
        (xy 169.928241 103.060044)
        (xy 169.928243 103.060045)
        (xy 170.045625 103.075499)
        (xy 170.045631 103.075499)
        (xy 170.045639 103.0755)
        (xy 170.52436 103.075499)
        (xy 170.568223 103.069724)
        (xy 170.641763 103.060044)
        (xy 170.733877 103.021888)
        (xy 170.787841 102.999536)
        (xy 170.913282 102.903282)
        (xy 171.009536 102.777841)
        (xy 171.070044 102.631762)
        (xy 171.078918 102.56436)
        (xy 171.085499 102.514374)
        (xy 171.085499 102.514367)
        (xy 171.0855 102.514361)
        (xy 171.085499 101.88564)
        (xy 171.078923 101.835683)
        (xy 171.070044 101.768236)
        (xy 171.009538 101.622162)
        (xy 171.007889 101.620013)
        (xy 171.005498 101.615165)
        (xy 171.001246 101.6078)
        (xy 171.001728 101.607521)
        (xy 170.96423 101.531477)
        (xy 170.957775 101.432972)
        (xy 170.989507 101.339496)
        (xy 171.054596 101.265278)
        (xy 171.143132 101.221619)
        (xy 171.20861 101.212999)
        (xy 171.721 101.212999)
        (xy 171.721 100.659)
        (xy 172.229 100.659)
        (xy 172.229 101.212999)
        (xy 172.866454 101.212999)
        (xy 172.90375 101.210065)
        (xy 173.0634 101.163681)
        (xy 173.206497 101.079055)
        (xy 173.324055 100.961497)
        (xy 173.408681 100.8184)
        (xy 173.454992 100.659)
        (xy 172.229 100.659)
        (xy 171.721 100.659)
        (xy 171.721 100.404)
        (xy 171.740258 100.307181)
        (xy 171.795102 100.225102)
        (xy 171.877181 100.170258)
        (xy 171.974 100.151)
        (xy 173.454992 100.151)
        (xy 173.454992 100.150999)
        (xy 173.425898 100.050857)
        (xy 173.41738 99.95251)
        (xy 173.447146 99.858389)
        (xy 173.510664 99.782823)
        (xy 173.598266 99.737317)
        (xy 173.696613 99.728799)
        (xy 173.714994 99.732756)
        (xy 173.71503 99.732531)
        (xy 173.734695 99.735645)
        (xy 173.734696 99.735646)
        (xy 173.828481 99.7505)
        (xy 173.9065 99.750499)
        (xy 174.003316 99.769756)
        (xy 174.085396 99.824599)
        (xy 174.14024 99.906677)
        (xy 174.1595 100.003496)
        (xy 174.1595 100.182475)
        (xy 174.187646 100.287515)
        (xy 174.191606 100.302295)
        (xy 174.197015 100.322485)
        (xy 174.269487 100.448012)
        (xy 174.269488 100.448013)
        (xy 174.269489 100.448014)
        (xy 174.26949 100.448015)
        (xy 174.936986 101.11551)
        (xy 174.952734 101.124602)
        (xy 174.996698 101.149985)
        (xy 175.020421 101.163681)
        (xy 175.062515 101.187984)
        (xy 175.202525 101.2255)
        (xy 175.202526 101.2255)
        (xy 175.347474 101.2255)
        (xy 175.81414 101.2255)
        (xy 175.910959 101.244758)
        (xy 175.983531 101.293249)
        (xy 175.983573 101.293196)
        (xy 175.984395 101.293827)
        (xy 175.993038 101.299602)
        (xy 176.008444 101.315008)
        (xy 176.006767 101.316684)
        (xy 176.056598 101.373511)
        (xy 176.088324 101.46699)
        (xy 176.081863 101.565494)
        (xy 176.038197 101.654027)
        (xy 176.007838 101.684386)
        (xy 176.008444 101.684992)
        (xy 175.996718 101.696717)
        (xy 175.900463 101.822159)
        (xy 175.839956 101.968237)
        (xy 175.839954 101.968243)
        (xy 175.8245 102.085625)
        (xy 175.8245 102.564356)
        (xy 175.824501 102.564367)
        (xy 175.839955 102.681763)
        (xy 175.900462 102.827838)
        (xy 175.900462 102.827839)
        (xy 175.997218 102.953933)
        (xy 176.040879 103.042469)
        (xy 176.0495 103.10795)
        (xy 176.0495 103.464069)
        (xy 176.055951 103.4965)
        (xy 176.074499 103.589746)
        (xy 176.123535 103.708128)
        (xy 176.123536 103.70813)
        (xy 176.194719 103.814665)
        (xy 176.194723 103.814669)
        (xy 177.285331 104.905277)
        (xy 177.391874 104.976466)
        (xy 177.391876 104.976467)
        (xy 177.391878 104.976468)
        (xy 177.46122 105.005189)
        (xy 177.500687 105.021537)
        (xy 177.510256 105.025501)
        (xy 177.570697 105.037523)
        (xy 177.661895 105.0753)
        (xy 177.731697 105.145102)
        (xy 177.769474 105.236304)
        (xy 177.769474 105.335019)
        (xy 177.731696 105.426221)
        (xy 177.675353 105.486379)
        (xy 177.596717 105.546718)
        (xy 177.500463 105.672159)
        (xy 177.439956 105.818237)
        (xy 177.439954 105.818243)
        (xy 177.4245 105.935625)
        (xy 177.4245 106.414356)
        (xy 177.424501 106.414367)
        (xy 177.439955 106.531763)
        (xy 177.500462 106.677838)
        (xy 177.596718 106.803282)
        (xy 177.608444 106.815008)
        (xy 177.606767 106.816684)
        (xy 177.656598 106.873511)
        (xy 177.688324 106.96699)
        (xy 177.681863 107.065494)
        (xy 177.638197 107.154027)
        (xy 177.607838 107.184386)
        (xy 177.608444 107.184992)
        (xy 177.596718 107.196717)
        (xy 177.500463 107.322159)
        (xy 177.439956 107.468237)
        (xy 177.439954 107.468243)
        (xy 177.4245 107.585625)
        (xy 177.4245 108.064356)
        (xy 177.424501 108.064367)
        (xy 177.439955 108.181763)
        (xy 177.500462 108.327838)
        (xy 177.500463 108.32784)
        (xy 177.500464 108.327841)
        (xy 177.596718 108.453282)
        (xy 177.59672 108.453284)
        (xy 177.650515 108.494562)
        (xy 177.715604 108.56878)
        (xy 177.747335 108.662256)
        (xy 177.7495 108.695281)
        (xy 177.7495 108.872474)
        (xy 177.787016 109.012486)
        (xy 177.829076 109.085335)
        (xy 177.859491 109.138015)
        (xy 177.961985 109.240509)
        (xy 178.087515 109.312984)
        (xy 178.200185 109.343174)
        (xy 178.288721 109.386835)
        (xy 178.353809 109.461053)
        (xy 178.38554 109.55453)
        (xy 178.379083 109.653034)
        (xy 178.335422 109.74157)
        (xy 178.313602 109.766451)
        (xy 177.282398 110.797656)
        (xy 177.200319 110.852499)
        (xy 177.1035 110.871758)
        (xy 177.006681 110.8525)
        (xy 176.924602 110.797656)
        (xy 176.869759 110.715577)
        (xy 176.8505 110.618758)
        (xy 176.8505 109.435933)
        (xy 176.843264 109.399555)
        (xy 176.825501 109.310256)
        (xy 176.796611 109.240509)
        (xy 176.776467 109.191877)
        (xy 176.776465 109.191874)
        (xy 176.776465 109.191873)
        (xy 176.762642 109.171186)
        (xy 176.74882 109.150498)
        (xy 176.727122 109.118025)
        (xy 176.705277 109.085331)
        (xy 172.714669 105.094723)
        (xy 172.714665 105.094719)
        (xy 172.60813 105.023536)
        (xy 172.608128 105.023535)
        (xy 172.489746 104.974499)
        (xy 172.439474 104.964499)
        (xy 172.364069 104.9495)
        (xy 163.153 104.9495)
        (xy 163.056181 104.930242)
        (xy 162.974102 104.875398)
        (xy 162.919258 104.793319)
        (xy 162.9 104.6965)
        (xy 162.9 97.189321)
        (xy 162.919258 97.092502)
        (xy 162.974102 97.010423)
        (xy 163.056181 96.955579)
        (xy 163.153 96.936321)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 183.322577 91.269758)
        (xy 183.404656 91.324602)
        (xy 187.275398 95.195344)
        (xy 187.330242 95.277423)
        (xy 187.3495 95.374242)
        (xy 187.3495 106.625758)
        (xy 187.330242 106.722577)
        (xy 187.275398 106.804656)
        (xy 185.804656 108.275398)
        (xy 185.722577 108.330242)
        (xy 185.625758 108.3495)
        (xy 181.0035 108.3495)
        (xy 180.906681 108.330242)
        (xy 180.824602 108.275398)
        (xy 180.769758 108.193319)
        (xy 180.7505 108.0965)
        (xy 180.7505 105.814356)
        (xy 181.8745 105.814356)
        (xy 181.874501 105.814367)
        (xy 181.889955 105.931763)
        (xy 181.950462 106.077838)
        (xy 182.046718 106.203282)
        (xy 182.04672 106.203284)
        (xy 182.100515 106.244562)
        (xy 182.165604 106.31878)
        (xy 182.197335 106.412256)
        (xy 182.1995 106.445281)
        (xy 182.1995 106.572474)
        (xy 182.237016 106.712486)
        (xy 182.302265 106.8255)
        (xy 182.309491 106.838015)
        (xy 182.411985 106.940509)
        (xy 182.537515 107.012984)
        (xy 182.677525 107.0505)
        (xy 182.822475 107.0505)
        (xy 182.962485 107.012984)
        (xy 183.088015 106.940509)
        (xy 183.190509 106.838015)
        (xy 183.262984 106.712485)
        (xy 183.3005 106.572475)
        (xy 183.3005 106.552466)
        (xy 183.319758 106.455647)
        (xy 183.374602 106.373568)
        (xy 183.456681 106.318724)
        (xy 183.5535 106.299466)
        (xy 183.650319 106.318724)
        (xy 183.684387 106.335954)
        (xy 183.83229 106.425365)
        (xy 183.832292 106.425366)
        (xy 183.996439 106.476517)
        (xy 184.067782 106.483)
        (xy 184.070999 106.482999)
        (xy 184.071 106.482998)
        (xy 184.071 105.754)
        (xy 184.579 105.754)
        (xy 184.579 106.482999)
        (xy 184.582217 106.482999)
        (xy 184.582239 106.482997)
        (xy 184.653555 106.476518)
        (xy 184.817707 106.425366)
        (xy 184.964842 106.33642)
        (xy 185.08642 106.214842)
        (xy 185.175366 106.067707)
        (xy 185.226517 105.903561)
        (xy 185.232999 105.832229)
        (xy 185.233 105.83222)
        (xy 185.233 105.754)
        (xy 184.579 105.754)
        (xy 184.071 105.754)
        (xy 184.071 104.517)
        (xy 184.579 104.517)
        (xy 184.579 105.246)
        (xy 185.232999 105.246)
        (xy 185.232999 105.167783)
        (xy 185.232997 105.16776)
        (xy 185.226518 105.096444)
        (xy 185.175366 104.932292)
        (xy 185.08642 104.785157)
        (xy 184.964842 104.663579)
        (xy 184.817707 104.574633)
        (xy 184.653561 104.523482)
        (xy 184.582229 104.517)
        (xy 184.579 104.517)
        (xy 184.071 104.517)
        (xy 184.067778 104.517001)
        (xy 184.067762 104.517001)
        (xy 183.996444 104.523481)
        (xy 183.832292 104.574633)
        (xy 183.685157 104.663579)
        (xy 183.600912 104.747824)
        (xy 183.518833 104.802667)
        (xy 183.422014 104.821925)
        (xy 183.325195 104.802666)
        (xy 183.267999 104.769644)
        (xy 183.17784 104.700463)
        (xy 183.031762 104.639956)
        (xy 183.031756 104.639954)
        (xy 182.914366 104.6245)
        (xy 182.435643 104.6245)
        (xy 182.435632 104.624501)
        (xy 182.318236 104.639955)
        (xy 182.172161 104.700462)
        (xy 182.046718 104.796718)
        (xy 181.950463 104.922159)
        (xy 181.889956 105.068237)
        (xy 181.889954 105.068243)
        (xy 181.8745 105.185625)
        (xy 181.8745 105.814356)
        (xy 180.7505 105.814356)
        (xy 180.7505 104.935933)
        (xy 180.749368 104.930242)
        (xy 180.725501 104.810256)
        (xy 180.722357 104.802666)
        (xy 180.676466 104.691874)
        (xy 180.676463 104.691869)
        (xy 180.605279 104.585334)
        (xy 180.514669 104.494724)
        (xy 179.914668 103.894722)
        (xy 179.914665 103.894719)
        (xy 179.80813 103.823536)
        (xy 179.808128 103.823535)
        (xy 179.689746 103.774499)
        (xy 179.639474 103.764499)
        (xy 179.564069 103.7495)
        (xy 179.564066 103.7495)
        (xy 179.088731 103.7495)
        (xy 178.991912 103.730242)
        (xy 178.909833 103.675398)
        (xy 178.854989 103.593319)
        (xy 178.835731 103.4965)
        (xy 178.854989 103.399681)
        (xy 178.909833 103.317602)
        (xy 178.991912 103.262758)
        (xy 179.018141 103.253547)
        (xy 179.110398 103.226744)
        (xy 179.251865 103.143081)
        (xy 179.368081 103.026865)
        (xy 179.451744 102.885398)
        (xy 179.497598 102.727569)
        (xy 179.5005 102.690694)
        (xy 179.5005 101.309318)
        (xy 179.500663 101.313455)
        (xy 179.516097 101.215954)
        (xy 179.567668 101.13178)
        (xy 179.647526 101.073749)
        (xy 179.743512 101.050696)
        (xy 179.753467 101.0505)
        (xy 180.246533 101.0505)
        (xy 180.343352 101.069758)
        (xy 180.425431 101.124602)
        (xy 180.480275 101.206681)
        (xy 180.499533 101.3035)
        (xy 180.499337 101.313455)
        (xy 180.4995 101.309318)
        (xy 180.4995 102.690696)
        (xy 180.502401 102.727568)
        (xy 180.526838 102.811677)
        (xy 180.548256 102.885398)
        (xy 180.631919 103.026865)
        (xy 180.748135 103.143081)
        (xy 180.889602 103.226744)
        (xy 181.013562 103.262758)
        (xy 181.047431 103.272598)
        (xy 181.084304 103.2755)
        (xy 181.084306 103.2755)
        (xy 181.840696 103.2755)
        (xy 181.865277 103.273565)
        (xy 181.877569 103.272598)
        (xy 182.035398 103.226744)
        (xy 182.176865 103.143081)
        (xy 182.293081 103.026865)
        (xy 182.376744 102.885398)
        (xy 182.422598 102.727569)
        (xy 182.4255 102.690694)
        (xy 182.4255 101.556684)
        (xy 182.444758 101.459865)
        (xy 182.499602 101.377786)
        (xy 182.581681 101.322942)
        (xy 182.6785 101.303684)
        (xy 182.775319 101.322942)
        (xy 182.857398 101.377786)
        (xy 182.858127 101.378575)
        (xy 182.864775 101.385223)
        (xy 182.864776 101.385224)
        (xy 183.056341 101.548836)
        (xy 183.271141 101.680466)
        (xy 183.503889 101.776873)
        (xy 183.748852 101.835683)
        (xy 183.982921 101.854104)
        (xy 183.999999 101.855449)
        (xy 184 101.855449)
        (xy 184.000001 101.855449)
        (xy 184.015991 101.85419)
        (xy 184.251148 101.835683)
        (xy 184.496111 101.776873)
        (xy 184.728859 101.680466)
        (xy 184.943659 101.548836)
        (xy 185.135224 101.385224)
        (xy 185.298836 101.193659)
        (xy 185.430466 100.978859)
        (xy 185.526873 100.746111)
        (xy 185.585683 100.501148)
        (xy 185.605449 100.25)
        (xy 185.585683 99.998852)
        (xy 185.526873 99.753889)
        (xy 185.430466 99.521141)
        (xy 185.298836 99.306341)
        (xy 185.135224 99.114776)
        (xy 184.943659 98.951164)
        (xy 184.943655 98.951161)
        (xy 184.943653 98.95116)
        (xy 184.728342 98.819217)
        (xy 184.655852 98.752208)
        (xy 184.614524 98.66256)
        (xy 184.610649 98.563921)
        (xy 184.644816 98.471307)
        (xy 184.711825 98.398817)
        (xy 184.801473 98.357489)
        (xy 184.860532 98.350499)
        (xy 185.231518 98.350499)
        (xy 185.325304 98.335646)
        (xy 185.438342 98.27805)
        (xy 185.52805 98.188342)
        (xy 185.585646 98.075304)
        (xy 185.6005 97.981519)
        (xy 185.600499 95.518482)
        (xy 185.585646 95.424696)
        (xy 185.585644 95.424692)
        (xy 185.585644 95.424691)
        (xy 185.555808 95.366137)
        (xy 185.52805 95.311658)
        (xy 185.528048 95.311656)
        (xy 185.528046 95.311653)
        (xy 185.438346 95.221953)
        (xy 185.438338 95.221947)
        (xy 185.325307 95.164355)
        (xy 185.325304 95.164354)
        (xy 185.257443 95.153606)
        (xy 185.231519 95.1495)
        (xy 185.231518 95.1495)
        (xy 182.768488 95.1495)
        (xy 182.768476 95.149501)
        (xy 182.718071 95.157484)
        (xy 182.619432 95.153606)
        (xy 182.529785 95.112275)
        (xy 182.462778 95.039784)
        (xy 182.428614 94.947169)
        (xy 182.4255 94.907598)
        (xy 182.4255 94.309304)
        (xy 182.422598 94.272431)
        (xy 182.407992 94.222159)
        (xy 182.376744 94.114602)
        (xy 182.293081 93.973135)
        (xy 182.176865 93.856919)
        (xy 182.035398 93.773256)
        (xy 181.877568 93.727401)
        (xy 181.840696 93.7245)
        (xy 181.840694 93.7245)
        (xy 181.084306 93.7245)
        (xy 181.084304 93.7245)
        (xy 181.047431 93.727401)
        (xy 180.889601 93.773256)
        (xy 180.8896 93.773257)
        (xy 180.748134 93.856919)
        (xy 180.631919 93.973134)
        (xy 180.548257 94.1146)
        (xy 180.548256 94.114601)
        (xy 180.502401 94.272431)
        (xy 180.4995 94.309304)
        (xy 180.4995 95.690682)
        (xy 180.499337 95.686545)
        (xy 180.483903 95.784046)
        (xy 180.432332 95.86822)
        (xy 180.352474 95.926251)
        (xy 180.256488 95.949304)
        (xy 180.246533 95.9495)
        (xy 179.753467 95.9495)
        (xy 179.656648 95.930242)
        (xy 179.574569 95.875398)
        (xy 179.519725 95.793319)
        (xy 179.500467 95.6965)
        (xy 179.500663 95.686545)
        (xy 179.5005 95.690682)
        (xy 179.5005 94.309304)
        (xy 179.497598 94.272431)
        (xy 179.482992 94.222159)
        (xy 179.451744 94.114602)
        (xy 179.368081 93.973135)
        (xy 179.251865 93.856919)
        (xy 179.110398 93.773256)
        (xy 178.952568 93.727401)
        (xy 178.915696 93.7245)
        (xy 178.915694 93.7245)
        (xy 178.159306 93.7245)
        (xy 178.159304 93.7245)
        (xy 178.122431 93.727401)
        (xy 177.964601 93.773256)
        (xy 177.9646 93.773257)
        (xy 177.823134 93.856919)
        (xy 177.695664 93.98439)
        (xy 177.693698 93.982424)
        (xy 177.637529 94.03116)
        (xy 177.543886 94.062395)
        (xy 177.445417 94.055417)
        (xy 177.358176 94.012107)
        (xy 177.27784 93.950463)
        (xy 177.131762 93.889956)
        (xy 177.131756 93.889954)
        (xy 177.014366 93.8745)
        (xy 176.385643 93.8745)
        (xy 176.385632 93.874501)
        (xy 176.268236 93.889955)
        (xy 176.122161 93.950462)
        (xy 176.004016 94.041118)
        (xy 175.915481 94.084778)
        (xy 175.816976 94.091234)
        (xy 175.723499 94.059503)
        (xy 175.695984 94.041118)
        (xy 175.57784 93.950463)
        (xy 175.431762 93.889956)
        (xy 175.431756 93.889954)
        (xy 175.314366 93.8745)
        (xy 174.685643 93.8745)
        (xy 174.685632 93.874501)
        (xy 174.568236 93.889955)
        (xy 174.422161 93.950462)
        (xy 174.296717 94.046718)
        (xy 174.296715 94.04672)
        (xy 174.285303 94.061593)
        (xy 174.211084 94.12668)
        (xy 174.150071 94.151952)
        (xy 174.112519 94.162014)
        (xy 174.112511 94.162017)
        (xy 173.986985 94.234489)
        (xy 173.73672 94.484754)
        (xy 173.65464 94.539598)
        (xy 173.557822 94.558856)
        (xy 173.461003 94.539597)
        (xy 173.378924 94.484754)
        (xy 173.32408 94.402674)
        (xy 173.317664 94.385436)
        (xy 173.268848 94.238117)
        (xy 173.268844 94.238109)
        (xy 173.178893 94.092275)
        (xy 173.178887 94.092268)
        (xy 173.057731 93.971112)
        (xy 173.057724 93.971106)
        (xy 172.91189 93.881155)
        (xy 172.911884 93.881152)
        (xy 172.749235 93.827256)
        (xy 172.749237 93.827256)
        (xy 172.64885 93.817)
        (xy 172.629 93.817)
        (xy 172.629 94.801)
        (xy 172.609742 94.897819)
        (xy 172.554898 94.979898)
        (xy 172.472819 95.034742)
        (xy 172.376 95.054)
        (xy 172.374 95.054)
        (xy 172.277181 95.034742)
        (xy 172.195102 94.979898)
        (xy 172.140258 94.897819)
        (xy 172.121 94.801)
        (xy 172.121 93.817)
        (xy 172.101149 93.817)
        (xy 172.000763 93.827256)
        (xy 171.848421 93.877738)
        (xy 171.750459 93.889911)
        (xy 171.655295 93.86367)
        (xy 171.589942 93.816478)
        (xy 171.524602 93.751138)
        (xy 171.469758 93.669059)
        (xy 171.4505 93.57224)
        (xy 171.4505 92.374242)
        (xy 171.469758 92.277423)
        (xy 171.524602 92.195344)
        (xy 172.395344 91.324602)
        (xy 172.477423 91.269758)
        (xy 172.574242 91.2505)
        (xy 183.225758 91.2505)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 177.743077 96.090258)
        (xy 177.825156 96.145102)
        (xy 177.827156 96.147102)
        (xy 177.882 96.229181)
        (xy 177.901258 96.326)
        (xy 177.882 96.422819)
        (xy 177.827156 96.504898)
        (xy 177.745077 96.559742)
        (xy 177.648258 96.579)
        (xy 176.699 96.579)
        (xy 176.602181 96.559742)
        (xy 176.520102 96.504898)
        (xy 176.465258 96.422819)
        (xy 176.446 96.326)
        (xy 176.446 96.324)
        (xy 176.465258 96.227181)
        (xy 176.520102 96.145102)
        (xy 176.602181 96.090258)
        (xy 176.699 96.071)
        (xy 177.646258 96.071)
      )
    )
  )
  (zone (net 1) (net_name "GND") (layer "F.Cu") (tstamp 9d353b77-51cb-4af3-abd5-7b5d5dd78873) (hatch edge 0.508)
    (priority 5)
    (connect_pads yes (clearance 0.3))
    (min_thickness 0.254) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.508) (thermal_bridge_width 0.508) (smoothing fillet) (radius 0.2))
    (polygon
      (pts
        (xy 167.8 111.4)
        (xy 165.7 111.4)
        (xy 164.5 112.4)
        (xy 162.9 112.4)
        (xy 162.9 111.3)
        (xy 164.5 109.8)
        (xy 167.8 109.8)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 167.8 111.4)
        (xy 165.7 111.4)
        (xy 164.5 112.4)
        (xy 162.9 112.4)
        (xy 162.9 111.3)
        (xy 164.5 109.8)
        (xy 167.8 109.8)
      )
    )
  )
  (zone (net 0) (net_name "") (layer "B.SilkS") (tstamp 46ad1240-798c-4e4c-aa7b-61fb3df10f24) (name "interlocking_terminals") (hatch edge 0.508)
    (connect_pads (clearance 0))
    (min_thickness 0.254) (filled_areas_thickness no)
    (keepout (tracks allowed) (vias allowed) (pads allowed) (copperpour allowed) (footprints allowed))
    (fill (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 162.5 130.5)
        (xy 104.8 130.5)
        (xy 104.8 121.2)
        (xy 162.5 121.2)
      )
    )
  )
)
