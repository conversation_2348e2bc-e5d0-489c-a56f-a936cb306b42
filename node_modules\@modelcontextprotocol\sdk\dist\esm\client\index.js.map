{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/client/index.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,iBAAiB,EACjB,QAAQ,GAGT,MAAM,uBAAuB,CAAC;AAE/B,OAAO,EAEL,oBAAoB,EAOpB,oBAAoB,EACpB,iBAAiB,EAEjB,qBAAqB,EAErB,sBAAsB,EACtB,uBAAuB,EAEvB,uBAAuB,EAEvB,yBAAyB,EAEzB,iCAAiC,EAEjC,qBAAqB,EAIrB,wBAAwB,EAKxB,2BAA2B,EAG3B,SAAS,EACT,QAAQ,GACT,MAAM,aAAa,CAAC;AACrB,OAAO,GAAG,MAAM,KAAK,CAAC;AAUtB;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,MAAM,OAAO,MAIX,SAAQ,QAIT;IAQC;;OAEG;IACH,YACU,WAA2B,EACnC,OAAuB;;QAEvB,KAAK,CAAC,OAAO,CAAC,CAAC;QAHP,gBAAW,GAAX,WAAW,CAAgB;QAP7B,gCAA2B,GAAkC,IAAI,GAAG,EAAE,CAAC;QAW7E,IAAI,CAAC,aAAa,GAAG,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,mCAAI,EAAE,CAAC;QACjD,IAAI,CAAC,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;IACxB,CAAC;IAED;;;;OAIG;IACI,oBAAoB,CAAC,YAAgC;QAC1D,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CACb,4DAA4D,CAC7D,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,iBAAiB,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;IAC3E,CAAC;IAES,gBAAgB,CACxB,UAAoC,EACpC,MAAc;;QAEd,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,mBAAmB,0CAAG,UAAU,CAAC,CAAA,EAAE,CAAC;YAC5C,MAAM,IAAI,KAAK,CACb,2BAA2B,UAAU,kBAAkB,MAAM,GAAG,CACjE,CAAC;QACJ,CAAC;IACH,CAAC;IAEQ,KAAK,CAAC,OAAO,CAAC,SAAoB,EAAE,OAAwB;QACnE,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC/B,iFAAiF;QACjF,kDAAkD;QAClD,IAAI,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;YACtC,OAAO;QACT,CAAC;QACD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC/B;gBACE,MAAM,EAAE,YAAY;gBACpB,MAAM,EAAE;oBACN,eAAe,EAAE,uBAAuB;oBACxC,YAAY,EAAE,IAAI,CAAC,aAAa;oBAChC,UAAU,EAAE,IAAI,CAAC,WAAW;iBAC7B;aACF,EACD,sBAAsB,EACtB,OAAO,CACR,CAAC;YAEF,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,0CAA0C,MAAM,EAAE,CAAC,CAAC;YACtE,CAAC;YAED,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE,CAAC;gBAClE,MAAM,IAAI,KAAK,CACb,+CAA+C,MAAM,CAAC,eAAe,EAAE,CACxE,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,YAAY,CAAC;YAC/C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,UAAU,CAAC;YACxC,qFAAqF;YACrF,IAAI,SAAS,CAAC,kBAAkB,EAAE,CAAC;gBACjC,SAAS,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACvD,CAAC;YAED,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,YAAY,CAAC;YAEzC,MAAM,IAAI,CAAC,YAAY,CAAC;gBACtB,MAAM,EAAE,2BAA2B;aACpC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,sCAAsC;YACtC,KAAK,IAAI,CAAC,KAAK,EAAE,CAAC;YAClB,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,qBAAqB;QACnB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,eAAe;QACb,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAES,yBAAyB,CAAC,MAA0B;;QAC5D,QAAQ,MAAiC,EAAE,CAAC;YAC1C,KAAK,kBAAkB;gBACrB,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,mBAAmB,0CAAE,OAAO,CAAA,EAAE,CAAC;oBACvC,MAAM,IAAI,KAAK,CACb,iDAAiD,MAAM,GAAG,CAC3D,CAAC;gBACJ,CAAC;gBACD,MAAM;YAER,KAAK,aAAa,CAAC;YACnB,KAAK,cAAc;gBACjB,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,mBAAmB,0CAAE,OAAO,CAAA,EAAE,CAAC;oBACvC,MAAM,IAAI,KAAK,CACb,iDAAiD,MAAM,GAAG,CAC3D,CAAC;gBACJ,CAAC;gBACD,MAAM;YAER,KAAK,gBAAgB,CAAC;YACtB,KAAK,0BAA0B,CAAC;YAChC,KAAK,gBAAgB,CAAC;YACtB,KAAK,qBAAqB,CAAC;YAC3B,KAAK,uBAAuB;gBAC1B,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,mBAAmB,0CAAE,SAAS,CAAA,EAAE,CAAC;oBACzC,MAAM,IAAI,KAAK,CACb,mDAAmD,MAAM,GAAG,CAC7D,CAAC;gBACJ,CAAC;gBAED,IACE,MAAM,KAAK,qBAAqB;oBAChC,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,SAAS,EAC7C,CAAC;oBACD,MAAM,IAAI,KAAK,CACb,gEAAgE,MAAM,GAAG,CAC1E,CAAC;gBACJ,CAAC;gBAED,MAAM;YAER,KAAK,YAAY,CAAC;YAClB,KAAK,YAAY;gBACf,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,mBAAmB,0CAAE,KAAK,CAAA,EAAE,CAAC;oBACrC,MAAM,IAAI,KAAK,CACb,+CAA+C,MAAM,GAAG,CACzD,CAAC;gBACJ,CAAC;gBACD,MAAM;YAER,KAAK,qBAAqB;gBACxB,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,mBAAmB,0CAAE,WAAW,CAAA,EAAE,CAAC;oBAC3C,MAAM,IAAI,KAAK,CACb,qDAAqD,MAAM,GAAG,CAC/D,CAAC;gBACJ,CAAC;gBACD,MAAM;YAER,KAAK,YAAY;gBACf,iDAAiD;gBACjD,MAAM;YAER,KAAK,MAAM;gBACT,2CAA2C;gBAC3C,MAAM;QACV,CAAC;IACH,CAAC;IAES,4BAA4B,CACpC,MAA+B;;QAE/B,QAAQ,MAAsC,EAAE,CAAC;YAC/C,KAAK,kCAAkC;gBACrC,IAAI,CAAC,CAAA,MAAA,IAAI,CAAC,aAAa,CAAC,KAAK,0CAAE,WAAW,CAAA,EAAE,CAAC;oBAC3C,MAAM,IAAI,KAAK,CACb,0EAA0E,MAAM,GAAG,CACpF,CAAC;gBACJ,CAAC;gBACD,MAAM;YAER,KAAK,2BAA2B;gBAC9B,kDAAkD;gBAClD,MAAM;YAER,KAAK,yBAAyB;gBAC5B,gDAAgD;gBAChD,MAAM;YAER,KAAK,wBAAwB;gBAC3B,4CAA4C;gBAC5C,MAAM;QACV,CAAC;IACH,CAAC;IAES,8BAA8B,CAAC,MAAc;QACrD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,wBAAwB;gBAC3B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,CAAC;oBACjC,MAAM,IAAI,KAAK,CACb,6DAA6D,MAAM,GAAG,CACvE,CAAC;gBACJ,CAAC;gBACD,MAAM;YAER,KAAK,oBAAoB;gBACvB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;oBACpC,MAAM,IAAI,KAAK,CACb,gEAAgE,MAAM,GAAG,CAC1E,CAAC;gBACJ,CAAC;gBACD,MAAM;YAER,KAAK,YAAY;gBACf,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;oBAC9B,MAAM,IAAI,KAAK,CACb,0DAA0D,MAAM,GAAG,CACpE,CAAC;gBACJ,CAAC;gBACD,MAAM;YAER,KAAK,MAAM;gBACT,2CAA2C;gBAC3C,MAAM;QACV,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,OAAwB;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,MAAiC,EAAE,OAAwB;QACxE,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,EACzC,oBAAoB,EACpB,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,KAAmB,EAAE,OAAwB;QACjE,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,kBAAkB,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,EACjD,iBAAiB,EACjB,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,SAAS,CACb,MAAkC,EAClC,OAAwB;QAExB,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,EACjC,qBAAqB,EACrB,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CACf,MAAqC,EACrC,OAAwB;QAExB,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,EAClC,uBAAuB,EACvB,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CACjB,MAAuC,EACvC,OAAwB;QAExB,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,EACpC,yBAAyB,EACzB,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,MAA+C,EAC/C,OAAwB;QAExB,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,0BAA0B,EAAE,MAAM,EAAE,EAC9C,iCAAiC,EACjC,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,MAAqC,EACrC,OAAwB;QAExB,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,EACpC,wBAAwB,EACxB,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,MAAkC,EAClC,OAAwB;QAExB,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,qBAAqB,EAAE,MAAM,EAAE,EACzC,iBAAiB,EACjB,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB,CACvB,MAAoC,EACpC,OAAwB;QAExB,OAAO,IAAI,CAAC,OAAO,CACjB,EAAE,MAAM,EAAE,uBAAuB,EAAE,MAAM,EAAE,EAC3C,iBAAiB,EACjB,OAAO,CACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ,CACZ,MAAiC,EACjC,eAE+C,oBAAoB,EACnE,OAAwB;QAExB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC/B,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,EAChC,YAAY,EACZ,OAAO,CACR,CAAC;QAEF,wCAAwC;QACxC,MAAM,SAAS,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAC3D,IAAI,SAAS,EAAE,CAAC;YACd,oFAAoF;YACpF,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBACjD,MAAM,IAAI,QAAQ,CAChB,SAAS,CAAC,cAAc,EACxB,QAAQ,MAAM,CAAC,IAAI,6DAA6D,CACjF,CAAC;YACJ,CAAC;YAED,0EAA0E;YAC1E,IAAI,MAAM,CAAC,iBAAiB,EAAE,CAAC;gBAC7B,IAAI,CAAC;oBACH,kFAAkF;oBAClF,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;oBAEpD,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,MAAM,IAAI,QAAQ,CAChB,SAAS,CAAC,aAAa,EACvB,+DAA+D,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CACxG,CAAC;oBACJ,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,KAAK,YAAY,QAAQ,EAAE,CAAC;wBAC9B,MAAM,KAAK,CAAC;oBACd,CAAC;oBACD,MAAM,IAAI,QAAQ,CAChB,SAAS,CAAC,aAAa,EACvB,0CAA0C,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CACnG,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,sBAAsB,CAAC,KAAa;QAC1C,IAAI,CAAC,2BAA2B,CAAC,KAAK,EAAE,CAAC;QAEzC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,sEAAsE;YACtE,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBACtB,IAAI,CAAC;oBACH,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBACvD,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAC7D,CAAC;gBAAC,WAAM,CAAC;oBACP,mCAAmC;gBACrC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC7C,OAAO,IAAI,CAAC,2BAA2B,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,SAAS,CACb,MAAmC,EACnC,OAAwB;QAExB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAC/B,EAAE,MAAM,EAAE,YAAY,EAAE,MAAM,EAAE,EAChC,qBAAqB,EACrB,OAAO,CACR,CAAC;QAEF,iEAAiE;QACjE,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAE1C,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,oBAAoB;QACxB,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,MAAM,EAAE,kCAAkC,EAAE,CAAC,CAAC;IAC3E,CAAC;CACF"}