(kicad_sch
	(version 20250227)
	(generator "eeschema")
	(generator_version "9.99")
	(uuid "d9ed75ce-7084-4177-b32b-fceb2cf7aac3")
	(paper "A4")
	(lib_symbols
		(symbol "Device:C"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0.254)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "C"
				(at 0.635 2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "C"
				(at 0.635 -2.54 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0.9652 -3.81 0)
				(hide yes)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(hide yes)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Description" "Unpolarized capacitor"
				(at 0 0 0)
				(hide yes)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "ki_keywords" "cap capacitor"
				(at 0 0 0)
				(hide yes)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "ki_fp_filters" "C_*"
				(at 0 0 0)
				(hide yes)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(symbol "C_0_1"
				(polyline
					(pts
						(xy -2.032 0.762) (xy 2.032 0.762)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy -2.032 -0.762) (xy 2.032 -0.762)
					)
					(stroke
						(width 0.508)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "C_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 2.794)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 2.794)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
		(symbol "Device:R"
			(pin_numbers
				(hide yes)
			)
			(pin_names
				(offset 0)
			)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "R"
				(at 2.032 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Value" "R"
				(at 0 0 90)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at -1.778 0 90)
				(hide yes)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(hide yes)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Description" "Resistor"
				(at 0 0 0)
				(hide yes)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "ki_keywords" "R res resistor"
				(at 0 0 0)
				(hide yes)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "ki_fp_filters" "R_*"
				(at 0 0 0)
				(hide yes)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(symbol "R_0_1"
				(rectangle
					(start -1.016 -2.54)
					(end 1.016 2.54)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "R_1_1"
				(pin passive line
					(at 0 3.81 270)
					(length 1.27)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
				(pin passive line
					(at 0 -3.81 90)
					(length 1.27)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "2"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(embedded_fonts no)
		)
	)
	(rule_area
		(polyline
			(pts
				(xy 59.69 54.61) (xy 71.12 54.61) (xy 71.12 68.58) (xy 59.69 68.58)
			)
			(stroke
				(width 0)
				(type dash)
			)
			(fill
				(type none)
			)
			(uuid 60676821-919a-4f11-bd58-e337d82fbd8b)
		)
	)
	(rule_area
		(polyline
			(pts
				(xy 83.82 49.53) (xy 111.76 49.53) (xy 111.76 71.12) (xy 83.82 71.12)
			)
			(stroke
				(width 0)
				(type dash)
			)
			(fill
				(type none)
			)
			(uuid a23b373f-8385-48e5-92cc-856340ee73bd)
		)
	)
	(rule_area
		(polyline
			(pts
				(xy 55.88 49.53) (xy 81.28 49.53) (xy 81.28 71.12) (xy 55.88 71.12)
			)
			(stroke
				(width 0)
				(type dash)
			)
			(fill
				(type none)
			)
			(uuid bf519547-0f5f-46b4-89ee-2b48c33cd0fa)
		)
	)
	(rule_area
		(polyline
			(pts
				(xy 87.63 54.61) (xy 99.06 54.61) (xy 99.06 68.58) (xy 87.63 68.58)
			)
			(stroke
				(width 0)
				(type dash)
			)
			(fill
				(type none)
			)
			(uuid cbff79ad-6039-46f6-903e-1814abb160d6)
		)
	)
	(netclass_flag ""
		(length 2.54)
		(shape round)
		(at 60.96 54.61 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "95ce2569-329d-4346-bbd4-b51dfac2d0ae")
		(property "Net Class" ""
			(at -93.98 -5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Component Class" "CLASS_2"
			(at 61.6585 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
					(italic yes)
				)
				(justify left)
			)
		)
	)
	(netclass_flag ""
		(length 2.54)
		(shape round)
		(at 88.9 54.61 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "aee35f39-27b9-471a-86b7-d88f529d2361")
		(property "Net Class" ""
			(at -66.04 -5.08 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Component Class" "CLASS_4"
			(at 89.5985 52.07 0)
			(effects
				(font
					(size 1.27 1.27)
					(italic yes)
				)
				(justify left)
			)
		)
	)
	(netclass_flag ""
		(length 2.54)
		(shape round)
		(at 57.15 49.53 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "dbcaa6d2-dcb9-4a23-9e8c-5540859c5c95")
		(property "Net Class" ""
			(at -97.79 -10.16 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Component Class" "CLASS_1"
			(at 57.8485 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
					(italic yes)
				)
				(justify left)
			)
		)
	)
	(netclass_flag ""
		(length 2.54)
		(shape round)
		(at 85.09 49.53 0)
		(fields_autoplaced yes)
		(effects
			(font
				(size 1.27 1.27)
			)
			(justify left bottom)
		)
		(uuid "ea51b758-1c26-4244-8696-148d5cd9f58c")
		(property "Net Class" ""
			(at -69.85 -10.16 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Component Class" "CLASS_3"
			(at 85.7885 46.99 0)
			(effects
				(font
					(size 1.27 1.27)
					(italic yes)
				)
				(justify left)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 64.77 60.96 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "46977abc-c53a-4b10-a881-2435f24468fa")
		(property "Reference" "R1"
			(at 67.31 59.6899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "R"
			(at 67.31 62.2299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_0805_2012Metric"
			(at 62.992 60.96 90)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Datasheet" "~"
			(at 64.77 60.96 0)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Description" "Resistor"
			(at 64.77 60.96 0)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "2"
			(uuid "53eae49a-03b2-47c9-b978-836e854c42a1")
		)
		(pin "1"
			(uuid "2dc56f9e-ed1a-423c-9716-246a54c077eb")
		)
		(instances
			(project ""
				(path "/d9ed75ce-7084-4177-b32b-fceb2cf7aac3"
					(reference "R1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:R")
		(at 74.93 60.96 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "759e3836-df02-4bcc-823f-2488f4ccdc6a")
		(property "Reference" "R2"
			(at 77.47 59.6899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "R"
			(at 77.47 62.2299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Resistor_SMD:R_1206_3216Metric"
			(at 73.152 60.96 90)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Datasheet" "~"
			(at 74.93 60.96 0)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Description" "Resistor"
			(at 74.93 60.96 0)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "2"
			(uuid "213fa619-1218-4639-837f-def396924d49")
		)
		(pin "1"
			(uuid "3c26611e-5590-4cfc-9f38-c8d4c2888995")
		)
		(instances
			(project "dynamic_component_class"
				(path "/d9ed75ce-7084-4177-b32b-fceb2cf7aac3"
					(reference "R2")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C")
		(at 92.71 60.96 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "ad3cf5f9-5e65-46c9-bb23-770c50780b5d")
		(property "Reference" "C1"
			(at 96.52 59.6899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "C"
			(at 96.52 62.2299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0805_2012Metric"
			(at 93.6752 64.77 0)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Datasheet" "~"
			(at 92.71 60.96 0)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Description" "Unpolarized capacitor"
			(at 92.71 60.96 0)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "58054707-8b09-45ae-8013-e46b0d7e0c18")
		)
		(pin "2"
			(uuid "6718e73b-d736-47bd-ad02-995313219a5c")
		)
		(instances
			(project ""
				(path "/d9ed75ce-7084-4177-b32b-fceb2cf7aac3"
					(reference "C1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Device:C")
		(at 104.14 60.96 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "efa7a2fa-3528-4332-b199-9931f7e1b80b")
		(property "Reference" "C2"
			(at 107.95 59.6899 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "C"
			(at 107.95 62.2299 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0805_2012Metric"
			(at 105.1052 64.77 0)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Datasheet" "~"
			(at 104.14 60.96 0)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Description" "Unpolarized capacitor"
			(at 104.14 60.96 0)
			(hide yes)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(pin "1"
			(uuid "cb84c371-6a34-4196-b6f2-6452853b524b")
		)
		(pin "2"
			(uuid "24d51371-3fd2-4d66-9272-f054638174b7")
		)
		(instances
			(project "dynamic_component_class"
				(path "/d9ed75ce-7084-4177-b32b-fceb2cf7aac3"
					(reference "C2")
					(unit 1)
				)
			)
		)
	)
	(sheet
		(at 148.59 40.64)
		(size 46.99 41.91)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(stroke
			(width 0.1524)
			(type solid)
		)
		(fill
			(color 0 0 0 0.0000)
		)
		(uuid "e826c7ec-b0cb-4f53-915d-da3597387304")
		(property "Sheetname" "SHEET1"
			(at 148.59 39.9284 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left bottom)
			)
		)
		(property "Sheetfile" "component_classes_sheet1.kicad_sch"
			(at 148.59 83.1346 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left top)
			)
		)
		(instances
			(project "component_classes"
				(path "/d9ed75ce-7084-4177-b32b-fceb2cf7aac3"
					(page "2")
				)
			)
		)
	)
	(sheet_instances
		(path "/"
			(page "1")
		)
	)
	(embedded_fonts no)
)
