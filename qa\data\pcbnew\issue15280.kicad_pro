{"board": {"3dviewports": [], "design_settings": {"defaults": {"board_outline_line_width": 0.15, "copper_line_width": 0.5, "copper_text_italic": false, "copper_text_size_h": 1.5, "copper_text_size_v": 1.5, "copper_text_thickness": 0.3, "copper_text_upright": false, "courtyard_line_width": 0.049999999999999996, "dimension_precision": 4, "dimension_units": 3, "dimensions": {"arrow_length": 1270000, "extension_offset": 500000, "keep_text_aligned": true, "suppress_zeroes": false, "text_position": 0, "units_format": 1}, "fab_line_width": 0.09999999999999999, "fab_text_italic": false, "fab_text_size_h": 1.0, "fab_text_size_v": 1.0, "fab_text_thickness": 0.15, "fab_text_upright": false, "other_line_width": 0.09999999999999999, "other_text_italic": false, "other_text_size_h": 1.0, "other_text_size_v": 1.0, "other_text_thickness": 0.15, "other_text_upright": false, "pads": {"drill": 0.762, "height": 1.524, "width": 1.524}, "silk_line_width": 0.15, "silk_text_italic": false, "silk_text_size_h": 0.6, "silk_text_size_v": 0.6, "silk_text_thickness": 0.09999999999999999, "silk_text_upright": false, "zones": {"min_clearance": 0.5}}, "diff_pair_dimensions": [{"gap": 0.0, "via_gap": 0.0, "width": 0.0}], "drc_exclusions": [], "meta": {"version": 2}, "rule_severities": {"annular_width": "error", "clearance": "error", "connection_width": "warning", "copper_edge_clearance": "ignore", "copper_sliver": "warning", "courtyards_overlap": "error", "diff_pair_gap_out_of_range": "error", "diff_pair_uncoupled_length_too_long": "error", "drill_out_of_range": "error", "duplicate_footprints": "warning", "extra_footprint": "warning", "footprint": "error", "footprint_type_mismatch": "error", "hole_clearance": "error", "hole_near_hole": "error", "invalid_outline": "error", "isolated_copper": "warning", "item_on_disabled_layer": "error", "items_not_allowed": "error", "length_out_of_range": "error", "lib_footprint_issues": "warning", "lib_footprint_mismatch": "warning", "malformed_courtyard": "error", "microvia_drill_out_of_range": "error", "missing_courtyard": "ignore", "missing_footprint": "warning", "net_conflict": "warning", "npth_inside_courtyard": "ignore", "padstack": "error", "pth_inside_courtyard": "ignore", "shorting_items": "error", "silk_edge_clearance": "warning", "silk_over_copper": "ignore", "silk_overlap": "ignore", "skew_out_of_range": "error", "solder_mask_bridge": "error", "starved_thermal": "error", "text_height": "warning", "text_thickness": "warning", "through_hole_pad_without_hole": "error", "too_many_vias": "error", "track_dangling": "error", "track_width": "error", "tracks_crossing": "error", "unconnected_items": "error", "unresolved_variable": "error", "via_dangling": "warning", "zones_intersect": "error"}, "rules": {"max_error": 0.005, "min_clearance": 0.125, "min_connection": 0.15, "min_copper_edge_clearance": 0.39999999999999997, "min_hole_clearance": 0.25, "min_hole_to_hole": 0.25, "min_microvia_diameter": 0.19999999999999998, "min_microvia_drill": 0.09999999999999999, "min_resolved_spokes": 2, "min_silk_clearance": 0.0, "min_text_height": 0.5, "min_text_thickness": 0.09999999999999999, "min_through_hole_diameter": 0.15, "min_track_width": 0.125, "min_via_annular_width": 0.049999999999999996, "min_via_diameter": 0.5, "solder_mask_to_copper_clearance": 0.0, "use_height_for_length_calcs": true}, "teardrop_options": [{"td_onpadsmd": true, "td_onroundshapesonly": false, "td_ontrackend": false, "td_onviapad": true}], "teardrop_parameters": [{"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_round_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_rect_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_track_end", "td_width_to_size_filter_ratio": 0.9}], "track_widths": [0.0], "via_dimensions": [{"diameter": 0.0, "drill": 0.0}], "zones_allow_external_fillets": true}, "layer_presets": [], "viewports": []}, "boards": [], "cvpcb": {"equivalence_files": []}, "libraries": {"pinned_footprint_libs": [], "pinned_symbol_libs": []}, "meta": {"filename": "stepper.kicad_pro", "version": 1}, "net_settings": {"classes": [{"bus_width": 12, "clearance": 0.125, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "<PERSON><PERSON><PERSON>", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.2, "via_diameter": 0.5, "via_drill": 0.15, "wire_width": 6}, {"bus_width": 12, "clearance": 0.125, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "Coil", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.2, "via_diameter": 0.5, "via_drill": 0.15, "wire_width": 6}, {"bus_width": 12, "clearance": 0.125, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "Gate Drive", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.2, "via_diameter": 0.5, "via_drill": 0.15, "wire_width": 6}, {"bus_width": 12, "clearance": 0.125, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "High Speed", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.2, "via_diameter": 0.5, "via_drill": 0.15, "wire_width": 6}, {"bus_width": 12, "clearance": 0.125, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "SPI", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.2, "via_diameter": 0.5, "via_drill": 0.15, "wire_width": 6}, {"bus_width": 12, "clearance": 0.125, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "Sys Iso", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.2, "via_diameter": 0.5, "via_drill": 0.15, "wire_width": 6}, {"bus_width": 12, "clearance": 0.125, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "Temp", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.2, "via_diameter": 0.5, "via_drill": 0.15, "wire_width": 6}], "meta": {"version": 3}, "net_colors": null, "netclass_assignments": null, "netclass_patterns": [{"netclass": "Coil", "pattern": "/Motor1/CoilA1"}, {"netclass": "Coil", "pattern": "/Motor1/CoilA2"}, {"netclass": "Coil", "pattern": "/Motor1/CoilB1"}, {"netclass": "Coil", "pattern": "/Motor1/CoilB2"}, {"netclass": "Coil", "pattern": "/Motor2/CoilA1"}, {"netclass": "Coil", "pattern": "/Motor2/CoilA2"}, {"netclass": "Coil", "pattern": "/Motor2/CoilB1"}, {"netclass": "Coil", "pattern": "/Motor2/CoilB2"}, {"netclass": "Coil", "pattern": "/Motor3/CoilA1"}, {"netclass": "Coil", "pattern": "/Motor3/CoilA2"}, {"netclass": "Coil", "pattern": "/Motor3/CoilB1"}, {"netclass": "Coil", "pattern": "/Motor3/CoilB2"}, {"netclass": "Coil", "pattern": "/Motor4/CoilA1"}, {"netclass": "Coil", "pattern": "/Motor4/CoilA2"}, {"netclass": "Coil", "pattern": "/Motor4/CoilB1"}, {"netclass": "Coil", "pattern": "/Motor4/CoilB2"}, {"netclass": "Gate Drive", "pattern": "/Motor1/HA1"}, {"netclass": "Gate Drive", "pattern": "/Motor1/HA2"}, {"netclass": "Gate Drive", "pattern": "/Motor1/HB1"}, {"netclass": "Gate Drive", "pattern": "/Motor1/HB2"}, {"netclass": "Gate Drive", "pattern": "/Motor1/LA1"}, {"netclass": "Gate Drive", "pattern": "/Motor1/LA2"}, {"netclass": "Gate Drive", "pattern": "/Motor1/LB1"}, {"netclass": "Gate Drive", "pattern": "/Motor1/LB2"}, {"netclass": "Gate Drive", "pattern": "/Motor2/HA1"}, {"netclass": "Gate Drive", "pattern": "/Motor2/HA2"}, {"netclass": "Gate Drive", "pattern": "/Motor2/HB1"}, {"netclass": "Gate Drive", "pattern": "/Motor2/HB2"}, {"netclass": "Gate Drive", "pattern": "/Motor2/LA1"}, {"netclass": "Gate Drive", "pattern": "/Motor2/LA2"}, {"netclass": "Gate Drive", "pattern": "/Motor2/LB1"}, {"netclass": "Gate Drive", "pattern": "/Motor2/LB2"}, {"netclass": "Gate Drive", "pattern": "/Motor3/HA1"}, {"netclass": "Gate Drive", "pattern": "/Motor3/HA2"}, {"netclass": "Gate Drive", "pattern": "/Motor3/HB1"}, {"netclass": "Gate Drive", "pattern": "/Motor3/HB2"}, {"netclass": "Gate Drive", "pattern": "/Motor3/LA1"}, {"netclass": "Gate Drive", "pattern": "/Motor3/LA2"}, {"netclass": "Gate Drive", "pattern": "/Motor3/LB1"}, {"netclass": "Gate Drive", "pattern": "/Motor3/LB2"}, {"netclass": "Gate Drive", "pattern": "/Motor4/HA1"}, {"netclass": "Gate Drive", "pattern": "/Motor4/HA2"}, {"netclass": "Gate Drive", "pattern": "/Motor4/HB1"}, {"netclass": "Gate Drive", "pattern": "/Motor4/HB2"}, {"netclass": "Gate Drive", "pattern": "/Motor4/LA1"}, {"netclass": "Gate Drive", "pattern": "/Motor4/LA2"}, {"netclass": "Gate Drive", "pattern": "/Motor4/LB1"}, {"netclass": "Gate Drive", "pattern": "/Motor4/LB2"}, {"netclass": "High Speed", "pattern": "/Motor1_Step"}, {"netclass": "High Speed", "pattern": "/Motor2_Step"}, {"netclass": "High Speed", "pattern": "/Motor3_Step"}, {"netclass": "High Speed", "pattern": "/Motor4_Step"}, {"netclass": "High Speed", "pattern": "/MotorX_clk16MHz"}, {"netclass": "SPI", "pattern": "/MotorX_SCK"}, {"netclass": "SPI", "pattern": "/MotorX_SDI"}, {"netclass": "SPI", "pattern": "/MotorX_SDO"}, {"netclass": "Sys Iso", "pattern": "/Motor1_CSND"}, {"netclass": "Sys Iso", "pattern": "/Motor1_DirectionD"}, {"netclass": "Sys Iso", "pattern": "/Motor1_StepD"}, {"netclass": "Sys Iso", "pattern": "/Motor2_CSND"}, {"netclass": "Sys Iso", "pattern": "/Motor2_DirectionD"}, {"netclass": "Sys Iso", "pattern": "/Motor2_StepD"}, {"netclass": "Sys Iso", "pattern": "/Motor3_CSND"}, {"netclass": "Sys Iso", "pattern": "/Motor3_DirectionD"}, {"netclass": "Sys Iso", "pattern": "/Motor3_StepD"}, {"netclass": "Sys Iso", "pattern": "/Motor4_CSND"}, {"netclass": "Sys Iso", "pattern": "/Motor4_DirectionD"}, {"netclass": "Sys Iso", "pattern": "/Motor4_StepD"}, {"netclass": "Sys Iso", "pattern": "/MotorX_EnableD"}, {"netclass": "Sys Iso", "pattern": "/MotorX_WDGD"}, {"netclass": "Sys Iso", "pattern": "/SCLD"}, {"netclass": "Sys Iso", "pattern": "/SCLKD"}, {"netclass": "Sys Iso", "pattern": "/SDAD"}, {"netclass": "Sys Iso", "pattern": "/SDID"}, {"netclass": "Sys Iso", "pattern": "/SDOD"}, {"netclass": "Temp", "pattern": "/StepperTemp/TEMP1+"}, {"netclass": "Temp", "pattern": "/StepperTemp/TEMP1-"}, {"netclass": "Temp", "pattern": "/StepperTemp/TEMP2+"}, {"netclass": "Temp", "pattern": "/StepperTemp/TEMP2-"}, {"netclass": "Temp", "pattern": "/StepperTemp/TEMP3+"}, {"netclass": "Temp", "pattern": "/StepperTemp/TEMP3-"}, {"netclass": "Temp", "pattern": "/StepperTemp/TEMP4+"}, {"netclass": "Temp", "pattern": "/StepperTemp/TEMP4-"}]}, "pcbnew": {"last_paths": {"gencad": "", "idf": "", "netlist": "", "plot": "", "pos_files": "", "specctra_dsn": "", "step": "", "svg": "", "vrml": ""}, "page_layout_descr_file": ""}, "schematic": {"legacy_lib_dir": "", "legacy_lib_list": []}, "sheets": [], "text_variables": {}}