(kicad_pcb (version 20221018) (generator pcbnew)

  (general
    (thickness 1.462)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal "Top.Cu")
    (1 "In1.Cu" mixed "In1.Cu-GND")
    (2 "In2.Cu" signal "In2.Cu-POWER")
    (3 "In3.Cu" signal)
    (4 "In4.Cu" signal)
    (5 "In5.Cu" signal)
    (6 "In6.Cu" signal)
    (31 "B.Cu" signal)
    (32 "<PERSON><PERSON><PERSON>" user "B.Adhesive")
    (33 "<PERSON>.<PERSON>hes" user "T.Adhesive")
    (34 "B.Paste" user)
    (35 "<PERSON>.Paste" user "T.Paste")
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "T.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user "T.Mask")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "T.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user "T.Fab")
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (thickness 0.015))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "prepreg") (thickness 0.22) (material "FR4") (epsilon_r 3.9) (loss_tangent 0.02))
      (layer "In1.Cu" (type "copper") (thickness 0.017))
      (layer "dielectric 2" (type "core") (thickness 0.15) (material "FR4") (epsilon_r 3.9) (loss_tangent 0.02))
      (layer "In2.Cu" (type "copper") (thickness 0.017))
      (layer "dielectric 3" (type "prepreg") (thickness 0.15) (material "FR4") (epsilon_r 3.9) (loss_tangent 0.02))
      (layer "In3.Cu" (type "copper") (thickness 0.017))
      (layer "dielectric 4" (type "core") (thickness 0.22) (material "FR4") (epsilon_r 3.9) (loss_tangent 0.02))
      (layer "In4.Cu" (type "copper") (thickness 0.017))
      (layer "dielectric 5" (type "prepreg") (thickness 0.15) (material "FR4") (epsilon_r 3.9) (loss_tangent 0.02))
      (layer "In5.Cu" (type "copper") (thickness 0.017))
      (layer "dielectric 6" (type "prepreg") (thickness 0.15) (material "FR4") (epsilon_r 3.9) (loss_tangent 0.02))
      (layer "In6.Cu" (type "copper") (thickness 0.017))
      (layer "dielectric 7" (type "core") (thickness 0.22) (material "FR4") (epsilon_r 3.9) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (thickness 0.015))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "None")
      (dielectric_constraints yes)
    )
    (pad_to_mask_clearance 0)
    (aux_axis_origin 40 125)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 6)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "Net-(U1-CAP_VDD)")
  (net 2 "GND")
  (net 3 "Net-(C2-Pad1)")
  (net 4 "Net-(U1-PWDWN)")
  (net 5 "unconnected-(U1-X2)")
  (net 6 "/camera signal/SION")
  (net 7 "Net-(C4-Pad1)")
  (net 8 "/camera signal/SIOP")
  (net 9 "/camera signal/714_ERROR")
  (net 10 "Net-(U1-MFP3{slash}SDA)")
  (net 11 "unconnected-(R7-Pad1)")
  (net 12 "Net-(U1-MFP4{slash}SCL)")
  (net 13 "/camera signal/CFG0_1")
  (net 14 "/camera signal/CFG1_1")
  (net 15 "/camera signal/GW5_FSYNC_OUT")
  (net 16 "/camera signal/GW5_SENS_RES")
  (net 17 "/camera signal/~{RESET}")
  (net 18 "/camera signal/IN.0P")
  (net 19 "/camera signal/IN.0N")
  (net 20 "/camera signal/IN.1P")
  (net 21 "/camera signal/IN.1N")
  (net 22 "/camera signal/IN.2P")
  (net 23 "/camera signal/IN.2N")
  (net 24 "/camera signal/IN.CP")
  (net 25 "/camera signal/IN.CN")
  (net 26 "/camera signal/IN.3P")
  (net 27 "/camera signal/IN.3N")
  (net 28 "unconnected-(U1-MFP2)")
  (net 29 "/camera signal/Clk_25MHz")
  (net 30 "VDD_1V0")
  (net 31 "Net-(U1-XRES)")
  (net 32 "unconnected-(U1-MFP6{slash}LMN0)")
  (net 33 "unconnected-(U1-MFP7{slash}LMN1)")
  (net 34 "VDD_1V8")
  (net 35 "/camera signal/VDD_1V2")
  (net 36 "Net-(U1-MFP5{slash}VSYNC)")

  (footprint "Resistor_SMD:R_0402_1005Metric" (layer "F.Cu")
    (tstamp 2955c770-9924-43ac-ab84-e13dbbe41ef0)
    (at 45.212 101.219)
    (descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/355c181f-2b36-4cac-ab7d-7c4a55dfc5e1")
    (attr smd)
    (fp_text reference "R7" (at 0 -1.17) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp dede4d83-82d7-4058-9387-5c3032e91762)
    )
    (fp_text value "33R" (at 0 1.17) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 06dab8b4-bec4-43cf-9b6b-e4489d1574a9)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.26 0.26) (thickness 0.04)))
      (tstamp 3e577459-fb96-4f9d-8c66-867572ee48b5)
    )
    (fp_line (start -0.153641 -0.38) (end 0.153641 -0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 7af1a5fc-9e38-465d-8f28-bb988b786506))
    (fp_line (start -0.153641 0.38) (end 0.153641 0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp ea162c91-41de-4778-9d18-21b35ee6a393))
    (fp_line (start -0.93 -0.47) (end 0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 45b087b4-a3b0-4001-b725-635f6539bb5e))
    (fp_line (start -0.93 0.47) (end -0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b949243f-73e2-4773-8760-c2b9e9a77ddb))
    (fp_line (start 0.93 -0.47) (end 0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0010f6b7-fd3a-4d44-96dc-d3a7159df7d9))
    (fp_line (start 0.93 0.47) (end -0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b3a933c3-3e28-44f5-8a6c-1b3cf95002c6))
    (fp_line (start -0.525 -0.27) (end 0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f2fe4560-5e8f-476d-bdfc-bcf6660e5dad))
    (fp_line (start -0.525 0.27) (end -0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp daeeb0ce-8d9d-4056-81e1-32fed8a9c3a7))
    (fp_line (start 0.525 -0.27) (end 0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9571befe-fcae-472d-bb14-7b1c8fdf8659))
    (fp_line (start 0.525 0.27) (end -0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 45b4adef-fbf4-4cab-badb-e3a09972ed7f))
    (pad "1" smd roundrect (at -0.51 0) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 11 "unconnected-(R7-Pad1)") (pintype "passive") (tstamp 89c4e325-358c-4e7a-a205-74c20da710f2))
    (pad "2" smd roundrect (at 0.51 0) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 12 "Net-(U1-MFP4{slash}SCL)") (pintype "passive") (tstamp b76135f1-f216-432e-a6e8-8e5dd591b1c5))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp 5f5d1d28-bb5b-443a-9eb1-a7dfa5d89a80)
    (at 49.022 105.029 90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Unpolarized capacitor, small symbol")
    (property "ki_keywords" "capacitor cap")
    (property "part number" "12345")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/99b41402-92d4-4419-ac8c-051fb2780f65")
    (attr smd)
    (fp_text reference "C2" (at 0 -1.16 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 927cbad9-a23f-4e34-9de9-79404b38b9f8)
    )
    (fp_text value "100n" (at 0 1.16 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 55cda923-54d7-4df2-bc45-5e4a1b13316e)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 5204fddd-d6e0-4f26-bed7-efa1715a5d63)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2d683364-225f-4a72-87b9-78d7ceb5cc8f))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b96aa217-397d-4246-922f-8022676c5804))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 01475393-9867-4d90-be7e-81aac0573b69))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 1e65f5d4-ec68-4df0-a6ab-c458aed5b1bc))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 713e17bc-394e-49b0-9a44-96d4375f65e8))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp a3b7c855-26f9-4864-ab91-cc3d447b8395))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b959d7fe-025d-4c6e-b396-f8584d0c0bdf))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7d852d88-c1a0-47ed-b714-8c2f2e76c95c))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a9c6fbe5-55b5-4570-9c36-f2d223c54745))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 67f465f5-f9a1-422e-86c6-9995763b3221))
    (pad "1" smd roundrect (at -0.48 0 90) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "Net-(C2-Pad1)") (pintype "passive") (tstamp dbf26a12-7ab0-404e-8bca-8e59df13d423))
    (pad "2" smd roundrect (at 0.48 0 90) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 6 "/camera signal/SION") (pintype "passive") (tstamp e520e321-fe72-46d8-818f-adf6d7103418))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "F.Cu")
    (tstamp 7d687642-f69e-4c8c-9978-56dd37af8f9b)
    (at 50.165 105.029 90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Unpolarized capacitor, small symbol")
    (property "ki_keywords" "capacitor cap")
    (property "part number" "12345")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/dc336e70-816f-419e-99f2-1afeb5010417")
    (attr smd)
    (fp_text reference "C4" (at 0 -1.16 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ef6ab5e5-5d48-4b70-ac16-575669baf93d)
    )
    (fp_text value "100n" (at 0 1.16 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 6844c257-1760-4eb9-824c-f67311538697)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp adeac02b-802f-494d-9fed-99e09ce3447b)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 4c2610e0-78ae-4f4a-a1ba-3a744a358fdc))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 25e50d46-2f46-4602-8401-01d83b9c8b68))
    (fp_line (start -0.91 -0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 5f86cc52-3096-4aff-b706-a381d01ddd3e))
    (fp_line (start -0.91 0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e14339ea-13c3-4a8b-ac21-22a8e415b395))
    (fp_line (start 0.91 -0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3cf3b7bf-4d38-4ea1-90f2-10c72ac7950f))
    (fp_line (start 0.91 0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp cff90cfc-48c1-411a-b7da-7e3de51663b9))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 25641732-2531-44c4-823b-5679a15ce40c))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 29e7ae97-a143-45fd-bfe9-17659e0ebc33))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 18b0faa0-8659-4498-94c0-b9ac901ab8b1))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8a6f7178-66a0-43b6-86b6-d905551d6ca7))
    (pad "1" smd roundrect (at -0.48 0 90) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 7 "Net-(C4-Pad1)") (pintype "passive") (tstamp d8e9aaf1-205f-4266-8161-5798fb2cacf8))
    (pad "2" smd roundrect (at 0.48 0 90) (size 0.56 0.62) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 8 "/camera signal/SIOP") (pintype "passive") (tstamp 58f871ec-f5b8-4d1b-9b24-87912b8ce64d))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0402_1005Metric" (layer "F.Cu")
    (tstamp a88fbd7e-70ed-485f-8e39-c7fc04afd054)
    (at 46.99 95.377 -90)
    (descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/1e57400f-821e-4637-bda0-1c120efd2e9d")
    (attr smd)
    (fp_text reference "R9" (at 0 -1.17 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp c276ba09-87a5-4f5c-9d7b-3f2bbefd15fc)
    )
    (fp_text value "10k" (at 0 1.17 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 38f308fd-46c3-41aa-9f9b-d872c7bfe9de)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.26 0.26) (thickness 0.04)))
      (tstamp 1410a391-fca8-483f-956e-d86a795bd495)
    )
    (fp_line (start -0.153641 -0.38) (end 0.153641 -0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp e7284a77-e473-49f0-ae4f-3ba50970a767))
    (fp_line (start -0.153641 0.38) (end 0.153641 0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 197a1a9f-c7ef-4d39-a41f-2410bdcb6106))
    (fp_line (start -0.93 -0.47) (end 0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp c8bd849d-fa94-4cb7-ad5e-124bcd23a3db))
    (fp_line (start -0.93 0.47) (end -0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp ba00ab18-e36c-46e4-aba1-7031f1ac0284))
    (fp_line (start 0.93 -0.47) (end 0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e8895e7b-cbbe-47b8-bac4-1c4112f0adbd))
    (fp_line (start 0.93 0.47) (end -0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 4e1e1c32-dc43-4ff9-8d29-e4abb12542f5))
    (fp_line (start -0.525 -0.27) (end 0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9fea0a60-7daa-4fff-a612-633c7ffd93ba))
    (fp_line (start -0.525 0.27) (end -0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp fdde5376-7c8c-461a-bf10-bef8be04af8f))
    (fp_line (start 0.525 -0.27) (end 0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b705dab0-c588-4f64-8303-262a62cdc0e2))
    (fp_line (start 0.525 0.27) (end -0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8c306e59-41a0-4876-b4d3-4960bff68ffe))
    (pad "1" smd roundrect (at -0.51 0 270) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 17 "/camera signal/~{RESET}") (pintype "passive") (tstamp e62b2f09-5d30-4616-82d7-dbcdc30414c7))
    (pad "2" smd roundrect (at 0.51 0 270) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 4 "Net-(U1-PWDWN)") (pintype "passive") (tstamp 5eb32e24-e1b5-4af1-87d7-629a29146048))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Package_DFN_QFN:QFN-32-1EP_5x5mm_P0.5mm_EP3.65x3.65mm" (layer "F.Cu")
    (tstamp ac81a31c-4776-471e-9d89-fee4183e7da2)
    (at 49.5785 100.465)
    (descr "QFN, 32 Pin (https://www.exar.com/ds/mxl7704.pdf#page=35), generated with kicad-footprint-generator ipc_noLead_generator.py")
    (tags "QFN NoLead")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/97f666d5-c4b1-4644-8ce7-d1cffbb66547")
    (attr smd)
    (fp_text reference "U1" (at 0 -3.8) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 8c86da9f-8cc0-4e2a-b277-4adfac6b5284)
    )
    (fp_text value "MAX96714" (at 0 3.8) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 5012c3ee-f92e-4c9f-b839-e51b43399a0a)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp c2b4890c-7803-44e2-8286-48aee46732dd)
    )
    (fp_line (start -2.61 2.61) (end -2.61 2.135)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c87d5bc4-16be-44cc-876b-2600e8d403ab))
    (fp_line (start -2.135 -2.61) (end -2.61 -2.61)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f71be0e6-7a51-43ab-99b4-6d9485ece857))
    (fp_line (start -2.135 2.61) (end -2.61 2.61)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cb50b1cc-621d-44cb-9146-8f47a5f55b59))
    (fp_line (start 2.135 -2.61) (end 2.61 -2.61)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 0aa7ab0b-5486-4608-8387-bc8ab4bb3949))
    (fp_line (start 2.135 2.61) (end 2.61 2.61)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 97a75a7f-aa77-4809-bed7-f903a5a333d4))
    (fp_line (start 2.61 -2.61) (end 2.61 -2.135)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 8534b2f0-ac3b-4cf1-ae57-d7de76c15050))
    (fp_line (start 2.61 2.61) (end 2.61 2.135)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp f17135ed-2612-4c8d-a671-29822701463a))
    (fp_line (start -3.1 -3.1) (end -3.1 3.1)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 4727cd1c-a81a-4808-a929-0fab865146ac))
    (fp_line (start -3.1 3.1) (end 3.1 3.1)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 72f0e3f0-7466-490a-b2fb-ec941c43dc04))
    (fp_line (start 3.1 -3.1) (end -3.1 -3.1)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 916b6f2d-27a3-4e46-94f8-3b0e975be2c5))
    (fp_line (start 3.1 3.1) (end 3.1 -3.1)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6389ca4d-93f9-46e3-8a8b-03658ca7c53f))
    (fp_line (start -2.5 -1.5) (end -1.5 -2.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e638b5d1-9660-4042-ad6c-592cd9b666b0))
    (fp_line (start -2.5 2.5) (end -2.5 -1.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8f020a88-c4a6-4719-8408-7b264be98060))
    (fp_line (start -1.5 -2.5) (end 2.5 -2.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 93226ab1-5f23-400a-a558-c81cc58f22ee))
    (fp_line (start 2.5 -2.5) (end 2.5 2.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0d1eecbd-9231-40c0-9117-9ff118d6a943))
    (fp_line (start 2.5 2.5) (end -2.5 2.5)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1e124a54-2266-47eb-8a6a-e1995b08a907))
    (pad "" smd roundrect (at -1.22 -1.22) (size 0.98 0.98) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 54de157a-98d8-49b1-a603-cb2c4c0d32c5))
    (pad "" smd roundrect (at -1.22 0) (size 0.98 0.98) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 30590c3c-4f6d-4542-9012-f2ba73d33dbf))
    (pad "" smd roundrect (at -1.22 1.22) (size 0.98 0.98) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp c13738c1-1c5e-40e7-994b-abf18a531ffb))
    (pad "" smd roundrect (at 0 -1.22) (size 0.98 0.98) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp b07d4746-2f52-461a-9e03-5d440cfc98f0))
    (pad "" smd roundrect (at 0 0) (size 0.98 0.98) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 019cf41b-43d4-4bf8-9ad6-45dcc5a33920))
    (pad "" smd roundrect (at 0 1.22) (size 0.98 0.98) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 688e4bed-8db4-4f11-9573-856e985043b4))
    (pad "" smd roundrect (at 1.22 -1.22) (size 0.98 0.98) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp bd01bce6-4cfa-43a9-b7f8-c6917871574f))
    (pad "" smd roundrect (at 1.22 0) (size 0.98 0.98) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 28a8fc16-3690-4131-bf43-f55ba19a857e))
    (pad "" smd roundrect (at 1.22 1.22) (size 0.98 0.98) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 270f270c-ce73-4f77-9cb5-721ed062adde))
    (pad "1" smd roundrect (at -2.475 -1.75) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 16 "/camera signal/GW5_SENS_RES") (pinfunction "MFP0") (pintype "input") (tstamp 6c07aac6-be09-475a-86fb-f08b74fd7bd7))
    (pad "2" smd roundrect (at -2.475 -1.25) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 15 "/camera signal/GW5_FSYNC_OUT") (pinfunction "MFP1") (pintype "input") (tstamp 68d42dba-b11a-4187-ac76-b2952559387b))
    (pad "3" smd roundrect (at -2.475 -0.75) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 28 "unconnected-(U1-MFP2)") (pinfunction "MFP2") (pintype "input+no_connect") (tstamp 3d2421a8-1c2a-4c4e-8430-0af74307f1de))
    (pad "4" smd roundrect (at -2.475 -0.25) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 10 "Net-(U1-MFP3{slash}SDA)") (pinfunction "MFP3/SDA") (pintype "input") (tstamp 529bcfb9-31be-4db3-8b5c-56704afa69e9))
    (pad "5" smd roundrect (at -2.475 0.25) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 13 "/camera signal/CFG0_1") (pinfunction "CFG0") (pintype "input") (tstamp b9802e2f-39e0-4449-9807-fb0313f68e3f))
    (pad "6" smd roundrect (at -2.475 0.75) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 12 "Net-(U1-MFP4{slash}SCL)") (pinfunction "MFP4/SCL") (pintype "input") (tstamp 709b986f-8120-4cda-8b4d-d865145254e5))
    (pad "7" smd roundrect (at -2.475 1.25) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 36 "Net-(U1-MFP5{slash}VSYNC)") (pinfunction "MFP5/VSYNC") (pintype "input") (tstamp cba6aa23-cef7-4a20-ad04-d8d844e66ad4))
    (pad "8" smd roundrect (at -2.475 1.75) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 29 "/camera signal/Clk_25MHz") (pinfunction "X1/OSC") (pintype "input") (tstamp d32cd069-3c5b-4300-a5cd-f89b0e47da0a))
    (pad "9" smd roundrect (at -1.75 2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "unconnected-(U1-X2)") (pinfunction "X2") (pintype "input+no_connect") (tstamp b76c08af-33ef-4300-81df-cb8391c69ec9))
    (pad "10" smd roundrect (at -1.25 2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 31 "Net-(U1-XRES)") (pinfunction "XRES") (pintype "input") (tstamp 0ff77f79-888d-4bb1-93c4-daf46d81f63b))
    (pad "11" smd roundrect (at -0.75 2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 34 "VDD_1V8") (pinfunction "VDD18") (pintype "input") (tstamp 61d125a0-0ad7-47a3-8d0c-4e827f970bc2))
    (pad "12" smd roundrect (at -0.25 2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 6 "/camera signal/SION") (pinfunction "SION") (pintype "input") (tstamp 200c765d-1461-4584-9384-853ebcbe7e86))
    (pad "13" smd roundrect (at 0.25 2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 8 "/camera signal/SIOP") (pinfunction "SIOP") (pintype "input") (tstamp 8ccefa16-21fd-4ad0-b505-98fcb8819b81))
    (pad "14" smd roundrect (at 0.75 2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "Net-(U1-CAP_VDD)") (pinfunction "CAP_VDD") (pintype "input") (tstamp c9093f80-1907-405e-ac52-dd29a0e0ffd9))
    (pad "15" smd roundrect (at 1.25 2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 30 "VDD_1V0") (pinfunction "VDD") (pintype "input") (tstamp 077493ad-084b-48eb-866f-bd5a4c6057df))
    (pad "16" smd roundrect (at 1.75 2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 14 "/camera signal/CFG1_1") (pinfunction "CFG1") (pintype "input") (tstamp 03ad4e7c-c206-4602-afae-2ec76255493b))
    (pad "17" smd roundrect (at 2.475 1.75) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 34 "VDD_1V8") (pinfunction "VDDIO") (pintype "input") (tstamp 364fb68e-aab6-485d-8f3b-5072094f3f36))
    (pad "18" smd roundrect (at 2.475 1.25) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 32 "unconnected-(U1-MFP6{slash}LMN0)") (pinfunction "MFP6/LMN0") (pintype "input+no_connect") (tstamp 4dd4214e-8d71-4b0d-8c7b-bef93821bdf6))
    (pad "19" smd roundrect (at 2.475 0.75) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 33 "unconnected-(U1-MFP7{slash}LMN1)") (pinfunction "MFP7/LMN1") (pintype "input+no_connect") (tstamp c55f06a5-9a40-4df6-96a7-8d9964367cd8))
    (pad "20" smd roundrect (at 2.475 0.25) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 35 "/camera signal/VDD_1V2") (pinfunction "VTERM") (pintype "input") (tstamp 2d76238b-b40d-43d2-9bb4-7565cf48d210))
    (pad "21" smd roundrect (at 2.475 -0.25) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 18 "/camera signal/IN.0P") (pinfunction "D0P") (pintype "input") (tstamp 26e459f8-fe1d-4c0a-adf5-802a3e3ee357))
    (pad "22" smd roundrect (at 2.475 -0.75) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 19 "/camera signal/IN.0N") (pinfunction "D0N") (pintype "input") (tstamp 41742929-acb6-47f6-84cb-1d61bf1832cb))
    (pad "23" smd roundrect (at 2.475 -1.25) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 20 "/camera signal/IN.1P") (pinfunction "D1P") (pintype "input") (tstamp c9031b78-e300-460b-b6ff-0305f54b3588))
    (pad "24" smd roundrect (at 2.475 -1.75) (size 0.75 0.25) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 21 "/camera signal/IN.1N") (pinfunction "D1N") (pintype "input") (tstamp adfadd71-2073-4272-aded-378321c66c53))
    (pad "25" smd roundrect (at 1.75 -2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 22 "/camera signal/IN.2P") (pinfunction "D2P") (pintype "input") (tstamp 05090bc3-fd01-441b-b8b2-35680b8b7478))
    (pad "26" smd roundrect (at 1.25 -2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 23 "/camera signal/IN.2N") (pinfunction "D2N") (pintype "input") (tstamp 21e148b2-7981-4370-9a53-942c6d243d1f))
    (pad "27" smd roundrect (at 0.75 -2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 24 "/camera signal/IN.CP") (pinfunction "CKP") (pintype "input") (tstamp 163789f7-07f1-4710-8d5b-a1fcf9e75440))
    (pad "28" smd roundrect (at 0.25 -2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 25 "/camera signal/IN.CN") (pinfunction "CKN") (pintype "input") (tstamp 6a723ce1-831d-4ded-8cc2-179199ea313c))
    (pad "29" smd roundrect (at -0.25 -2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 26 "/camera signal/IN.3P") (pinfunction "D3P") (pintype "input") (tstamp 8d11dc4e-4358-434d-895b-f29aca0b2e21))
    (pad "30" smd roundrect (at -0.75 -2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 27 "/camera signal/IN.3N") (pinfunction "D3N") (pintype "input") (tstamp 30813684-23e9-4669-8d81-2858d50cf760))
    (pad "31" smd roundrect (at -1.25 -2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 4 "Net-(U1-PWDWN)") (pinfunction "PWDWN") (pintype "input") (tstamp 603d225a-47ca-440c-a752-e6cc92741021))
    (pad "32" smd roundrect (at -1.75 -2.475) (size 0.25 0.75) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 9 "/camera signal/714_ERROR") (pinfunction "MFP8/Lock") (pintype "input") (tstamp 97a83e2f-8e4a-45a3-8566-282313f1214e))
    (pad "33" smd rect (at 0 0) (size 3.65 3.65) (layers "F.Cu" "F.Mask")
      (net 2 "GND") (pinfunction "EPAD") (pintype "input") (tstamp 986981aa-19ea-4750-bda6-4bdc44bb35a1))
    (model "${KICAD6_3DMODEL_DIR}/Package_DFN_QFN.3dshapes/QFN-32-1EP_5x5mm_P0.5mm_EP3.65x3.65mm.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0402_1005Metric" (layer "F.Cu")
    (tstamp bb01bce3-87c1-4a88-a9df-7a42d744be06)
    (at 50.673 106.934)
    (descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/e72c69c7-f919-49f0-b966-6d15b3bd245f")
    (attr smd)
    (fp_text reference "R1" (at 0 -1.17) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 4f485047-ccc0-42a8-9eae-649f5af8200f)
    )
    (fp_text value "49,9R" (at 0 1.17) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b8e0f71d-bb6a-4cb7-a750-ab09b9bfef62)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
        (effects (font (size 0.26 0.26) (thickness 0.04)))
      (tstamp c564adde-9efa-4414-861b-6c93b0a0bf2f)
    )
    (fp_line (start -0.153641 -0.38) (end 0.153641 -0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 90f945ce-f08d-4841-908a-37e0329a9ee3))
    (fp_line (start -0.153641 0.38) (end 0.153641 0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 6892f8db-257a-40ec-9009-df353aa1955d))
    (fp_line (start -0.93 -0.47) (end 0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 00b39300-7acc-4b5c-976e-367eabfa5b82))
    (fp_line (start -0.93 0.47) (end -0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3b7e9186-d451-49c2-87ed-2a913855f2a7))
    (fp_line (start 0.93 -0.47) (end 0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp a8845996-c16b-4dc7-b858-1631ce2bcb08))
    (fp_line (start 0.93 0.47) (end -0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 8e5ad135-f01d-45af-8016-574afaf8d73f))
    (fp_line (start -0.525 -0.27) (end 0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a79ee1c9-8f53-45e8-b8af-9b9ded867d28))
    (fp_line (start -0.525 0.27) (end -0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bcffe6f3-f7ee-497a-bed4-fca9f2adbe0d))
    (fp_line (start 0.525 -0.27) (end 0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7325a293-486f-41b5-bd95-88bfb84b06c7))
    (fp_line (start 0.525 0.27) (end -0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c4cfc504-5ee1-45f0-976e-bf03c014d9dd))
    (pad "1" smd roundrect (at -0.51 0) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 7 "Net-(C4-Pad1)") (pintype "passive") (tstamp 03c299a0-a26a-4caf-af3e-c48d2b1e3eea))
    (pad "2" smd roundrect (at 0.51 0) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "GND") (pintype "passive") (tstamp 580b9853-11a2-4784-91cb-e46e10e76ee8))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0402_1005Metric" (layer "F.Cu")
    (tstamp cb422f8e-a5a5-4805-9f28-e9cacbd6a62a)
    (at 47.244 104.648 90)
    (descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Resistor")
    (property "ki_keywords" "R res resistor")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/4929cb90-2990-4893-806f-662d0f6a6550")
    (attr smd)
    (fp_text reference "R17" (at 0 -1.17 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 24427cd6-629e-4988-a02b-a2448b97e762)
    )
    (fp_text value "402R" (at 0 1.17 90) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 91762b6d-6622-4014-9a96-44efd2369db2)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab")
        (effects (font (size 0.26 0.26) (thickness 0.04)))
      (tstamp 9202d062-6c0e-4348-9f73-a63adbab237b)
    )
    (fp_line (start -0.153641 -0.38) (end 0.153641 -0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp aacaf81f-018c-417d-b9cf-6ba566df8411))
    (fp_line (start -0.153641 0.38) (end 0.153641 0.38)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp b9a4ddda-4c87-444b-a92c-1a0dab563ca0))
    (fp_line (start -0.93 -0.47) (end 0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 71b7d5fc-a1c5-4a67-a7ae-878de0410741))
    (fp_line (start -0.93 0.47) (end -0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 6c3280b4-976e-4695-b1b0-b621bfb13a22))
    (fp_line (start 0.93 -0.47) (end 0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 94368592-6358-4bfe-9a37-5f34f053b845))
    (fp_line (start 0.93 0.47) (end -0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp fdd6cfad-0c55-4ea8-8d64-d1219b4c2e32))
    (fp_line (start -0.525 -0.27) (end 0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7c3cb00c-6e05-4776-bf41-75c02a74ef9c))
    (fp_line (start -0.525 0.27) (end -0.525 -0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2b8dccc7-eec0-4615-afdb-3928b3a28d99))
    (fp_line (start 0.525 -0.27) (end 0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 23077092-db4f-4808-8610-6579f3528926))
    (fp_line (start 0.525 0.27) (end -0.525 0.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0ae07f00-7045-4d85-93d4-72f10472df79))
    (pad "1" smd roundrect (at -0.51 0 90) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "GND") (pintype "passive") (tstamp 4cb875f7-f5a5-4134-8399-f5af3d25ada0))
    (pad "2" smd roundrect (at 0.51 0 90) (size 0.54 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 31 "Net-(U1-XRES)") (pintype "passive") (tstamp 09dcbff2-7118-42c6-9231-05ee7eec4c28))
    (model "${KICAD6_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "B.Cu")
    (tstamp 2a7330a4-ff0a-48e8-a44d-a16c50ab92dd)
    (at 48.006 102.743 90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Unpolarized capacitor, small symbol")
    (property "ki_keywords" "capacitor cap")
    (property "part number" "12345")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/8bd8fe78-06f6-4b74-a7ae-8a35f68cd31b")
    (attr smd)
    (fp_text reference "C12" (at 0 1.16 90) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp b7763e15-279a-4047-be0a-c9fd4f80ca35)
    )
    (fp_text value "10n" (at 0 -1.16 90) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 62650b2f-912a-48e9-aa2f-e930983b4725)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "B.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)) (justify mirror))
      (tstamp 0a442ae9-faad-460f-b9d3-40431ea728ea)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp aea4d3f6-6293-4dc5-9ba8-855f019f15b4))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 6e8b3178-78a4-477c-9538-67e0043f8540))
    (fp_line (start -0.91 -0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 8754302f-9d80-45a7-8452-45c2743bf860))
    (fp_line (start -0.91 0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 78bc686f-24ef-4264-bb6c-2081a3872d29))
    (fp_line (start 0.91 -0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 4c512039-4049-4631-9183-1da55969be68))
    (fp_line (start 0.91 0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 5c3bb5d4-6401-416b-b07f-cd7b4707631e))
    (fp_line (start -0.5 -0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 0b34bdb1-e9ed-4720-8498-0f104aea2bc0))
    (fp_line (start -0.5 0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp d68dba85-2008-4962-b489-2048b9e2a8a8))
    (fp_line (start 0.5 -0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 02424c62-d551-45ba-8541-71a0a9d8aa66))
    (fp_line (start 0.5 0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp fa857033-aa3e-43ba-a0be-e14624592496))
    (pad "1" smd roundrect (at -0.48 0 90) (size 0.56 0.62) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 34 "VDD_1V8") (pintype "passive") (tstamp 0f50484c-e189-4409-b402-bc923eca9419))
    (pad "2" smd roundrect (at 0.48 0 90) (size 0.56 0.62) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 2 "GND") (pintype "passive") (tstamp f5461dd7-6971-4e43-bae9-2e690df69a3d))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0805_2012Metric" (layer "B.Cu")
    (tstamp 598f7a2a-ea98-421b-a1f4-e5fe64220d1f)
    (at 51.181 105.918 -90)
    (descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Unpolarized capacitor, small symbol")
    (property "ki_keywords" "capacitor cap")
    (property "part number" "23456")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/263aabe5-63dc-4b6d-801c-8a0dd1c95ca5")
    (attr smd)
    (fp_text reference "C8" (at 0 1.68 90) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp c38fce62-cfb5-496f-b72d-55d1950e88af)
    )
    (fp_text value "10u" (at 0 -1.68 90) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp d77dee54-c29c-4943-9df0-39ba1c05bb5f)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "B.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)) (justify mirror))
      (tstamp 42738d96-42bc-4f3e-8f0e-b77459f2f2c8)
    )
    (fp_line (start -0.261252 -0.735) (end 0.261252 -0.735)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 81a844be-0160-4bb4-a9bc-27659c50f50a))
    (fp_line (start -0.261252 0.735) (end 0.261252 0.735)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp bbe8dcb9-a0d4-424a-97a4-45e031f2e1f9))
    (fp_line (start -1.7 -0.98) (end -1.7 0.98)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp ff6cf3ea-6822-4d4d-a519-13802d43e55e))
    (fp_line (start -1.7 0.98) (end 1.7 0.98)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 69df6441-0744-4f38-9263-9ce9482c4786))
    (fp_line (start 1.7 -0.98) (end -1.7 -0.98)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 6d072a0e-3551-4141-9aad-70255a033ba6))
    (fp_line (start 1.7 0.98) (end 1.7 -0.98)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp ddecc87d-875a-487a-ab76-30c643f1f80f))
    (fp_line (start -1 -0.625) (end -1 0.625)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 4c1614f9-9315-4499-a1c1-0cf1a7ea805e))
    (fp_line (start -1 0.625) (end 1 0.625)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 47946a43-d1d0-4b64-a85e-2959cfb06b28))
    (fp_line (start 1 -0.625) (end -1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp a9c98bc4-e815-42af-8947-303bb491fd59))
    (fp_line (start 1 0.625) (end 1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 6c68cec3-b28e-4c81-a502-64a23cb4a0b7))
    (pad "1" smd roundrect (at -0.95 0 270) (size 1 1.45) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 1 "Net-(U1-CAP_VDD)") (pintype "passive") (tstamp 5a04ecbe-05e8-4ac6-be87-cfa344cbec71))
    (pad "2" smd roundrect (at 0.95 0 270) (size 1 1.45) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 2 "GND") (pintype "passive") (tstamp 9986b812-db33-40ea-8674-c7efd4d0ebb8))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "My_Devices:TestPoint_Pad_D0,8mm" (layer "B.Cu")
    (tstamp 7e623bc6-23b6-4661-93b8-1f672e1d4d74)
    (at 44.9985 97.663 180)
    (descr "SMD pad as test Point, diameter 1.0mm")
    (tags "test point SMD pad")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "exclude_from_bom" "")
    (property "ki_description" "test point")
    (property "ki_keywords" "test point tp")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/b9b4fdba-d953-487f-ad8d-cd31a5357f4a")
    (attr smd exclude_from_bom)
    (fp_text reference "TP2" (at -0.105 1.27) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp aa612870-0188-44da-9969-17466ed32169)
    )
    (fp_text value "TestPoint_small" (at 0.1 -2) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 94ccde68-0876-4556-82b7-8461c8b2537b)
    )
    (fp_circle (center 0 0) (end 0.6 0)
      (stroke (width 0.12) (type solid)) (fill none) (layer "B.SilkS") (tstamp d9b704bb-3a34-4893-997d-3453466c7af2))
    (fp_circle (center 0 0) (end 0.7 0)
      (stroke (width 0.05) (type solid)) (fill none) (layer "B.CrtYd") (tstamp b72b345e-0c39-4ca9-bd67-7ede04186491))
    (pad "1" smd circle (at 0 0 180) (size 0.8 0.8) (layers "B.Cu" "B.Mask")
      (net 16 "/camera signal/GW5_SENS_RES") (pinfunction "1") (pintype "passive") (tstamp 7c572613-31bf-4cba-8117-06a7eca23bc0))
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "B.Cu")
    (tstamp 8dd02aee-11ae-4f43-83a1-97fea6673b5e)
    (at 51.816 101.727 180)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Unpolarized capacitor, small symbol")
    (property "ki_keywords" "capacitor cap")
    (property "part number" "12345")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/c4798f5f-cdb3-491d-b114-b29bcbacb7da")
    (attr smd)
    (fp_text reference "C19" (at 0 1.16) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp fd8e1a06-7e68-427d-865a-5a86e37bfee7)
    )
    (fp_text value "10n" (at 0 -1.16) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 19f707e7-d8df-4b7b-aab8-c6b3ded40da0)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "B.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)) (justify mirror))
      (tstamp a591ade6-fdb1-4027-ad1c-ed26d9bbf995)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp cb6b089f-9db6-4d64-b0dc-32ff8edea5f8))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp d3d7879a-c0bc-4ed2-b851-e0fd8a3e9ff7))
    (fp_line (start -0.91 -0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp e8f34088-db2b-467a-bb15-b80b9cc88862))
    (fp_line (start -0.91 0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp f93fa417-0b70-4b41-82f5-8c16198db6f2))
    (fp_line (start 0.91 -0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 01ac5c0a-ad18-463f-99a3-bdbddc21c064))
    (fp_line (start 0.91 0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 862110f7-eb9c-44df-bb3d-c3cae567ada4))
    (fp_line (start -0.5 -0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 8ec9b7c1-b29a-4a03-ade7-1ff470fb652e))
    (fp_line (start -0.5 0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 8bb8f86e-7b6c-490f-a508-228ae453e099))
    (fp_line (start 0.5 -0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp cd71b28f-011c-4810-9e44-4f23633a9e11))
    (fp_line (start 0.5 0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 3b60f59f-968a-4ac9-ba6e-3f5e61f2e215))
    (pad "1" smd roundrect (at -0.48 0 180) (size 0.56 0.62) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 34 "VDD_1V8") (pintype "passive") (tstamp a979f894-9b4a-452b-82f1-82d30da3674d))
    (pad "2" smd roundrect (at 0.48 0 180) (size 0.56 0.62) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 2 "GND") (pintype "passive") (tstamp 2916b7fd-83d0-4a9a-85fd-46314338a565))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0805_2012Metric" (layer "B.Cu")
    (tstamp 9f09b318-ee6a-47d2-be2e-dcc471a5c5ee)
    (at 53.213 105.918 -90)
    (descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Unpolarized capacitor, small symbol")
    (property "ki_keywords" "capacitor cap")
    (property "part number" "23456")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/ce1df4fe-bf57-4dce-9dc0-db7923da0fa6")
    (attr smd)
    (fp_text reference "C16" (at 0 1.68 90) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 66a1a639-58d9-4901-a678-312690f6f6ec)
    )
    (fp_text value "10u" (at 0 -1.68 90) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp f9f51835-fdc7-40ad-bc08-bf6f5ab073fa)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "B.Fab")
        (effects (font (size 0.5 0.5) (thickness 0.08)) (justify mirror))
      (tstamp 902dfe33-2c26-40ff-8194-5dabe2e699cd)
    )
    (fp_line (start -0.261252 -0.735) (end 0.261252 -0.735)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp c7ad21a0-fd4f-4b97-8890-8197c3cbf892))
    (fp_line (start -0.261252 0.735) (end 0.261252 0.735)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 1e142532-39e0-4442-996f-8dc9571239b4))
    (fp_line (start -1.7 -0.98) (end -1.7 0.98)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 2e9b82b8-a936-434d-8330-2f3cdb80c782))
    (fp_line (start -1.7 0.98) (end 1.7 0.98)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 5f7885d5-353a-4ebc-bf74-362aaa869598))
    (fp_line (start 1.7 -0.98) (end -1.7 -0.98)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp b94bc769-b5c4-484b-861d-ed33418c60ce))
    (fp_line (start 1.7 0.98) (end 1.7 -0.98)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp db360bc3-a591-4ef9-81fe-54084f33d63a))
    (fp_line (start -1 -0.625) (end -1 0.625)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 3f2eaab9-edd7-46e1-8866-fceee6c39c52))
    (fp_line (start -1 0.625) (end 1 0.625)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 76c9370a-a3ea-4ecf-b633-cfe174a85799))
    (fp_line (start 1 -0.625) (end -1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 9680eab0-8b66-41f4-a85b-d235526b7602))
    (fp_line (start 1 0.625) (end 1 -0.625)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 19714e93-84f1-4f97-9aa3-0d942f90ad65))
    (pad "1" smd roundrect (at -0.95 0 270) (size 1 1.45) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 30 "VDD_1V0") (pintype "passive") (tstamp 17062b6d-d728-4c52-a4dd-668b2e93168d))
    (pad "2" smd roundrect (at 0.95 0 270) (size 1 1.45) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 2 "GND") (pintype "passive") (tstamp 14dadf22-73af-47de-8cf8-1235f1570064))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "B.Cu")
    (tstamp a070a78d-392b-4c1a-9c53-7a2b2e9876ae)
    (at 51.816 102.743 180)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Unpolarized capacitor, small symbol")
    (property "ki_keywords" "capacitor cap")
    (property "part number" "12345")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/7a0f4822-3019-4c4e-a8ba-20559724928a")
    (attr smd)
    (fp_text reference "C17" (at 0 1.16) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp c4e4f547-e0f2-4adc-aae0-3e13031b0312)
    )
    (fp_text value "10n" (at 0 -1.16) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 25054018-7c20-4f64-be7d-26ebc4e1334d)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "B.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)) (justify mirror))
      (tstamp cc1e1fb1-9f4c-4bbf-a975-edfd976a5bae)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp aba7cd96-a5dc-4b13-ab7c-8d8a6a480982))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 3e831a52-6e2f-4342-a4cc-d668384b2449))
    (fp_line (start -0.91 -0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 4d46de26-3157-4c48-aab9-e65489ae2278))
    (fp_line (start -0.91 0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 5153e7e9-9f99-4d53-94f0-714caa968a16))
    (fp_line (start 0.91 -0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 5b582426-5f6c-4e6c-bc5c-bf9d24649659))
    (fp_line (start 0.91 0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 1aeeaf7e-518b-449a-be2d-f6a0b80d4733))
    (fp_line (start -0.5 -0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 2408096d-4589-407a-a60c-9e1af7c9917c))
    (fp_line (start -0.5 0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 32cd2834-3c56-4695-acbc-f2a1d7721849))
    (fp_line (start 0.5 -0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 0423b79f-b7e0-4c24-99d0-c096708cc9a1))
    (fp_line (start 0.5 0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 7fcd30c3-2ddf-42a0-8200-23109e1a4b5d))
    (pad "1" smd roundrect (at -0.48 0 180) (size 0.56 0.62) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 30 "VDD_1V0") (pintype "passive") (tstamp 4e7505f5-9812-4333-a984-5889417a9150))
    (pad "2" smd roundrect (at 0.48 0 180) (size 0.56 0.62) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 2 "GND") (pintype "passive") (tstamp a9c0c3d4-a704-4111-93af-bc782173fffa))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0603_1608Metric" (layer "B.Cu")
    (tstamp ab2e068a-a259-4257-bccf-62b86b6fb5f0)
    (at 48.133 105.664 -90)
    (descr "Capacitor SMD 0603 (1608 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Unpolarized capacitor, small symbol")
    (property "ki_keywords" "capacitor cap")
    (property "part number" "34567")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/d448f9c1-1909-4e5c-8ec1-b37d99641860")
    (attr smd)
    (fp_text reference "C18" (at 0 1.43 90) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 15142dbd-664b-4a1d-a544-c3ce3bc852ce)
    )
    (fp_text value "10u" (at 0 -1.43 90) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp a3fb7324-6bc0-4f58-abf4-e5a23cfde309)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "B.Fab")
        (effects (font (size 0.4 0.4) (thickness 0.06)) (justify mirror))
      (tstamp 2c5397ae-da8c-4b66-8dec-1ff24728c0df)
    )
    (fp_line (start -0.14058 -0.51) (end 0.14058 -0.51)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp dfe80a19-a510-430e-9101-9d134e5b8b18))
    (fp_line (start -0.14058 0.51) (end 0.14058 0.51)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp e89a76b0-fcdd-4be5-8072-9c4d5210d426))
    (fp_line (start -1.48 -0.73) (end -1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp e1fdf8ee-53b2-4267-b2b3-9205dbfd5b43))
    (fp_line (start -1.48 0.73) (end 1.48 0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 1647a631-3ac0-4261-8839-5887fbb83767))
    (fp_line (start 1.48 -0.73) (end -1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 3556a342-236c-4bf7-bcb2-d72b891ab9df))
    (fp_line (start 1.48 0.73) (end 1.48 -0.73)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 79eed0d1-a4be-4540-95da-f704e944bd3b))
    (fp_line (start -0.8 -0.4) (end -0.8 0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 1b323fca-f276-4b39-a8c0-75bc1f644605))
    (fp_line (start -0.8 0.4) (end 0.8 0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp ab13d824-4eb9-4537-ae33-6b538f526568))
    (fp_line (start 0.8 -0.4) (end -0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp bcc57232-af6b-4c86-95b5-14877baba71b))
    (fp_line (start 0.8 0.4) (end 0.8 -0.4)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 423cd76f-64f0-41ce-8737-6b1f5360a123))
    (pad "1" smd roundrect (at -0.775 0 270) (size 0.9 0.95) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 34 "VDD_1V8") (pintype "passive") (tstamp d309089c-2f9a-4fc1-ab24-47d50e0d3b37))
    (pad "2" smd roundrect (at 0.775 0 270) (size 0.9 0.95) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 2 "GND") (pintype "passive") (tstamp c682f45c-68d2-4cb8-b9d3-285de35fe58f))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0603_1608Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "B.Cu")
    (tstamp bd975193-08cb-422a-b3c9-8e0ee097cc86)
    (at 50.419 102.743 90)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Unpolarized capacitor, small symbol")
    (property "ki_keywords" "capacitor cap")
    (property "part number" "12345")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/3cc26c07-86fd-459c-b5c2-ef5fbf7539b8")
    (attr smd)
    (fp_text reference "C9" (at 0 1.16 90) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 25c1aea1-b28c-4851-af9e-4a008369c30b)
    )
    (fp_text value "10n" (at 0 -1.16 90) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 05beea41-9662-4638-8431-72f511e718da)
    )
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "B.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)) (justify mirror))
      (tstamp dc1b5c8d-32fc-42dd-9a94-04b751e82362)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 8dbbbc5a-3811-4921-b95f-dff5cb3a68ca))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp a7888962-61df-4cac-95e6-298e12451987))
    (fp_line (start -0.91 -0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp ab2dd0e5-f648-418b-919a-5fcd8f468e9d))
    (fp_line (start -0.91 0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 84182509-1880-40a5-b52d-e52018f9dce9))
    (fp_line (start 0.91 -0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp ba69a90e-934e-45e6-97d0-2920a7c73d58))
    (fp_line (start 0.91 0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 94d50c01-84df-414c-aed2-0fc32dce75ab))
    (fp_line (start -0.5 -0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 101491f7-08d4-44c9-bfb8-fef3784ffb14))
    (fp_line (start -0.5 0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp d7800341-7cb8-4bf1-a97f-2138dac6c374))
    (fp_line (start 0.5 -0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp d7527d8b-958b-4dff-84f4-28691303cfe6))
    (fp_line (start 0.5 0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 7350d73a-c77b-4298-bdbb-31fd3953e8e6))
    (pad "1" smd roundrect (at -0.48 0 90) (size 0.56 0.62) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 1 "Net-(U1-CAP_VDD)") (pintype "passive") (tstamp e124d5d6-6e9c-47c7-a024-fdb8ddd7d489))
    (pad "2" smd roundrect (at 0.48 0 90) (size 0.56 0.62) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 2 "GND") (pintype "passive") (tstamp 7a0ff1c3-6346-430b-a453-df6c73f8be97))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_SMD:C_0402_1005Metric" (layer "B.Cu")
    (tstamp c58c2471-94cf-42d0-97cb-bd90e175fe15)
    (at 51.816 100.711 180)
    (descr "Capacitor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "ki_description" "Unpolarized capacitor, small symbol")
    (property "ki_keywords" "capacitor cap")
    (property "part number" "12345")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/f8429b2c-1a87-4b46-9df3-df5b2f5127b2")
    (attr smd)
    (fp_text reference "C22" (at 0 1.16) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp d970d6d3-bb70-4ad0-9d31-61ae5fd008d2)
    )
    (fp_text value "10n" (at 0 -1.16) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 73bf2118-6a38-4595-96c6-1907d4b9a6bd)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "B.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)) (justify mirror))
      (tstamp 32d29249-f70e-460f-950e-4926141f757f)
    )
    (fp_line (start -0.107836 -0.36) (end 0.107836 -0.36)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 897fbf20-b68f-4769-b26d-1150a8453356))
    (fp_line (start -0.107836 0.36) (end 0.107836 0.36)
      (stroke (width 0.12) (type solid)) (layer "B.SilkS") (tstamp 06a3afa6-755b-49a4-b00d-fc8713a0e0c7))
    (fp_line (start -0.91 -0.46) (end -0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp af3ffded-5758-495d-ba42-abf91c5ffdc8))
    (fp_line (start -0.91 0.46) (end 0.91 0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp fc4e8222-f1a5-4f00-acd4-ad33726aa252))
    (fp_line (start 0.91 -0.46) (end -0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 0011d36f-ee0f-4370-a7cb-baeebb995060))
    (fp_line (start 0.91 0.46) (end 0.91 -0.46)
      (stroke (width 0.05) (type solid)) (layer "B.CrtYd") (tstamp 4ec752cd-0b58-4d03-a421-8c4ad42746a6))
    (fp_line (start -0.5 -0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp 42cbc489-4269-415f-9898-99e1c5c85409))
    (fp_line (start -0.5 0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp c868c6f3-05ec-437b-8c5d-9237a9e469ea))
    (fp_line (start 0.5 -0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp ae63649a-0c4b-46cc-8058-1658156106c0))
    (fp_line (start 0.5 0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "B.Fab") (tstamp def2a506-18aa-4dfa-9893-bbab6e9c4159))
    (pad "1" smd roundrect (at -0.48 0 180) (size 0.56 0.62) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 35 "/camera signal/VDD_1V2") (pintype "passive") (tstamp 75fcf4bb-a54e-46ef-9b7b-f528a300a1f0))
    (pad "2" smd roundrect (at 0.48 0 180) (size 0.56 0.62) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.25)
      (net 2 "GND") (pintype "passive") (tstamp 25a38159-9ce8-4837-ad38-807c637d0817))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "My_Devices:TestPoint_Pad_D0,8mm" (layer "B.Cu")
    (tstamp dd6946a8-dd8d-42eb-b56d-3a7de7d7b127)
    (at 42.0775 96.774 180)
    (descr "SMD pad as test Point, diameter 1.0mm")
    (tags "test point SMD pad")
    (property "Sheetfile" "File: camera_signal.kicad_sch")
    (property "Sheetname" "camera signal")
    (property "exclude_from_bom" "")
    (property "ki_description" "test point")
    (property "ki_keywords" "test point tp")
    (path "/9199403a-20f5-4f23-857e-174abbc8bc86/8f1f8b48-ca01-4dca-a518-9bb0446f361a")
    (attr smd exclude_from_bom)
    (fp_text reference "TP1" (at 1.974 0.155) (layer "B.SilkS")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp b281b0f7-7e9d-44d1-837b-bbeec620c22b)
    )
    (fp_text value "TestPoint_small" (at 0.1 -2) (layer "B.Fab")
        (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp 26dff0d4-8056-4004-a135-b2aa6571b42c)
    )
    (fp_circle (center 0 0) (end 0.6 0)
      (stroke (width 0.12) (type solid)) (fill none) (layer "B.SilkS") (tstamp cbb6478b-6b5e-485c-95c4-55113d7e45cc))
    (fp_circle (center 0 0) (end 0.7 0)
      (stroke (width 0.05) (type solid)) (fill none) (layer "B.CrtYd") (tstamp e2bcfe12-b020-41eb-b686-c714ad6cf865))
    (pad "1" smd circle (at 0 0 180) (size 0.8 0.8) (layers "B.Cu" "B.Mask")
      (net 15 "/camera signal/GW5_FSYNC_OUT") (pinfunction "1") (pintype "passive") (tstamp 6380f1f9-ba08-4091-a50e-80d789c997f2))
  )

  (gr_arc (start 83.82 44.756549) (mid 87.073685 49.559116) (end 81.407 50.8)
    (stroke (width 0.15) (type default)) (layer "F.SilkS") (tstamp 859e3825-4025-433d-84f9-f0c9e0e7e485))
  (gr_arc (start 87.073685 49.559116) (mid 91.815729 55.116859) (end 84.811073 53.040738)
    (stroke (width 0.15) (type default)) (layer "F.SilkS") (tstamp ac05f1b4-aab5-4a6e-9064-0d2537df129f))
  (gr_rect (start 40 25) (end 202 125)
    (stroke (width 0.1) (type solid)) (fill none) (layer "Edge.Cuts") (tstamp 6f800d37-0cde-4abf-bff7-90d6983699d8))

  (via (at 51.181 104.013) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 1) (tstamp 987d53ee-85a5-4d05-b4a4-c590bc9035b2))
  (segment (start 50.419 103.223) (end 50.419 103.251) (width 0.12) (layer "B.Cu") (net 1) (tstamp 5305be30-c994-46ae-bab4-c9230a09d205))
  (segment (start 50.419 103.251) (end 51.181 104.013) (width 0.12) (layer "B.Cu") (net 1) (tstamp cbe13ea8-acbb-4ea5-9234-cbec84cfba9b))
  (segment (start 51.181 104.968) (end 51.181 104.013) (width 0.12) (layer "B.Cu") (net 1) (tstamp f7266e0f-c0b9-4320-b9a4-6e8cf1217ade))
  (via (at 48.133 99.06) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 2) (tstamp 4f55b463-3833-4b7b-b6bb-a8a98addb660))
  (via (at 51.054 101.981) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 2) (tstamp 7267d743-bb8c-4426-bd1b-9c9824c59bf1))
  (via (at 51.054 99.06) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 2) (tstamp 8dae1d13-9c7a-47f3-bf5f-95c02d59f1f1))
  (via (at 48.133 101.981) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 2) (tstamp f2f6ead5-6fd4-4d6a-8ff4-83e5419935a2))
  (segment (start 48.26 107.061) (end 49.022 106.299) (width 0.25) (layer "F.Cu") (net 3) (tstamp 5b08e0f7-3fd5-4c1c-8953-618226e8bd4a))
  (segment (start 49.022 106.299) (end 49.022 105.509) (width 0.25) (layer "F.Cu") (net 3) (tstamp a26396db-5d32-4e9a-a049-1ea2b6f08a6a))
  (segment (start 48.26 108.966) (end 48.26 107.061) (width 0.25) (layer "F.Cu") (net 3) (tstamp c6108da7-4bb1-46aa-b3dd-9420dced272c))
  (segment (start 48.781 109.487) (end 48.26 108.966) (width 0.25) (layer "F.Cu") (net 3) (tstamp c6497e66-82c7-421b-bd6c-090890ec2c0d))
  (segment (start 48.3285 97.99) (end 48.3285 97.2255) (width 0.12) (layer "F.Cu") (net 4) (tstamp 3bbbd0ca-85dd-43de-ab35-5b33599fb8ef))
  (segment (start 48.3285 97.2255) (end 46.99 95.887) (width 0.12) (layer "F.Cu") (net 4) (tstamp cbdbe101-5230-4336-88f4-730d9ea99a45))
  (segment (start 49.4485 103.06) (end 49.4485 104.1225) (width 0.1) (layer "F.Cu") (net 6) (tstamp c5dbf6ef-74f7-4393-946f-9b735bad65f2))
  (segment (start 49.3285 102.94) (end 49.4485 103.06) (width 0.1) (layer "F.Cu") (net 6) (tstamp d1d6c7a8-2e01-4386-a259-a2a8d1778dd7))
  (segment (start 49.4485 104.1225) (end 49.022 104.549) (width 0.1) (layer "F.Cu") (net 6) (tstamp ef6e2e32-457b-443f-9fbc-0d5e9fc0e3f7))
  (segment (start 50.165 105.509) (end 50.165 106.932) (width 0.25) (layer "F.Cu") (net 7) (tstamp 60028a5d-a3b5-4053-acaa-273061eed62e))
  (segment (start 50.165 106.932) (end 50.163 106.934) (width 0.25) (layer "F.Cu") (net 7) (tstamp 617f3d5e-4325-425d-992b-35da907ec874))
  (segment (start 49.7085 104.0925) (end 50.165 104.549) (width 0.1) (layer "F.Cu") (net 8) (tstamp 3f22510e-37ac-4e36-9104-993db15f13f5))
  (segment (start 49.7085 103.06) (end 49.7085 104.0925) (width 0.1) (layer "F.Cu") (net 8) (tstamp 6f0ceaaf-7c9b-4ac6-ae52-88603e6698d0))
  (segment (start 49.8285 102.94) (end 49.7085 103.06) (width 0.1) (layer "F.Cu") (net 8) (tstamp e245c681-3976-43a2-adbb-7c8e7885c197))
  (segment (start 45.726 101.215) (end 45.722 101.219) (width 0.12) (layer "F.Cu") (net 12) (tstamp 9e3185de-2b27-45ec-9409-5c197e7a3cf3))
  (segment (start 47.1035 101.215) (end 45.726 101.215) (width 0.12) (layer "F.Cu") (net 12) (tstamp a4e11fc8-eb15-44ed-aa7f-195132353e41))
  (segment (start 53.465716 100.007132) (end 53.636132 99.836716) (width 0.1) (layer "F.Cu") (net 18) (tstamp 025d209f-aea5-4eba-b1fc-f6e10e760971))
  (segment (start 53.724 99.624584) (end 53.724 97.764) (width 0.1) (layer "F.Cu") (net 18) (tstamp 278b23aa-c35c-4c66-a0e1-fe50f13eee92))
  (segment (start 54.064415 96.794) (end 53.874 96.794) (width 0.1) (layer "F.Cu") (net 18) (tstamp 538ebea8-80e2-4117-b0a8-836c336aca5d))
  (segment (start 52.297764 100.095) (end 53.253584 100.095) (width 0.1) (layer "F.Cu") (net 18) (tstamp a8b72e77-8b9a-4932-b34d-03ee41568354))
  (segment (start 53.874 97.614) (end 54.064415 97.614) (width 0.1) (layer "F.Cu") (net 18) (tstamp b566d91a-c296-4fde-bbc0-9240a8a31166))
  (segment (start 53.724961 96.643039) (end 53.724961 92.076225) (width 0.1) (layer "F.Cu") (net 18) (tstamp bc612b05-5c6f-46dc-9105-7a5c3d4f9bb1))
  (segment (start 52.0535 100.215) (end 52.085632 100.182868) (width 0.1) (layer "F.Cu") (net 18) (tstamp e844eb40-13ed-444b-8869-70986563a826))
  (segment (start 53.724 96.644) (end 53.724961 96.643039) (width 0.1) (layer "F.Cu") (net 18) (tstamp f4941912-a04b-466a-9db1-34cd5d4510c8))
  (arc (start 53.724 99.624584) (mid 53.701164 99.739389) (end 53.636132 99.836716) (width 0.1) (layer "F.Cu") (net 18) (tstamp 1effeca1-f5c9-48bc-b431-c03b56c5ec2a))
  (arc (start 53.724 97.764) (mid 53.767934 97.657934) (end 53.874 97.614) (width 0.1) (layer "F.Cu") (net 18) (tstamp 4a0ebf29-29ae-4d5e-801a-089018567805))
  (arc (start 52.085632 100.182868) (mid 52.182959 100.117836) (end 52.297764 100.095) (width 0.1) (layer "F.Cu") (net 18) (tstamp 674f1e20-274d-48c3-aa41-6e28aa75b128))
  (arc (start 53.874 96.794) (mid 53.767934 96.750066) (end 53.724 96.644) (width 0.1) (layer "F.Cu") (net 18) (tstamp a14eaa77-8ff2-4c28-81f4-a5498405c227))
  (arc (start 54.064415 97.614) (mid 54.354329 97.493914) (end 54.474415 97.204) (width 0.1) (layer "F.Cu") (net 18) (tstamp b8477192-0107-40a1-b153-a1f596f55ae4))
  (arc (start 54.474415 97.204) (mid 54.354329 96.914086) (end 54.064415 96.794) (width 0.1) (layer "F.Cu") (net 18) (tstamp cf5ae0d2-e8b7-4c11-b09a-105f86a5a497))
  (arc (start 53.253584 100.095) (mid 53.368389 100.072164) (end 53.465716 100.007132) (width 0.1) (layer "F.Cu") (net 18) (tstamp ee17a33c-da6c-443c-bdc6-8ab5eba4fa45))
  (segment (start 54.064415 97.054) (end 53.874 97.054) (width 0.1) (layer "F.Cu") (net 19) (tstamp 2d542f3c-fca7-4a4a-8872-dc03f0293a36))
  (segment (start 53.35802 99.747132) (end 53.376132 99.72902) (width 0.1) (layer "F.Cu") (net 19) (tstamp 3c824532-a85e-4b43-96ca-43427a3c228f))
  (segment (start 52.297764 99.835) (end 53.145888 99.835) (width 0.1) (layer "F.Cu") (net 19) (tstamp 4c1de354-7a76-45ab-a17c-0ede03854e60))
  (segment (start 53.464 96.644) (end 53.467 96.641) (width 0.1) (layer "F.Cu") (net 19) (tstamp 9dcc8b65-fd0b-4077-bd9e-c413461d81a7))
  (segment (start 52.0535 99.715) (end 52.085632 99.747132) (width 0.1) (layer "F.Cu") (net 19) (tstamp a4d65257-4a68-4c71-a25a-4da6d8d31858))
  (segment (start 53.874 97.354) (end 54.064415 97.354) (width 0.1) (layer "F.Cu") (net 19) (tstamp d676ac55-1616-4c78-919e-9e6d5797db5c))
  (segment (start 53.467 96.641) (end 53.467 92.236021) (width 0.1) (layer "F.Cu") (net 19) (tstamp e268d9a2-571d-4259-9c25-196bd22fe84a))
  (segment (start 53.464 99.516888) (end 53.464 97.764) (width 0.1) (layer "F.Cu") (net 19) (tstamp fcea2cbd-e46c-425f-8466-49c1ed508626))
  (arc (start 53.35802 99.747132) (mid 53.260693 99.812164) (end 53.145888 99.835) (width 0.1) (layer "F.Cu") (net 19) (tstamp 1d357013-4ca7-44c6-bc13-453d781980d6))
  (arc (start 53.376132 99.72902) (mid 53.441164 99.631693) (end 53.464 99.516888) (width 0.1) (layer "F.Cu") (net 19) (tstamp 361cd396-d3da-41bf-9559-d9ea2610de7f))
  (arc (start 53.874 97.054) (mid 53.584086 96.933914) (end 53.464 96.644) (width 0.1) (layer "F.Cu") (net 19) (tstamp 57c55add-d019-4a6d-a484-0d9c405dc164))
  (arc (start 54.214415 97.204) (mid 54.170481 97.097934) (end 54.064415 97.054) (width 0.1) (layer "F.Cu") (net 19) (tstamp 6f1fecaf-05bf-4f7c-a2d0-fefe280236b0))
  (arc (start 54.064415 97.354) (mid 54.170481 97.310066) (end 54.214415 97.204) (width 0.1) (layer "F.Cu") (net 19) (tstamp bb4f454e-2012-4354-886e-0f294b7bb7c1))
  (arc (start 53.464 97.764) (mid 53.584086 97.474086) (end 53.874 97.354) (width 0.1) (layer "F.Cu") (net 19) (tstamp eeb03a66-ddcb-4d6e-b4b3-7487217a62df))
  (arc (start 52.297764 99.835) (mid 52.182959 99.812164) (end 52.085632 99.747132) (width 0.1) (layer "F.Cu") (net 19) (tstamp f418ba07-fe3a-438a-b817-d7a03d74d1a5))
  (segment (start 52.297764 99.095) (end 52.602584 99.095) (width 0.1) (layer "F.Cu") (net 20) (tstamp 2bb6349e-127d-488d-ae4f-00a8f9da241e))
  (segment (start 53.089 98.608584) (end 53.089 96.36484) (width 0.1) (layer "F.Cu") (net 20) (tstamp 68a6ed2a-ae2e-4fc4-8180-6fddfb30a378))
  (segment (start 53.001132 96.152708) (end 51.522868 94.674444) (width 0.1) (layer "F.Cu") (net 20) (tstamp 6f9e1d50-ccc6-43df-9425-586a682981fb))
  (segment (start 52.0535 99.215) (end 52.085632 99.182868) (width 0.1) (layer "F.Cu") (net 20) (tstamp cd79aed3-1528-436a-81df-33bf58d8e4a1))
  (segment (start 52.814716 99.007132) (end 53.001132 98.820716) (width 0.1) (layer "F.Cu") (net 20) (tstamp f9deb959-5dfb-42d3-a81d-503ccaf6a4f5))
  (arc (start 53.089 96.36484) (mid 53.066164 96.250035) (end 53.001132 96.152708) (width 0.1) (layer "F.Cu") (net 20) (tstamp 73213892-06c0-447f-9b08-177a16f18fee))
  (arc (start 53.089 98.608584) (mid 53.066164 98.723389) (end 53.001132 98.820716) (width 0.1) (layer "F.Cu") (net 20) (tstamp d6a0048e-c2ad-4fbc-bd5e-0203c49fe824))
  (arc (start 52.297764 99.095) (mid 52.182959 99.117836) (end 52.085632 99.182868) (width 0.1) (layer "F.Cu") (net 20) (tstamp f7a43a27-a69b-4a13-9b08-e33410e6ea92))
  (arc (start 52.602584 99.095) (mid 52.717389 99.072164) (end 52.814716 99.007132) (width 0.1) (layer "F.Cu") (net 20) (tstamp f89a81d6-e5b9-4391-8286-9ae09621a568))
  (segment (start 52.297764 98.835) (end 52.494888 98.835) (width 0.1) (layer "F.Cu") (net 21) (tstamp 12f71ed3-2e7e-4e86-a6d4-fad37f15015f))
  (segment (start 52.70702 98.747132) (end 52.741132 98.71302) (width 0.1) (layer "F.Cu") (net 21) (tstamp acc2c420-b4e6-4633-99d5-097194a11278))
  (segment (start 52.741132 96.259698) (end 51.26214 94.780706) (width 0.1) (layer "F.Cu") (net 21) (tstamp ba32fb20-b0a7-4945-8379-f761999a307e))
  (segment (start 52.829 98.500888) (end 52.829 96.47183) (width 0.1) (layer "F.Cu") (net 21) (tstamp ddfa408e-d364-4b50-8e4b-ff16aab24859))
  (segment (start 52.0535 98.715) (end 52.085632 98.747132) (width 0.1) (layer "F.Cu") (net 21) (tstamp e1f20050-ea50-444d-bc40-f0b8f7730097))
  (arc (start 52.085632 98.747132) (mid 52.182959 98.812164) (end 52.297764 98.835) (width 0.1) (layer "F.Cu") (net 21) (tstamp 266913de-fb42-4f9d-9eea-600126042113))
  (arc (start 52.494888 98.835) (mid 52.609693 98.812164) (end 52.70702 98.747132) (width 0.1) (layer "F.Cu") (net 21) (tstamp 74d412bf-68d6-41d4-80e8-2735a1a46337))
  (arc (start 52.829 96.47183) (mid 52.806164 96.357025) (end 52.741132 96.259698) (width 0.1) (layer "F.Cu") (net 21) (tstamp c4e55915-9e77-40e8-b9c7-8ed463f69346))
  (arc (start 52.741132 98.71302) (mid 52.806164 98.615693) (end 52.829 98.500888) (width 0.1) (layer "F.Cu") (net 21) (tstamp c8ec399f-4c77-4409-857f-f79d06ef642a))
  (segment (start 51.2085 97.745736) (end 51.2085 97.692348) (width 0.1) (layer "F.Cu") (net 22) (tstamp 061fae5f-96e3-47b2-846c-b7e2c9acecb2))
  (segment (start 51.3285 97.99) (end 51.296368 97.957868) (width 0.1) (layer "F.Cu") (net 22) (tstamp 2aea44f3-6b6f-4a15-a514-3d1730eb19a3))
  (segment (start 52.144766 96.880346) (end 52.303346 96.880346) (width 0.1) (layer "F.Cu") (net 22) (tstamp bf087267-7478-472d-bb16-6e505a333bb2))
  (segment (start 51.2085 97.692348) (end 51.932634 96.968214) (width 0.1) (layer "F.Cu") (net 22) (tstamp d065f39c-0c9f-4f6b-9035-e493e7c101c2))
  (via (at 52.303346 96.880346) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 22) (tstamp 3480f37a-ca85-4983-a5f0-37ea789e3126))
  (arc (start 51.296368 97.957868) (mid 51.231336 97.860541) (end 51.2085 97.745736) (width 0.1) (layer "F.Cu") (net 22) (tstamp b36f7f32-9058-4d90-a2c1-2e947168cbf5))
  (arc (start 51.932634 96.968214) (mid 52.029961 96.903182) (end 52.144766 96.880346) (width 0.1) (layer "F.Cu") (net 22) (tstamp c1be3d5c-e2a6-4d04-a8da-9aa30d0b2ae8))
  (segment (start 54.664112 94.361) (end 54.864 94.361) (width 0.1) (layer "In1.Cu") (net 22) (tstamp 0c9a947d-b04f-4f94-ac15-2ea6b8868b8e))
  (segment (start 52.303346 96.880346) (end 52.303346 96.721766) (width 0.1) (layer "In1.Cu") (net 22) (tstamp 0f2bc765-045e-4cde-8b00-a17424a1220c))
  (segment (start 52.391214 96.509634) (end 54.45198 94.448868) (width 0.1) (layer "In1.Cu") (net 22) (tstamp 7631eae6-ccb2-44b6-b23f-d6e3150111f4))
  (arc (start 52.391214 96.509634) (mid 52.326182 96.606961) (end 52.303346 96.721766) (width 0.1) (layer "In1.Cu") (net 22) (tstamp c073f541-1920-4015-8dd6-be06d6d265b9))
  (arc (start 54.45198 94.448868) (mid 54.549307 94.383836) (end 54.664112 94.361) (width 0.1) (layer "In1.Cu") (net 22) (tstamp e0fad8e8-9d33-4277-b1d3-fc99d9c8f400))
  (segment (start 50.8285 97.99) (end 50.860632 97.957868) (width 0.1) (layer "F.Cu") (net 23) (tstamp 018577e2-86b5-40ce-8e83-fa2ebdb67002))
  (segment (start 51.836654 96.572234) (end 51.836654 96.413654) (width 0.1) (layer "F.Cu") (net 23) (tstamp 285250f7-7120-463c-a152-a0655461af0a))
  (segment (start 51.036368 97.496784) (end 51.748786 96.784366) (width 0.1) (layer "F.Cu") (net 23) (tstamp 3b1998d6-ac23-42f4-9dc3-fbe0bd6ceefe))
  (segment (start 50.9485 97.745736) (end 50.9485 97.708916) (width 0.1) (layer "F.Cu") (net 23) (tstamp cc4e0922-1a9b-4859-915b-fddae8488db0))
  (via (at 51.836654 96.413654) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 23) (tstamp 3b20c9b3-1cb7-4d07-8cf5-694392e71004))
  (arc (start 50.860632 97.957868) (mid 50.925664 97.860541) (end 50.9485 97.745736) (width 0.1) (layer "F.Cu") (net 23) (tstamp 6bf8e99c-bc78-4a0c-b27b-218a570b370f))
  (arc (start 51.036368 97.496784) (mid 50.971336 97.594111) (end 50.9485 97.708916) (width 0.1) (layer "F.Cu") (net 23) (tstamp d065c4f3-fe18-4e1d-9282-be24e3c3ded5))
  (arc (start 51.748786 96.784366) (mid 51.813818 96.687039) (end 51.836654 96.572234) (width 0.1) (layer "F.Cu") (net 23) (tstamp efcdefe1-1e11-4d64-a190-60a277111f9a))
  (segment (start 54.386652 94.022236) (end 54.386652 93.878039) (width 0.1) (layer "In1.Cu") (net 23) (tstamp 8dc86386-ea01-4102-a7e0-29b3a9e4d438))
  (segment (start 52.207366 96.325786) (end 54.298784 94.234368) (width 0.1) (layer "In1.Cu") (net 23) (tstamp df2a1767-ab3d-420f-a27e-cda08a8feb5f))
  (segment (start 51.836654 96.413654) (end 51.995234 96.413654) (width 0.1) (layer "In1.Cu") (net 23) (tstamp ea432234-12a1-4eab-8076-1cc973c133a5))
  (arc (start 51.995234 96.413654) (mid 52.110039 96.390818) (end 52.207366 96.325786) (width 0.1) (layer "In1.Cu") (net 23) (tstamp 77592875-750e-4418-944d-1fdb8d1dab95))
  (arc (start 54.298784 94.234368) (mid 54.363816 94.137041) (end 54.386652 94.022236) (width 0.1) (layer "In1.Cu") (net 23) (tstamp c461867d-ea85-4ba8-866a-2d405d1a346e))
  (segment (start 51.043634 95.952214) (end 50.296368 96.69948) (width 0.1) (layer "F.Cu") (net 24) (tstamp 3816a559-b6d3-4db4-be96-10e6d986b9e9))
  (segment (start 50.2085 96.911612) (end 50.2085 97.745736) (width 0.1) (layer "F.Cu") (net 24) (tstamp 5a538c5d-2112-4aae-89de-404faba4e49b))
  (segment (start 50.296368 97.957868) (end 50.3285 97.99) (width 0.1) (layer "F.Cu") (net 24) (tstamp 5f14ec7a-bac8-40d9-90f8-1fc230ad4714))
  (arc (start 50.2085 96.911612) (mid 50.231336 96.796807) (end 50.296368 96.69948) (width 0.1) (layer "F.Cu") (net 24) (tstamp 3156cdf1-45e0-4052-9b9c-f6164f62996e))
  (arc (start 50.2085 97.745736) (mid 50.231336 97.860541) (end 50.296368 97.957868) (width 0.1) (layer "F.Cu") (net 24) (tstamp b507e244-c15b-41d5-bb1a-ea2a4d0177ac))
  (segment (start 52.557346 94.721346) (end 52.398766 94.721346) (width 0.1) (layer "In1.Cu") (net 24) (tstamp 108ace05-9b91-4ae8-8f1a-30845ac84bd8))
  (segment (start 51.414346 95.705766) (end 51.414346 95.864346) (width 0.1) (layer "In1.Cu") (net 24) (tstamp 6bc1c671-d9be-4a6d-b0a5-f9e91558c50b))
  (segment (start 52.186634 94.809214) (end 51.502214 95.493634) (width 0.1) (layer "In1.Cu") (net 24) (tstamp 9dfd5006-640c-4546-9344-4bd53422c414))
  (arc (start 51.414346 95.705766) (mid 51.437182 95.590961) (end 51.502214 95.493634) (width 0.1) (layer "In1.Cu") (net 24) (tstamp 929881bb-bf12-42b0-8242-795c1c7914d5))
  (arc (start 52.398766 94.721346) (mid 52.283961 94.744182) (end 52.186634 94.809214) (width 0.1) (layer "In1.Cu") (net 24) (tstamp ef47d10d-2fe7-40d5-83b6-9b94c69b9222))
  (segment (start 49.9485 96.803916) (end 49.9485 97.745736) (width 0.1) (layer "F.Cu") (net 25) (tstamp 6e2e520e-7458-4f5e-9515-281b656b3a0a))
  (segment (start 50.859786 95.768366) (end 50.036368 96.591784) (width 0.1) (layer "F.Cu") (net 25) (tstamp 75b78c6e-c971-4b9f-969a-64df2d84d52b))
  (segment (start 49.860632 97.957868) (end 49.8285 97.99) (width 0.1) (layer "F.Cu") (net 25) (tstamp 8b9063b1-d927-43a4-a4db-e99b94535ca1))
  (arc (start 49.860632 97.957868) (mid 49.925664 97.860541) (end 49.9485 97.745736) (width 0.1) (layer "F.Cu") (net 25) (tstamp 8843f357-248e-4735-b196-ded21e5711b3))
  (arc (start 50.036368 96.591784) (mid 49.971336 96.689111) (end 49.9485 96.803916) (width 0.1) (layer "F.Cu") (net 25) (tstamp d29d7910-a7ff-404d-a5f0-f46bbc02cd28))
  (segment (start 52.090654 94.254654) (end 52.090654 94.413234) (width 0.1) (layer "In1.Cu") (net 25) (tstamp 5c9bd35c-22a2-454b-a391-b4ff4cc3ab83))
  (segment (start 51.106234 95.397654) (end 50.947654 95.397654) (width 0.1) (layer "In1.Cu") (net 25) (tstamp 96e6d935-33b5-40fa-8576-a6153bb2ccea))
  (segment (start 52.002786 94.625366) (end 51.318366 95.309786) (width 0.1) (layer "In1.Cu") (net 25) (tstamp 974d9d4a-0cd6-4f78-81ef-acad64ac1cba))
  (arc (start 52.090654 94.413234) (mid 52.067818 94.528039) (end 52.002786 94.625366) (width 0.1) (layer "In1.Cu") (net 25) (tstamp cda12217-0458-4532-b297-367125dabd73))
  (arc (start 51.106234 95.397654) (mid 51.221039 95.374818) (end 51.318366 95.309786) (width 0.1) (layer "In1.Cu") (net 25) (tstamp ee9ad11d-80c3-4748-b00a-e291541a88e9))
  (segment (start 49.296368 97.957868) (end 49.3285 97.99) (width 0.1) (layer "F.Cu") (net 26) (tstamp 158e0c00-6d62-42fd-94b5-e46fd66f7fa9))
  (segment (start 49.2085 96.1315) (end 49.2085 97.745736) (width 0.1) (layer "F.Cu") (net 26) (tstamp 1f0b6b11-f51f-4078-9a16-50622dce7873))
  (arc (start 49.296368 97.957868) (mid 49.231336 97.860541) (end 49.2085 97.745736) (width 0.1) (layer "F.Cu") (net 26) (tstamp 1f04c074-0225-4b7f-bcdb-5421db83201e))
  (arc (start 48.7735 95.6965) (mid 49.081091 95.823909) (end 49.2085 96.1315) (width 0.1) (layer "F.Cu") (net 26) (tstamp 8155f2a9-252a-45a6-a5f6-e6f0e6e934a7))
  (segment (start 48.9485 96.1315) (end 48.9485 97.745736) (width 0.1) (layer "F.Cu") (net 27) (tstamp 1f1e63fa-a87d-4808-8555-02d28cfd23c6))
  (segment (start 48.860632 97.957868) (end 48.8285 97.99) (width 0.1) (layer "F.Cu") (net 27) (tstamp 839fb2f3-8ad1-4589-87bf-70b1b7702b36))
  (arc (start 48.7735 95.9565) (mid 48.897244 96.007756) (end 48.9485 96.1315) (width 0.1) (layer "F.Cu") (net 27) (tstamp 1e0b6e47-cf2c-4d08-8450-eb6ff28afc79))
  (arc (start 48.9485 97.745736) (mid 48.925664 97.860541) (end 48.860632 97.957868) (width 0.1) (layer "F.Cu") (net 27) (tstamp aa6564c5-f15f-4f01-af9c-3be8c8c86574))
  (segment (start 52.314 103.495) (end 55.489 103.495) (width 0.12) (layer "F.Cu") (net 30) (tstamp 127d4286-3915-4448-8ea1-7331f719e9b4))
  (segment (start 51.041276 103.495) (end 52.314 103.495) (width 0.12) (layer "F.Cu") (net 30) (tstamp 486c8cb8-c448-4c30-a176-896551e87dfd))
  (segment (start 50.8285 102.94) (end 50.8285 103.282224) (width 0.12) (layer "F.Cu") (net 30) (tstamp 762d5a3e-c879-433e-a6d4-bf44e9496a2b))
  (segment (start 50.8285 103.282224) (end 51.041276 103.495) (width 0.12) (layer "F.Cu") (net 30) (tstamp 89130528-ea95-4c0b-bc75-805fb6551bd5))
  (via (at 52.314 103.495) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 30) (tstamp ec15b4c3-2120-4ae7-a438-6bdbcfcdc7e7))
  (segment (start 52.314 104.069) (end 53.213 104.968) (width 0.12) (layer "B.Cu") (net 30) (tstamp 14fe239a-031c-4f34-917c-75e2c28ceb81))
  (segment (start 52.314 103.495) (end 52.314 104.069) (width 0.12) (layer "B.Cu") (net 30) (tstamp 6e7ef9f4-0043-494d-8e5c-1a86699efbe2))
  (segment (start 52.314 103.495) (end 52.296 103.477) (width 0.12) (layer "B.Cu") (net 30) (tstamp afec40f6-33d0-4f7a-b16d-d49ca90abc97))
  (segment (start 52.296 103.477) (end 52.296 102.743) (width 0.12) (layer "B.Cu") (net 30) (tstamp f8b3b868-c3a1-4685-b660-2348141e1b52))
  (segment (start 47.75 103.632) (end 47.244 104.138) (width 0.12) (layer "F.Cu") (net 31) (tstamp 406b957c-36c9-4ce3-a8c0-23b383e8a3a4))
  (segment (start 48.3285 103.3095) (end 48.006 103.632) (width 0.12) (layer "F.Cu") (net 31) (tstamp 4c91619b-2790-4838-a83d-3ae6be56f61d))
  (segment (start 48.3285 102.94) (end 48.3285 103.3095) (width 0.12) (layer "F.Cu") (net 31) (tstamp 86f4410e-15a4-4590-9067-a245aa1a47f3))
  (segment (start 48.006 103.632) (end 47.75 103.632) (width 0.12) (layer "F.Cu") (net 31) (tstamp c9fad9b0-5355-4995-8dcb-447bea52c19c))
  (segment (start 48.23354 104.03946) (end 48.14182 104.03946) (width 0.12) (layer "F.Cu") (net 34) (tstamp 2a8d8d4a-d4e8-40c1-877b-b5eef460f1d3))
  (segment (start 52.0535 102.215) (end 52.725 102.215) (width 0.12) (layer "F.Cu") (net 34) (tstamp 450a9bef-0a15-4cfb-b8d7-45282ad1ddfe))
  (segment (start 48.8285 103.4445) (end 48.23354 104.03946) (width 0.12) (layer "F.Cu") (net 34) (tstamp 8add2989-cc40-44a5-8e8a-7fb200de060f))
  (segment (start 48.8285 102.94) (end 48.8285 103.4445) (width 0.12) (layer "F.Cu") (net 34) (tstamp c69d3272-1a32-49bc-a310-1e624d03f07e))
  (segment (start 52.725 102.215) (end 53.213 101.727) (width 0.12) (layer "F.Cu") (net 34) (tstamp d70ed1a7-157e-431c-a2a3-60eaaa0bdcf5))
  (via (at 53.213 101.727) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 34) (tstamp 42569374-76ba-4530-a33a-8940aef9081d))
  (via (at 48.14182 104.03946) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 34) (tstamp 659955c2-3e96-4dfe-beef-009ee6ac19bd))
  (segment (start 53.213 101.727) (end 52.296 101.727) (width 0.12) (layer "B.Cu") (net 34) (tstamp 244461bc-62b9-4d65-95a8-65bcc59928dc))
  (segment (start 48.006 103.90364) (end 48.14182 104.03946) (width 0.12) (layer "B.Cu") (net 34) (tstamp 4d184dcd-f37c-4612-80b1-347334d4d427))
  (segment (start 48.006 103.223) (end 48.006 103.90364) (width 0.12) (layer "B.Cu") (net 34) (tstamp 81d4d5c8-4504-4ee8-919c-4f710a021beb))
  (segment (start 48.133 104.04828) (end 48.14182 104.03946) (width 0.12) (layer "B.Cu") (net 34) (tstamp b71418ac-a936-4eb6-a52b-60642f390b04))
  (segment (start 48.133 104.889) (end 48.133 104.04828) (width 0.12) (layer "B.Cu") (net 34) (tstamp ebe4972c-6663-4540-9503-cb782188390d))
  (segment (start 53.209 100.715) (end 53.213 100.711) (width 0.12) (layer "F.Cu") (net 35) (tstamp 9a129e82-1378-4a92-9eca-51aa5409d5b9))
  (segment (start 52.0535 100.715) (end 53.209 100.715) (width 0.12) (layer "F.Cu") (net 35) (tstamp cfe16b94-5f81-4171-b7b4-f6f8831c251f))
  (via (at 53.213 100.711) (size 0.5) (drill 0.3) (layers "F.Cu" "B.Cu") (net 35) (tstamp 6a7c6ead-2430-421e-af72-e4ae7841243c))
  (segment (start 53.213 100.711) (end 52.296 100.711) (width 0.12) (layer "B.Cu") (net 35) (tstamp 291be652-206c-4349-adb6-9293c608a748))

  (zone (net 0) (net_name "") (layer "F.Cu") (tstamp 0299e45b-7051-4cca-8909-a5bff677586a) (name "BGA_inner_clearance") (hatch edge 0.508)
    (connect_pads (clearance 0))
    (min_thickness 0.254) (filled_areas_thickness no)
    (keepout (tracks allowed) (vias allowed) (pads allowed) (copperpour allowed) (footprints allowed))
    (fill (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 52.578 98.4099)
        (xy 52.578 100.4927)
        (xy 51.5112 100.4927)
        (xy 51.5112 98.4099)
      )
    )
  )
  (zone (net 0) (net_name "") (layer "F.Cu") (tstamp 2eae407e-7b61-4656-90f8-b628db25b23a) (name "BGA_inner_clearance") (hatch edge 0.508)
    (connect_pads (clearance 0))
    (min_thickness 0.254) (filled_areas_thickness no)
    (keepout (tracks allowed) (vias allowed) (pads allowed) (copperpour allowed) (footprints allowed))
    (fill (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 51.6025 98.4758)
        (xy 48.5545 98.4758)
        (xy 48.5545 97.409)
        (xy 51.6025 97.409)
      )
    )
  )
  (zone (net 0) (net_name "") (layer "F.Cu") (tstamp dab74ac4-58fd-4883-b13c-02c8b25b1651) (name "BGA_inner_clearance") (hatch edge 0.508)
    (connect_pads (clearance 0))
    (min_thickness 0.254) (filled_areas_thickness no)
    (keepout (tracks allowed) (vias allowed) (pads allowed) (copperpour allowed) (footprints allowed))
    (fill (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 50.5865 103.4288)
        (xy 48.5545 103.4288)
        (xy 48.5545 102.362)
        (xy 50.5865 102.362)
      )
    )
  )
  (group "" (id 6c3c90b8-f816-4c49-ad15-9f111d4a2c71)
    (members
      ac81a31c-4776-471e-9d89-fee4183e7da2
      dab74ac4-58fd-4883-b13c-02c8b25b1651
    )
  )
  (group "" (id 885bed0b-8118-4b21-b408-328afdc15058)
    (members
      0299e45b-7051-4cca-8909-a5bff677586a
      2eae407e-7b61-4656-90f8-b628db25b23a
    )
  )
)
