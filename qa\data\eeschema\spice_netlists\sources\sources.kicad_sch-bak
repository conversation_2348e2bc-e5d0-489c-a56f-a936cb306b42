(kicad_sch (version 20220622) (generator eeschema)

  (uuid 3a449e21-a736-4714-8865-e7318f0055f6)

  (paper "A4")

  (lib_symbols
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:IDC" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "IDC" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "dc(1)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, DC" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "IDC_0_0"
        (polyline
          (pts
            (xy -1.27 0.254)
            (xy 1.27 0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.254)
            (xy -1.27 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.254 -0.254)
            (xy -0.254 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.254)
            (xy 0.762 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "IDC_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "IDC_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:IEXP" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "IEXP" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "exp(0 1 2n 30n 60n 40n)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, exponential" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "IEXP_0_0"
        (polyline
          (pts
            (xy -1.27 -0.762)
            (xy -1.778 -0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.762)
            (xy 1.778 -0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0.762) (mid -0.851 0.1794) (end -1.27 -0.762)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0.762) (mid 0.3591 -0.2299) (end 1.27 -0.762)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "IEXP_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "IEXP_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:IPULSE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "IPULSE" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "pulse(0 1 2n 2n 2n 50n 100n)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, pulse" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "IPULSE_0_0"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy -1.397 -0.762)
            (xy -1.143 0.762)
            (xy -0.127 0.762)
            (xy 0.127 -0.762)
            (xy 1.143 -0.762)
            (xy 1.397 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "IPULSE_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "IPULSE_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:ISIN" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "I" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "ISIN" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "I" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "sin(0 1 1k)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Current source, sinusoidal" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "ISIN_0_0"
        (arc (start 0 0) (mid -0.635 0.635) (end -1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0) (mid 0.635 -0.635) (end 1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "ISIN_0_1"
        (polyline
          (pts
            (xy 0 1.27)
            (xy 0 2.286)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.254 1.778)
            (xy 0 1.27)
            (xy 0.254 1.778)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "ISIN_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VAM" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VAM" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "am(1 0 100k 1k 1n)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation amplitude modulated" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, AM" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VAM_0_0"
        (arc (start 0 0.508) (mid -0.254 0.762) (end -0.508 0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VAM_0_1"
        (arc (start -1.016 0) (mid -1.27 0.254) (end -1.524 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start -1.016 0) (mid -0.762 -0.254) (end -0.508 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 -0.508) (mid 0.254 -0.762) (end 0.508 -0.508)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.508 0.508)
            (xy -0.508 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 0.508)
            (xy 0 -0.508)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.508 -0.508)
            (xy 0.508 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
        (arc (start 1.016 0) (mid 0.762 0.254) (end 0.508 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 1.016 0) (mid 1.27 -0.254) (end 1.524 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "VAM_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VDC" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VDC" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "dc(1)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, DC" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VDC_0_0"
        (polyline
          (pts
            (xy -1.27 0.254)
            (xy 1.27 0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.254)
            (xy -1.27 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.254 -0.254)
            (xy -0.254 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.254)
            (xy 0.762 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VDC_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VDC_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VEXP" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VEXP" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "exp(0 1 2n 30n 60n 40n)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, exponential" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VEXP_0_0"
        (polyline
          (pts
            (xy -1.27 -0.762)
            (xy -1.778 -0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.762)
            (xy 1.778 -0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0.762) (mid -0.851 0.1794) (end -1.27 -0.762)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0.762) (mid 0.3591 -0.2299) (end 1.27 -0.762)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VEXP_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VEXP_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VPULSE" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VPULSE" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "pulse(0 1 2n 2n 2n 50n 100n)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, pulse" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VPULSE_0_0"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy -1.397 -0.762)
            (xy -1.143 0.762)
            (xy -0.127 0.762)
            (xy 0.127 -0.762)
            (xy 1.143 -0.762)
            (xy 1.397 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VPULSE_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VPULSE_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VSIN" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VSIN" (id 1) (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "sin(0 1 1k)" (id 6) (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (id 7) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, sinusoidal" (id 8) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VSIN_0_0"
        (arc (start 0 0) (mid -0.635 0.635) (end -1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0) (mid 0.635 -0.635) (end 1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VSIN_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VSIN_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (id 0) (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (id 1) (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )


  (wire (pts (xy 76.2 127) (xy 76.2 129.54))
    (stroke (width 0) (type default))
    (uuid 028c6938-0993-4248-8a82-5e0fd5ffdea0)
  )
  (wire (pts (xy 139.7 60.96) (xy 139.7 62.23))
    (stroke (width 0) (type default))
    (uuid 0c0fe282-fb9a-4ebe-902c-3436ae15ae7a)
  )
  (wire (pts (xy 76.2 111.76) (xy 76.2 113.03))
    (stroke (width 0) (type default))
    (uuid 109f7d05-3943-47cd-9dab-adebcd695dc0)
  )
  (wire (pts (xy 88.9 25.4) (xy 88.9 26.67))
    (stroke (width 0) (type default))
    (uuid 11105a07-f174-4045-a151-8cc05cf2cd25)
  )
  (wire (pts (xy 76.2 76.2) (xy 76.2 78.74))
    (stroke (width 0) (type default))
    (uuid 137dac01-7f5c-48e9-ab94-25be00d66629)
  )
  (wire (pts (xy 139.7 101.6) (xy 139.7 104.14))
    (stroke (width 0) (type default))
    (uuid 18cc687e-56a5-4eac-9d6e-e1295ba57b86)
  )
  (wire (pts (xy 76.2 86.36) (xy 76.2 87.63))
    (stroke (width 0) (type default))
    (uuid 1f7438a6-e13f-4b21-84a4-35652544ae07)
  )
  (wire (pts (xy 88.9 50.8) (xy 139.7 50.8))
    (stroke (width 0) (type default))
    (uuid 2b5a7b59-7f96-4ad6-8e58-530601a023cc)
  )
  (wire (pts (xy 76.2 101.6) (xy 76.2 104.14))
    (stroke (width 0) (type default))
    (uuid 32ac05f5-b31a-455b-a8e7-989fe772a077)
  )
  (wire (pts (xy 25.4 101.6) (xy 25.4 102.87))
    (stroke (width 0) (type default))
    (uuid 358f4bfd-0e03-453a-a96e-ff2c9f4727ce)
  )
  (wire (pts (xy 139.7 25.4) (xy 139.7 27.94))
    (stroke (width 0) (type default))
    (uuid 3d0252e4-bdc5-45a1-8f15-7e16c11107b4)
  )
  (wire (pts (xy 139.7 35.56) (xy 139.7 36.83))
    (stroke (width 0) (type default))
    (uuid 416b5fea-8c8e-495e-98e0-4f3a2f782b5b)
  )
  (wire (pts (xy 76.2 50.8) (xy 76.2 53.34))
    (stroke (width 0) (type default))
    (uuid 4a865f4e-cc4b-435d-9c80-1154a0b5d2e0)
  )
  (wire (pts (xy 76.2 137.16) (xy 76.2 138.43))
    (stroke (width 0) (type default))
    (uuid 53066e60-c6ea-4853-9387-d6e1e3835812)
  )
  (wire (pts (xy 25.4 76.2) (xy 76.2 76.2))
    (stroke (width 0) (type default))
    (uuid 5bb1bc40-0505-4589-8bec-32f6f0a57a34)
  )
  (wire (pts (xy 25.4 25.4) (xy 25.4 26.67))
    (stroke (width 0) (type default))
    (uuid 5dd78378-313c-49df-9ffe-101aed3939ba)
  )
  (wire (pts (xy 88.9 76.2) (xy 88.9 77.47))
    (stroke (width 0) (type default))
    (uuid 5dfbde72-a864-4faf-b4b2-c025c488827f)
  )
  (wire (pts (xy 139.7 111.76) (xy 139.7 113.03))
    (stroke (width 0) (type default))
    (uuid *************-4fe9-a181-9198633b73ad)
  )
  (wire (pts (xy 88.9 50.8) (xy 88.9 52.07))
    (stroke (width 0) (type default))
    (uuid 6946bab2-5cf1-45ae-95d4-3a3f79f7f45d)
  )
  (wire (pts (xy 25.4 127) (xy 25.4 128.27))
    (stroke (width 0) (type default))
    (uuid 8c53f180-b466-463f-857d-432d7c7ac137)
  )
  (wire (pts (xy 139.7 86.36) (xy 139.7 87.63))
    (stroke (width 0) (type default))
    (uuid 9e232d1a-9f1a-4194-a902-5ed9a2d30780)
  )
  (wire (pts (xy 76.2 25.4) (xy 76.2 27.94))
    (stroke (width 0) (type default))
    (uuid 9e8ff586-132f-45a7-9bc6-ad6a124a1656)
  )
  (wire (pts (xy 88.9 25.4) (xy 139.7 25.4))
    (stroke (width 0) (type default))
    (uuid aa9cad90-fd91-46a1-b9e4-6f387c090a56)
  )
  (wire (pts (xy 139.7 50.8) (xy 139.7 53.34))
    (stroke (width 0) (type default))
    (uuid b5ed141e-4a6e-480b-9274-7051b072927f)
  )
  (wire (pts (xy 88.9 101.6) (xy 139.7 101.6))
    (stroke (width 0) (type default))
    (uuid b63b5678-51c3-4350-acf6-fff0c1c1e0cb)
  )
  (wire (pts (xy 25.4 101.6) (xy 76.2 101.6))
    (stroke (width 0) (type default))
    (uuid bbec9f00-b0b6-4e61-8b02-b295dc39960e)
  )
  (wire (pts (xy 25.4 50.8) (xy 25.4 52.07))
    (stroke (width 0) (type default))
    (uuid bde1f230-21c5-4c07-a4cd-6ab13675d880)
  )
  (wire (pts (xy 76.2 60.96) (xy 76.2 62.23))
    (stroke (width 0) (type default))
    (uuid c182574e-a626-4718-bc2d-731bdc7fa75d)
  )
  (wire (pts (xy 76.2 35.56) (xy 76.2 36.83))
    (stroke (width 0) (type default))
    (uuid cebfa657-110a-4ffc-9cd3-3c17258ad0fb)
  )
  (wire (pts (xy 25.4 25.4) (xy 76.2 25.4))
    (stroke (width 0) (type default))
    (uuid d247f77f-93a8-4d45-9721-62da982b1c6b)
  )
  (wire (pts (xy 88.9 101.6) (xy 88.9 102.87))
    (stroke (width 0) (type default))
    (uuid d5654d44-46f0-473a-87dd-1074c856de36)
  )
  (wire (pts (xy 25.4 127) (xy 76.2 127))
    (stroke (width 0) (type default))
    (uuid d77ff81f-6059-4570-91e0-b812e168b3dc)
  )
  (wire (pts (xy 25.4 76.2) (xy 25.4 77.47))
    (stroke (width 0) (type default))
    (uuid e01910ef-df16-4108-ab06-25db9e6c027e)
  )
  (wire (pts (xy 88.9 76.2) (xy 139.7 76.2))
    (stroke (width 0) (type default))
    (uuid e48cd23a-b439-42ce-94ec-2dc2e52aa101)
  )
  (wire (pts (xy 139.7 76.2) (xy 139.7 78.74))
    (stroke (width 0) (type default))
    (uuid e52ee726-121e-421a-9c76-b1d8e06ff5a2)
  )
  (wire (pts (xy 25.4 50.8) (xy 76.2 50.8))
    (stroke (width 0) (type default))
    (uuid ea8bcbf5-5c2c-44e7-98cd-0fced2e4e345)
  )

  (symbol (lib_id "power:GND") (at 76.2 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 127c6869-e53c-4a8f-bb84-075e336124e9)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 865dbd5b-2462-4259-87fa-07e8d57338bb))
  )

  (symbol (lib_id "Simulation_SPICE:VSIN") (at 25.4 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 162dd4fd-6b0c-4c88-9d11-dfd2cf5af61f)
    (default_instance (reference "V") (unit 1) (value "VSIN") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 55.245 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VSIN" (id 1) (at 29.21 57.785 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b0709f45-82fa-4eb5-81f8-914f50317636))
    (pin "2" (uuid c3453967-d074-4cc2-9a43-eec70062d515))
  )

  (symbol (lib_id "Device:R") (at 76.2 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 183b6ba4-3657-4b16-8996-abd81eff6890)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 56.515 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 59.055 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 57.15 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d635c9ad-9314-4ae4-98e0-c89730125f77))
    (pin "2" (uuid 3b78e370-b246-4148-9537-daea197778e0))
  )

  (symbol (lib_id "power:GND") (at 139.7 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 2b8ae715-294b-4d1b-8930-56ebe519569d)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b1b8ab09-0ad4-4779-a7d9-538e04564c83))
  )

  (symbol (lib_id "Device:R") (at 139.7 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 2cb952de-7eb1-4747-ae80-3e8dcefdf7b8)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 56.515 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 59.055 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 57.15 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 307b8e9b-66c1-4fbc-a363-807fee4c3c11))
    (pin "2" (uuid 1f637481-6f82-411d-8891-2624843f64d4))
  )

  (symbol (lib_id "power:GND") (at 139.7 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 2d43b4ca-185e-47a6-a2b2-d3e67a2155a7)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3375af0c-ff20-484e-bf63-159d22e8dae1))
  )

  (symbol (lib_id "Device:R") (at 76.2 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 2d99cf32-c26c-4a2f-8c01-427fabe7f976)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 107.95 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b98b4ded-3960-41d0-8d6d-8cf44e45a9d9))
    (pin "2" (uuid 36b661ef-1901-4914-a6e6-c917fa7fd27b))
  )

  (symbol (lib_id "Device:R") (at 139.7 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 3926526d-6bdf-4df1-b6ca-e1ed30b5305b)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 81.915 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 84.455 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 82.55 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 11572ec7-121b-4b0e-b3fb-a3d720d62082))
    (pin "2" (uuid 62e9cf51-dda3-431e-ade4-d0ba172eb6d0))
  )

  (symbol (lib_id "power:GND") (at 76.2 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 3b6aa53c-9a95-4c84-90db-32d8028b844d)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f26363ad-a99e-4f84-904b-e4a709ae8f71))
  )

  (symbol (lib_id "Simulation_SPICE:ISIN") (at 88.9 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 4a11c031-0cff-43e4-b00d-ffd7533c6b4c)
    (default_instance (reference "I") (unit 1) (value "ISIN") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 55.245 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "ISIN" (id 1) (at 92.71 57.785 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 69ef791b-1979-4fde-b83c-471cb92caf57))
    (pin "2" (uuid fadc8087-627b-48e1-9cd9-************))
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 25.4 31.75 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 4a13630f-aae8-4aa8-b67e-b09bc4077213)
    (default_instance (reference "V") (unit 1) (value "VDC") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 29.845 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VDC" (id 1) (at 29.21 32.385 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 36b46520-4f81-40de-8d75-aad344d9d805))
    (pin "2" (uuid e70914e9-dede-41bf-a8a0-b9315978f18a))
  )

  (symbol (lib_id "power:GND") (at 139.7 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 4b3bd156-df27-4707-b782-364ea4daec71)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 121b9b69-6264-4e78-8876-42871e0b0fd5))
  )

  (symbol (lib_id "Simulation_SPICE:IPULSE") (at 88.9 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 530cae72-e62d-4472-a544-cf6e7504a547)
    (default_instance (reference "I") (unit 1) (value "IPULSE") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 80.645 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IPULSE" (id 1) (at 92.71 83.185 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5f53eb4d-b42a-4ffd-be9d-b89c771d7a54))
    (pin "2" (uuid 90f78bc7-5d64-40c7-bde9-6ee2bed32215))
  )

  (symbol (lib_id "Device:R") (at 76.2 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 59c377ca-586c-4d47-8389-118b72e33ab9)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 81.915 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 84.455 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 82.55 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c39116b0-12c9-4d05-b5dd-6144ac4623b3))
    (pin "2" (uuid ba346ec9-ab94-493e-808c-90e051dc3412))
  )

  (symbol (lib_id "Simulation_SPICE:IEXP") (at 88.9 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 5b2fd2dc-d066-4c39-8bc9-ea8e365975ae)
    (default_instance (reference "I") (unit 1) (value "IEXP") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 106.045 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IEXP" (id 1) (at 92.71 108.585 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5355afb3-2ba4-4480-afbe-0ce0324c0e77))
    (pin "2" (uuid 7ff6cc4d-cdb3-4fb1-bd2d-a3ac7bcaab9e))
  )

  (symbol (lib_id "Device:R") (at 139.7 31.75 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 5bd5c24b-684f-4add-8e04-b731deb77ff7)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 31.115 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 33.655 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 31.75 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 4441eec1-1bdd-4f51-bbfa-ffc16e5eb05b))
    (pin "2" (uuid 55c6bd68-8d14-42b5-a0b9-a3cdda47a276))
  )

  (symbol (lib_id "power:GND") (at 76.2 138.43 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 72571608-d69f-4ba1-82c8-************)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 144.78 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 143.51 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8986cf06-79cb-4335-8440-a6d98cb2cb3d))
  )

  (symbol (lib_id "power:GND") (at 88.9 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 74654e3a-e2bb-42f4-9b7b-9d294fd6386e)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 89be6608-7ebd-413e-84ce-e99c0f3f7036))
  )

  (symbol (lib_id "power:GND") (at 25.4 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 78e47da6-e06f-44cd-a293-3d24fc58787d)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid dcb87709-7689-4d8f-a1b2-7f1246aac0bd))
  )

  (symbol (lib_id "power:GND") (at 25.4 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 8ead6375-404c-4c81-ad7a-987ea2c4d03d)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 78bad74c-e5c3-41da-a618-c1f8a6af808f))
  )

  (symbol (lib_id "power:GND") (at 88.9 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 96b63841-de75-42e3-b7ed-d987ad24c00f)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1d892116-42a5-459e-9aa7-8cde0a6d5915))
  )

  (symbol (lib_id "power:GND") (at 76.2 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid a4b6db34-c852-47c5-8cdd-e8801ad1c485)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 9d260b42-ad17-4f2e-9889-d6868bcd889c))
  )

  (symbol (lib_id "power:GND") (at 76.2 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid a54fb244-ba0f-4464-aebd-33800b8b27be)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 76.2 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 76.2 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 76.2 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 76.2 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid bc28fe56-f534-4ed1-ae33-a2388a98648c))
  )

  (symbol (lib_id "power:GND") (at 88.9 113.03 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid a8a8d31d-dc05-4c9f-8748-a64d133cadb1)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 119.38 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 118.11 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7dd7c82a-e14a-41df-91f8-41fd31e10f32))
  )

  (symbol (lib_id "Simulation_SPICE:VEXP") (at 25.4 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid b2411a13-3e8d-4353-8659-2d857db7af3d)
    (default_instance (reference "V") (unit 1) (value "VEXP") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VEXP" (id 1) (at 29.21 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid cfc0a1a4-06fe-4baa-bc18-76bc2ac42867))
    (pin "2" (uuid 55540642-5d12-42a0-985d-8b664e394ff4))
  )

  (symbol (lib_id "Device:R") (at 76.2 31.75 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid b6ec050d-c595-459d-a9f4-99286f86bde8)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 31.115 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 33.655 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 31.75 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c61c0cb9-3773-41b6-8d83-376aca3c1373))
    (pin "2" (uuid b2d65b45-cc42-44c5-8ac1-44ce92522175))
  )

  (symbol (lib_id "Simulation_SPICE:VPULSE") (at 25.4 82.55 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid b7c3917c-3f65-47ba-9428-cd2c6af6c0a0)
    (default_instance (reference "V") (unit 1) (value "VPULSE") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 80.645 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VPULSE" (id 1) (at 29.21 83.185 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 82.55 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6f03fd92-1628-4882-8b4e-2b3c90883e8e))
    (pin "2" (uuid b4436efa-ed17-45e3-bd96-b5f12763923e))
  )

  (symbol (lib_id "power:GND") (at 139.7 36.83 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid bc458ec0-a49c-4827-848d-cfd66e255f12)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 139.7 43.18 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 139.7 41.91 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 139.7 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 139.7 36.83 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8c07c4ee-e25f-478d-a915-4c03834851ef))
  )

  (symbol (lib_id "Simulation_SPICE:IDC") (at 88.9 31.75 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid c56bb3f4-658b-426f-95a6-42baa2464677)
    (default_instance (reference "I") (unit 1) (value "IDC") (footprint ""))
    (property "Reference" "I" (id 0) (at 92.71 29.845 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "IDC" (id 1) (at 92.71 32.385 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 88.9 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 88.9 31.75 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid fa213b16-0b6d-468e-8962-b60168a90f61))
    (pin "2" (uuid 869d1d17-0c14-4d04-a1a4-6fe425bcf57d))
  )

  (symbol (lib_id "power:GND") (at 88.9 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid d6a3c3ac-aab0-4596-a5a7-df9f31d8935c)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 88.9 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 88.9 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 88.9 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 88.9 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b4177792-6657-4e2b-bbc4-70b996757b7f))
  )

  (symbol (lib_id "power:GND") (at 25.4 87.63 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid dddca9c8-1d32-4357-85ee-7242407d9f0e)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 92.71 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 51be085b-39ea-4262-9cb6-fac0c305b4b3))
  )

  (symbol (lib_id "power:GND") (at 25.4 138.43 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid eb4ac000-6296-4f85-9652-7020045d4017)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 144.78 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 143.51 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 138.43 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7812269a-0e62-4536-9f6c-73bfe31d1ae0))
  )

  (symbol (lib_id "Device:R") (at 76.2 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid ed8d1d5a-1962-4286-8cce-b4277960ea0b)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 78.74 132.715 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 78.74 135.255 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 74.422 133.35 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 76.2 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 8049ba31-d508-4844-b74a-0b36d5464e8a))
    (pin "2" (uuid 950f2c97-6058-46f5-92a5-79340f364351))
  )

  (symbol (lib_id "Simulation_SPICE:VAM") (at 25.4 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid edcc68da-c6e7-4292-bb9f-b3c5ceeaff94)
    (default_instance (reference "V") (unit 1) (value "VAM") (footprint ""))
    (property "Reference" "V" (id 0) (at 29.21 131.445 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VAM" (id 1) (at 29.21 133.985 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 25.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 25.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 4) (at 25.4 133.35 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 25.4 133.35 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Spice_Model" "am(1 0 100k 1k 1n)" (id 6) (at 29.21 136.525 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (pin "1" (uuid 52e9fb06-7ea9-42dc-b52f-3e9a328ed912))
    (pin "2" (uuid 01a75fe8-1d81-45dc-9ea9-47222c283f9a))
  )

  (symbol (lib_id "power:GND") (at 25.4 62.23 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid f42fd0ec-cf74-4d08-90b5-a959b1ecbd18)
    (default_instance (reference "#PWR") (unit 1) (value "GND") (footprint ""))
    (property "Reference" "#PWR" (id 0) (at 25.4 68.58 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 25.4 67.31 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 25.4 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 25.4 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid ec00c1d1-919f-41d1-b7f5-7fecf24cf839))
  )

  (symbol (lib_id "Device:R") (at 139.7 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid fc0d6ea0-e468-4260-9fe0-aa14c6cfb400)
    (default_instance (reference "R") (unit 1) (value "R") (footprint ""))
    (property "Reference" "R" (id 0) (at 142.24 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 142.24 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 137.922 107.95 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 139.7 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 2d776c9f-58ea-4353-9773-11cc3ca56a4c))
    (pin "2" (uuid f2443325-015b-4f8e-8a11-80366e3c4f5a))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/78e47da6-e06f-44cd-a293-3d24fc58787d"
      (reference "#PWR01") (unit 1) (value "GND") (footprint "")
    )
    (path "/127c6869-e53c-4a8f-bb84-075e336124e9"
      (reference "#PWR02") (unit 1) (value "GND") (footprint "")
    )
    (path "/96b63841-de75-42e3-b7ed-d987ad24c00f"
      (reference "#PWR03") (unit 1) (value "GND") (footprint "")
    )
    (path "/bc458ec0-a49c-4827-848d-cfd66e255f12"
      (reference "#PWR04") (unit 1) (value "GND") (footprint "")
    )
    (path "/f42fd0ec-cf74-4d08-90b5-a959b1ecbd18"
      (reference "#PWR05") (unit 1) (value "GND") (footprint "")
    )
    (path "/3b6aa53c-9a95-4c84-90db-32d8028b844d"
      (reference "#PWR06") (unit 1) (value "GND") (footprint "")
    )
    (path "/d6a3c3ac-aab0-4596-a5a7-df9f31d8935c"
      (reference "#PWR07") (unit 1) (value "GND") (footprint "")
    )
    (path "/2d43b4ca-185e-47a6-a2b2-d3e67a2155a7"
      (reference "#PWR08") (unit 1) (value "GND") (footprint "")
    )
    (path "/dddca9c8-1d32-4357-85ee-7242407d9f0e"
      (reference "#PWR09") (unit 1) (value "GND") (footprint "")
    )
    (path "/a4b6db34-c852-47c5-8cdd-e8801ad1c485"
      (reference "#PWR010") (unit 1) (value "GND") (footprint "")
    )
    (path "/74654e3a-e2bb-42f4-9b7b-9d294fd6386e"
      (reference "#PWR011") (unit 1) (value "GND") (footprint "")
    )
    (path "/4b3bd156-df27-4707-b782-364ea4daec71"
      (reference "#PWR012") (unit 1) (value "GND") (footprint "")
    )
    (path "/8ead6375-404c-4c81-ad7a-987ea2c4d03d"
      (reference "#PWR013") (unit 1) (value "GND") (footprint "")
    )
    (path "/a54fb244-ba0f-4464-aebd-33800b8b27be"
      (reference "#PWR014") (unit 1) (value "GND") (footprint "")
    )
    (path "/a8a8d31d-dc05-4c9f-8748-a64d133cadb1"
      (reference "#PWR015") (unit 1) (value "GND") (footprint "")
    )
    (path "/2b8ae715-294b-4d1b-8930-56ebe519569d"
      (reference "#PWR016") (unit 1) (value "GND") (footprint "")
    )
    (path "/eb4ac000-6296-4f85-9652-7020045d4017"
      (reference "#PWR017") (unit 1) (value "GND") (footprint "")
    )
    (path "/72571608-d69f-4ba1-82c8-************"
      (reference "#PWR018") (unit 1) (value "GND") (footprint "")
    )
    (path "/c56bb3f4-658b-426f-95a6-42baa2464677"
      (reference "IDC") (unit 1) (value "dc=1") (footprint "")
    )
    (path "/5b2fd2dc-d066-4c39-8bc9-ea8e365975ae"
      (reference "IEXP") (unit 1) (value "v2=1 td1=1m tau1=500u td2=1m tau2=500u") (footprint "")
    )
    (path "/530cae72-e62d-4472-a544-cf6e7504a547"
      (reference "IPULSE") (unit 1) (value "v2=1 td=1m tr=1u tf=1u tw=1m per=2m") (footprint "")
    )
    (path "/4a11c031-0cff-43e4-b00d-ffd7533c6b4c"
      (reference "ISIN") (unit 1) (value "ampl=1 f=1k td=1m") (footprint "")
    )
    (path "/b6ec050d-c595-459d-a9f4-99286f86bde8"
      (reference "R1") (unit 1) (value "100") (footprint "")
    )
    (path "/5bd5c24b-684f-4add-8e04-b731deb77ff7"
      (reference "R2") (unit 1) (value "100") (footprint "")
    )
    (path "/183b6ba4-3657-4b16-8996-abd81eff6890"
      (reference "R3") (unit 1) (value "100") (footprint "")
    )
    (path "/2cb952de-7eb1-4747-ae80-3e8dcefdf7b8"
      (reference "R4") (unit 1) (value "100") (footprint "")
    )
    (path "/59c377ca-586c-4d47-8389-118b72e33ab9"
      (reference "R5") (unit 1) (value "100") (footprint "")
    )
    (path "/3926526d-6bdf-4df1-b6ca-e1ed30b5305b"
      (reference "R6") (unit 1) (value "100") (footprint "")
    )
    (path "/2d99cf32-c26c-4a2f-8c01-427fabe7f976"
      (reference "R7") (unit 1) (value "100") (footprint "")
    )
    (path "/fc0d6ea0-e468-4260-9fe0-aa14c6cfb400"
      (reference "R8") (unit 1) (value "100") (footprint "")
    )
    (path "/ed8d1d5a-1962-4286-8cce-b4277960ea0b"
      (reference "R9") (unit 1) (value "100") (footprint "")
    )
    (path "/edcc68da-c6e7-4292-bb9f-b3c5ceeaff94"
      (reference "V1") (unit 1) (value "VAM") (footprint "")
    )
    (path "/4a13630f-aae8-4aa8-b67e-b09bc4077213"
      (reference "VDC") (unit 1) (value "dc=1") (footprint "")
    )
    (path "/b2411a13-3e8d-4353-8659-2d857db7af3d"
      (reference "VEXP") (unit 1) (value "v2=1 td1=1m tau1=500u td2=1m tau2=500u") (footprint "")
    )
    (path "/b7c3917c-3f65-47ba-9428-cd2c6af6c0a0"
      (reference "VPULSE") (unit 1) (value "v2=1 td=1m tr=1u tf=1u tw=1m per=2m") (footprint "")
    )
    (path "/162dd4fd-6b0c-4c88-9d11-dfd2cf5af61f"
      (reference "VSIN") (unit 1) (value "ampl=1 f=1k td=1m") (footprint "")
    )
  )
)
