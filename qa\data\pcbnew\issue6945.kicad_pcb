(kicad_pcb (version 20210722) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.<PERSON>hes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (color "Green") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "core") (thickness 1.51) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (color "Green") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "None")
      (dielectric_constraints no)
    )
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (svguseinch false)
      (svgprecision 6)
      (excludeedgelayer true)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "Net-(C1-Pad1)")
  (net 2 "GND")

  (footprint "MountingHole:MountingHole_3.2mm_M3_DIN965_Pad" (layer "F.Cu")
    (tedit 56D1B4CB) (tstamp 7a5e0185-cb99-4816-a212-1cb5fdda6fe8)
    (at 186.25 88.75)
    (descr "Mounting Hole 3.2mm, M3, DIN965")
    (tags "mounting hole 3.2mm m3 din965")
    (property "Sheet file" "Test.kicad_sch")
    (property "Sheet name" "")
    (path "/fb2e017d-a5c5-49fb-ba95-cb396aae3918")
    (attr exclude_from_pos_files)
    (fp_text reference "H3" (at 0 4) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 600b29e3-3ea7-48fd-a954-b81d7d21815f)
    )
    (fp_text value "PTH-3mm" (at 0 3.8) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 982881b5-77b1-4780-b97a-b48fc7c793a6)
    )
    (fp_text user "${REFERENCE}" (at 0.3 0) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 1710c190-e24b-4590-9dd6-c08f5a3e6148)
    )
    (fp_circle (center 0 0) (end 2.8 0) (layer "Cmts.User") (width 0.15) (fill none) (tstamp 533cb978-4c67-4904-86ed-173bc2ff1b45))
    (fp_circle (center 0 0) (end 3.05 0) (layer "F.CrtYd") (width 0.05) (fill none) (tstamp c08b7dbb-612b-43e9-a6cc-d4e66c1ea404))
    (pad "1" thru_hole circle locked (at 0 0) (size 5.6 5.6) (drill 3.2) (layers *.Cu *.Mask) (tstamp cfbc907d-8a25-46e9-a224-6b8152fa214f))
  )

  (footprint "MountingHole:MountingHole_3.2mm_M3_DIN965_Pad" (layer "F.Cu")
    (tedit 56D1B4CB) (tstamp 7effd7e7-a352-4b60-90dc-d04da9924f3e)
    (at 166.25 88.75)
    (descr "Mounting Hole 3.2mm, M3, DIN965")
    (tags "mounting hole 3.2mm m3 din965")
    (property "Sheet file" "Test.kicad_sch")
    (property "Sheet name" "")
    (path "/01326b80-c3a1-4c72-b881-eee45cdb68c5")
    (attr exclude_from_pos_files)
    (fp_text reference "H4" (at 0 4) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 3dfd39dc-326f-4ebf-a42f-a5544836a15a)
    )
    (fp_text value "PTH-3mm" (at 0 3.8) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp bbec47e3-9ffa-4d68-9bd8-d2c50951193f)
    )
    (fp_text user "${REFERENCE}" (at 0.3 0) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ba466aa3-f900-4724-b154-de8778c10cec)
    )
    (fp_circle (center 0 0) (end 2.8 0) (layer "Cmts.User") (width 0.15) (fill none) (tstamp 7f8e2e3b-3a66-47a9-a58a-28516317d286))
    (fp_circle (center 0 0) (end 3.05 0) (layer "F.CrtYd") (width 0.05) (fill none) (tstamp ee76c024-e387-4561-9d80-268c0015a52b))
    (pad "1" thru_hole circle locked (at 0 0) (size 5.6 5.6) (drill 3.2) (layers *.Cu *.Mask) (tstamp b5c3eb60-d01c-403e-abf7-cd1ddd2c14be))
  )

  (footprint "TestPoint:TestPoint_Pad_D1.0mm" (layer "F.Cu")
    (tedit 5A0F774F) (tstamp 88c05984-d2ca-4f92-8721-280a6a4f9613)
    (at 170.9 97.25)
    (descr "SMD pad as test Point, diameter 1.0mm")
    (tags "test point SMD pad")
    (property "Sheet file" "Test.kicad_sch")
    (property "Sheet name" "")
    (path "/22d401bd-63a1-4770-a508-b1b48b6cefcb")
    (attr exclude_from_pos_files)
    (fp_text reference "TP2" (at 0 -1.448) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 95aa5855-69ff-4482-ac57-7409361e9f0c)
    )
    (fp_text value "1mm" (at 0 1.55) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 77c0d172-dc11-4bcd-aac2-ff44367ce90a)
    )
    (fp_text user "${REFERENCE}" (at 0 -1.45) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp e99f1170-d41e-48e3-a59a-ad2865e9b09f)
    )
    (fp_circle (center 0 0) (end 0 0.7) (layer "F.SilkS") (width 0.12) (fill none) (tstamp 8b0d7507-fb4d-4872-a9e9-8e34f9f7d611))
    (fp_circle (center 0 0) (end 1 0) (layer "F.CrtYd") (width 0.05) (fill none) (tstamp cfb52abb-7736-4b9e-992e-1b57ed23e569))
    (pad "1" smd circle locked (at 0 0) (size 1 1) (layers "F.Cu" "F.Mask")
      (net 2 "GND") (pinfunction "1") (tstamp 3c07ca65-23ee-4d7e-ba6d-d99fadda8a85))
  )

  (footprint "TestPoint:TestPoint_Pad_D1.0mm" (layer "F.Cu")
    (tedit 5A0F774F) (tstamp 96f153b4-cd24-4b26-bc7a-d42fcdb48bb1)
    (at 171.8 102.25)
    (descr "SMD pad as test Point, diameter 1.0mm")
    (tags "test point SMD pad")
    (property "Sheet file" "Test.kicad_sch")
    (property "Sheet name" "")
    (path "/e26921cb-9238-4343-b11c-78d669aa4c42")
    (attr exclude_from_pos_files)
    (fp_text reference "TP1" (at 0 -1.448) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 06893409-635f-4a9b-b9cb-9a19061173ac)
    )
    (fp_text value "1mm" (at 0 1.55) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 9738c967-6430-439c-862d-139eddba34c9)
    )
    (fp_text user "${REFERENCE}" (at 0 -1.45) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 4d8e8d09-5585-4a2c-8d4a-538b8cb0b948)
    )
    (fp_circle (center 0 0) (end 0 0.7) (layer "F.SilkS") (width 0.12) (fill none) (tstamp 8034cd09-52c3-4f62-bd1f-fd4ca1bf676e))
    (fp_circle (center 0 0) (end 1 0) (layer "F.CrtYd") (width 0.05) (fill none) (tstamp 1ff92f25-6dd7-4eae-9d62-94d1b7101e9d))
    (pad "1" smd circle locked (at 0 0) (size 1 1) (layers "F.Cu" "F.Mask")
      (net 1 "Net-(C1-Pad1)") (pinfunction "1") (tstamp 4fd153e0-67a6-416a-9342-e114d60e8da3))
  )

  (footprint "MountingHole:MountingHole_3.2mm_M3" (layer "F.Cu")
    (tedit 56D1B4CB) (tstamp b155e8e6-593a-4fdd-b7be-b9cbbe47b854)
    (at 166.25 111)
    (descr "Mounting Hole 3.2mm, no annular, M3")
    (tags "mounting hole 3.2mm no annular m3")
    (property "Sheet file" "Test.kicad_sch")
    (property "Sheet name" "")
    (path "/9c1f6247-3f70-4646-bb3c-a8c45bbfc056")
    (attr exclude_from_pos_files)
    (fp_text reference "H2" (at 0 -4.2) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ccc0899b-bbf2-4044-9984-8b8b6c30c83f)
    )
    (fp_text value "NPTH-3mm" (at 0 4.2) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b473fa3b-3b92-4646-b990-3a686d714123)
    )
    (fp_text user "${REFERENCE}" (at 0.3 0) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp d71db0be-3896-4a51-beac-40768d8d3ef1)
    )
    (fp_circle (center 0 0) (end 3.2 0) (layer "Cmts.User") (width 0.15) (fill none) (tstamp d4901f12-4dae-4cd4-9bb1-5d962ba4e57d))
    (fp_circle (center 0 0) (end 3.45 0) (layer "F.CrtYd") (width 0.05) (fill none) (tstamp 3d371e65-4ed5-435b-8cfc-b2bca6d607db))
    (pad "1" np_thru_hole circle locked (at 0 0) (size 3.2 3.2) (drill 3.2) (layers *.Cu *.Mask) (tstamp 30bed439-ee98-4fbc-9d75-6c7a47f1e483))
  )

  (footprint "MountingHole:MountingHole_3.2mm_M3" (layer "F.Cu")
    (tedit 56D1B4CB) (tstamp dd6e559d-f8e5-4209-a83a-cd7404d50c5d)
    (at 186.25 111)
    (descr "Mounting Hole 3.2mm, no annular, M3")
    (tags "mounting hole 3.2mm no annular m3")
    (property "Sheet file" "Test.kicad_sch")
    (property "Sheet name" "")
    (path "/e0c75322-6681-4b92-9d80-a5c2e61fb79f")
    (attr exclude_from_pos_files)
    (fp_text reference "H1" (at 0 -4.2) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 5b6ed842-a876-4bd0-8f81-70bdfe998dc6)
    )
    (fp_text value "NPTH-3mm" (at 0 4.2) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp fa284b56-4b5e-4ac6-bcab-b929f46c97c6)
    )
    (fp_text user "${REFERENCE}" (at 0.3 0) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 5054f0ca-f585-4ee1-a91e-658ca5387bf2)
    )
    (fp_circle (center 0 0) (end 3.2 0) (layer "Cmts.User") (width 0.15) (fill none) (tstamp ad1a6974-bb9c-4845-bf1b-4eb349a7b5f9))
    (fp_circle (center 0 0) (end 3.45 0) (layer "F.CrtYd") (width 0.05) (fill none) (tstamp 7495fc84-6ec6-45ca-9071-d10637312859))
    (pad "1" np_thru_hole circle locked (at 0 0) (size 3.2 3.2) (drill 3.2) (layers *.Cu *.Mask) (tstamp 3c6562ef-6330-45e0-b952-0a82c3116b50))
  )

  (footprint "Connector_JST:JST_XH_B2B-XH-A_1x02_P2.50mm_Vertical" (layer "F.Cu")
    (tedit 5C28146C) (tstamp e918d824-8add-477a-bc72-c18e218465c7)
    (at 165.5 101.25 90)
    (descr "JST XH series connector, B2B-XH-A (http://www.jst-mfg.com/product/pdf/eng/eXH.pdf), generated with kicad-footprint-generator")
    (tags "connector JST XH vertical")
    (property "LCSC N°" "C20079")
    (property "N°" "XH-2A")
    (property "Sheet file" "Test.kicad_sch")
    (property "Sheet name" "")
    (path "/73dee3db-4043-47b4-b8c7-6ed59b861244")
    (attr through_hole)
    (fp_text reference "J1" (at -3.75 -2 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f9009f25-1208-4105-9f60-1e769861c0b9)
    )
    (fp_text value "XH-2A" (at 1.25 4.6 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 5addb007-8ec3-4029-b4b3-109887a8afbf)
    )
    (fp_text user "${REFERENCE}" (at 1.25 2.7 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp aa56998b-ed2f-428f-b788-3cb40902350a)
    )
    (fp_line (start 1.75 -1.7) (end 1.75 -2.45) (layer "F.SilkS") (width 0.12) (tstamp 0adc786e-c434-4492-a65f-ea2dbf023bbb))
    (fp_line (start 3.25 -2.45) (end 3.25 -1.7) (layer "F.SilkS") (width 0.12) (tstamp 0d7ef226-df0e-4ed6-9909-f4b0f354bab3))
    (fp_line (start -0.75 -1.7) (end -0.75 -2.45) (layer "F.SilkS") (width 0.12) (tstamp 160e3aa7-3701-49c7-ad98-92f1e15d11f8))
    (fp_line (start -0.75 -2.45) (end -2.55 -2.45) (layer "F.SilkS") (width 0.12) (tstamp 2b7bbe5f-c126-44cb-b4b1-e244ebde95ee))
    (fp_line (start 5.06 3.51) (end 5.06 -2.46) (layer "F.SilkS") (width 0.12) (tstamp 3a2be3ae-5b9f-4985-b114-a1d73118bdd5))
    (fp_line (start 4.3 2.75) (end 1.25 2.75) (layer "F.SilkS") (width 0.12) (tstamp 4e612ece-fe1a-4dc9-bcaa-56144fb56ae7))
    (fp_line (start 1.75 -2.45) (end 0.75 -2.45) (layer "F.SilkS") (width 0.12) (tstamp 693f5d3d-c257-4591-ba57-643f4dd8f12f))
    (fp_line (start -1.8 2.75) (end 1.25 2.75) (layer "F.SilkS") (width 0.12) (tstamp 72ff50c9-cd49-4a9d-95e2-0bddea9e2ec6))
    (fp_line (start -2.85 -2.75) (end -2.85 -1.5) (layer "F.SilkS") (width 0.12) (tstamp 8b531cb0-7ec5-4668-88c3-ed1f751d9ae0))
    (fp_line (start -2.56 3.51) (end 5.06 3.51) (layer "F.SilkS") (width 0.12) (tstamp 8db24d26-d584-4a2b-b885-53f2a17f7f23))
    (fp_line (start 5.05 -0.2) (end 4.3 -0.2) (layer "F.SilkS") (width 0.12) (tstamp 9ad31be3-f81c-4b40-93c6-8b9855b7b1e8))
    (fp_line (start 5.05 -1.7) (end 5.05 -2.45) (layer "F.SilkS") (width 0.12) (tstamp a2f9f4d8-e32c-4580-80ed-c6cc2fa36113))
    (fp_line (start 0.75 -2.45) (end 0.75 -1.7) (layer "F.SilkS") (width 0.12) (tstamp aac2db70-65e0-4898-bd39-c8b4fba038bd))
    (fp_line (start -2.55 -0.2) (end -1.8 -0.2) (layer "F.SilkS") (width 0.12) (tstamp aef9a042-b20f-4834-b164-e4cc1cb1f32b))
    (fp_line (start -1.6 -2.75) (end -2.85 -2.75) (layer "F.SilkS") (width 0.12) (tstamp b5f184ae-0e27-4509-8a1d-e7b96ae4c9fa))
    (fp_line (start 4.3 -0.2) (end 4.3 2.75) (layer "F.SilkS") (width 0.12) (tstamp cc8b3c58-18e6-4da7-a2a3-9ecde619a75c))
    (fp_line (start -2.56 -2.46) (end -2.56 3.51) (layer "F.SilkS") (width 0.12) (tstamp d1320845-83f4-4737-b44b-07cd86704024))
    (fp_line (start 3.25 -1.7) (end 5.05 -1.7) (layer "F.SilkS") (width 0.12) (tstamp d7823cab-9c8f-4dc0-9506-0a2a7b8fdae4))
    (fp_line (start 5.05 -2.45) (end 3.25 -2.45) (layer "F.SilkS") (width 0.12) (tstamp dc90412b-136f-45a5-a31f-0b39e3721120))
    (fp_line (start 5.06 -2.46) (end -2.56 -2.46) (layer "F.SilkS") (width 0.12) (tstamp e9fcc632-df34-4d57-8ec2-22abfbb30b49))
    (fp_line (start 0.75 -1.7) (end 1.75 -1.7) (layer "F.SilkS") (width 0.12) (tstamp ed07a464-ed7a-413f-8d50-8d9c57b9309e))
    (fp_line (start -2.55 -2.45) (end -2.55 -1.7) (layer "F.SilkS") (width 0.12) (tstamp f242d7ed-dd2b-4b02-8b66-0400b2ebf1ca))
    (fp_line (start -2.55 -1.7) (end -0.75 -1.7) (layer "F.SilkS") (width 0.12) (tstamp f63d17db-7403-4945-9e25-4449fa42e5d6))
    (fp_line (start -1.8 -0.2) (end -1.8 2.75) (layer "F.SilkS") (width 0.12) (tstamp fd2a834b-e8e8-4a59-91b4-ad4e82a6491f))
    (fp_line (start -2.95 3.9) (end 5.45 3.9) (layer "F.CrtYd") (width 0.05) (tstamp 4474e8cf-5c61-44f1-8ff7-1135bc46ae95))
    (fp_line (start -2.95 -2.85) (end -2.95 3.9) (layer "F.CrtYd") (width 0.05) (tstamp 8b55d4c2-87a8-403f-838c-e6b39d114ff0))
    (fp_line (start 5.45 -2.85) (end -2.95 -2.85) (layer "F.CrtYd") (width 0.05) (tstamp e1ec3d4c-ab08-4d7a-8324-c084d2f73cc2))
    (fp_line (start 5.45 3.9) (end 5.45 -2.85) (layer "F.CrtYd") (width 0.05) (tstamp feda8083-3526-4fc0-8620-a5e5ff75c5c7))
    (fp_line (start 0 -1.35) (end 0.625 -2.35) (layer "F.Fab") (width 0.1) (tstamp 049059f8-ba6f-4aa3-bf0e-92fba6efdda8))
    (fp_line (start -2.45 -2.35) (end -2.45 3.4) (layer "F.Fab") (width 0.1) (tstamp 2b5a54ae-a37d-4aff-a271-0f825a8e855d))
    (fp_line (start 4.95 -2.35) (end -2.45 -2.35) (layer "F.Fab") (width 0.1) (tstamp 757b9ed4-8089-4438-80e3-665859bf0e00))
    (fp_line (start -2.45 3.4) (end 4.95 3.4) (layer "F.Fab") (width 0.1) (tstamp b6e260db-b404-43d2-9d13-3d72bf702940))
    (fp_line (start -0.625 -2.35) (end 0 -1.35) (layer "F.Fab") (width 0.1) (tstamp d901e84b-f741-4b4a-93a0-440497371c23))
    (fp_line (start 4.95 3.4) (end 4.95 -2.35) (layer "F.Fab") (width 0.1) (tstamp e9b8a997-2bef-41da-8437-468ed47a22a5))
    (pad "1" thru_hole roundrect locked (at 0 0 90) (size 1.7 2) (drill 1) (layers *.Cu *.Mask) (roundrect_rratio 0.147059)
      (net 1 "Net-(C1-Pad1)") (pinfunction "Pin_1") (tstamp 3bbb4641-3942-4c40-a8f5-028930bd1924))
    (pad "2" thru_hole oval locked (at 2.5 0 90) (size 1.7 2) (drill 1) (layers *.Cu *.Mask)
      (net 2 "GND") (pinfunction "Pin_2") (tstamp 63fbe990-c88a-466e-a812-693fc90734fb))
    (model "${KISYS3DMOD}/Connector_JST.3dshapes/JST_XH_B2B-XH-A_1x02_P2.50mm_Vertical.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Capacitor_THT:CP_Radial_D10.0mm_P5.00mm" (layer "F.Cu")
    (tedit 5AE50EF1) (tstamp fecac377-cfa5-4d67-9270-d63a7c66c00c)
    (at 180 102.25 90)
    (descr "CP, Radial series, Radial, pin pitch=5.00mm, , diameter=10mm, Electrolytic Capacitor")
    (tags "CP Radial series Radial pin pitch 5.00mm  diameter 10mm Electrolytic Capacitor")
    (property "LCSC N°" "C269977")
    (property "N°" "NPXE21E102MF")
    (property "Sheet file" "Test.kicad_sch")
    (property "Sheet name" "")
    (path "/7ebeac02-393a-4151-9559-9db3b8be9961")
    (attr through_hole)
    (fp_text reference "C1" (at -3.75 0 180) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp e5e52899-e0de-4e04-8746-78b577c36af5)
    )
    (fp_text value "1m25vPY_5x10x16" (at 2.5 6.25 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 98f8be92-7c06-4a54-97f1-61787cc482c3)
    )
    (fp_text user "${REFERENCE}" (at 2.5 0 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 79624cc3-c938-4ff0-8a1f-3b13ce2aa3d3)
    )
    (fp_line (start 7.501 -1.062) (end 7.501 1.062) (layer "F.SilkS") (width 0.12) (tstamp 00b05f18-0e06-4b46-821b-c7f161938bac))
    (fp_line (start 4.181 -4.797) (end 4.181 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 01f2e100-cdd3-42bd-acf5-7a88e45cce89))
    (fp_line (start 5.501 1.241) (end 5.501 4.11) (layer "F.SilkS") (width 0.12) (tstamp 024a7aca-bec2-4578-8445-48a708a13045))
    (fp_line (start 5.861 1.241) (end 5.861 3.824) (layer "F.SilkS") (width 0.12) (tstamp 0594fa4e-2824-4f8f-8462-e1e1334dc2d3))
    (fp_line (start 4.101 -4.824) (end 4.101 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 06236bb5-26c7-4456-b124-49ef63b0ecfc))
    (fp_line (start 6.141 1.241) (end 6.141 3.561) (layer "F.SilkS") (width 0.12) (tstamp 06ab3f7f-0f0b-4483-a8bd-cebb81bf193a))
    (fp_line (start 3.341 -5.011) (end 3.341 5.011) (layer "F.SilkS") (width 0.12) (tstamp 075ab4e9-9612-4837-b5bb-f91cc1114ec7))
    (fp_line (start 4.981 -4.44) (end 4.981 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 087ab4c3-6e8b-430e-ad09-5ab00bd82065))
    (fp_line (start 6.461 -3.206) (end 6.461 3.206) (layer "F.SilkS") (width 0.12) (tstamp 08bca672-6143-40fa-9ba4-f14c540fdff4))
    (fp_line (start 4.501 1.241) (end 4.501 4.674) (layer "F.SilkS") (width 0.12) (tstamp 097859a4-9a0e-493d-a779-f392ad3891f8))
    (fp_line (start 4.301 -4.754) (end 4.301 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 0affac55-d139-4a0c-b828-287224830a85))
    (fp_line (start 6.341 -3.347) (end 6.341 3.347) (layer "F.SilkS") (width 0.12) (tstamp 0e82f150-a23f-4f27-94e0-d560d33c65de))
    (fp_line (start 7.461 -1.23) (end 7.461 1.23) (layer "F.SilkS") (width 0.12) (tstamp 0f9735c4-48b5-4f25-bdfc-10aa7e217bf2))
    (fp_line (start 6.181 1.241) (end 6.181 3.52) (layer "F.SilkS") (width 0.12) (tstamp 0fa2bc3d-8823-4b75-a4af-ccda3d353330))
    (fp_line (start 4.821 1.241) (end 4.821 4.525) (layer "F.SilkS") (width 0.12) (tstamp 10b86fbe-23ef-48af-80c6-0c5493244771))
    (fp_line (start 6.261 -3.436) (end 6.261 3.436) (layer "F.SilkS") (width 0.12) (tstamp 13f3d4ce-597f-455d-80ae-315f78884dfc))
    (fp_line (start 4.221 1.241) (end 4.221 4.783) (layer "F.SilkS") (width 0.12) (tstamp 14c210a8-2285-47ac-8099-fd1ad3b59913))
    (fp_line (start 5.341 -4.221) (end 5.341 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 15c4a868-b475-4019-a726-517d2fcd437b))
    (fp_line (start 3.06 -5.05) (end 3.06 5.05) (layer "F.SilkS") (width 0.12) (tstamp 15f58a68-c178-47c1-8ff9-f98d23bc5227))
    (fp_line (start 3.501 -4.982) (end 3.501 4.982) (layer "F.SilkS") (width 0.12) (tstamp 2095ccb6-9c42-43a8-a724-99616f9199f1))
    (fp_line (start 2.78 -5.073) (end 2.78 5.073) (layer "F.SilkS") (width 0.12) (tstamp 20f8682f-96e2-4143-9fc3-83809f1982ff))
    (fp_line (start 6.421 -3.254) (end 6.421 3.254) (layer "F.SilkS") (width 0.12) (tstamp 21c1ba5c-4e69-4797-b955-5fce5e2b75c1))
    (fp_line (start 7.261 -1.846) (end 7.261 1.846) (layer "F.SilkS") (width 0.12) (tstamp 22545a84-fda7-4790-83f9-027f6f9f548d))
    (fp_line (start 6.781 -2.77) (end 6.781 2.77) (layer "F.SilkS") (width 0.12) (tstamp 2507935a-1f85-454f-9826-e6331a415534))
    (fp_line (start 5.581 -4.05) (end 5.581 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 29aca26e-38f5-48a4-843b-554578d9b614))
    (fp_line (start 4.901 1.241) (end 4.901 4.483) (layer "F.SilkS") (width 0.12) (tstamp 2d585dcb-616c-4b76-b00b-980f97ea8fa4))
    (fp_line (start 5.221 -4.298) (end 5.221 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 2e79819f-be74-4e23-946c-1c3e1e94df06))
    (fp_line (start 5.181 -4.323) (end 5.181 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 2f3f6771-98bd-40b0-a9d2-30e9468e15f6))
    (fp_line (start 3.821 1.241) (end 3.821 4.907) (layer "F.SilkS") (width 0.12) (tstamp 3110298d-5024-4503-a8e3-e300d94eaaa7))
    (fp_line (start 3.861 -4.897) (end 3.861 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 328c63e4-f3c4-4a0e-a34c-aac1448dbf59))
    (fp_line (start 5.261 -4.273) (end 5.261 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 32c875f0-075f-43af-a268-23a9dcc69ace))
    (fp_line (start 6.061 1.241) (end 6.061 3.64) (layer "F.SilkS") (width 0.12) (tstamp 34b909a0-e084-48d5-b0b8-6435ad6c6d0c))
    (fp_line (start 4.901 -4.483) (end 4.901 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 351a705f-9d8d-4ac8-bb2b-f0c0d08b9834))
    (fp_line (start 3.541 -4.974) (end 3.541 4.974) (layer "F.SilkS") (width 0.12) (tstamp 35699a4f-8e19-4673-aa40-8e098e615b30))
    (fp_line (start 3.02 -5.054) (end 3.02 5.054) (layer "F.SilkS") (width 0.12) (tstamp 360d4c22-351f-4ab0-9c44-6f97df18883c))
    (fp_line (start 7.301 -1.742) (end 7.301 1.742) (layer "F.SilkS") (width 0.12) (tstamp 3dce54ce-6b84-4d3f-82d2-ed2fd79e54f2))
    (fp_line (start 6.541 -3.106) (end 6.541 3.106) (layer "F.SilkS") (width 0.12) (tstamp 3e01d98b-2d9c-4b0e-9ba6-c80253237900))
    (fp_line (start 3.301 -5.018) (end 3.301 5.018) (layer "F.SilkS") (width 0.12) (tstamp 3e5b5017-7ee6-4e89-a4a9-4024259d2207))
    (fp_line (start 4.581 -4.639) (end 4.581 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 3f0382f0-10fb-4d53-ad3e-527b4387e50e))
    (fp_line (start 5.701 1.241) (end 5.701 3.957) (layer "F.SilkS") (width 0.12) (tstamp 3ff3e0e6-936b-4268-95cd-3d73b38321dc))
    (fp_line (start 3.221 -5.03) (end 3.221 5.03) (layer "F.SilkS") (width 0.12) (tstamp 4014377f-e7a9-482b-9b92-d12670b51571))
    (fp_line (start 4.141 -4.811) (end 4.141 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 4308de1c-6d88-48c1-93a7-c374543255a1))
    (fp_line (start 5.701 -3.957) (end 5.701 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 4443a06a-03ba-4ac9-a6eb-b0f2bd40f57a))
    (fp_line (start 5.901 -3.789) (end 5.901 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 448f0e9b-8735-4d76-8c46-49b29a9685eb))
    (fp_line (start 5.821 1.241) (end 5.821 3.858) (layer "F.SilkS") (width 0.12) (tstamp 465d24e5-397a-4783-a2e3-cb3069d3ad4d))
    (fp_line (start 3.701 -4.938) (end 3.701 4.938) (layer "F.SilkS") (width 0.12) (tstamp 46654ee5-cb57-441e-a04f-361fcfa352c8))
    (fp_line (start 2.66 -5.078) (end 2.66 5.078) (layer "F.SilkS") (width 0.12) (tstamp 4824cece-77c5-4a40-8ef6-d425462172ed))
    (fp_line (start 5.101 -4.371) (end 5.101 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 4c478afd-dcb2-48a4-903e-c8e7ac24b6f7))
    (fp_line (start 3.18 -5.035) (end 3.18 5.035) (layer "F.SilkS") (width 0.12) (tstamp 4d6dcb9c-f004-4cda-a343-4ae882b6769a))
    (fp_line (start 5.381 1.241) (end 5.381 4.194) (layer "F.SilkS") (width 0.12) (tstamp 4dac91e1-0296-4ed8-b215-5c92f437459f))
    (fp_line (start 5.741 1.241) (end 5.741 3.925) (layer "F.SilkS") (width 0.12) (tstamp 4db1051c-3f7c-4cd9-9066-a0a23b2d342c))
    (fp_line (start 6.621 -3) (end 6.621 3) (layer "F.SilkS") (width 0.12) (tstamp 4f68a024-9417-41fc-978f-8cfd734abf86))
    (fp_line (start 5.141 1.241) (end 5.141 4.347) (layer "F.SilkS") (width 0.12) (tstamp 51a60e65-74ef-4569-8386-534884225370))
    (fp_line (start 2.9 -5.065) (end 2.9 5.065) (layer "F.SilkS") (width 0.12) (tstamp 5323904c-9def-4308-8b4a-b9452427decd))
    (fp_line (start 6.381 -3.301) (end 6.381 3.301) (layer "F.SilkS") (width 0.12) (tstamp 55dfd4de-1303-4f9f-b50b-57690aea5ac9))
    (fp_line (start 5.061 1.241) (end 5.061 4.395) (layer "F.SilkS") (width 0.12) (tstamp 55f73149-cd2b-4a83-84ca-47e302be4f6b))
    (fp_line (start 3.741 -4.928) (end 3.741 4.928) (layer "F.SilkS") (width 0.12) (tstamp 5649c996-d8d0-4645-8e28-6556a8f6ba9e))
    (fp_line (start 4.421 -4.707) (end 4.421 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 586c9930-509a-48dc-a9b5-6e6b43255fe6))
    (fp_line (start 5.101 1.241) (end 5.101 4.371) (layer "F.SilkS") (width 0.12) (tstamp 59ddec14-c507-47f5-9536-bd0e39971356))
    (fp_line (start 3.421 -4.997) (end 3.421 4.997) (layer "F.SilkS") (width 0.12) (tstamp 5b67b876-1c86-44cb-98b7-4e6665a37198))
    (fp_line (start 4.941 -4.462) (end 4.941 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 5bf49bd5-ce3b-4423-a60b-d31f1e8c5012))
    (fp_line (start 6.221 1.241) (end 6.221 3.478) (layer "F.SilkS") (width 0.12) (tstamp 5d14b903-b5a9-4935-873b-e7afb4bd5c92))
    (fp_line (start 5.661 -3.989) (end 5.661 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 5e4289c1-04da-4383-9843-9aff061e6718))
    (fp_line (start 4.541 1.241) (end 4.541 4.657) (layer "F.SilkS") (width 0.12) (tstamp 60ed5d4c-63d3-41be-a18a-3a410dd7a32e))
    (fp_line (start 4.661 -4.603) (end 4.661 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 6155d754-1341-4df9-8a64-b55d3b22f7cc))
    (fp_line (start 7.421 -1.378) (end 7.421 1.378) (layer "F.SilkS") (width 0.12) (tstamp 636c2718-5d83-4666-94c1-2357824415ed))
    (fp_line (start 6.101 1.241) (end 6.101 3.601) (layer "F.SilkS") (width 0.12) (tstamp 6444cc49-2cb7-4803-b370-653805920e97))
    (fp_line (start 6.941 -2.51) (end 6.941 2.51) (layer "F.SilkS") (width 0.12) (tstamp 64879e0a-5371-4f90-a8e7-169dfb430090))
    (fp_line (start 4.341 -4.738) (end 4.341 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 653fca3b-28f8-48b7-a4cf-37149907bb17))
    (fp_line (start 4.621 -4.621) (end 4.621 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 6567566b-1f2b-49fb-b627-cb1e22aea713))
    (fp_line (start 2.7 -5.077) (end 2.7 5.077) (layer "F.SilkS") (width 0.12) (tstamp 67c03d8a-1a8e-4007-87b9-651110652ad7))
    (fp_line (start 6.221 -3.478) (end 6.221 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 68c1a27a-61d4-4d69-900f-7a8903290b20))
    (fp_line (start 6.181 -3.52) (end 6.181 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 6ba1b01d-8063-4e09-953e-1a468760b89e))
    (fp_line (start 4.781 -4.545) (end 4.781 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 6bb6d9f6-3d22-4afb-a2cb-1e080089e637))
    (fp_line (start 5.941 -3.753) (end 5.941 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 6c744d22-9a11-4c83-8ecc-70071bf0d791))
    (fp_line (start 3.861 1.241) (end 3.861 4.897) (layer "F.SilkS") (width 0.12) (tstamp 6d48f5c5-f872-4d62-9608-ccd225d0da51))
    (fp_line (start 4.861 -4.504) (end 4.861 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 6dda008c-26ad-449f-83ab-c612ff1457d0))
    (fp_line (start 4.941 1.241) (end 4.941 4.462) (layer "F.SilkS") (width 0.12) (tstamp 6fb2f71b-74b2-4d76-b953-06b10a298916))
    (fp_line (start 5.821 -3.858) (end 5.821 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 7095031d-69b9-429f-a292-f4644b2f0ce7))
    (fp_line (start 6.581 -3.054) (end 6.581 3.054) (layer "F.SilkS") (width 0.12) (tstamp 745ed2d0-bc1a-45ac-a7e9-7a03b02eeb22))
    (fp_line (start 6.981 -2.439) (end 6.981 2.439) (layer "F.SilkS") (width 0.12) (tstamp 760730dd-00dd-4cda-b68b-82102a85aaab))
    (fp_line (start 7.541 -0.862) (end 7.541 0.862) (layer "F.SilkS") (width 0.12) (tstamp 770e6227-b93d-47c2-8d2e-095d2537b7c0))
    (fp_line (start 5.781 1.241) (end 5.781 3.892) (layer "F.SilkS") (width 0.12) (tstamp 7931b7ae-941d-40ce-89c2-4484b905dc9a))
    (fp_line (start 4.021 1.241) (end 4.021 4.85) (layer "F.SilkS") (width 0.12) (tstamp 7a327928-2c1d-4099-b263-292bc93b2d9d))
    (fp_line (start 5.021 -4.417) (end 5.021 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 7bcc4e09-c987-4e9c-a234-36b2fabd8d7d))
    (fp_line (start 5.341 1.241) (end 5.341 4.221) (layer "F.SilkS") (width 0.12) (tstamp 7cd70240-afaa-4709-a6ac-8b244a7ee6ad))
    (fp_line (start 4.821 -4.525) (end 4.821 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 7d3897bd-9f5f-4524-ac99-9488291299e7))
    (fp_line (start 4.381 1.241) (end 4.381 4.723) (layer "F.SilkS") (width 0.12) (tstamp 7d886562-8e4c-4b33-89e5-2cbda899bb88))
    (fp_line (start 5.421 -4.166) (end 5.421 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 7e5ca114-8994-49f3-bc35-104624c20cb3))
    (fp_line (start 7.141 -2.125) (end 7.141 2.125) (layer "F.SilkS") (width 0.12) (tstamp 7ec7636f-e3fa-4365-b96e-308cfded0a7c))
    (fp_line (start 4.301 1.241) (end 4.301 4.754) (layer "F.SilkS") (width 0.12) (tstamp 7feec632-75eb-4aac-822f-44a3ed9652ca))
    (fp_line (start 6.021 1.241) (end 6.021 3.679) (layer "F.SilkS") (width 0.12) (tstamp 803d3bad-800b-4e4f-96d3-eeeafe6b9bfb))
    (fp_line (start 4.501 -4.674) (end 4.501 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 826d73ec-7bb9-4d4a-a15b-ddfedbaff37f))
    (fp_line (start 2.98 -5.058) (end 2.98 5.058) (layer "F.SilkS") (width 0.12) (tstamp 82a8b867-cb05-49eb-a12e-49c70401fcc3))
    (fp_line (start 4.741 -4.564) (end 4.741 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 8304fee6-30de-4f03-8604-793dac127ac9))
    (fp_line (start 3.1 -5.045) (end 3.1 5.045) (layer "F.SilkS") (width 0.12) (tstamp 83bcc279-3bc9-44b7-9454-7d97c636d8b4))
    (fp_line (start 7.341 -1.63) (end 7.341 1.63) (layer "F.SilkS") (width 0.12) (tstamp 8648109c-7d10-4e37-82ab-0a18dbe043f5))
    (fp_line (start 3.981 1.241) (end 3.981 4.862) (layer "F.SilkS") (width 0.12) (tstamp 88469774-57ff-4793-80eb-3844b5f12a78))
    (fp_line (start 4.101 1.241) (end 4.101 4.824) (layer "F.SilkS") (width 0.12) (tstamp 897dbb8b-46c8-44f4-af23-79382d6b8735))
    (fp_line (start 6.661 -2.945) (end 6.661 2.945) (layer "F.SilkS") (width 0.12) (tstamp 8b50beb0-adfc-4f16-b748-787265e8df62))
    (fp_line (start 3.661 -4.947) (end 3.661 4.947) (layer "F.SilkS") (width 0.12) (tstamp 8b97e4e4-80a2-4fff-9a65-c2e69e0964f9))
    (fp_line (start 4.221 -4.783) (end 4.221 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 8c1bfcec-1efa-4faa-bfb2-c9607823bc04))
    (fp_line (start 2.58 -5.08) (end 2.58 5.08) (layer "F.SilkS") (width 0.12) (tstamp 8cf2ceff-c770-4895-b7b1-6acb66494e4f))
    (fp_line (start 4.661 1.241) (end 4.661 4.603) (layer "F.SilkS") (width 0.12) (tstamp 8d1043f9-f06c-438f-8fc1-a9979642f4de))
    (fp_line (start 2.86 -5.068) (end 2.86 5.068) (layer "F.SilkS") (width 0.12) (tstamp 8d531e35-b815-4e34-bd75-8ba6a898dcc0))
    (fp_line (start -2.479646 -3.375) (end -2.479646 -2.375) (layer "F.SilkS") (width 0.12) (tstamp 8d718d22-560d-4317-9f8d-a9db9086243a))
    (fp_line (start 3.261 -5.024) (end 3.261 5.024) (layer "F.SilkS") (width 0.12) (tstamp 90581459-fe4c-4747-b412-be3d8b19c70b))
    (fp_line (start 4.181 1.241) (end 4.181 4.797) (layer "F.SilkS") (width 0.12) (tstamp 915215b2-6428-4d80-894c-ff7570ce630f))
    (fp_line (start -2.979646 -2.875) (end -1.979646 -2.875) (layer "F.SilkS") (width 0.12) (tstamp 93d53163-0674-415b-a586-1f07bc1edf8f))
    (fp_line (start 5.461 1.241) (end 5.461 4.138) (layer "F.SilkS") (width 0.12) (tstamp 93e084fa-b00c-4bf3-bf83-3e07e7f793ce))
    (fp_line (start 3.901 -4.885) (end 3.901 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 94f9a7b7-7405-46f1-81f5-6a885796acac))
    (fp_line (start 7.061 -2.289) (end 7.061 2.289) (layer "F.SilkS") (width 0.12) (tstamp 9527dd48-5479-49f6-966d-ce29e5d251f6))
    (fp_line (start 4.061 1.241) (end 4.061 4.837) (layer "F.SilkS") (width 0.12) (tstamp 95b2e4a4-7a7f-4304-889f-27e6a75f7131))
    (fp_line (start 3.901 1.241) (end 3.901 4.885) (layer "F.SilkS") (width 0.12) (tstamp 978c07dd-a688-4516-9deb-135e9084a0b8))
    (fp_line (start 7.581 -0.599) (end 7.581 0.599) (layer "F.SilkS") (width 0.12) (tstamp 99467e89-9e60-497c-b834-286269f5804b))
    (fp_line (start 4.261 1.241) (end 4.261 4.768) (layer "F.SilkS") (width 0.12) (tstamp 9a53c171-2f37-424b-b87b-35eb42295883))
    (fp_line (start 7.221 -1.944) (end 7.221 1.944) (layer "F.SilkS") (width 0.12) (tstamp 9a76fc7d-2ed7-42ba-8076-fe65e01b4156))
    (fp_line (start 3.14 -5.04) (end 3.14 5.04) (layer "F.SilkS") (width 0.12) (tstamp 9a920a88-d15d-4171-a60f-8e9129241404))
    (fp_line (start 5.621 -4.02) (end 5.621 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 9b0adb63-309b-40e9-bf2e-ee3fca634dc3))
    (fp_line (start 4.061 -4.837) (end 4.061 -1.241) (layer "F.SilkS") (width 0.12) (tstamp 9b5457f8-0e32-415c-a5d7-bdf1c3020c06))
    (fp_line (start 2.82 -5.07) (end 2.82 5.07) (layer "F.SilkS") (width 0.12) (tstamp 9c46841c-1bb4-4a24-8ec9-e7f9bac61aeb))
    (fp_line (start 5.901 1.241) (end 5.901 3.789) (layer "F.SilkS") (width 0.12) (tstamp 9c478603-1935-4bb9-8c37-8ef8c46c7573))
    (fp_line (start 7.381 -1.51) (end 7.381 1.51) (layer "F.SilkS") (width 0.12) (tstamp 9d04d366-e218-496b-b141-aaecc539a766))
    (fp_line (start 3.581 -4.965) (end 3.581 4.965) (layer "F.SilkS") (width 0.12) (tstamp 9f0bc0ea-da98-458e-be95-608230693920))
    (fp_line (start 4.421 1.241) (end 4.421 4.707) (layer "F.SilkS") (width 0.12) (tstamp 9f805312-b381-4858-b3ad-a601f6f5dc15))
    (fp_line (start 4.621 1.241) (end 4.621 4.621) (layer "F.SilkS") (width 0.12) (tstamp a0b35aae-de7f-483d-a8a6-1cedf0db7e6c))
    (fp_line (start 6.701 -2.889) (end 6.701 2.889) (layer "F.SilkS") (width 0.12) (tstamp a239ecac-612e-4693-a5e4-11ac1ad49430))
    (fp_line (start 5.301 -4.247) (end 5.301 -1.241) (layer "F.SilkS") (width 0.12) (tstamp a2f6a828-ce5c-44d9-8ed2-82985417d921))
    (fp_line (start 6.861 -2.645) (end 6.861 2.645) (layer "F.SilkS") (width 0.12) (tstamp a5ab3588-e3ba-4348-a585-d93c7c7165dd))
    (fp_line (start 6.901 -2.579) (end 6.901 2.579) (layer "F.SilkS") (width 0.12) (tstamp a600ddea-0a86-4c35-99c4-d2cc00e3249c))
    (fp_line (start 4.261 -4.768) (end 4.261 -1.241) (layer "F.SilkS") (width 0.12) (tstamp a9a2ab35-21e4-4311-8f91-07de98eda808))
    (fp_line (start 4.021 -4.85) (end 4.021 -1.241) (layer "F.SilkS") (width 0.12) (tstamp aa814003-1ba4-4558-a6eb-615930f29490))
    (fp_line (start 4.981 1.241) (end 4.981 4.44) (layer "F.SilkS") (width 0.12) (tstamp acd6a93f-ffb4-4159-8605-80f3521971e9))
    (fp_line (start 5.861 -3.824) (end 5.861 -1.241) (layer "F.SilkS") (width 0.12) (tstamp ad17ce75-f501-4cb4-811c-ffeadc45fbee))
    (fp_line (start 2.5 -5.08) (end 2.5 5.08) (layer "F.SilkS") (width 0.12) (tstamp b04cbd22-1413-4fd1-a90d-ed548b06a3ce))
    (fp_line (start 5.381 -4.194) (end 5.381 -1.241) (layer "F.SilkS") (width 0.12) (tstamp b2b48266-6f6d-48b8-aa62-fe6fe663fc7b))
    (fp_line (start 5.461 -4.138) (end 5.461 -1.241) (layer "F.SilkS") (width 0.12) (tstamp b57f840b-3df9-4697-bc08-de4acec04ec9))
    (fp_line (start 5.261 1.241) (end 5.261 4.273) (layer "F.SilkS") (width 0.12) (tstamp b7a96062-d07d-4968-a26e-a6f69e2f20b3))
    (fp_line (start 3.941 -4.874) (end 3.941 -1.241) (layer "F.SilkS") (width 0.12) (tstamp bc912439-7bc2-4c1b-9519-43c15c9d405d))
    (fp_line (start 3.621 -4.956) (end 3.621 4.956) (layer "F.SilkS") (width 0.12) (tstamp bd209c4e-6d9d-4f2f-adb3-5662f64b95ac))
    (fp_line (start 6.101 -3.601) (end 6.101 -1.241) (layer "F.SilkS") (width 0.12) (tstamp bd9dd838-7d15-4fef-ba78-c184e8fc7501))
    (fp_line (start 4.781 1.241) (end 4.781 4.545) (layer "F.SilkS") (width 0.12) (tstamp bf3e1d95-5341-4145-8c45-9971cb4dd1d2))
    (fp_line (start 5.541 -4.08) (end 5.541 -1.241) (layer "F.SilkS") (width 0.12) (tstamp bfa87417-64ef-47fa-a305-ed7b966c7c94))
    (fp_line (start 5.981 1.241) (end 5.981 3.716) (layer "F.SilkS") (width 0.12) (tstamp c02a00ee-6839-40b6-8934-0cd39f1125d7))
    (fp_line (start 4.581 1.241) (end 4.581 4.639) (layer "F.SilkS") (width 0.12) (tstamp c105a172-99a1-452f-96d2-90cb41c42f46))
    (fp_line (start 5.981 -3.716) (end 5.981 -1.241) (layer "F.SilkS") (width 0.12) (tstamp c282486f-e72c-4fc0-be9c-a137a5745dda))
    (fp_line (start 5.301 1.241) (end 5.301 4.247) (layer "F.SilkS") (width 0.12) (tstamp c2d0ec68-495b-4dca-b0d2-0688c1341529))
    (fp_line (start 5.421 1.241) (end 5.421 4.166) (layer "F.SilkS") (width 0.12) (tstamp c2d393ea-adaf-4846-b15c-7e59abc58b83))
    (fp_line (start 7.021 -2.365) (end 7.021 2.365) (layer "F.SilkS") (width 0.12) (tstamp c7433b48-1dce-4871-a630-066b70e5eada))
    (fp_line (start 5.661 1.241) (end 5.661 3.989) (layer "F.SilkS") (width 0.12) (tstamp c79ca0d9-9eab-4485-ac8b-b61b32decf43))
    (fp_line (start 3.821 -4.907) (end 3.821 -1.241) (layer "F.SilkS") (width 0.12) (tstamp c81a88b6-b8ec-446b-9ec2-23150b6280ee))
    (fp_line (start 4.381 -4.723) (end 4.381 -1.241) (layer "F.SilkS") (width 0.12) (tstamp cafbdda1-3cf8-4adc-b9c6-d406fb248bc3))
    (fp_line (start 3.941 1.241) (end 3.941 4.874) (layer "F.SilkS") (width 0.12) (tstamp ccfa855d-5bf7-4a8a-8d69-faeb92b0eb29))
    (fp_line (start 5.781 -3.892) (end 5.781 -1.241) (layer "F.SilkS") (width 0.12) (tstamp cd61ceac-90d2-4247-97b8-a4b8bcc2455c))
    (fp_line (start 4.541 -4.657) (end 4.541 -1.241) (layer "F.SilkS") (width 0.12) (tstamp ceb84962-5b84-4434-aee4-62ce6d60d502))
    (fp_line (start 5.541 1.241) (end 5.541 4.08) (layer "F.SilkS") (width 0.12) (tstamp cff2f0d7-b228-41fc-810e-cc3f1b9f5556))
    (fp_line (start 5.621 1.241) (end 5.621 4.02) (layer "F.SilkS") (width 0.12) (tstamp d0676532-ce19-45d3-bf53-ae3bfe47c604))
    (fp_line (start 3.981 -4.862) (end 3.981 -1.241) (layer "F.SilkS") (width 0.12) (tstamp d147fab7-0795-4fb7-9d63-40895e0bac2f))
    (fp_line (start 6.821 -2.709) (end 6.821 2.709) (layer "F.SilkS") (width 0.12) (tstamp d1762de7-669d-43c5-a57f-28ad7ed78206))
    (fp_line (start 5.941 1.241) (end 5.941 3.753) (layer "F.SilkS") (width 0.12) (tstamp d2db1f5d-e162-4690-ac0f-b3ef490f09cd))
    (fp_line (start 5.741 -3.925) (end 5.741 -1.241) (layer "F.SilkS") (width 0.12) (tstamp d31bfe78-eaee-4cd1-85a2-1c0f0f28448e))
    (fp_line (start 2.94 -5.062) (end 2.94 5.062) (layer "F.SilkS") (width 0.12) (tstamp d3975807-4b20-47c2-94f6-a53c0dcd090b))
    (fp_line (start 7.101 -2.209) (end 7.101 2.209) (layer "F.SilkS") (width 0.12) (tstamp d463ed2a-3ccf-4cab-a57e-6171b7df49ba))
    (fp_line (start 6.301 -3.392) (end 6.301 3.392) (layer "F.SilkS") (width 0.12) (tstamp d5776fd4-bef5-494d-a4c5-959971e6b3ba))
    (fp_line (start 5.221 1.241) (end 5.221 4.298) (layer "F.SilkS") (width 0.12) (tstamp d62c6028-25d4-4720-8704-442dae79e1e4))
    (fp_line (start 3.781 -4.918) (end 3.781 -1.241) (layer "F.SilkS") (width 0.12) (tstamp d638727d-b546-4cee-8f67-3aa8c1e4ccd6))
    (fp_line (start 4.141 1.241) (end 4.141 4.811) (layer "F.SilkS") (width 0.12) (tstamp d6d4debe-7777-4130-af11-b631a866bd82))
    (fp_line (start 4.701 -4.584) (end 4.701 -1.241) (layer "F.SilkS") (width 0.12) (tstamp dd015371-1695-4777-b5cd-d1d26b323efe))
    (fp_line (start 4.861 1.241) (end 4.861 4.504) (layer "F.SilkS") (width 0.12) (tstamp dddcc2fc-0260-43c2-bd0c-f1ae41ea30e3))
    (fp_line (start 4.461 -4.69) (end 4.461 -1.241) (layer "F.SilkS") (width 0.12) (tstamp e380fea8-3e17-42de-81eb-8d376235825a))
    (fp_line (start 5.141 -4.347) (end 5.141 -1.241) (layer "F.SilkS") (width 0.12) (tstamp e609a25f-73cb-4674-88f2-4daf5c21a11d))
    (fp_line (start 6.061 -3.64) (end 6.061 -1.241) (layer "F.SilkS") (width 0.12) (tstamp e6f11173-f124-4e4d-bb9a-36d2ed2398f0))
    (fp_line (start 2.62 -5.079) (end 2.62 5.079) (layer "F.SilkS") (width 0.12) (tstamp ea39debf-28f7-455e-a4bd-5aa983c021d2))
    (fp_line (start 5.021 1.241) (end 5.021 4.417) (layer "F.SilkS") (width 0.12) (tstamp eadd193e-ae5c-4c83-bf6a-3404427a1165))
    (fp_line (start 2.74 -5.075) (end 2.74 5.075) (layer "F.SilkS") (width 0.12) (tstamp eae8cd29-8dcb-417f-9ba0-405fc0491008))
    (fp_line (start 4.701 1.241) (end 4.701 4.584) (layer "F.SilkS") (width 0.12) (tstamp ed43f6e7-2754-4435-ad07-cfc53435a7fc))
    (fp_line (start 6.021 -3.679) (end 6.021 -1.241) (layer "F.SilkS") (width 0.12) (tstamp ed99211a-0bdb-4d48-88f1-1cd3f8af3edb))
    (fp_line (start 4.461 1.241) (end 4.461 4.69) (layer "F.SilkS") (width 0.12) (tstamp f03fcb65-1d13-44e5-8708-f27d682fe434))
    (fp_line (start 3.381 -5.004) (end 3.381 5.004) (layer "F.SilkS") (width 0.12) (tstamp f0e5c7e9-2a24-4647-9485-1ac5c992f9c8))
    (fp_line (start 5.061 -4.395) (end 5.061 -1.241) (layer "F.SilkS") (width 0.12) (tstamp f23f9197-a158-4651-9ed5-5a9f794e42a8))
    (fp_line (start 6.741 -2.83) (end 6.741 2.83) (layer "F.SilkS") (width 0.12) (tstamp f2f793ea-950e-4ee7-ae0b-a55f38b0d593))
    (fp_line (start 4.341 1.241) (end 4.341 4.738) (layer "F.SilkS") (width 0.12) (tstamp f44c8cb0-4178-4846-a1eb-c16b51dda54b))
    (fp_line (start 5.581 1.241) (end 5.581 4.05) (layer "F.SilkS") (width 0.12) (tstamp f6412363-4315-4058-96cc-1e9b8819e0a8))
    (fp_line (start 4.741 1.241) (end 4.741 4.564) (layer "F.SilkS") (width 0.12) (tstamp f6e41d4d-9fb0-437f-b367-b16a7f1465b5))
    (fp_line (start 2.54 -5.08) (end 2.54 5.08) (layer "F.SilkS") (width 0.12) (tstamp fb26ef8d-d266-46aa-94a4-78ffea24776e))
    (fp_line (start 3.781 1.241) (end 3.781 4.918) (layer "F.SilkS") (width 0.12) (tstamp fb657bdc-02cb-4b0c-b894-8a409951a913))
    (fp_line (start 6.141 -3.561) (end 6.141 -1.241) (layer "F.SilkS") (width 0.12) (tstamp fd24332e-60da-4974-9c44-bf83556bd232))
    (fp_line (start 3.461 -4.99) (end 3.461 4.99) (layer "F.SilkS") (width 0.12) (tstamp fe3af0f1-abb8-4b64-8541-83ddb214e8c9))
    (fp_line (start 7.181 -2.037) (end 7.181 2.037) (layer "F.SilkS") (width 0.12) (tstamp fe666047-7dc3-4fee-b6fe-ef9ebac7d5e0))
    (fp_line (start 5.181 1.241) (end 5.181 4.323) (layer "F.SilkS") (width 0.12) (tstamp fec95837-b655-47b6-9bc7-c9a4357cde96))
    (fp_line (start 5.501 -4.11) (end 5.501 -1.241) (layer "F.SilkS") (width 0.12) (tstamp fee9dd2c-0369-46a2-9f68-39b6a4059674))
    (fp_line (start 6.501 -3.156) (end 6.501 3.156) (layer "F.SilkS") (width 0.12) (tstamp ff615066-83eb-4e0d-9a9c-afb7b52f7148))
    (fp_circle (center 2.5 0) (end 7.62 0) (layer "F.SilkS") (width 0.12) (fill none) (tstamp c5502fc4-681e-4a0e-8c85-2f62505c7633))
    (fp_circle (center 2.5 0) (end 7.75 0) (layer "F.CrtYd") (width 0.05) (fill none) (tstamp bbb7ad6c-6f51-491d-8fb2-8b7566a0b0d9))
    (fp_line (start -1.288861 -2.6875) (end -1.288861 -1.6875) (layer "F.Fab") (width 0.1) (tstamp b6c4deb0-fbd8-49e2-b960-ea1125602887))
    (fp_line (start -1.788861 -2.1875) (end -0.788861 -2.1875) (layer "F.Fab") (width 0.1) (tstamp fae7d5ad-c3c5-4fcb-bcd4-fc23876b2809))
    (fp_circle (center 2.5 0) (end 7.5 0) (layer "F.Fab") (width 0.1) (fill none) (tstamp 5b63012c-bc76-4056-91fe-35f030921af3))
    (pad "1" thru_hole rect locked (at 0 0 90) (size 2 2) (drill 1) (layers *.Cu *.Mask)
      (net 1 "Net-(C1-Pad1)") (tstamp d1b41142-17bd-4982-b9b6-d4a10278ed3d))
    (pad "2" thru_hole circle locked (at 5 0 90) (size 2 2) (drill 1) (layers *.Cu *.Mask)
      (net 2 "GND") (tstamp a9206b2b-cfcc-466f-81ba-e763a0afb122))
    (model "${KISYS3DMOD}/Capacitor_THT.3dshapes/CP_Radial_D10.0mm_P5.00mm.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_rect (start 162.5 85) (end 190 115) (layer "Edge.Cuts") (width 0.1) (fill none) (tstamp d6dc7ba5-12ea-4d23-aae3-e1f22944a80b))
  (dimension (type leader) (layer "Dwgs.User") (tstamp 3f749de1-cddc-4992-8135-8d1854ebbd09)
    (pts (xy 186.96 87.96) (xy 190.64 84.1))
    (gr_text "Max Hole Violation" (at 195.68 80.93) (layer "Dwgs.User") (tstamp 3f749de1-cddc-4992-8135-8d1854ebbd09)
      (effects (font (size 1.5 1.5) (thickness 0.3)))
    )
    (format (units 0) (units_format 0) (precision 4) (override_value "Max Hole Violation"))
    (style (thickness 0.2) (arrow_length 1.27) (text_position_mode 0) (text_frame 0) (extension_offset 0.5))
  )
  (dimension (type leader) (layer "Dwgs.User") (tstamp 97a34143-ea77-4aa4-a1be-f5635d2ece1d)
    (pts (xy 187.2 109.71) (xy 189.56 107.05))
    (gr_text "Max Hole Violation" (at 195.77 101.67) (layer "Dwgs.User") (tstamp 97a34143-ea77-4aa4-a1be-f5635d2ece1d)
      (effects (font (size 1.5 1.5) (thickness 0.3)))
    )
    (format (units 0) (units_format 0) (precision 4) (override_value "Max Hole Violation"))
    (style (thickness 0.2) (arrow_length 1.27) (text_position_mode 0) (text_frame 0) (extension_offset 0.5))
  )
  (dimension (type leader) (layer "Dwgs.User") (tstamp 9ad22b4a-fd89-466c-aa16-c2235d0b5ae8)
    (pts (xy 167.95 100.95) (xy 170.31 98.29))
    (gr_text "PTH Violation" (at 176.52 92.91) (layer "Dwgs.User") (tstamp 9ad22b4a-fd89-466c-aa16-c2235d0b5ae8)
      (effects (font (size 1.5 1.5) (thickness 0.3)))
    )
    (format (units 0) (units_format 0) (precision 4) (override_value "PTH Violation"))
    (style (thickness 0.2) (arrow_length 1.27) (text_position_mode 0) (text_frame 0) (extension_offset 0.5))
  )
  (dimension (type leader) (layer "Dwgs.User") (tstamp f97ec491-6aec-40e3-8d3d-15199c42cb1f)
    (pts (xy 168.47 111.15) (xy 173.05 116.98))
    (gr_text "NPTH Violation" (at 183.34 117.01) (layer "Dwgs.User") (tstamp f97ec491-6aec-40e3-8d3d-15199c42cb1f)
      (effects (font (size 1.5 1.5) (thickness 0.3)))
    )
    (format (units 0) (units_format 0) (precision 4) (override_value "NPTH Violation"))
    (style (thickness 0.2) (arrow_length 1.27) (text_position_mode 0) (text_frame 0) (extension_offset 0.5))
  )

  (segment (start 165.5 101.25) (end 166.5 102.25) (width 1) (layer "F.Cu") (net 1) (tstamp 68174454-c7fe-4239-83a0-db746e47b6c3))
  (segment (start 166.5 102.25) (end 171.8 102.25) (width 1) (layer "F.Cu") (net 1) (tstamp eafc71b6-f98c-4ede-81f9-8efc0aa01e64))
  (via (at 171.8 102.25) (size 0.8) (drill 0.4) (layers "F.Cu" "B.Cu") (net 1) (tstamp 95a3eb59-b7b9-473d-ad27-3ff21aac1b09))
  (segment (start 171.8 102.25) (end 180 102.25) (width 1) (layer "B.Cu") (net 1) (tstamp 9233d5c5-a0c3-4ad4-a9fe-ff4dd24dccb0))
  (segment (start 170.9 97.25) (end 167 97.25) (width 1) (layer "F.Cu") (net 2) (tstamp 94e3f540-92ed-4170-bba2-139f1ed5e316))
  (segment (start 167 97.25) (end 165.64999 98.60001) (width 1) (layer "F.Cu") (net 2) (tstamp a89a510a-cb46-4754-b4a9-9f748be904fa))
  (via (at 170.9 97.25) (size 0.8) (drill 0.4) (layers "F.Cu" "B.Cu") (net 2) (tstamp eb204bc6-f128-4291-9c0a-08c25cf4c430))
  (segment (start 170.9 97.25) (end 180 97.25) (width 1) (layer "B.Cu") (net 2) (tstamp cdb7f705-4d32-47a5-8826-a7facc5256a6))

  (zone (net 2) (net_name "GND") (layer "B.Cu") (tstamp 9457f5a4-61ff-4365-abfe-2db7366d4e60) (hatch edge 0.508)
    (connect_pads (clearance 0.508))
    (min_thickness 0.254) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 189.5 114.5)
        (xy 163 114.5)
        (xy 163 85.5)
        (xy 189.5 85.5)
      )
    )
    (filled_polygon
      (layer "B.Cu")
      (pts
        (xy 184.870416 85.528002)
        (xy 184.916909 85.581658)
        (xy 184.927013 85.651932)
        (xy 184.897519 85.716512)
        (xy 184.858614 85.746713)
        (xy 184.611385 85.870246)
        (xy 184.611378 85.87025)
        (xy 184.608298 85.871789)
        (xy 184.302374 86.069322)
        (xy 184.29969 86.071507)
        (xy 184.299686 86.07151)
        (xy 184.022658 86.297046)
        (xy 184.019973 86.299232)
        (xy 183.764507 86.558743)
        (xy 183.539061 86.84472)
        (xy 183.346358 87.153709)
        (xy 183.344855 87.15684)
        (xy 183.190224 87.478855)
        (xy 183.19022 87.478865)
        (xy 183.188725 87.481978)
        (xy 183.068067 87.825563)
        (xy 182.98584 88.180313)
        (xy 182.985436 88.18373)
        (xy 182.985434 88.183739)
        (xy 182.961039 88.389854)
        (xy 182.943038 88.541944)
        (xy 182.940178 88.906087)
        (xy 182.977295 89.268346)
        (xy 183.053939 89.624343)
        (xy 183.169186 89.969781)
        (xy 183.321643 90.300485)
        (xy 183.509469 90.612463)
        (xy 183.730395 90.901946)
        (xy 183.732781 90.904447)
        (xy 183.979358 91.162928)
        (xy 183.979364 91.162933)
        (xy 183.981753 91.165438)
        (xy 184.260508 91.399755)
        (xy 184.563291 91.602069)
        (xy 184.886447 91.769935)
        (xy 185.226072 91.901326)
        (xy 185.22942 91.902214)
        (xy 185.229424 91.902215)
        (xy 185.479178 91.968436)
        (xy 185.578064 91.994655)
        (xy 185.791209 92.0267)
        (xy 185.934751 92.048281)
        (xy 185.934757 92.048282)
        (xy 185.938172 92.048795)
        (xy 185.941629 92.048931)
        (xy 185.941631 92.048931)
        (xy 186.04231 92.052886)
        (xy 186.302046 92.063091)
        (xy 186.30549 92.062847)
        (xy 186.305499 92.062847)
        (xy 186.661834 92.037617)
        (xy 186.661837 92.037617)
        (xy 186.665292 92.037372)
        (xy 186.668698 92.03675)
        (xy 187.020112 91.972571)
        (xy 187.020119 91.972569)
        (xy 187.023521 91.971948)
        (xy 187.372408 91.867608)
        (xy 187.707738 91.725614)
        (xy 187.710746 91.723929)
        (xy 187.710753 91.723926)
        (xy 187.931782 91.600144)
        (xy 188.025462 91.547681)
        (xy 188.321742 91.335956)
        (xy 188.592998 91.092998)
        (xy 188.835956 90.821742)
        (xy 189.047681 90.525462)
        (xy 189.123923 90.389322)
        (xy 189.223926 90.210753)
        (xy 189.223929 90.210746)
        (xy 189.225614 90.207738)
        (xy 189.249973 90.150212)
        (xy 189.294954 90.095283)
        (xy 189.362492 90.073392)
        (xy 189.431143 90.091489)
        (xy 189.479112 90.14383)
        (xy 189.492 90.199343)
        (xy 189.492 114.366)
        (xy 189.471998 114.434121)
        (xy 189.418342 114.480614)
        (xy 189.366 114.492)
        (xy 163.134 114.492)
        (xy 163.065879 114.471998)
        (xy 163.019386 114.418342)
        (xy 163.008 114.366)
        (xy 163.008 110.977868)
        (xy 164.136616 110.977868)
        (xy 164.153166 111.264892)
        (xy 164.153991 111.269097)
        (xy 164.153992 111.269105)
        (xy 164.165223 111.326348)
        (xy 164.208516 111.547014)
        (xy 164.301642 111.819014)
        (xy 164.430822 112.075859)
        (xy 164.593664 112.312796)
        (xy 164.787155 112.52544)
        (xy 165.007716 112.709857)
        (xy 165.011357 112.712141)
        (xy 165.247624 112.860352)
        (xy 165.247628 112.860354)
        (xy 165.251264 112.862635)
        (xy 165.513293 112.980945)
        (xy 165.517412 112.982165)
        (xy 165.784841 113.061382)
        (xy 165.784846 113.061383)
        (xy 165.788954 113.0626)
        (xy 165.793188 113.063248)
        (xy 165.793193 113.063249)
        (xy 166.046037 113.10194)
        (xy 166.073147 113.106088)
        (xy 166.219498 113.108387)
        (xy 166.356321 113.110537)
        (xy 166.356327 113.110537)
        (xy 166.360612 113.110604)
        (xy 166.364864 113.110089)
        (xy 166.364872 113.110089)
        (xy 166.586529 113.083265)
        (xy 166.64603 113.076064)
        (xy 166.650179 113.074976)
        (xy 166.650182 113.074975)
        (xy 166.91997 113.004198)
        (xy 166.924121 113.003109)
        (xy 167.189736 112.893087)
        (xy 167.238827 112.864401)
        (xy 167.434255 112.750202)
        (xy 167.434257 112.7502)
        (xy 167.437963 112.748035)
        (xy 167.664208 112.570637)
        (xy 167.864283 112.364175)
        (xy 167.866816 112.360727)
        (xy 167.86682 112.360722)
        (xy 168.031949 112.135925)
        (xy 168.034487 112.13247)
        (xy 168.065224 112.075859)
        (xy 168.169621 111.883585)
        (xy 168.169622 111.883583)
        (xy 168.171671 111.879809)
        (xy 168.273295 111.610869)
        (xy 168.337479 111.330624)
        (xy 168.34297 111.269105)
        (xy 168.362816 111.046728)
        (xy 168.362816 111.046726)
        (xy 168.363036 111.044262)
        (xy 168.3635 111)
        (xy 168.361992 110.977868)
        (xy 184.136616 110.977868)
        (xy 184.153166 111.264892)
        (xy 184.153991 111.269097)
        (xy 184.153992 111.269105)
        (xy 184.165223 111.326348)
        (xy 184.208516 111.547014)
        (xy 184.301642 111.819014)
        (xy 184.430822 112.075859)
        (xy 184.593664 112.312796)
        (xy 184.787155 112.52544)
        (xy 185.007716 112.709857)
        (xy 185.011357 112.712141)
        (xy 185.247624 112.860352)
        (xy 185.247628 112.860354)
        (xy 185.251264 112.862635)
        (xy 185.513293 112.980945)
        (xy 185.517412 112.982165)
        (xy 185.784841 113.061382)
        (xy 185.784846 113.061383)
        (xy 185.788954 113.0626)
        (xy 185.793188 113.063248)
        (xy 185.793193 113.063249)
        (xy 186.046037 113.10194)
        (xy 186.073147 113.106088)
        (xy 186.219498 113.108387)
        (xy 186.356321 113.110537)
        (xy 186.356327 113.110537)
        (xy 186.360612 113.110604)
        (xy 186.364864 113.110089)
        (xy 186.364872 113.110089)
        (xy 186.586529 113.083265)
        (xy 186.64603 113.076064)
        (xy 186.650179 113.074976)
        (xy 186.650182 113.074975)
        (xy 186.91997 113.004198)
        (xy 186.924121 113.003109)
        (xy 187.189736 112.893087)
        (xy 187.238827 112.864401)
        (xy 187.434255 112.750202)
        (xy 187.434257 112.7502)
        (xy 187.437963 112.748035)
        (xy 187.664208 112.570637)
        (xy 187.864283 112.364175)
        (xy 187.866816 112.360727)
        (xy 187.86682 112.360722)
        (xy 188.031949 112.135925)
        (xy 188.034487 112.13247)
        (xy 188.065224 112.075859)
        (xy 188.169621 111.883585)
        (xy 188.169622 111.883583)
        (xy 188.171671 111.879809)
        (xy 188.273295 111.610869)
        (xy 188.337479 111.330624)
        (xy 188.34297 111.269105)
        (xy 188.362816 111.046728)
        (xy 188.362816 111.046726)
        (xy 188.363036 111.044262)
        (xy 188.3635 111)
        (xy 188.343946 110.713165)
        (xy 188.339409 110.691254)
        (xy 188.286513 110.435835)
        (xy 188.285644 110.431638)
        (xy 188.189674 110.160628)
        (xy 188.057812 109.90515)
        (xy 187.892498 109.669931)
        (xy 187.889573 109.666783)
        (xy 187.699711 109.462468)
        (xy 187.699708 109.462466)
        (xy 187.69679 109.459325)
        (xy 187.693474 109.456611)
        (xy 187.693471 109.456608)
        (xy 187.477629 109.279942)
        (xy 187.477622 109.279937)
        (xy 187.474311 109.277227)
        (xy 187.229176 109.127009)
        (xy 187.22524 109.125281)
        (xy 186.969851 109.013172)
        (xy 186.969847 109.013171)
        (xy 186.965923 109.011448)
        (xy 186.689421 108.932685)
        (xy 186.478794 108.902708)
        (xy 186.409041 108.892781)
        (xy 186.409039 108.892781)
        (xy 186.404789 108.892176)
        (xy 186.253976 108.891387)
        (xy 186.121579 108.890693)
        (xy 186.121573 108.890693)
        (xy 186.117292 108.890671)
        (xy 186.113048 108.89123)
        (xy 186.113044 108.89123)
        (xy 185.987864 108.90771)
        (xy 185.832251 108.928197)
        (xy 185.828111 108.92933)
        (xy 185.828109 108.92933)
        (xy 185.811547 108.933861)
        (xy 185.55494 109.004061)
        (xy 185.53358 109.013172)
        (xy 185.294433 109.115176)
        (xy 185.294426 109.11518)
        (xy 185.290491 109.116858)
        (xy 185.28681 109.119061)
        (xy 185.047479 109.262297)
        (xy 185.047475 109.2623)
        (xy 185.043797 109.264501)
        (xy 185.040454 109.267179)
        (xy 185.04045 109.267182)
        (xy 184.930922 109.354931)
        (xy 184.819423 109.444259)
        (xy 184.816479 109.447361)
        (xy 184.816475 109.447365)
        (xy 184.802143 109.462468)
        (xy 184.62152 109.652804)
        (xy 184.453752 109.886279)
        (xy 184.319222 110.140362)
        (xy 184.220419 110.410352)
        (xy 184.159173 110.691254)
        (xy 184.136616 110.977868)
        (xy 168.361992 110.977868)
        (xy 168.343946 110.713165)
        (xy 168.339409 110.691254)
        (xy 168.286513 110.435835)
        (xy 168.285644 110.431638)
        (xy 168.189674 110.160628)
        (xy 168.057812 109.90515)
        (xy 167.892498 109.669931)
        (xy 167.889573 109.666783)
        (xy 167.699711 109.462468)
        (xy 167.699708 109.462466)
        (xy 167.69679 109.459325)
        (xy 167.693474 109.456611)
        (xy 167.693471 109.456608)
        (xy 167.477629 109.279942)
        (xy 167.477622 109.279937)
        (xy 167.474311 109.277227)
        (xy 167.229176 109.127009)
        (xy 167.22524 109.125281)
        (xy 166.969851 109.013172)
        (xy 166.969847 109.013171)
        (xy 166.965923 109.011448)
        (xy 166.689421 108.932685)
        (xy 166.478794 108.902708)
        (xy 166.409041 108.892781)
        (xy 166.409039 108.892781)
        (xy 166.404789 108.892176)
        (xy 166.253976 108.891387)
        (xy 166.121579 108.890693)
        (xy 166.121573 108.890693)
        (xy 166.117292 108.890671)
        (xy 166.113048 108.89123)
        (xy 166.113044 108.89123)
        (xy 165.987864 108.90771)
        (xy 165.832251 108.928197)
        (xy 165.828111 108.92933)
        (xy 165.828109 108.92933)
        (xy 165.811547 108.933861)
        (xy 165.55494 109.004061)
        (xy 165.53358 109.013172)
        (xy 165.294433 109.115176)
        (xy 165.294426 109.11518)
        (xy 165.290491 109.116858)
        (xy 165.28681 109.119061)
        (xy 165.047479 109.262297)
        (xy 165.047475 109.2623)
        (xy 165.043797 109.264501)
        (xy 165.040454 109.267179)
        (xy 165.04045 109.267182)
        (xy 164.930922 109.354931)
        (xy 164.819423 109.444259)
        (xy 164.816479 109.447361)
        (xy 164.816475 109.447365)
        (xy 164.802143 109.462468)
        (xy 164.62152 109.652804)
        (xy 164.453752 109.886279)
        (xy 164.319222 110.140362)
        (xy 164.220419 110.410352)
        (xy 164.159173 110.691254)
        (xy 164.136616 110.977868)
        (xy 163.008 110.977868)
        (xy 163.008 100.593978)
        (xy 163.9915 100.593978)
        (xy 163.9915 101.892885)
        (xy 164.006978 102.025642)
        (xy 164.009474 102.03252)
        (xy 164.009475 102.032522)
        (xy 164.037146 102.108753)
        (xy 164.067313 102.191863)
        (xy 164.164269 102.339747)
        (xy 164.292645 102.461358)
        (xy 164.445555 102.550175)
        (xy 164.614796 102.601433)
        (xy 164.621236 102.602008)
        (xy 164.621237 102.602008)
        (xy 164.691185 102.608251)
        (xy 164.691191 102.608251)
        (xy 164.693978 102.6085)
        (xy 166.292885 102.6085)
        (xy 166.388105 102.597398)
        (xy 166.41837 102.59387)
        (xy 166.418371 102.59387)
        (xy 166.425642 102.593022)
        (xy 166.591864 102.532687)
        (xy 166.724357 102.445821)
        (xy 166.733628 102.439743)
        (xy 166.733629 102.439742)
        (xy 166.739747 102.435731)
        (xy 166.861358 102.307355)
        (xy 166.898782 102.242924)
        (xy 170.786525 102.242924)
        (xy 170.804452 102.439911)
        (xy 170.80619 102.445817)
        (xy 170.806191 102.445821)
        (xy 170.810764 102.461358)
        (xy 170.860299 102.629664)
        (xy 170.951939 102.804955)
        (xy 171.075882 102.959109)
        (xy 171.227406 103.086253)
        (xy 171.232798 103.089217)
        (xy 171.232802 103.08922)
        (xy 171.374227 103.166969)
        (xy 171.40074 103.181544)
        (xy 171.406607 103.183405)
        (xy 171.406609 103.183406)
        (xy 171.583412 103.239491)
        (xy 171.589282 103.241353)
        (xy 171.742155 103.2585)
        (xy 178.378443 103.2585)
        (xy 178.446564 103.278502)
        (xy 178.493057 103.332158)
        (xy 178.499337 103.348997)
        (xy 178.532904 103.463316)
        (xy 178.537775 103.470895)
        (xy 178.607051 103.578691)
        (xy 178.607053 103.578694)
        (xy 178.611923 103.586271)
        (xy 178.618733 103.592172)
        (xy 178.715569 103.676082)
        (xy 178.715572 103.676084)
        (xy 178.722381 103.681984)
        (xy 178.85533 103.7427)
        (xy 178.864245 103.743982)
        (xy 178.864246 103.743982)
        (xy 178.995552 103.762861)
        (xy 178.995559 103.762862)
        (xy 179 103.7635)
        (xy 181 103.7635)
        (xy 181.073079 103.758273)
        (xy 181.151165 103.735345)
        (xy 181.20467 103.719635)
        (xy 181.204672 103.719634)
        (xy 181.213316 103.717096)
        (xy 181.277135 103.676082)
        (xy 181.328691 103.642949)
        (xy 181.328694 103.642947)
        (xy 181.336271 103.638077)
        (xy 181.376048 103.592172)
        (xy 181.426082 103.534431)
        (xy 181.426084 103.534428)
        (xy 181.431984 103.527619)
        (xy 181.4927 103.39467)
        (xy 181.502993 103.323079)
        (xy 181.512861 103.254448)
        (xy 181.512862 103.254441)
        (xy 181.5135 103.25)
        (xy 181.5135 101.25)
        (xy 181.508273 101.176921)
        (xy 181.467096 101.036684)
        (xy 181.421393 100.965569)
        (xy 181.392949 100.921309)
        (xy 181.392947 100.921306)
        (xy 181.388077 100.913729)
        (xy 181.381267 100.907828)
        (xy 181.284431 100.823918)
        (xy 181.284428 100.823916)
        (xy 181.277619 100.818016)
        (xy 181.14467 100.7573)
        (xy 181.135755 100.756018)
        (xy 181.135754 100.756018)
        (xy 181.004448 100.737139)
        (xy 181.004441 100.737138)
        (xy 181 100.7365)
        (xy 179 100.7365)
        (xy 178.926921 100.741727)
        (xy 178.873884 100.7573)
        (xy 178.79533 100.780365)
        (xy 178.795328 100.780366)
        (xy 178.786684 100.782904)
        (xy 178.779105 100.787775)
        (xy 178.671309 100.857051)
        (xy 178.671306 100.857053)
        (xy 178.663729 100.861923)
        (xy 178.657828 100.868733)
        (xy 178.573918 100.965569)
        (xy 178.573916 100.965572)
        (xy 178.568016 100.972381)
        (xy 178.5073 101.10533)
        (xy 178.506018 101.114247)
        (xy 178.50326 101.13343)
        (xy 178.473767 101.198011)
        (xy 178.414042 101.236396)
        (xy 178.378542 101.2415)
        (xy 171.749006 101.2415)
        (xy 171.603143 101.255802)
        (xy 171.413785 101.312973)
        (xy 171.239138 101.405834)
        (xy 171.153073 101.476027)
        (xy 171.090628 101.526955)
        (xy 171.090625 101.526958)
        (xy 171.085853 101.53085)
        (xy 171.081926 101.535597)
        (xy 171.081924 101.535599)
        (xy 170.963698 101.678509)
        (xy 170.963695 101.678514)
        (xy 170.95977 101.683258)
        (xy 170.865692 101.857253)
        (xy 170.8072 102.046208)
        (xy 170.806557 102.052326)
        (xy 170.806556 102.052331)
        (xy 170.791248 102.197986)
        (xy 170.786525 102.242924)
        (xy 166.898782 102.242924)
        (xy 166.950175 102.154445)
        (xy 167.001433 101.985204)
        (xy 167.0085 101.906022)
        (xy 167.0085 100.607115)
        (xy 166.993022 100.474358)
        (xy 166.932687 100.308137)
        (xy 166.835731 100.160253)
        (xy 166.707355 100.038642)
        (xy 166.701028 100.034967)
        (xy 166.701024 100.034964)
        (xy 166.578598 99.963854)
        (xy 166.529739 99.912343)
        (xy 166.516485 99.842595)
        (xy 166.543045 99.776753)
        (xy 166.554911 99.76373)
        (xy 166.66697 99.65683)
        (xy 166.67401 99.648873)
        (xy 166.805218 99.472523)
        (xy 166.810822 99.463486)
        (xy 166.910441 99.26755)
        (xy 166.914441 99.257699)
        (xy 166.979622 99.047778)
        (xy 166.981905 99.037396)
        (xy 166.983951 99.021959)
        (xy 166.981754 99.007792)
        (xy 166.96857 99.004)
        (xy 165.754002 99.003999)
        (xy 165.753996 99.004)
        (xy 164.033716 99.003999)
        (xy 164.020185 99.007972)
        (xy 164.01866 99.018579)
        (xy 164.043391 99.136445)
        (xy 164.046451 99.146642)
        (xy 164.127182 99.351068)
        (xy 164.131916 99.360604)
        (xy 164.245942 99.548514)
        (xy 164.252206 99.557104)
        (xy 164.396264 99.723116)
        (xy 164.403895 99.730536)
        (xy 164.435322 99.756305)
        (xy 164.475317 99.814965)
        (xy 164.477248 99.885935)
        (xy 164.440503 99.946684)
        (xy 164.413192 99.965478)
        (xy 164.408136 99.967313)
        (xy 164.402011 99.971329)
        (xy 164.291663 100.043676)
        (xy 164.260253 100.064269)
        (xy 164.138642 100.192645)
        (xy 164.049825 100.345555)
        (xy 163.998567 100.514796)
        (xy 163.9915 100.593978)
        (xy 163.008 100.593978)
        (xy 163.008 98.478041)
        (xy 164.016049 98.478041)
        (xy 164.018246 98.492208)
        (xy 164.03143 98.496)
        (xy 165.227885 98.496001)
        (xy 165.243124 98.491526)
        (xy 165.244329 98.490136)
        (xy 165.246 98.482453)
        (xy 165.246 98.477885)
        (xy 165.753999 98.477885)
        (xy 165.758474 98.493124)
        (xy 165.759864 98.494329)
        (xy 165.767547 98.496)
        (xy 166.966284 98.496001)
        (xy 166.979815 98.492028)
        (xy 166.98134 98.481421)
        (xy 166.981017 98.479877)
        (xy 179.134739 98.479877)
        (xy 179.143846 98.491734)
        (xy 179.221157 98.546677)
        (xy 179.229815 98.551838)
        (xy 179.438488 98.654518)
        (xy 179.447872 98.658233)
        (xy 179.670257 98.726223)
        (xy 179.680127 98.728393)
        (xy 179.91052 98.759953)
        (xy 179.920611 98.760517)
        (xy 180.153091 98.754836)
        (xy 180.163124 98.753781)
        (xy 180.391716 98.711001)
        (xy 180.401455 98.708355)
        (xy 180.620263 98.629579)
        (xy 180.629456 98.625409)
        (xy 180.832848 98.512667)
        (xy 180.841264 98.507076)
        (xy 180.858704 98.493353)
        (xy 180.867173 98.481451)
        (xy 180.860676 98.469886)
        (xy 180.012812 97.622022)
        (xy 179.998868 97.614408)
        (xy 179.997035 97.614539)
        (xy 179.99042 97.61879)
        (xy 179.141243 98.467967)
        (xy 179.134739 98.479877)
        (xy 166.981017 98.479877)
        (xy 166.956609 98.363555)
        (xy 166.953549 98.353358)
        (xy 166.872818 98.148932)
        (xy 166.868084 98.139396)
        (xy 166.754058 97.951486)
        (xy 166.747794 97.942896)
        (xy 166.603736 97.776884)
        (xy 166.596105 97.769464)
        (xy 166.42614 97.6301)
        (xy 166.417373 97.624075)
        (xy 166.226356 97.515342)
        (xy 166.216692 97.510877)
        (xy 166.010084 97.435882)
        (xy 165.999816 97.433111)
        (xy 165.782354 97.393788)
        (xy 165.774124 97.392853)
        (xy 165.769889 97.392654)
        (xy 165.756876 97.396475)
        (xy 165.755671 97.397865)
        (xy 165.754 97.405548)
        (xy 165.753999 98.477885)
        (xy 165.246 98.477885)
        (xy 165.246001 97.413939)
        (xy 165.241691 97.399261)
        (xy 165.229808 97.397198)
        (xy 165.125669 97.406035)
        (xy 165.115181 97.407827)
        (xy 164.902427 97.463048)
        (xy 164.892399 97.46658)
        (xy 164.691998 97.556853)
        (xy 164.682692 97.562033)
        (xy 164.500366 97.684782)
        (xy 164.49208 97.691443)
        (xy 164.33303 97.84317)
        (xy 164.32599 97.851127)
        (xy 164.194782 98.027477)
        (xy 164.189178 98.036514)
        (xy 164.089559 98.23245)
        (xy 164.085559 98.242301)
        (xy 164.020378 98.452222)
        (xy 164.018095 98.462604)
        (xy 164.016049 98.478041)
        (xy 163.008 98.478041)
        (xy 163.008 97.350479)
        (xy 178.490739 97.350479)
        (xy 178.491443 97.36055)
        (xy 178.526218 97.59049)
        (xy 178.528522 97.600314)
        (xy 178.599613 97.821735)
        (xy 178.603461 97.831071)
        (xy 178.70904 98.038283)
        (xy 178.714325 98.046874)
        (xy 178.758392 98.107083)
        (xy 178.769404 98.115513)
        (xy 178.782146 98.108644)
        (xy 179.627978 97.262812)
        (xy 179.634355 97.251132)
        (xy 180.364408 97.251132)
        (xy 180.364539 97.252965)
        (xy 180.36879 97.25958)
        (xy 181.218567 98.109357)
        (xy 181.230947 98.116117)
        (xy 181.23968 98.109579)
        (xy 181.323069 97.983113)
        (xy 181.327926 97.974278)
        (xy 181.423259 97.762154)
        (xy 181.426642 97.752653)
        (xy 181.486836 97.528007)
        (xy 181.488655 97.518095)
        (xy 181.512379 97.284527)
        (xy 181.512698 97.278816)
        (xy 181.51297 97.252857)
        (xy 181.512771 97.247152)
        (xy 181.493943 97.013142)
        (xy 181.492331 97.003189)
        (xy 181.436856 96.777337)
        (xy 181.433673 96.767767)
        (xy 181.342803 96.55369)
        (xy 181.33813 96.544753)
        (xy 181.242036 96.392155)
        (xy 181.231465 96.382824)
        (xy 181.222303 96.386907)
        (xy 180.372022 97.237188)
        (xy 180.364408 97.251132)
        (xy 179.634355 97.251132)
        (xy 179.635592 97.248868)
        (xy 179.635461 97.247035)
        (xy 179.63121 97.24042)
        (xy 178.781314 96.390524)
        (xy 178.768746 96.383661)
        (xy 178.757697 96.391845)
        (xy 178.731296 96.426375)
        (xy 178.725832 96.434854)
        (xy 178.615936 96.639809)
        (xy 178.611894 96.649061)
        (xy 178.536182 96.868947)
        (xy 178.533673 96.878719)
        (xy 178.494089 97.107885)
        (xy 178.493174 97.117933)
        (xy 178.490739 97.350479)
        (xy 163.008 97.350479)
        (xy 163.008 96.018957)
        (xy 179.134891 96.018957)
        (xy 179.142081 96.032871)
        (xy 179.987188 96.877978)
        (xy 180.001132 96.885592)
        (xy 180.002965 96.885461)
        (xy 180.00958 96.88121)
        (xy 180.861493 96.029297)
        (xy 180.868701 96.016096)
        (xy 180.862495 96.007403)
        (xy 180.85912 96.005057)
        (xy 180.658114 95.888069)
        (xy 180.649023 95.883714)
        (xy 180.431907 95.800371)
        (xy 180.42223 95.797523)
        (xy 180.194591 95.749967)
        (xy 180.184564 95.7487)
        (xy 179.952251 95.738152)
        (xy 179.942173 95.738503)
        (xy 179.71115 95.765233)
        (xy 179.701256 95.767192)
        (xy 179.477473 95.830516)
        (xy 179.468029 95.834028)
        (xy 179.257247 95.932318)
        (xy 179.248482 95.937297)
        (xy 179.143215 96.008836)
        (xy 179.134891 96.018957)
        (xy 163.008 96.018957)
        (xy 163.008 90.194416)
        (xy 163.028002 90.126295)
        (xy 163.081658 90.079802)
        (xy 163.151932 90.069698)
        (xy 163.216512 90.099192)
        (xy 163.248425 90.141664)
        (xy 163.321643 90.300485)
        (xy 163.509469 90.612463)
        (xy 163.730395 90.901946)
        (xy 163.732781 90.904447)
        (xy 163.979358 91.162928)
        (xy 163.979364 91.162933)
        (xy 163.981753 91.165438)
        (xy 164.260508 91.399755)
        (xy 164.563291 91.602069)
        (xy 164.886447 91.769935)
        (xy 165.226072 91.901326)
        (xy 165.22942 91.902214)
        (xy 165.229424 91.902215)
        (xy 165.479178 91.968436)
        (xy 165.578064 91.994655)
        (xy 165.791209 92.0267)
        (xy 165.934751 92.048281)
        (xy 165.934757 92.048282)
        (xy 165.938172 92.048795)
        (xy 165.941629 92.048931)
        (xy 165.941631 92.048931)
        (xy 166.04231 92.052886)
        (xy 166.302046 92.063091)
        (xy 166.30549 92.062847)
        (xy 166.305499 92.062847)
        (xy 166.661834 92.037617)
        (xy 166.661837 92.037617)
        (xy 166.665292 92.037372)
        (xy 166.668698 92.03675)
        (xy 167.020112 91.972571)
        (xy 167.020119 91.972569)
        (xy 167.023521 91.971948)
        (xy 167.372408 91.867608)
        (xy 167.707738 91.725614)
        (xy 167.710746 91.723929)
        (xy 167.710753 91.723926)
        (xy 167.931782 91.600144)
        (xy 168.025462 91.547681)
        (xy 168.321742 91.335956)
        (xy 168.592998 91.092998)
        (xy 168.835956 90.821742)
        (xy 169.047681 90.525462)
        (xy 169.123923 90.389322)
        (xy 169.223926 90.210753)
        (xy 169.223929 90.210746)
        (xy 169.225614 90.207738)
        (xy 169.367608 89.872408)
        (xy 169.471948 89.523521)
        (xy 169.492001 89.413725)
        (xy 169.53675 89.168698)
        (xy 169.53675 89.168697)
        (xy 169.537372 89.165292)
        (xy 169.563091 88.802046)
        (xy 169.5635 88.75)
        (xy 169.54349 88.386395)
        (xy 169.509759 88.183739)
        (xy 169.48427 88.030608)
        (xy 169.4837 88.027182)
        (xy 169.384854 87.6767)
        (xy 169.248144 87.33918)
        (xy 169.075223 87.018701)
        (xy 168.868179 86.719132)
        (xy 168.629511 86.444093)
        (xy 168.362104 86.196904)
        (xy 168.069187 85.980552)
        (xy 168.066197 85.978815)
        (xy 168.066192 85.978812)
        (xy 167.757301 85.799394)
        (xy 167.757297 85.799392)
        (xy 167.754298 85.79765)
        (xy 167.644797 85.74924)
        (xy 167.590581 85.703402)
        (xy 167.569753 85.635529)
        (xy 167.588927 85.567171)
        (xy 167.642015 85.52003)
        (xy 167.695744 85.508)
        (xy 184.802295 85.508)
      )
    )
  )
)
