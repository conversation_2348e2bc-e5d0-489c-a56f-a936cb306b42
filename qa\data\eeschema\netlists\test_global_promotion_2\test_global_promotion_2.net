(export (version "E")
  (design
    (source "test_global_promotion_2.kicad_sch")
    (date "Sun Feb 21 20:23:42 2021")
    (tool "Eeschema (5.99.0-9272-g68b81c8271-dirty)")
    (sheet (number "1") (name "/") (tstamps "/")
      (title_block
        (title)
        (company)
        (rev)
        (date)
        (source "test_global_promotion_2.kicad_sch")
        (comment (number "1") (value ""))
        (comment (number "2") (value ""))
        (comment (number "3") (value ""))
        (comment (number "4") (value ""))
        (comment (number "5") (value ""))
        (comment (number "6") (value ""))
        (comment (number "7") (value ""))
        (comment (number "8") (value ""))
        (comment (number "9") (value ""))))
    (sheet (number "2") (name "/Sheet5CC15EF8/") (tstamps "/00000000-0000-0000-0000-00005cc15ef9/")
      (title_block
        (title)
        (company)
        (rev)
        (date)
        (source "subsheet.kicad_sch")
        (comment (number "1") (value ""))
        (comment (number "2") (value ""))
        (comment (number "3") (value ""))
        (comment (number "4") (value ""))
        (comment (number "5") (value ""))
        (comment (number "6") (value ""))
        (comment (number "7") (value ""))
        (comment (number "8") (value ""))
        (comment (number "9") (value ""))))
    (sheet (number "3") (name "/sheet5CC165F1/") (tstamps "/00000000-0000-0000-0000-00005cc165f1/")
      (title_block
        (title)
        (company)
        (rev)
        (date)
        (source "subsheet.kicad_sch")
        (comment (number "1") (value ""))
        (comment (number "2") (value ""))
        (comment (number "3") (value ""))
        (comment (number "4") (value ""))
        (comment (number "5") (value ""))
        (comment (number "6") (value ""))
        (comment (number "7") (value ""))
        (comment (number "8") (value ""))
        (comment (number "9") (value "")))))
  (components
    (comp (ref "J1")
      (value "Conn_01x03_Male")
      (footprint "Connector_PinHeader_2.54mm:PinHeader_1x03_P2.54mm_Vertical")
      (datasheet "~")
      (libsource (lib "Connector") (part "Conn_01x03_Male") (description "Generic connector, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"))
      (property (name "Sheetname") (value ""))
      (property (name "Sheetfile") (value "test_global_promotion_2.kicad_sch"))
      (sheetpath (names "/") (tstamps "/"))
      (tstamps "00000000-0000-0000-0000-00005ccf482d"))
    (comp (ref "J2")
      (value "Conn_01x03_Male")
      (footprint "Connector_PinHeader_2.54mm:PinHeader_1x03_P2.54mm_Vertical")
      (datasheet "~")
      (libsource (lib "Connector") (part "Conn_01x03_Male") (description "Generic connector, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)"))
      (property (name "Sheetname") (value ""))
      (property (name "Sheetfile") (value "test_global_promotion_2.kicad_sch"))
      (sheetpath (names "/") (tstamps "/"))
      (tstamps "00000000-0000-0000-0000-00005cc1686d"))
    (comp (ref "R1")
      (value "R")
      (footprint "Resistor_SMD:R_0603_1608Metric")
      (datasheet "~")
      (libsource (lib "Device") (part "R") (description "Resistor"))
      (property (name "Sheetname") (value "Sheet5CC15EF8"))
      (property (name "Sheetfile") (value "subsheet.kicad_sch"))
      (sheetpath (names "/Sheet5CC15EF8/") (tstamps "/00000000-0000-0000-0000-00005cc15ef9/"))
      (tstamps "00000000-0000-0000-0000-00005cc15f6f"))
    (comp (ref "R2")
      (value "R")
      (footprint "Resistor_SMD:R_0603_1608Metric")
      (datasheet "~")
      (libsource (lib "Device") (part "R") (description "Resistor"))
      (property (name "Sheetname") (value "Sheet5CC15EF8"))
      (property (name "Sheetfile") (value "subsheet.kicad_sch"))
      (sheetpath (names "/Sheet5CC15EF8/") (tstamps "/00000000-0000-0000-0000-00005cc15ef9/"))
      (tstamps "00000000-0000-0000-0000-00005ce0545a"))
    (comp (ref "R3")
      (value "R")
      (footprint "Resistor_SMD:R_0603_1608Metric")
      (datasheet "~")
      (libsource (lib "Device") (part "R") (description "Resistor"))
      (property (name "Sheetname") (value "sheet5CC165F1"))
      (property (name "Sheetfile") (value "subsheet.kicad_sch"))
      (sheetpath (names "/sheet5CC165F1/") (tstamps "/00000000-0000-0000-0000-00005cc165f1/"))
      (tstamps "00000000-0000-0000-0000-00005cc15f6f"))
    (comp (ref "R4")
      (value "R")
      (footprint "Resistor_SMD:R_0603_1608Metric")
      (datasheet "~")
      (libsource (lib "Device") (part "R") (description "Resistor"))
      (property (name "Sheetname") (value "sheet5CC165F1"))
      (property (name "Sheetfile") (value "subsheet.kicad_sch"))
      (sheetpath (names "/sheet5CC165F1/") (tstamps "/00000000-0000-0000-0000-00005cc165f1/"))
      (tstamps "00000000-0000-0000-0000-00005ce0545a")))
  (libparts
    (libpart (lib "Connector") (part "Conn_01x03_Male")
      (description "Generic connector, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)")
      (footprints
        (fp "Connector*:*_1x??_*"))
      (fields
        (field (name "Reference") "J")
        (field (name "Value") "Conn_01x03_Male"))
      (pins
        (pin (num "1") (name "Pin_1") (type "passive"))
        (pin (num "2") (name "Pin_2") (type "passive"))
        (pin (num "3") (name "Pin_3") (type "passive"))))
    (libpart (lib "Device") (part "R")
      (description "Resistor")
      (footprints
        (fp "R_*"))
      (fields
        (field (name "Reference") "R")
        (field (name "Value") "R"))
      (pins
        (pin (num "1") (name "~") (type "passive"))
        (pin (num "2") (name "~") (type "passive")))))
  (libraries)
  (nets
    (net (code "1") (name "/LIVE_2")
      (node (ref "J2") (pin "2") (pinfunction "Pin_2") (pintype "passive"))
      (node (ref "R3") (pin "1") (pintype "passive"))
      (node (ref "R4") (pin "1") (pintype "passive")))
    (net (code "2") (name "/Sheet5CC15EF8/LIVE")
      (node (ref "J2") (pin "1") (pinfunction "Pin_1") (pintype "passive"))
      (node (ref "R1") (pin "1") (pintype "passive"))
      (node (ref "R2") (pin "1") (pintype "passive")))
    (net (code "3") (name "/Sheet5CC15EF8/NEUTRAL")
      (node (ref "J2") (pin "3") (pinfunction "Pin_3") (pintype "passive"))
      (node (ref "R1") (pin "2") (pintype "passive"))
      (node (ref "R3") (pin "2") (pintype "passive")))
    (net (code "4") (name "/sheet5CC165F1/LIVE_1")
      (node (ref "J1") (pin "2") (pinfunction "Pin_2") (pintype "passive"))
      (node (ref "R4") (pin "2") (pintype "passive")))
    (net (code "5") (name "LIVE")
      (node (ref "J1") (pin "1") (pinfunction "Pin_1") (pintype "passive"))
      (node (ref "R2") (pin "2") (pintype "passive")))
    (net (code "6") (name "unconnected-(J1-Pin_3-Pad3)")
      (node (ref "J1") (pin "3") (pinfunction "Pin_3") (pintype "passive")))))
