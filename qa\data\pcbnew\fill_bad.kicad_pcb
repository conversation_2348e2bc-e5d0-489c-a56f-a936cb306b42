(kicad_pcb (version 20230620) (generator pcbnew)

  (general
    (thickness 0.8)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (1 "In1.Cu" signal)
    (2 "In2.Cu" signal)
    (31 "B.Cu" signal)
    (32 "<PERSON><PERSON>" user "B.Adhesive")
    (33 "<PERSON><PERSON>" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.<PERSON>" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "prepreg") (thickness 0.1) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In1.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 2" (type "core") (thickness 0.44) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In2.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 3" (type "prepreg") (thickness 0.1) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "None")
      (dielectric_constraints no)
    )
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00310fc_ffffffff)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions true)
      (usegerberattributes false)
      (usegerberadvancedattributes false)
      (creategerberjobfile false)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 6)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (pdf_front_fp_property_popups true)
      (pdf_back_fp_property_popups true)
      (dxfpolygonmode true)
      (dxfimperialunits false)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue false)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk true)
      (outputformat 1)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "production")
    )
  )

  (property "PROJECT_ID" "726")
  (property "PROJECT_NAME" "sbm-swappable-battery-module")
  (property "PROJECT_NUMBER" "1743")
  (property "PROJECT_VERSION" "V1.0.0")

  (net 0 "")
  (net 1 "A")
  (net 2 "B")

  (footprint "lib:SolderWirePad_1x01_SMD_2.9x7.8mm" (layer "F.Cu")
    (tstamp 985a7dcf-c008-45a3-beea-302b347528bd)
    (at 43.415695 32.455979)
    (descr "Wire Pad, Square, SMD Pad,  2.9mm x 7.8mm,")
    (tags "MesurementPoint Square SMDPad 2.9mmx7.8mm ")
    (property "Reference" "J2" (at 0 -5 0) (layer "F.SilkS") hide (tstamp ac0323d0-f9d1-415d-8ab0-4f2b542b7105)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "CELL2" (at 0 2.54 0) (layer "F.Fab") (tstamp ee4ebfd6-b470-4619-8a05-587482ca9107)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 132e5ecd-832a-43a1-bd36-f8d2f08d9111)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp de9c06cd-cc8f-480e-88b8-0b8b0e8513d3)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Generic connector, single row, 01x01, script generated (kicad-library-utils/schlib/autogen/connector/)" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 96b75f7f-efa2-4720-b13b-389d9d633236)
      (effects (font (size 1.27 1.27)))
    )
    (path "/91b6036e-0932-4b4d-be06-d288cef86586")
    (sheetfile "prj.kicad_sch")
    (clearance 1)
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_line (start -1.8 -4.25) (end -1.8 4.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp cbf60708-41dc-406e-93fd-3eef2d09e171))
    (fp_line (start -1.8 -4.25) (end 1.8 -4.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 9ab144db-ea9d-4b42-b580-0b5c3609563a))
    (fp_line (start -1.8 4.25) (end 1.8 4.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp d825f37d-1d08-4e80-a724-6f49cb22d11d))
    (fp_line (start 1.8 4.25) (end 1.8 -4.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e2990c39-af71-4866-856c-0e69ec4b057c))
    (fp_line (start -0.63 1.27) (end -0.63 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3717a07f-2ca1-438e-8252-f4f0e7118a61))
    (fp_line (start 0.63 -1.27) (end 0.63 1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3eb3e3dc-a49f-4bdc-b602-89cabfabd598))
    (fp_line (start 0.63 1.27) (end -0.63 1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1260bb2d-354e-4a5b-8a86-af46d5621096))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 7506a551-abf0-4da1-b5ba-412716d0cda2)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad "1" smd roundrect (at 0 0) (size 2.6 7.5) (layers "F.Cu" "F.Mask") (roundrect_rratio 0.1)
      (net 2 "B") (pinfunction "Pin_1") (pintype "passive")
      (tstamp 01d6b91f-b519-4000-9ea0-db5a3aad940e)
    )
  )

  (footprint "lib:SolderWirePad_1x01_SMD_2.9x7.8mm" (layer "F.Cu")
    (tstamp a6236385-3cd5-4b9e-b0ab-d5b0da6d6f5f)
    (at 36.750695 25.759979 -90)
    (descr "Wire Pad, Square, SMD Pad,  2.9mm x 7.8mm,")
    (tags "MesurementPoint Square SMDPad 2.9mmx7.8mm ")
    (property "Reference" "J1" (at 0 -5 90) (layer "F.SilkS") hide (tstamp 4cc314e5-52bd-4cbf-bfa6-75b6e1d965d6)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "CELL3" (at 0 2.54 90) (layer "F.Fab") (tstamp 3f2e07ef-e2e7-46a1-8808-6cad1c39ee0d)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 00cc72aa-5cbb-4759-9109-da5bedbf8875)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 61a02f88-4556-4f60-acb1-8e258fd9e93b)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "Generic connector, single row, 01x01, script generated (kicad-library-utils/schlib/autogen/connector/)" (at 0 0 -90 unlocked) (layer "F.Fab") hide (tstamp 0b01f958-4be9-4ed1-b246-813a8218eab6)
      (effects (font (size 1.27 1.27)))
    )
    (property "exclude_from_bom" "" (at 0 0 0) (layer "F.Fab") hide (tstamp 39446602-4c58-47e6-bb20-782f536cdd9e)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (path "/5ad82b76-2335-44e8-a6b4-714733473d51")
    (sheetfile "prj.kicad_sch")
    (clearance 1)
    (attr exclude_from_pos_files exclude_from_bom)
    (fp_line (start -1.8 4.25) (end 1.8 4.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 57a45e4d-441c-4e4e-a353-f1fe755abc3e))
    (fp_line (start 1.8 4.25) (end 1.8 -4.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp d4b8af1c-7a09-401a-a2ae-3eac5d829e3e))
    (fp_line (start -1.8 -4.25) (end -1.8 4.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 8695f857-504f-488c-b70e-52ba78f15ba0))
    (fp_line (start -1.8 -4.25) (end 1.8 -4.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 14150567-0f61-4d40-86d9-e9cfdfc67e54))
    (fp_line (start -0.63 1.27) (end -0.63 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 77292bf1-0b86-4fe3-b82f-c8342f27593e))
    (fp_line (start 0.63 1.27) (end -0.63 1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7a6ad48d-b38f-4800-8a23-0b89bcebdc52))
    (fp_line (start 0.63 -1.27) (end 0.63 1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2ff8271a-3b16-46cc-9ce0-2706191c79ef))
    (pad "1" smd roundrect (at 0 0 270) (size 2.6 7.5) (layers "F.Cu" "F.Mask") (roundrect_rratio 0.1)
      (net 1 "A") (pinfunction "Pin_1") (pintype "passive")
      (tstamp 346be68c-a951-4598-9dfa-9f55a97eec49)
    )
  )

  (gr_rect (start 30.638641 20.033393) (end 47.301417 38.344136)
    (stroke (width 0.15) (type default)) (fill none) (layer "Edge.Cuts") (tstamp 95593e1f-5d4b-498b-a07b-44ed17522f54))

  (zone (net 1) (net_name "A") (layer "F.Cu") (tstamp 529fe1ce-df30-480a-99f3-dacacf3a1338) (hatch edge 0.5)
    (priority 1)
    (connect_pads yes (clearance 0.127))
    (min_thickness 0.127) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.5) (thermal_bridge_width 0.5) (smoothing fillet) (radius 0.127))
    (polygon
      (pts
        (xy 32.425429 21.365684)
        (xy 32.287681 28.387628)
        (xy 42.897681 28.367628)
        (xy 43.035429 21.345684)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 42.913717 21.3481)
        (xy 42.953588 21.358856)
        (xy 42.981891 21.375396)
        (xy 43.00477 21.398683)
        (xy 43.020809 21.42728)
        (xy 43.030854 21.467328)
        (xy 43.03272 21.483759)
        (xy 42.900278 28.235242)
        (xy 42.897893 28.251158)
        (xy 42.886857 28.289852)
        (xy 42.870555 28.317294)
        (xy 42.847837 28.339613)
        (xy 42.820109 28.355427)
        (xy 42.781224 28.365775)
        (xy 42.765269 28.367877)
        (xy 32.425782 28.387367)
        (xy 32.409386 28.38521)
        (xy 32.384958 28.37862)
        (xy 32.369522 28.374456)
        (xy 32.341218 28.357915)
        (xy 32.318339 28.334628)
        (xy 32.3023 28.306032)
        (xy 32.292254 28.265981)
        (xy 32.290389 28.249556)
        (xy 32.422832 21.498062)
        (xy 32.425214 21.482162)
        (xy 32.436253 21.443456)
        (xy 32.452552 21.416019)
        (xy 32.475274 21.393696)
        (xy 32.502997 21.377885)
        (xy 32.541891 21.367535)
        (xy 32.557834 21.365434)
        (xy 42.897332 21.345944)
      )
    )
  )
)
