EESchema-LIBRARY Version  11/12/2006-20:12:43
#
#
# C
#
DEF C C 0 10 N Y 1 F N
F0 "C" 50 100 50 H V L C
F1 "C" 50 -100 50 H V L C
$FPLIST
 C?
 SM*
$ENDFPLIST
DRAW
P 2 0 1 0  -100 -30  100 -30 N
P 2 0 1 0  -100 30  100 30 N
X ~ 1 0 200 170 D 40 40 1 1 P
X ~ 2 0 -200 170 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# CONN_2
#
DEF CONN_2 P 0 40 Y N 1 F N
F0 "P" -50 0 40 V V C C
F1 "CONN_2" 50 0 40 V V C C
DRAW
S -100 150 100 -150 0 1 0 N
X PM 2 -350 -100 250 R 60 60 1 1 P I
X P1 1 -350 100 250 R 60 60 1 1 P I
ENDDRAW
ENDDEF
#
# ECC83_2
#
DEF ECC83_2 U 0 40 Y N 1 F N
F0 "U" 100 -500 60 H V C C
F1 "ECC83_2" 150 700 60 H V C C
DRAW
C 150 -125 25 0 1 0 F
C -150 -125 25 0 1 0 F
P 3 0 1 0  150 0  175 0  175 0 N
P 3 0 1 0  275 -125  175 -125  175 -125 N
A -200 -149 100 -1794 -900 0 1 0 N -300 -150 -200 -250
P 3 0 1 0  -200 250  200 250  200 250 N
A -150 -150 50 -1799 -1 0 1 0 N -200 -150 -100 -150
A 200 -149 100 -5 -900 0 1 0 N 300 -150 200 -250
P 4 0 1 0  -350 -150  -275 -125  -175 -125  -175 -125 N
P 3 0 1 0  -300 -150  -300 150  -300 150 N
P 3 0 1 0  -25 0  0 0  0 0 N
P 3 0 1 0  200 -250  -200 -250  -200 -250 N
S 50 200 250 175 0 1 0 N
P 3 0 1 0  300 -150  300 -150  300 -150 N
A 199 150 100 894 0 0 1 0 N 200 250 300 150
P 3 0 1 0  -175 0  -150 0  -150 0 N
P 3 0 1 0  300 150  300 -150  300 -150 N
A 150 -150 50 -1799 -1 0 1 0 N 100 -150 200 -150
P 3 0 1 0  350 -150  275 -125  275 -125 N
P 3 0 1 0  100 0  125 0  125 0 N
P 5 0 1 0  -225 0  -200 0  -200 0  -200 0  -200 0 N
P 3 0 1 0  -100 -150  50 -150  50 -150 N
A -199 150 100 905 1800 0 1 0 N -200 250 -300 150
S -250 200 -50 175 0 1 0 N
P 3 0 1 0  50 -150  100 -150  100 -150 N
P 3 0 1 0  50 0  75 0  75 0 N
P 3 0 1 0  200 0  225 0  225 0 N
P 3 0 1 0  -125 0  -100 0  -100 0 N
X Anod 1 -150 500 300 D 50 50 1 1 P
X Anod 1 -150 500 300 D 50 50 1 1 P
X Anod 6 150 500 300 D 50 50 0 0 P
X GRID 7 550 0 300 L 50 50 0 0 I
X GRID 2 -550 0 300 R 50 50 1 1 I
X GRID 2 -550 0 300 R 50 50 1 1 I
X K 3 -350 -350 200 U 50 50 1 1 I
X K 3 -350 -350 200 U 50 50 1 1 I
X Heather 5 -200 -450 300 U 50 50 1 1 I
X Heather 9 0 -450 300 U 50 50 1 1 I
X Heather 4 200 -450 300 U 50 50 1 1 I
X K 8 350 -350 200 U 50 50 0 0 I
ENDDRAW
ENDDEF
#
# GND
#
DEF ~GND #PWR 0 0 Y Y 1 F P
F0 "#PWR" 0 0 30 H I C C
F1 "GND" 0 -70 30 H I C C
DRAW
P 4 0 1 0  -50 0  0 -50  50 0  -50 0 N
X GND 1 0 0 0 U 30 30 1 1 W N
ENDDRAW
ENDDEF
#
# PWR_FLAG
#
DEF PWR_FLAG #FLG 0 0 N N 1 F P
F0 "#FLG" 0 270 30 H I C C
F1 "PWR_FLAG" 0 230 30 H V C C
DRAW
P 3 0 1 0  0 0  0 100  0 100 N
P 5 0 1 0  0 100  -100 150  0 200  100 150  0 100 N
X pwr 1 0 0 0 U 20 20 0 0 w
ENDDRAW
ENDDEF
#
# R
#
DEF R R 0 0 N Y 1 F N
F0 "R" 80 0 50 V V C C
F1 "R" 0 0 50 V V C C
$FPLIST
 R?
 SM0603
 SM0805
$ENDFPLIST
DRAW
S -40 150 40 -150 0 1 0 N
X ~ 1 0 250 100 D 60 60 1 1 P
X ~ 2 0 -250 100 U 60 60 1 1 P
ENDDRAW
ENDDEF
#
#EndLibrary
