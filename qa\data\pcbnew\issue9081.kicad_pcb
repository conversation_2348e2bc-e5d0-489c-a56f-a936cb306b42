(kicad_pcb (version 20210824) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.Adhes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
    (50 "User.1" user)
    (51 "User.2" user)
    (52 "User.3" user)
    (53 "User.4" user)
    (54 "User.5" user)
    (55 "User.6" user)
    (56 "User.7" user)
    (57 "User.8" user)
    (58 "User.9" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (color "Green") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "core") (thickness 1.51) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (color "Green") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "None")
      (dielectric_constraints no)
    )
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (svguseinch false)
      (svgprecision 6)
      (excludeedgelayer true)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "unconnected-(J1-Pad1)")
  (net 2 "unconnected-(J1-Pad2)")
  (net 3 "Net-(J1-Pad3)")
  (net 4 "unconnected-(J1-Pad4)")
  (net 5 "unconnected-(J1-Pad5)")
  (net 6 "Net-(J1-Pad6)")
  (net 7 "unconnected-(J1-Pad7)")
  (net 8 "unconnected-(J1-Pad8)")

  (footprint "Connector_PinHeader_2.54mm:PinHeader_1x08_P2.54mm_Vertical" (layer "F.Cu")
    (tedit 59FED5CC) (tstamp f942499e-6e89-458d-96f7-b17de56e7805)
    (at 133.87 100.395)
    (descr "Through hole straight pin header, 1x08, 2.54mm pitch, single row")
    (tags "Through hole pin header THT 1x08 2.54mm single row")
    (property "Sheetfile" "asdf_DRC_THThole_5.99.kicad_sch")
    (property "Sheetname" "")
    (path "/10d9fe7c-1470-4087-b5e0-7fc4e0ea6dcc")
    (attr through_hole)
    (fp_text reference "J1" (at 0 -2.33) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp abddb9f1-e8f1-45e1-9018-331fe9da67d7)
    )
    (fp_text value "Conn_01x08" (at 0 20.11) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp f1da5067-9a8e-4918-b22b-6e92d8080b49)
    )
    (fp_text user "${REFERENCE}" (at 0 8.89 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 988fbed7-2acf-4203-b243-e17951632c6f)
    )
    (fp_line (start -1.33 -1.33) (end 0 -1.33) (layer "F.SilkS") (width 0.12) (tstamp 308c4ce8-ac74-4b13-9dac-ba9f88ec9623))
    (fp_line (start -1.33 1.27) (end -1.33 19.11) (layer "F.SilkS") (width 0.12) (tstamp 5385afbc-f099-466a-b564-bc19e404d27c))
    (fp_line (start 1.33 1.27) (end 1.33 19.11) (layer "F.SilkS") (width 0.12) (tstamp 6a514254-8ce8-4715-9582-619bad3ceaa1))
    (fp_line (start -1.33 19.11) (end 1.33 19.11) (layer "F.SilkS") (width 0.12) (tstamp b4ce60c4-bb1b-43df-adb0-f56112bcea07))
    (fp_line (start -1.33 0) (end -1.33 -1.33) (layer "F.SilkS") (width 0.12) (tstamp dd34df30-1b0b-41e6-9b3a-9bcee81ce5b3))
    (fp_line (start -1.33 1.27) (end 1.33 1.27) (layer "F.SilkS") (width 0.12) (tstamp f07f7e83-06b1-454b-9ce1-69f925a12bb5))
    (fp_line (start 1.8 19.55) (end 1.8 -1.8) (layer "F.CrtYd") (width 0.05) (tstamp 01fc8f3d-08da-491d-bd47-02ca97a27197))
    (fp_line (start -1.8 19.55) (end 1.8 19.55) (layer "F.CrtYd") (width 0.05) (tstamp 29035e40-5d75-423b-8cfd-bbe1f552a44e))
    (fp_line (start 1.8 -1.8) (end -1.8 -1.8) (layer "F.CrtYd") (width 0.05) (tstamp cfb9f145-2d5d-46aa-b6a1-4c4b224391b3))
    (fp_line (start -1.8 -1.8) (end -1.8 19.55) (layer "F.CrtYd") (width 0.05) (tstamp dce14071-229c-4686-b95a-52b73a31b5bd))
    (fp_line (start 1.27 19.05) (end -1.27 19.05) (layer "F.Fab") (width 0.1) (tstamp 13e7305b-06d1-44bd-ac40-1cdc9036b511))
    (fp_line (start -1.27 -0.635) (end -0.635 -1.27) (layer "F.Fab") (width 0.1) (tstamp 24a3216b-141c-41a1-b16d-8c24ac4e3961))
    (fp_line (start -0.635 -1.27) (end 1.27 -1.27) (layer "F.Fab") (width 0.1) (tstamp 681e4038-5284-466a-9c13-6ab67b25fc23))
    (fp_line (start -1.27 19.05) (end -1.27 -0.635) (layer "F.Fab") (width 0.1) (tstamp 70cad101-1312-4d40-a56c-4fc77a8f6e91))
    (fp_line (start 1.27 -1.27) (end 1.27 19.05) (layer "F.Fab") (width 0.1) (tstamp 7f7f3567-cd01-4a12-aab2-eaf6b6652bf4))
    (pad "1" thru_hole rect locked (at 0 0) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 1 "unconnected-(J1-Pad1)") (pinfunction "Pin_1") (pintype "passive+no_connect") (tstamp 66e2a15f-475a-473d-911d-4cbf29bed935))
    (pad "2" thru_hole oval locked (at 0 2.54) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 2 "unconnected-(J1-Pad2)") (pinfunction "Pin_2") (pintype "passive+no_connect") (tstamp f00656a3-c63d-4945-950c-98b6eb364dd3))
    (pad "3" thru_hole oval locked (at 0 5.08) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 3 "Net-(J1-Pad3)") (pinfunction "Pin_3") (pintype "passive") (tstamp 335bf42e-0bd8-4d93-811e-88428674ac40))
    (pad "4" thru_hole oval locked (at 0 7.62) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 4 "unconnected-(J1-Pad4)") (pinfunction "Pin_4") (pintype "passive+no_connect") (tstamp a86070f5-e288-414d-8ab1-f3c0dd3a7e97))
    (pad "5" thru_hole oval locked (at 0 10.16) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 5 "unconnected-(J1-Pad5)") (pinfunction "Pin_5") (pintype "passive+no_connect") (tstamp 691c2f09-0a78-40a0-b609-fcb3aca7d156))
    (pad "6" thru_hole oval locked (at 0 12.7) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 6 "Net-(J1-Pad6)") (pinfunction "Pin_6") (pintype "passive") (tstamp 48025f67-e6da-4fe1-9e0b-54d232312e9f))
    (pad "7" thru_hole oval locked (at 0 15.24) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 7 "unconnected-(J1-Pad7)") (pinfunction "Pin_7") (pintype "passive+no_connect") (tstamp 500d306c-d007-4238-9d4a-6cc36d39098e))
    (pad "8" thru_hole oval locked (at 0 17.78) (size 1.7 1.7) (drill 1) (layers *.Cu *.Mask)
      (net 8 "unconnected-(J1-Pad8)") (pinfunction "Pin_8") (pintype "passive+no_connect") (tstamp 2393e835-0eb7-46f7-9a1c-f3b1eb2c9dc7))
    (model "${KISYS3DMOD}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x08_P2.54mm_Vertical.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_4020_10251Metric" (layer "B.Cu")
    (tedit 5F68FEEE) (tstamp ba0edce2-e1c7-4597-861d-911cbd56157e)
    (at 133.41 106.76 180)
    (descr "Resistor SMD 4020 (10251 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: http://datasheet.octopart.com/HVC0603T5004FET-Ohmite-datasheet-26699797.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Sheetfile" "asdf_DRC_THThole_5.99.kicad_sch")
    (property "Sheetname" "")
    (path "/48f2fbd7-87d9-45c4-ae2d-cb0a73f9651d")
    (attr smd)
    (fp_text reference "R1" (at 7.5 4.49) (layer "B.SilkS")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp b2713db1-b75e-46b4-9d27-1ef8d6894927)
    )
    (fp_text value "2k2" (at 0 -3.6) (layer "B.Fab")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp d3438eb8-ebcc-47df-b35e-7c547b35ddd0)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "B.Fab")
      (effects (font (size 1 1) (thickness 0.15)) (justify mirror))
      (tstamp b486d3fd-365b-4c78-8eb4-67bf0a49d27c)
    )
    (fp_line (start -3.886252 -2.66) (end 3.886252 -2.66) (layer "B.SilkS") (width 0.12) (tstamp 2b8e5ba7-99af-4594-b33b-abb700a73b44))
    (fp_line (start -3.886252 2.66) (end 3.886252 2.66) (layer "B.SilkS") (width 0.12) (tstamp 7e2abf26-3709-4f3f-993d-c982036b3ef5))
    (fp_line (start -5.8 2.9) (end 5.8 2.9) (layer "B.CrtYd") (width 0.05) (tstamp 1e0317f1-6e91-4e2f-bed7-20909212f91b))
    (fp_line (start 5.8 -2.9) (end -5.8 -2.9) (layer "B.CrtYd") (width 0.05) (tstamp 4a2873de-68ea-4073-9a54-5f8bdd3d427d))
    (fp_line (start 5.8 2.9) (end 5.8 -2.9) (layer "B.CrtYd") (width 0.05) (tstamp 4cb2cc63-3904-43c1-9f76-a3a94c000740))
    (fp_line (start -5.8 -2.9) (end -5.8 2.9) (layer "B.CrtYd") (width 0.05) (tstamp aa2aae78-3106-4115-bb72-f2b5dfeed4e5))
    (fp_line (start 5.1 -2.55) (end -5.1 -2.55) (layer "B.Fab") (width 0.1) (tstamp 097174c7-9aac-4118-9d84-1f0f0429d6c8))
    (fp_line (start -5.1 2.55) (end 5.1 2.55) (layer "B.Fab") (width 0.1) (tstamp 13be7c0d-72ea-430b-b983-a3eaa6574a4f))
    (fp_line (start -5.1 -2.55) (end -5.1 2.55) (layer "B.Fab") (width 0.1) (tstamp 6dd20bc6-59b2-4bdf-ba8c-aaf47741f3fe))
    (fp_line (start 5.1 2.55) (end 5.1 -2.55) (layer "B.Fab") (width 0.1) (tstamp b12dc0c3-d7c3-45dd-a897-1a26cc990510))
    (pad "1" smd roundrect locked (at -4.8125 0 180) (size 1.475 5.3) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.169492)
      (net 3 "Net-(J1-Pad3)") (pintype "passive") (tstamp 3fd16f59-8444-4dd4-9b56-d1b966f0f5ac))
    (pad "2" smd roundrect locked (at 4.8125 0 180) (size 1.475 5.3) (layers "B.Cu" "B.Paste" "B.Mask") (roundrect_rratio 0.169492)
      (net 6 "Net-(J1-Pad6)") (pintype "passive") (tstamp 2b13bf58-f783-450d-a6d8-a330c40a1a11))
    (model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_4020_10251Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_line (start 115 95) (end 150 95) (layer "Edge.Cuts") (width 0.1) (tstamp 508789b7-8160-4edf-97b2-2f94d3dc3ecc))
  (gr_line (start 150 125) (end 115 125) (layer "Edge.Cuts") (width 0.1) (tstamp 62f107c4-9268-467f-a52c-18597dd50b2b))
  (gr_line (start 115 125) (end 115 95) (layer "Edge.Cuts") (width 0.1) (tstamp bdc07e5d-1ae1-4436-a65d-f3c282bd5e38))
  (gr_line (start 150 95) (end 150 125) (layer "Edge.Cuts") (width 0.1) (tstamp cb3a7820-b0f4-4754-ad1c-112b2bb9728c))

  (segment (start 135.285 105.475) (end 133.87 105.475) (width 0.25) (layer "B.Cu") (net 3) (tstamp 07e8d662-ffd7-445f-881c-d9612b64d24a))
  (segment (start 136.57 106.76) (end 135.285 105.475) (width 0.25) (layer "B.Cu") (net 3) (tstamp 08a34781-a86b-44e8-ad5f-e50dc2b94e76))
  (segment (start 138.2225 106.76) (end 136.57 106.76) (width 0.25) (layer "B.Cu") (net 3) (tstamp 6551195f-0c71-4858-abb3-ff54e00c83e0))
  (segment (start 130.65 111.93) (end 131.815 113.095) (width 0.25) (layer "B.Cu") (net 6) (tstamp 3d7ce15c-bca4-4619-b22b-d9410c853f16))
  (segment (start 131.815 113.095) (end 133.87 113.095) (width 0.25) (layer "B.Cu") (net 6) (tstamp c165a1d4-e530-4c0c-bd38-764ec374b425))
  (segment (start 128.5975 106.76) (end 129.85 106.76) (width 0.25) (layer "B.Cu") (net 6) (tstamp d07b7106-dcb4-4cbb-bf1d-f732f880407f))
  (segment (start 129.85 106.76) (end 130.65 107.56) (width 0.25) (layer "B.Cu") (net 6) (tstamp e38e2372-182d-4ab1-9a7c-47fc7e2d8f14))
  (segment (start 130.65 107.56) (end 130.65 111.93) (width 0.25) (layer "B.Cu") (net 6) (tstamp e5037c3d-a2d4-4183-8ba8-ef4d22780edb))

)
