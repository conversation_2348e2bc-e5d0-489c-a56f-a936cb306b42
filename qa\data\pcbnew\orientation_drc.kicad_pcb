(kicad_pcb
	(version 20250302)
	(generator "pcbnew")
	(generator_version "9.99")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(2 "B.Cu" signal)
		(9 "F.<PERSON>hes" user "F.Adhesive")
		(11 "B.Adhes" user "B.Adhesive")
		(13 "F.Paste" user)
		(15 "B.Paste" user)
		(5 "F.SilkS" user "F.Silkscreen")
		(7 "B.SilkS" user "B.Silkscreen")
		(1 "F.Mask" user)
		(3 "B.Mask" user)
		(17 "Dwgs.User" user "User.Drawings")
		(19 "Cmts.User" user "User.Comments")
		(21 "Eco1.User" user "User.Eco1")
		(23 "Eco2.User" user "User.Eco2")
		(25 "Edge.Cuts" user)
		(27 "Margin" user)
		(31 "F.CrtYd" user "F.Courtyard")
		(29 "B.CrtYd" user "B.Courtyard")
		(35 "F.Fab" user)
		(33 "B.Fab" user)
		(39 "User.1" user)
		(41 "User.2" user)
		(43 "User.3" user)
		(45 "User.4" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(tenting
			(front yes)
			(back yes)
		)
		(covering
			(front no)
			(back no)
		)
		(plugging
			(front no)
			(back no)
		)
		(capping no)
		(filling no)
		(pcbplotparams
			(layerselection 0x00000000_00000000_55555555_5755f5ff)
			(plot_on_all_layers_selection 0x00000000_00000000_00000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(pdf_metadata yes)
			(pdf_single_document no)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plot_black_and_white yes)
			(sketchpadsonfab no)
			(plotpadnumbers no)
			(hidednponfab no)
			(sketchdnponfab yes)
			(crossoutdnponfab yes)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(net 1 "unconnected-(R1-Pad1)")
	(net 2 "unconnected-(R1-Pad2)")
	(net 3 "unconnected-(R2-Pad2)")
	(net 4 "unconnected-(R2-Pad1)")
	(footprint "Resistor_SMD:R_0805_2012Metric"
		(layer "F.Cu")
		(uuid "3d7ab994-f1c8-44d1-94f5-40e16d2bdd66")
		(at 145.5875 111.5 90)
		(descr "Resistor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R1"
			(at 0 -1.65 90)
			(layer "F.SilkS")
			(uuid "1a949990-be19-4df4-b5b9-109410c28dd0")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "R"
			(at 0 1.65 90)
			(layer "F.Fab")
			(uuid "896f005c-ce62-432a-8aa3-df96d7f8e230")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "3280b6bd-790e-496f-8354-10291a9a0df3")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" "Resistor"
			(at 0 0 90)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "c01c9276-96ee-4693-94cf-4b4b5b062fc1")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_*")
		(path "/78ce3210-130d-4ca2-9fe7-eda05cf92547")
		(sheetname "/")
		(sheetfile "OrientationDRCTests.kicad_sch")
		(attr smd)
		(fp_line
			(start -0.227064 -0.735)
			(end 0.227064 -0.735)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3a6c75ab-4d3c-4bdb-a696-c3c8cc21cf88")
		)
		(fp_line
			(start -0.227064 0.735)
			(end 0.227064 0.735)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8ea18b4a-60d1-46fc-9ead-50a6a44fe5e1")
		)
		(fp_line
			(start 1.68 -0.95)
			(end 1.68 0.95)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "5e3d1c98-e4a5-4d5d-a420-400f94605b1a")
		)
		(fp_line
			(start -1.68 -0.95)
			(end 1.68 -0.95)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "daab392b-8908-432e-860f-5dff886597f7")
		)
		(fp_line
			(start 1.68 0.95)
			(end -1.68 0.95)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "4349a3be-34c4-452a-8701-10d0aa3bd896")
		)
		(fp_line
			(start -1.68 0.95)
			(end -1.68 -0.95)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "7c7aa244-ee08-4c48-9b26-9186c4394c4e")
		)
		(fp_line
			(start 1 -0.625)
			(end 1 0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "8e7cc798-0b96-4c4b-a7e2-586204a47b5f")
		)
		(fp_line
			(start -1 -0.625)
			(end 1 -0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f68fd3b2-b4bc-40f9-9bbb-09433e033feb")
		)
		(fp_line
			(start 1 0.625)
			(end -1 0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "825a3f46-00ce-4eda-ad09-684ab45f219a")
		)
		(fp_line
			(start -1 0.625)
			(end -1 -0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "06601ae6-eccf-443f-9b5c-6bbb7e9b80ab")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 90)
			(layer "F.Fab")
			(uuid "250fc635-bba2-43bd-8e52-935d2658f780")
			(effects
				(font
					(size 0.5 0.5)
					(thickness 0.08)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.9125 0 90)
			(size 1.025 1.4)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.243902)
			(net 1 "unconnected-(R1-Pad1)")
			(pintype "passive")
			(tenting
				(front none)
				(back none)
			)
			(uuid "59c80513-0658-4dbf-aadd-8b4acef9a932")
		)
		(pad "2" smd roundrect
			(at 0.9125 0 90)
			(size 1.025 1.4)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.243902)
			(net 2 "unconnected-(R1-Pad2)")
			(pintype "passive")
			(tenting
				(front none)
				(back none)
			)
			(uuid "e66984c6-7ab8-48aa-bbc4-63dc28276778")
		)
		(embedded_fonts no)
		(model "${KICAD8_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0805_2012Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Resistor_SMD:R_0805_2012Metric"
		(layer "F.Cu")
		(uuid "964275ba-ae1c-4fd2-8475-85f99b9444fc")
		(at 152.0875 111.5)
		(descr "Resistor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 72, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
		(tags "resistor")
		(property "Reference" "R2"
			(at 0 -1.65 0)
			(layer "F.SilkS")
			(uuid "a990bdd7-a8ec-4ba3-9adc-8b91daf2cf3a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "R"
			(at 0 1.65 0)
			(layer "F.Fab")
			(uuid "29c91ec1-f1c9-4ee5-a933-807e54a7c487")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "3a4dd716-b9ec-4238-bbcb-5355367ad675")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" "Resistor"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "a1bd6edd-48b5-40b4-a16d-2d524aad16f2")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property ki_fp_filters "R_*")
		(path "/e998a5f2-5064-4f83-9e24-b6e42f61adb8")
		(sheetname "/")
		(sheetfile "OrientationDRCTests.kicad_sch")
		(attr smd)
		(fp_line
			(start -0.227064 -0.735)
			(end 0.227064 -0.735)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "2fa684d0-d685-444c-96a3-870524c0d726")
		)
		(fp_line
			(start -0.227064 0.735)
			(end 0.227064 0.735)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "00cdad07-83f0-43c8-b68e-bf8c5a04ea4a")
		)
		(fp_line
			(start -1.68 -0.95)
			(end 1.68 -0.95)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "6d49111e-36b6-412f-917a-0c34ff3d9507")
		)
		(fp_line
			(start -1.68 0.95)
			(end -1.68 -0.95)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "baf01a70-00ec-4e2b-9f81-ec050e5b7af6")
		)
		(fp_line
			(start 1.68 -0.95)
			(end 1.68 0.95)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "806cf91a-8a69-4fa0-a130-bebf73c1831e")
		)
		(fp_line
			(start 1.68 0.95)
			(end -1.68 0.95)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b58374f6-ecce-4e7c-a047-eb012e76192c")
		)
		(fp_line
			(start -1 -0.625)
			(end 1 -0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "eb0e9839-9086-4ad7-943c-62532b454d32")
		)
		(fp_line
			(start -1 0.625)
			(end -1 -0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "771786f2-20c1-42ab-9e35-aa9b07014288")
		)
		(fp_line
			(start 1 -0.625)
			(end 1 0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "3d79bdc9-f6b8-4223-93d8-ca938cd42b35")
		)
		(fp_line
			(start 1 0.625)
			(end -1 0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "490ce74a-c056-4eaf-a4ad-5f845fbc88b9")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "57fca843-d521-4744-be43-4d06b490a176")
			(effects
				(font
					(size 0.5 0.5)
					(thickness 0.08)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.9125 0)
			(size 1.025 1.4)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.243902)
			(net 4 "unconnected-(R2-Pad1)")
			(pintype "passive")
			(tenting
				(front none)
				(back none)
			)
			(uuid "f5070382-1695-42cd-b75f-f2bb8be542bc")
		)
		(pad "2" smd roundrect
			(at 0.9125 0)
			(size 1.025 1.4)
			(layers "F.Cu" "F.Mask" "F.Paste")
			(roundrect_rratio 0.243902)
			(net 3 "unconnected-(R2-Pad2)")
			(pintype "passive")
			(tenting
				(front none)
				(back none)
			)
			(uuid "c4d7a85a-0a32-4681-be66-8d7abcc21434")
		)
		(embedded_fonts no)
		(model "${KICAD8_3DMODEL_DIR}/Resistor_SMD.3dshapes/R_0805_2012Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(gr_line
		(start 137 111.5)
		(end 141.5 111.5)
		(stroke
			(width 0.2)
			(type default)
		)
		(layer "F.Cu")
		(uuid "74cdd401-b81d-42f5-824c-7482de3e31bd")
	)
	(gr_line
		(start 162 111.5)
		(end 156 111.5)
		(stroke
			(width 0.2)
			(type default)
		)
		(layer "F.Cu")
		(uuid "bc39fd34-33cd-4ae9-a336-e9221d0786f4")
	)
	(gr_text "DOESN'T TRIGGER ASSERT"
		(at 162.5 112.5 0)
		(layer "F.Cu")
		(uuid "412c5de9-46ba-4db2-9d86-765496e08788")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3)
				(bold yes)
			)
			(justify left bottom)
		)
	)
	(gr_text "TRIGGERS ASSERT"
		(at 116 112.5 0)
		(layer "F.Cu")
		(uuid "c81482f2-e8ff-4e8c-8fa0-29127c0e1f31")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3)
				(bold yes)
			)
			(justify left bottom)
		)
	)
	(gr_text "This board has the DRC rules:\n\n(rule {dblquote}A{dblquote}\n   (condition {dblquote}A.Type == 'Footprint' && A.Orientation == 90 deg{dblquote})\n   (constraint assertion {dblquote}A.Orientation != A.Orientation{dblquote})\n)\n\nIt tests that only a footprint with Orientation 90 degrees triggers the assert"
		(at 124 98 0)
		(layer "F.Cu")
		(uuid "e44c7b1d-0b02-4475-ad25-91abec419418")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3)
				(bold yes)
			)
			(justify left bottom)
		)
	)
	(embedded_fonts no)
)
