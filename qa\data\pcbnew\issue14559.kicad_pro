{"board": {"3dviewports": [], "design_settings": {"defaults": {"board_outline_line_width": 0.09999999999999999, "copper_line_width": 0.19999999999999998, "copper_text_italic": false, "copper_text_size_h": 1.0, "copper_text_size_v": 1.0, "copper_text_thickness": 0.15, "copper_text_upright": false, "courtyard_line_width": 0.049999999999999996, "dimension_precision": 4, "dimension_units": 3, "dimensions": {"arrow_length": 1270000, "extension_offset": 500000, "keep_text_aligned": true, "suppress_zeroes": false, "text_position": 0, "units_format": 1}, "fab_line_width": 0.09999999999999999, "fab_text_italic": false, "fab_text_size_h": 1.0, "fab_text_size_v": 1.0, "fab_text_thickness": 0.15, "fab_text_upright": false, "other_line_width": 0.15, "other_text_italic": false, "other_text_size_h": 1.0, "other_text_size_v": 1.0, "other_text_thickness": 0.15, "other_text_upright": false, "pads": {"drill": 2.0, "height": 2.0, "width": 2.0}, "silk_line_width": 0.15, "silk_text_italic": false, "silk_text_size_h": 1.0, "silk_text_size_v": 1.0, "silk_text_thickness": 0.15, "silk_text_upright": false, "zones": {"45_degree_only": false, "min_clearance": 0.19999999999999998}}, "diff_pair_dimensions": [{"gap": 0.0, "via_gap": 0.0, "width": 0.0}], "drc_exclusions": [], "meta": {"version": 2}, "rule_severities": {"annular_width": "error", "clearance": "ignore", "connection_width": "error", "copper_edge_clearance": "error", "copper_sliver": "error", "courtyards_overlap": "error", "diff_pair_gap_out_of_range": "error", "diff_pair_uncoupled_length_too_long": "error", "drill_out_of_range": "error", "duplicate_footprints": "error", "extra_footprint": "error", "footprint": "error", "footprint_type_mismatch": "error", "hole_clearance": "error", "hole_near_hole": "error", "invalid_outline": "error", "isolated_copper": "error", "item_on_disabled_layer": "error", "items_not_allowed": "error", "length_out_of_range": "error", "lib_footprint_issues": "ignore", "lib_footprint_mismatch": "ignore", "malformed_courtyard": "error", "microvia_drill_out_of_range": "error", "missing_courtyard": "error", "missing_footprint": "error", "net_conflict": "error", "npth_inside_courtyard": "error", "padstack": "error", "pth_inside_courtyard": "error", "shorting_items": "error", "silk_edge_clearance": "error", "silk_over_copper": "error", "silk_overlap": "error", "skew_out_of_range": "error", "solder_mask_bridge": "error", "starved_thermal": "error", "text_height": "error", "text_thickness": "error", "through_hole_pad_without_hole": "error", "too_many_vias": "error", "track_dangling": "error", "track_width": "error", "tracks_crossing": "error", "unconnected_items": "error", "unresolved_variable": "error", "via_dangling": "error", "zones_intersect": "error"}, "rules": {"allow_blind_buried_vias": false, "allow_microvias": false, "max_error": 0.005, "min_clearance": 0.19999999999999998, "min_connection": 0.19999999999999998, "min_copper_edge_clearance": 0.19999999999999998, "min_hole_clearance": 0.19999999999999998, "min_hole_to_hole": 0.19999999999999998, "min_microvia_diameter": 0.19999999999999998, "min_microvia_drill": 0.09999999999999999, "min_resolved_spokes": 2, "min_silk_clearance": 0.0, "min_text_height": 0.7999999999999999, "min_text_thickness": 0.12, "min_through_hole_diameter": 0.19999999999999998, "min_track_width": 0.19999999999999998, "min_via_annular_width": 0.09999999999999999, "min_via_diameter": 0.39999999999999997, "solder_mask_clearance": 0.0, "solder_mask_min_width": 0.0, "solder_mask_to_copper_clearance": 0.0, "use_height_for_length_calcs": true}, "teardrop_options": [{"td_onpadsmd": true, "td_onroundshapesonly": false, "td_ontrackend": false, "td_onviapad": true}], "teardrop_parameters": [{"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_round_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_rect_shape", "td_width_to_size_filter_ratio": 0.9}, {"td_allow_use_two_tracks": true, "td_curve_segcount": 0, "td_height_ratio": 1.0, "td_length_ratio": 0.5, "td_maxheight": 2.0, "td_maxlen": 1.0, "td_on_pad_in_zone": false, "td_target_name": "td_track_end", "td_width_to_size_filter_ratio": 0.9}], "track_widths": [0.0, 0.2, 0.3, 0.5, 0.8, 2.0], "via_dimensions": [{"diameter": 0.0, "drill": 0.0}, {"diameter": 0.4, "drill": 0.2}], "zones_allow_external_fillets": true, "zones_use_no_outline": true}, "layer_presets": [], "viewports": []}, "boards": [], "cvpcb": {"equivalence_files": []}, "erc": {"erc_exclusions": [], "meta": {"version": 0}, "pin_map": [[0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 0, 1, 0, 0, 1, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 2], [0, 1, 0, 0, 0, 0, 1, 1, 2, 1, 1, 2], [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 2], [0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 1, 2, 0, 0, 1, 0, 2, 2, 2, 2], [0, 2, 0, 1, 0, 0, 1, 0, 2, 0, 0, 2], [0, 2, 1, 1, 0, 0, 1, 0, 2, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "rule_severities": {"bus_definition_conflict": "error", "bus_entry_needed": "error", "bus_to_bus_conflict": "error", "bus_to_net_conflict": "error", "conflicting_netclasses": "error", "different_unit_footprint": "error", "different_unit_net": "error", "duplicate_reference": "error", "duplicate_sheet_names": "error", "endpoint_off_grid": "error", "extra_units": "error", "global_label_dangling": "error", "hier_label_mismatch": "error", "label_dangling": "error", "lib_symbol_issues": "error", "missing_bidi_pin": "error", "missing_input_pin": "error", "missing_power_pin": "error", "missing_unit": "error", "multiple_net_names": "error", "net_not_bus_member": "error", "no_connect_connected": "error", "no_connect_dangling": "error", "pin_not_connected": "error", "pin_not_driven": "error", "pin_to_pin": "error", "power_pin_not_driven": "error", "similar_labels": "error", "simulation_model_issue": "error", "unannotated": "error", "unit_value_mismatch": "error", "unresolved_variable": "error", "wire_dangling": "error"}}, "libraries": {"pinned_footprint_libs": [], "pinned_symbol_libs": []}, "meta": {"filename": "issue14559.kicad_pro", "version": 1}, "net_settings": {"classes": [{"bus_width": 12, "clearance": 0.2, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "<PERSON><PERSON><PERSON>", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.2, "via_diameter": 0.4, "via_drill": 0.2, "wire_width": 6}], "meta": {"version": 3}, "net_colors": null, "netclass_assignments": null, "netclass_patterns": []}, "pcbnew": {"last_paths": {"gencad": "", "idf": "", "netlist": "", "plot": "", "pos_files": "", "specctra_dsn": "", "step": "", "svg": "", "vrml": ""}, "page_layout_descr_file": "${KICAD_USER_TEMPLATE_DIR}/V2_A4_Empty.kicad_wks"}, "schematic": {"annotate_start_num": 0, "drawing": {"dashed_lines_dash_length_ratio": 12.0, "dashed_lines_gap_length_ratio": 3.0, "default_line_thickness": 6.0, "default_text_size": 50.0, "field_names": [], "intersheets_ref_own_page": false, "intersheets_ref_prefix": "", "intersheets_ref_short": false, "intersheets_ref_show": false, "intersheets_ref_suffix": "", "junction_size_choice": 3, "label_size_ratio": 0.375, "pin_symbol_size": 25.0, "text_offset_ratio": 0.1}, "legacy_lib_dir": "", "legacy_lib_list": [], "meta": {"version": 1}, "net_format_name": "", "ngspice": {"fix_include_paths": true, "fix_passive_vals": false, "meta": {"version": 0}, "model_mode": 0, "workbook_filename": ""}, "page_layout_descr_file": "${KICAD_USER_TEMPLATE_DIR}/V2_A4.kicad_wks", "plot_directory": "", "spice_adjust_passive_values": false, "spice_current_sheet_as_root": false, "spice_external_command": "spice \"%I\"", "spice_model_current_sheet_as_root": true, "spice_save_all_currents": false, "spice_save_all_voltages": false, "subpart_first_id": 65, "subpart_id_separator": 0}, "sheets": [["6c8448b4-b04d-47e1-934e-e40cbe27a7be", ""], ["ecb8eef8-bb26-4217-a3d3-147bd9b06112", "PWM Channel 1"], ["c0032080-b223-4ef5-af6e-f10445519ca7", "PWM Channel 2"], ["72fc00ec-a037-43b2-8be3-f6274b0c3d34", "PWM Channel 3"], ["c5e21047-c711-42c5-833c-f87be56058f4", "PWM Channel 4"], ["a89ccf9d-6e1e-42ce-a365-b8a588b2a764", "PWM Channel 5"], ["2e65de88-75bc-4218-bada-fc991c11693d", "PWM Channel 6"], ["a178fee9-8654-4c6d-afdd-d0f611515c27", "PWM Channel 7"], ["c39b559a-eba7-416b-9b64-29e457c350f9", "PWM Channel 8"], ["f374a830-6190-4414-97f7-cd95a96fcb7b", "PWM Channel 9"], ["f929036c-a21a-4759-9a71-4bb346b616ac", "PWM Channel 12"], ["11be0dca-9aa7-44a3-a97c-59531fd35a86", "PWM Channel 11"], ["0c49b5ee-ddea-43ea-ab5a-6c52ceb8d790", "PWM Channel 10"], ["87daeacc-2e6b-4f3e-a4a8-59a3ffd747c2", "PWM Channel 13"], ["1b24bac2-35ae-4e4e-8bc5-d88b5d418adf", "PWM Channel 16"], ["02d7dc07-d88b-46a9-8c27-5f474f94bba5", "PWM Channel 15"], ["ef3f4e24-65f6-49ab-9666-2948510217b4", "PWM Channel 14"], ["ba748654-beb9-4861-aa35-c19e08caaf2e", "Power +24V"], ["c6db2f80-f80f-4104-8ca1-02f0ab85cfea", "<PERSON>"], ["2a03ea79-269e-4abf-833c-000ba0ff13d7", "Link Socket"]], "text_variables": {"Order-Number": "JLCJLCJLCJLC"}}