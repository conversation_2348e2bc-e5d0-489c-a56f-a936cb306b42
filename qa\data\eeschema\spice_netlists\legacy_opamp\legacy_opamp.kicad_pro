{"board": {"3dviewports": [], "design_settings": {"defaults": {"board_outline_line_width": 0.1, "copper_line_width": 0.2, "copper_text_size_h": 1.5, "copper_text_size_v": 1.5, "copper_text_thickness": 0.3, "other_line_width": 0.15, "silk_line_width": 0.15, "silk_text_size_h": 1.0, "silk_text_size_v": 1.0, "silk_text_thickness": 0.15}, "diff_pair_dimensions": [], "drc_exclusions": [], "rules": {"min_copper_edge_clearance": 0.0, "solder_mask_clearance": 0.0, "solder_mask_min_width": 0.0}, "track_widths": [], "via_dimensions": []}, "layer_presets": [], "viewports": []}, "boards": [], "cvpcb": {"equivalence_files": []}, "erc": {"erc_exclusions": [], "meta": {"version": 0}, "pin_map": [[0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 0, 1, 0, 0, 1, 0, 2, 2, 2, 2], [0, 0, 0, 0, 0, 0, 1, 0, 1, 0, 1, 2], [0, 1, 0, 0, 0, 0, 1, 1, 2, 1, 1, 2], [0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 2], [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2], [1, 1, 1, 1, 1, 0, 1, 1, 1, 1, 1, 2], [0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 2], [0, 2, 1, 2, 0, 0, 1, 0, 2, 2, 2, 2], [0, 2, 0, 1, 0, 0, 1, 0, 2, 0, 0, 2], [0, 2, 1, 1, 0, 0, 1, 0, 2, 0, 0, 2], [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2]], "rule_severities": {"bus_definition_conflict": "error", "bus_entry_needed": "error", "bus_label_syntax": "error", "bus_to_bus_conflict": "error", "bus_to_net_conflict": "error", "different_unit_footprint": "error", "different_unit_net": "error", "duplicate_reference": "error", "duplicate_sheet_names": "error", "extra_units": "error", "global_label_dangling": "warning", "hier_label_mismatch": "error", "label_dangling": "error", "lib_symbol_issues": "warning", "multiple_net_names": "warning", "net_not_bus_member": "warning", "no_connect_connected": "warning", "no_connect_dangling": "warning", "pin_not_connected": "error", "pin_not_driven": "error", "pin_to_pin": "warning", "power_pin_not_driven": "error", "similar_labels": "warning", "simulation_model_issue": "error", "unannotated": "error", "unit_value_mismatch": "error", "unresolved_variable": "error", "wire_dangling": "error"}}, "libraries": {"pinned_footprint_libs": [], "pinned_symbol_libs": []}, "meta": {"filename": "legacy_opamp.kicad_pro", "version": 1}, "net_settings": {"classes": [{"bus_width": 12.0, "clearance": 0.2, "diff_pair_gap": 0.25, "diff_pair_via_gap": 0.25, "diff_pair_width": 0.2, "line_style": 0, "microvia_diameter": 0.3, "microvia_drill": 0.1, "name": "<PERSON><PERSON><PERSON>", "pcb_color": "rgba(0, 0, 0, 0.000)", "schematic_color": "rgba(0, 0, 0, 0.000)", "track_width": 0.25, "via_diameter": 0.8, "via_drill": 0.4, "wire_width": 6.0}], "meta": {"version": 3}, "net_colors": null, "netclass_assignments": null, "netclass_patterns": []}, "pcbnew": {"last_paths": {"gencad": "", "idf": "", "netlist": "", "specctra_dsn": "", "step": "", "vrml": ""}, "page_layout_descr_file": ""}, "schematic": {"annotate_start_num": 0, "drawing": {"dashed_lines_dash_length_ratio": 12.0, "dashed_lines_gap_length_ratio": 3.0, "default_line_thickness": 6.0, "default_text_size": 50.0, "field_names": [], "intersheets_ref_own_page": false, "intersheets_ref_prefix": "", "intersheets_ref_short": false, "intersheets_ref_show": false, "intersheets_ref_suffix": "", "junction_size_choice": 3, "label_size_ratio": 0.375, "pin_symbol_size": 25.0, "text_offset_ratio": 0.15}, "legacy_lib_dir": "", "legacy_lib_list": [], "meta": {"version": 1}, "net_format_name": "", "ngspice": {"fix_include_paths": true, "fix_passive_vals": false, "meta": {"version": 0}, "model_mode": 0, "workbook_filename": ""}, "page_layout_descr_file": "", "plot_directory": "", "spice_adjust_passive_values": false, "spice_external_command": "spice \"%I\"", "spice_save_all_currents": false, "spice_save_all_voltages": false, "subpart_first_id": 65, "subpart_id_separator": 0}, "sheets": [["986bd0ba-4f89-4046-b50f-9d5b4a950ed1", ""]], "text_variables": {}}