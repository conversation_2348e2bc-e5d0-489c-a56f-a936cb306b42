(kicad_sch (version 20221004) (generator eeschema)

  (uuid fd5567b0-04f0-4d9e-b647-1775367d8ff4)

  (paper "A4")

  (lib_symbols
    (symbol "Amplifier_Operational:MCP6001-OT" (pin_names (offset 0.127)) (in_bom yes) (on_board yes)
      (property "Reference" "U" (at -1.27 6.35 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "MCP6001-OT" (at -1.27 3.81 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "Package_TO_SOT_SMD:SOT-23-5" (at -2.54 -5.08 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Datasheet" "http://ww1.microchip.com/downloads/en/DeviceDoc/21733j.pdf" (at 0 5.08 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "single opamp" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "1MHz, Low-Power Op Amp, SOT-23-5" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "SOT?23*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "MCP6001-OT_0_1"
        (polyline
          (pts
            (xy -5.08 5.08)
            (xy 5.08 0)
            (xy -5.08 -5.08)
            (xy -5.08 5.08)
          )
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
        (pin power_in line (at -2.54 -7.62 90) (length 3.81)
          (name "V-" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -2.54 7.62 270) (length 3.81)
          (name "V+" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "MCP6001-OT_1_1"
        (pin output line (at 7.62 0 180) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -7.62 2.54 0) (length 2.54)
          (name "+" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -7.62 -2.54 0) (length 2.54)
          (name "-" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VDC" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VDC" (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "dc(1)" (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, DC" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VDC_0_0"
        (polyline
          (pts
            (xy -1.27 0.254)
            (xy 1.27 0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.254)
            (xy -1.27 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.254 -0.254)
            (xy -0.254 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.254)
            (xy 0.762 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VDC_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VDC_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VSIN" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VSIN" (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "sin(0 1 1k)" (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, sinusoidal" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VSIN_0_0"
        (arc (start 0 0) (mid -0.635 0.6323) (end -1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0) (mid 0.635 -0.6323) (end 1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VSIN_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VSIN_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 148.59 111.125) (diameter 0) (color 0 0 0 0)
    (uuid 7d5f9c7f-c76d-40e1-8801-1df86d468cda)
  )

  (wire (pts (xy 138.43 86.36) (xy 148.59 86.36))
    (stroke (width 0) (type default))
    (uuid 21fe00e7-ba60-4b15-8ef1-025eba89442f)
  )
  (wire (pts (xy 165.1 88.9) (xy 165.1 111.125))
    (stroke (width 0) (type default))
    (uuid 2738cace-8583-4dd0-a73a-e0a00514d71c)
  )
  (wire (pts (xy 148.59 91.44) (xy 148.59 111.125))
    (stroke (width 0) (type default))
    (uuid 384ae57c-e54d-47a4-ba2d-a5f118361f7b)
  )
  (wire (pts (xy 165.1 111.125) (xy 156.21 111.125))
    (stroke (width 0) (type default))
    (uuid 464d44f8-2560-4ecf-89c9-500fb5853871)
  )
  (wire (pts (xy 138.43 88.9) (xy 138.43 86.36))
    (stroke (width 0) (type default))
    (uuid 494e3baa-08d1-4e6f-b155-e344680652a8)
  )
  (wire (pts (xy 163.83 88.9) (xy 165.1 88.9))
    (stroke (width 0) (type default))
    (uuid 55368b74-95a7-4cf2-8f86-415db816907d)
  )

  (text ".tran 10u 10m" (at 139.7 127 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid be48cb8e-7c9f-4216-838e-ee151aaeb3f9)
  )

  (label "out" (at 165.1 88.9 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid c6d665cf-2828-4128-ae59-06a65deb6b7e)
  )
  (label "in" (at 138.43 86.36 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid e8a13b17-85b1-4981-ba25-adfdae039d30)
  )

  (symbol (lib_id "power:GND") (at 153.67 106.68 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 032a39b4-2686-4cc0-a834-fb52cf55377c)
    (property "Reference" "#PWR04" (at 153.67 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 157.48 108.585 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 153.67 106.68 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 153.67 106.68 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 233df94f-5b6f-4745-8784-1c2d9119d27f))
    (instances
      (project "opamp"
        (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4"
          (reference "#PWR04") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VSIN") (at 138.43 93.98 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 10f44001-5f65-44eb-a535-b7b3c67d5b83)
    (property "Reference" "VIN1" (at 116.84 92.71 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "${Sim.Device} ${Sim.Type}" (at 116.84 95.25 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 138.43 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 138.43 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 138.43 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Type" "SIN" (at 138.43 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "ampl=500m f=1k" (at 124.46 97.79 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 19da877c-a69c-4a1d-8029-3483030b2fe0))
    (pin "2" (uuid 4c449474-8aa6-4bd1-b153-a08aa763d45a))
    (instances
      (project "opamp"
        (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4"
          (reference "VIN1") (unit 1) (value "${Sim.Device} ${Sim.Type}") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 152.4 111.125 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 2a5b0448-42a8-4fa4-9a14-267dc34f9ff7)
    (property "Reference" "R2" (at 152.4 113.665 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "10k" (at 152.4 116.205 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 152.4 112.903 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 152.4 111.125 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 152.4 111.125 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7cf6d433-268b-4901-b74d-8803afdfc194))
    (pin "2" (uuid 63068397-add3-45ea-b295-240f56e8c4f9))
    (instances
      (project "opamp"
        (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4"
          (reference "R2") (unit 1) (value "10k") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 153.67 76.2 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 383ea298-1cb3-473e-8fce-19d3c7ac3912)
    (property "Reference" "V2" (at 157.48 74.295 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "5" (at 157.48 76.835 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 153.67 76.2 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 153.67 76.2 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 153.67 76.2 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 885c89aa-c780-4741-b398-9d4b8e38f094))
    (pin "2" (uuid 8a2dca0c-b99f-4109-a426-af5e80ea91fe))
    (instances
      (project "opamp"
        (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4"
          (reference "V2") (unit 1) (value "5") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 140.97 111.125 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 71c5abbf-da89-4208-ab90-ab47cb47a86d)
    (property "Reference" "#PWR02" (at 134.62 111.125 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 137.16 111.76 90)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 140.97 111.125 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 140.97 111.125 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 70c0dc3a-c9e8-4d2a-a705-c6c89d9bebea))
    (instances
      (project "opamp"
        (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4"
          (reference "#PWR02") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 153.67 71.12 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid bf78fcdf-1d24-40dc-a76b-1c91df364f4e)
    (property "Reference" "#PWR03" (at 153.67 64.77 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 157.48 69.85 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 153.67 71.12 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 153.67 71.12 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d51f887c-ce71-4e7a-9f6c-4362d9b8286a))
    (instances
      (project "opamp"
        (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4"
          (reference "#PWR03") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Amplifier_Operational:MCP6001-OT") (at 156.21 88.9 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid c1142581-9f6c-4783-9cde-************)
    (property "Reference" "U1" (at 163.83 82.55 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "MCP6001-OT" (at 163.83 85.09 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "Package_TO_SOT_SMD:SOT-23-5" (at 153.67 93.98 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
    (property "Datasheet" "http://ww1.microchip.com/downloads/en/DeviceDoc/21733j.pdf" (at 156.21 83.82 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Name" "uopamp_lvl2" (at 156.21 88.9 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Library" "uopamp.lib.spice" (at 156.21 88.9 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "SUBCKT" (at 156.21 88.9 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Pins" "3=+IN 4=-IN 5=VCC 2=VEE 1=OUT" (at 156.21 88.9 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "2" (uuid 5f74e7f3-6e5a-4915-9655-9c0f0523fb0a))
    (pin "5" (uuid 9784a75a-18be-45d0-8108-f0556b776534))
    (pin "1" (uuid 60030f74-1274-44bb-bcfd-d5cec20ed230))
    (pin "3" (uuid 69bf4152-2799-46c3-acc2-46d3456c830b))
    (pin "4" (uuid daf27674-a2aa-43e8-b45b-cf8f98a49a42))
    (instances
      (project "opamp"
        (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4"
          (reference "U1") (unit 1) (value "MCP6001-OT") (footprint "Package_TO_SOT_SMD:SOT-23-5")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 138.43 99.06 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid c4334e6a-69e5-475f-b5f5-911b3308f64a)
    (property "Reference" "#PWR01" (at 138.43 105.41 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 138.43 102.87 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 138.43 99.06 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 138.43 99.06 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1af0463f-605e-40b1-8e1c-d6b819b8b4f8))
    (instances
      (project "opamp"
        (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4"
          (reference "#PWR01") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 153.67 101.6 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid d2018f30-5def-453e-85fe-dac327cfbe9b)
    (property "Reference" "V3" (at 157.48 99.695 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "5" (at 157.48 102.235 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 153.67 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 153.67 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 153.67 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 62bdf880-7061-4b86-be3a-09bf959fae21))
    (pin "2" (uuid 9bd26aa2-337b-4c5a-a9de-bdab770672a9))
    (instances
      (project "opamp"
        (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4"
          (reference "V3") (unit 1) (value "5") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 144.78 111.125 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid fbe00ebb-3cab-46ea-988c-9e8169e9708f)
    (property "Reference" "R1" (at 144.78 113.665 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "10k" (at 144.78 116.205 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 144.78 112.903 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 144.78 111.125 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 144.78 111.125 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid fe3f00f2-cee0-4154-8f20-4097e41e635f))
    (pin "2" (uuid 5480c24e-8f90-4441-b07c-0db2096e1071))
    (instances
      (project "opamp"
        (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4"
          (reference "R1") (unit 1) (value "10k") (footprint "")
        )
      )
    )
  )

  (sheet_instances
    (path "/fd5567b0-04f0-4d9e-b647-1775367d8ff4" (page "1"))
  )
)
