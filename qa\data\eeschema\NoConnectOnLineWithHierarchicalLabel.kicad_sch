(kicad_sch (version 20211123) (generator eeschema)

  (uuid b5d9ebeb-f800-4288-8ce9-7033224d3b21)

  (paper "A4")

  (lib_symbols
    (symbol "Connector:TestPoint" (pin_numbers hide) (pin_names (offset 0.762) hide) (in_bom yes) (on_board yes)
      (property "Reference" "TP" (id 0) (at 0 6.858 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "TestPoint" (id 1) (at 0 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 5.08 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 5.08 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "test point tp" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "test point" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Pin* Test*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "TestPoint_0_1"
        (circle (center 0 3.302) (radius 0.762)
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
      )
      (symbol "TestPoint_1_1"
        (pin passive line (at 0 0 90) (length 2.54)
          (name "1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 31.75 31.75) (diameter 0.9144) (color 0 0 0 0)
    (uuid 737959f7-b51e-4ddd-971c-1c86ab0ee4a9)
  )

  (no_connect (at 31.75 38.1) (uuid 38e4d6ee-1caa-4107-afdf-9817c33f9c25))
  (no_connect (at 71.12 31.75) (uuid f9998283-5d38-41d7-8c08-d81f053774ec))

  (wire (pts (xy 31.75 31.75) (xy 34.29 31.75))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid 4c68c6ce-7d5e-444c-b28d-6b1ab9830b10)
  )
  (wire (pts (xy 31.75 25.4) (xy 31.75 31.75))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid c55e19e3-1b36-4112-9bca-54b9f9dee350)
  )
  (wire (pts (xy 31.75 31.75) (xy 31.75 38.1))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid c55e19e3-1b36-4112-9bca-54b9f9dee351)
  )
  (wire (pts (xy 71.12 31.75) (xy 73.66 31.75))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid eb8568c2-b3b9-407b-8cd9-ebd8f225efaf)
  )

  (text "No errors with pin" (at 26.67 44.45 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 20a4c07b-2e3b-4ca8-81ba-5157f33c5e17)
  )
  (text "1 error (no pin)" (at 71.12 35.56 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 2e51c0a4-24b7-42fa-9bca-6358d096838f)
  )

  (hierarchical_label "test_ok" (shape input) (at 34.29 31.75 0)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid a11efb39-593e-4e55-9c2f-32db4d581aeb)
  )
  (hierarchical_label "test_err" (shape input) (at 73.66 31.75 0)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid b1a417d5-6a5d-4c29-91f2-2d4aa19b90db)
  )

  (symbol (lib_id "Connector:TestPoint") (at 31.75 25.4 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid 6307406d-c1c0-4900-af1b-9056ce7df57f)
    (property "Reference" "TP1" (id 0) (at 34.29 22.2884 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "TestPoint" (id 1) (at 34.29 24.8284 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 36.83 25.4 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 36.83 25.4 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid cd7f2944-da22-4cf2-b462-7121d97c4a0b))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/6307406d-c1c0-4900-af1b-9056ce7df57f"
      (reference "TP1") (unit 1) (value "TestPoint") (footprint "")
    )
  )
)
