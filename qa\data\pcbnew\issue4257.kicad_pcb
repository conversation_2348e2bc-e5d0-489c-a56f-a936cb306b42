(kicad_pcb (version 20200119) (host pcbnew "(5.99.0-1404-g52d891940-dirty)")

  (general
    (thickness 4.62)
    (drawings 1)
    (tracks 5)
    (modules 1)
    (nets 1)
  )

  (page "A4")
  (layers
    (0 "F.Cu" signal)
    (1 "In1.Cu" signal)
    (2 "In2.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user)
    (33 "<PERSON><PERSON>Ad<PERSON>" user)
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user)
    (37 "F.SilkS" user)
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user)
    (41 "Cmts.User" user)
    (42 "Eco1.User" user)
    (43 "Eco2.User" user)
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user)
    (47 "F.CrtYd" user)
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (color "Green") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "core") (thickness 1.44) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In1.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 2" (type "prepreg") (thickness 1.51) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In2.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 3" (type "core") (thickness 1.51) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (color "Green") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "HAL lead-free")
      (dielectric_constraints no)
    )
    (last_trace_width 0.25)
    (user_trace_width 1)
    (trace_clearance 0.2)
    (zone_clearance 0.508)
    (zone_45_only no)
    (trace_min 0.2)
    (via_size 0.8)
    (via_drill 0.4)
    (via_min_size 0.4)
    (via_min_drill 0.3)
    (uvia_size 0.3)
    (uvia_drill 0.1)
    (uvias_allowed no)
    (uvia_min_size 0.2)
    (uvia_min_drill 0.1)
    (max_error 0.005)
    (defaults
      (edge_clearance 0.01)
      (edge_cuts_line_width 0.05)
      (courtyard_line_width 0.05)
      (copper_line_width 0.2)
      (copper_text_dims (size 1.5 1.5) (thickness 0.3))
      (silk_line_width 0.12)
      (silk_text_dims (size 1 1) (thickness 0.15))
      (other_layers_line_width 0.1)
      (other_layers_text_dims (size 1 1) (thickness 0.15))
      (dimension_units 0)
      (dimension_precision 1)
    )
    (pad_size 1.5 1.5)
    (pad_drill 0)
    (pad_to_mask_clearance 0.2)
    (aux_axis_origin 0 0)
    (visible_elements FFFFFF7F)
    (pcbplotparams
      (layerselection 0x01000_7ffffff9)
      (usegerberextensions false)
      (usegerberattributes false)
      (usegerberadvancedattributes false)
      (creategerberjobfile false)
      (excludeedgelayer true)
      (linewidth 0.101600)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (padsonsilk false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "${plot}")
    )
  )

  (net 0 "")

  (net_class "Default" "This is the default net class."
    (clearance 0.2)
    (trace_width 0.25)
    (via_dia 0.8)
    (via_drill 0.4)
    (uvia_dia 0.3)
    (uvia_drill 0.1)
  )

  (module "Housings_DIP:DIP-40_W15.24mm" (layer "F.Cu") (tedit 54130A77) (tstamp 00000000-0000-0000-0000-00005e384273)
    (at 164.719 77.597)
    (descr "40-lead dip package, row spacing 15.24 mm (600 mils)")
    (tags "dil dip 2.54 600")
    (fp_text reference "U1" (at 0 -5.22) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text value "DIP-40_W15.24mm${stuff}" (at 0 -3.72) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (fp_text user "field on copper" (at 7.85622 26.98242 unlocked) (layer "F.Cu")
      (effects (font (size 1 1) (thickness 0.2)))
    )
    (fp_line (start 0.135 -1.025) (end -0.8 -1.025) (layer "F.SilkS") (width 0.15))
    (fp_line (start 0.135 50.555) (end 15.105 50.555) (layer "F.SilkS") (width 0.15))
    (fp_line (start 0.135 -2.295) (end 15.105 -2.295) (layer "F.SilkS") (width 0.15))
    (fp_line (start 0.135 50.555) (end 0.135 49.285) (layer "F.SilkS") (width 0.15))
    (fp_line (start 15.105 50.555) (end 15.105 49.285) (layer "F.SilkS") (width 0.15))
    (fp_line (start 15.105 -2.295) (end 15.105 -1.025) (layer "F.SilkS") (width 0.15))
    (fp_line (start 0.135 -2.295) (end 0.135 -1.025) (layer "F.SilkS") (width 0.15))
    (fp_line (start -1.05 50.75) (end 16.3 50.75) (layer "F.CrtYd") (width 0.05))
    (fp_line (start -1.05 -2.45) (end 16.3 -2.45) (layer "F.CrtYd") (width 0.05))
    (fp_line (start 16.3 -2.45) (end 16.3 50.75) (layer "F.CrtYd") (width 0.05))
    (fp_line (start -1.05 -2.45) (end -1.05 50.75) (layer "F.CrtYd") (width 0.05))
    (pad "40" thru_hole oval (at 15.24 0) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 3c03b006-992b-4656-8d8b-1a40717bad9b))
    (pad "39" thru_hole oval (at 15.24 2.54) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 17e4ee73-c0b6-4f16-a52f-3352b4c4a589))
    (pad "38" thru_hole oval (at 15.24 5.08) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 078e3443-d124-424c-8675-549896b6e109))
    (pad "37" thru_hole oval (at 15.24 7.62) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp c80e473b-e8f3-40b2-a34a-cb8ba2396f2c))
    (pad "36" thru_hole oval (at 15.24 10.16) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp e272137d-dd3b-4558-883f-a111afa3585b))
    (pad "35" thru_hole oval (at 15.24 12.7) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 05ee1c41-eca6-4139-9ea4-47a6d84294ba))
    (pad "34" thru_hole oval (at 15.24 15.24) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp e4636fae-e5c5-4bba-a464-4ac9b773ed47))
    (pad "33" thru_hole oval (at 15.24 17.78) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 6ed828ab-d8d8-4fe9-b16b-ebfc33e038e4))
    (pad "32" thru_hole oval (at 15.24 20.32) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 19d71dad-df19-4f33-b0e6-6c030568956a))
    (pad "31" thru_hole oval (at 15.24 22.86) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 3b8bd1af-63d2-4e0c-9197-0ad6380b78d7))
    (pad "30" thru_hole oval (at 15.24 25.4) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 3336d357-9487-4cef-80b0-4d77065fab3e))
    (pad "29" thru_hole oval (at 15.24 27.94) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 05ebfe05-f6e7-4ad1-b07f-9b049cf5b14b))
    (pad "28" thru_hole oval (at 15.24 30.48) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 24d921f2-823b-411d-a601-08992f32e566))
    (pad "27" thru_hole oval (at 15.24 33.02) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 456fa4e1-431b-49d2-a1db-9738e920f534))
    (pad "26" thru_hole oval (at 15.24 35.56) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 991a8590-8312-43da-b38f-102ea1fac869))
    (pad "25" thru_hole oval (at 15.24 38.1) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 63a7153a-fee4-461e-a81d-346402828e19))
    (pad "24" thru_hole oval (at 15.24 40.64) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp df527ce2-b732-425c-950a-a2246d2e2816))
    (pad "23" thru_hole oval (at 15.24 43.18) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 2652c9ab-ff66-4247-bb34-3a859d22dd65))
    (pad "22" thru_hole oval (at 15.24 45.72) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 1f4cc26e-f3c7-4fb3-b473-460f7b146bc1))
    (pad "21" thru_hole oval (at 15.24 48.26) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 32fe534f-83ab-48c1-bece-80fc1226c6d7))
    (pad "20" thru_hole oval (at 0 48.26) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp e876604e-ed53-40dd-9e3d-8ae62c69ec6f))
    (pad "19" thru_hole oval (at 0 45.72) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp d17f5e4d-a323-40ff-996e-9d1748ea4193))
    (pad "18" thru_hole oval (at 0 43.18) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp e0797ab1-555e-4086-945b-6adb8e80549c))
    (pad "17" thru_hole oval (at 0 40.64) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 898c11b5-3a02-432c-a417-e902595c7baa))
    (pad "16" thru_hole oval (at 0 38.1) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp ea9d9849-4c1c-4767-bc66-4a07ba4f6072))
    (pad "15" thru_hole oval (at 0 35.56) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 288bbbf0-75a5-4670-91ca-5f36d3c210d6))
    (pad "14" thru_hole oval (at 0 33.02) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 9d83fd3c-30f0-410d-b81f-c06388279e95))
    (pad "13" thru_hole oval (at 0 30.48) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 8d278dd7-c747-4010-9785-831b0766e30a))
    (pad "12" thru_hole oval (at 0 27.94) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp f0bbe286-d2de-445f-ac17-8293f5dc5a01))
    (pad "11" thru_hole oval (at 0 25.4) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp ef8288b1-8803-486f-884f-7b7ad74f6c41))
    (pad "10" thru_hole oval (at 0 22.86) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 9434a295-ac46-41d0-9f94-9671cf7b99b0))
    (pad "9" thru_hole oval (at 0 20.32) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 6ba3a876-a0b2-4c5a-b07a-0387a0d52657))
    (pad "8" thru_hole oval (at 0 17.78) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 1382b21a-9297-41c7-95bb-c4c8a5cf04dc))
    (pad "7" thru_hole oval (at 0 15.24) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 87e8c4e5-6cef-426d-90b6-0abce8bcec3f))
    (pad "6" thru_hole oval (at 0 12.7) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp bc4f65fe-8685-4fe9-b6e0-f903c1e2bae5))
    (pad "5" thru_hole oval (at 0 10.16) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 687510dd-b060-4c32-8ca8-3f1db85c0207))
    (pad "4" thru_hole oval (at 0 7.62) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp ef6bbd64-4a61-433f-90b6-1c71e2b5ea83))
    (pad "3" thru_hole oval (at 0 5.08) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 72b089cd-8b4d-41b2-8e3f-4222bed7df10))
    (pad "2" thru_hole oval (at 0 2.54) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 54f38516-e465-4f36-9335-8003d6c4f47c))
    (pad "1" thru_hole oval (at 0 0) (size 1.6 1.6) (drill 0.8) (layers *.Cu *.Mask) (tstamp 02483e4d-3ab1-489c-951b-83a16cc9b30a))
    (model "/Users/<USER>/kicad/Package_QFP.3dshapes/HTQFP-64-1EP_10x10mm_P0.5mm_EP8x8mm.wrl"
      (opacity 0.6500)      (offset (xyz 0 -13 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
    (model "/Users/<USER>/kicad/Package_QFP.3dshapes/TQFP-48_7x7mm_P0.5mm.wrl"
      (opacity 0.2100)      (at (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_text "Text: ${LAYER} ${00000000-0000-0000-0000-00005e384273:REFERENCE}" (at 151.0919 104.5464) (layer "F.Cu") (tstamp 4a940769-dbd0-4522-898c-8e905a074b1e)
    (effects (font (size 1.5 1.5) (thickness 0.375)))
  )

  (segment (start 156.93898 86.995) (end 148.04898 86.995) (width 1) (layer "F.Cu") (net 0) (tstamp cae62b0c-d3ee-4246-bdc8-283dac6f3145))
  (via (at 156.84754 86.995) (size 4) (drill 2) (layers "F.Cu" "B.Cu") (net 0) (tstamp 19ccc761-ed00-4cfc-bfdb-96decfd7b640))
  (segment (start 156.845 86.995) (end 147.955 86.995) (width 1) (layer "F.Cu") (net 0) (tstamp 5b13b4cc-9d32-4a9c-92f5-295a3af198b7))
  (via (at 156.845 86.995) (size 4) (drill 2) (layers "F.Cu" "B.Cu") (net 0) (tstamp 9f3db980-ce93-4ae6-813a-9832a6affc36))
  (via blind (at 147.955 86.995) (size 4) (drill 2) (layers "F.Cu" "In1.Cu") (net 0) (tstamp 645cc3b2-3724-4b57-962a-a7d28e4f84a5))

)
