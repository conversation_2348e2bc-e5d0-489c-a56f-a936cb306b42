(kicad_sch
	(version 20231120)
	(generator "eeschema")
	(generator_version "8.0")
	(uuid "c9b0dfd9-0180-4a78-8afb-2260035ff793")
	(paper "A4")
	(lib_symbols
		(symbol "Connector_Generic:Conn_01x01"
			(pin_names
				(offset 1.016) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "J1"
				(at 2.54 1.2701 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Value" "Conn_01x01"
				(at 2.54 -1.2699 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(justify left)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Generic connector, single row, 01x01, script generated (kicad-library-utils/schlib/autogen/connector/)"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "connector"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_fp_filters" "Connector*:*_1x??_*"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "Conn_01x01_1_1"
				(rectangle
					(start -1.27 0.127)
					(end 0 -0.127)
					(stroke
						(width 0.1524)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(rectangle
					(start -1.27 1.27)
					(end 1.27 -1.27)
					(stroke
						(width 0.254)
						(type default)
					)
					(fill
						(type background)
					)
				)
				(pin unspecified line
					(at -5.08 0 0)
					(length 3.81)
					(name "Pin_1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:GND"
			(power)
			(pin_numbers hide)
			(pin_names
				(offset 0) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -6.35 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "GND"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "GND_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 -1.27) (xy 1.27 -1.27) (xy 0 -2.54) (xy -1.27 -1.27) (xy 0 -1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "GND_1_1"
				(pin power_in line
					(at 0 0 270)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
		(symbol "power:PWR_FLAG"
			(power)
			(pin_numbers hide)
			(pin_names
				(offset 0) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#FLG"
				(at 0 1.905 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "PWR_FLAG"
				(at 0 3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" "~"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Special symbol for telling ERC where power comes from"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "flag power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "PWR_FLAG_0_0"
				(pin power_out line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
			(symbol "PWR_FLAG_0_1"
				(polyline
					(pts
						(xy 0 0) (xy 0 1.27) (xy -1.016 1.905) (xy 0 2.54) (xy 1.016 1.905) (xy 0 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
		)
		(symbol "power:VCC"
			(power)
			(pin_numbers hide)
			(pin_names
				(offset 0) hide)
			(exclude_from_sim no)
			(in_bom yes)
			(on_board yes)
			(property "Reference" "#PWR"
				(at 0 -3.81 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Value" "VCC"
				(at 0 3.556 0)
				(effects
					(font
						(size 1.27 1.27)
					)
				)
			)
			(property "Footprint" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Datasheet" ""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "Description" "Power symbol creates a global label with name \"VCC\""
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(property "ki_keywords" "global power"
				(at 0 0 0)
				(effects
					(font
						(size 1.27 1.27)
					)
					(hide yes)
				)
			)
			(symbol "VCC_0_1"
				(polyline
					(pts
						(xy -0.762 1.27) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 0) (xy 0 2.54)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
				(polyline
					(pts
						(xy 0 2.54) (xy 0.762 1.27)
					)
					(stroke
						(width 0)
						(type default)
					)
					(fill
						(type none)
					)
				)
			)
			(symbol "VCC_1_1"
				(pin power_in line
					(at 0 0 90)
					(length 0)
					(name "~"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
					(number "1"
						(effects
							(font
								(size 1.27 1.27)
							)
						)
					)
				)
			)
		)
	)
	(junction
		(at 96.52 68.58)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "0633dbac-fdd0-44b2-b5a1-5fb3e935e35c")
	)
	(junction
		(at 111.76 68.58)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "5a493683-f92a-42a8-8485-4380ca122dc2")
	)
	(junction
		(at 101.6 85.09)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "6024c909-1a40-456d-a085-047200c9087e")
	)
	(junction
		(at 91.44 68.58)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "614e14e8-93ec-44e0-9452-f823c5aab88f")
	)
	(junction
		(at 96.52 85.09)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "6a557880-3fe6-4d44-9570-deea497822e9")
	)
	(junction
		(at 86.36 85.09)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "6df36e97-f380-4ef9-93db-db3234cdfc59")
	)
	(junction
		(at 106.68 85.09)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "8387cf20-ca52-40e5-ae3f-8ef5ff88c2b1")
	)
	(junction
		(at 101.6 68.58)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "b3fa58a6-f0d1-4cf5-a7e6-7335ef8faa2e")
	)
	(junction
		(at 91.44 85.09)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "cd088a0d-116e-4180-9017-d6594842877a")
	)
	(junction
		(at 86.36 68.58)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "cf76f7b5-1ada-484e-8078-2a81d2561d3b")
	)
	(junction
		(at 111.76 85.09)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "efc41310-8076-45b3-8ae4-9c94964f63f0")
	)
	(junction
		(at 106.68 68.58)
		(diameter 0)
		(color 0 0 0 0)
		(uuid "fe57e1a4-fa25-4be1-932e-feda738bb807")
	)
	(wire
		(pts
			(xy 111.76 85.09) (xy 111.76 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "0d469046-c7bf-484f-9c1e-e61106a0176d")
	)
	(wire
		(pts
			(xy 106.68 85.09) (xy 111.76 85.09)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "22405147-36c2-4cca-b322-1e00b2628e91")
	)
	(wire
		(pts
			(xy 106.68 68.58) (xy 111.76 68.58)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "2d9e821b-9532-4d6d-86cd-b06c281526d6")
	)
	(wire
		(pts
			(xy 106.68 85.09) (xy 106.68 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "33744bb7-31d6-445e-8f02-6ed4b10945f9")
	)
	(wire
		(pts
			(xy 96.52 85.09) (xy 101.6 85.09)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4e08aef3-b48e-4d48-8f86-b9eb80c8b67f")
	)
	(wire
		(pts
			(xy 101.6 85.09) (xy 106.68 85.09)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "4ed15847-8044-4cff-8f6f-8c987954520e")
	)
	(wire
		(pts
			(xy 101.6 68.58) (xy 106.68 68.58)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "53a3fef1-a389-45ad-a6a4-3759a6d4afac")
	)
	(wire
		(pts
			(xy 86.36 85.09) (xy 86.36 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "597e7f78-e02e-455b-9103-7703eb7929e8")
	)
	(wire
		(pts
			(xy 91.44 64.77) (xy 91.44 68.58)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "672614c2-e800-48b9-9f20-71d2a0993955")
	)
	(wire
		(pts
			(xy 91.44 85.09) (xy 96.52 85.09)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "6f7a8653-ed41-41c9-9598-85b847fc2e54")
	)
	(wire
		(pts
			(xy 101.6 68.58) (xy 101.6 64.77)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "76283adc-4501-4a76-afff-b28088c38f82")
	)
	(wire
		(pts
			(xy 91.44 68.58) (xy 96.52 68.58)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "7bb392d8-b081-4027-938f-e8dd3f26b1e6")
	)
	(wire
		(pts
			(xy 86.36 64.77) (xy 86.36 68.58)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "8b27f395-ca33-4b6e-9072-ff0ec0afc291")
	)
	(wire
		(pts
			(xy 86.36 85.09) (xy 91.44 85.09)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "9282d83d-b343-4516-bef4-67c2849dfda0")
	)
	(wire
		(pts
			(xy 111.76 68.58) (xy 111.76 64.77)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "99f4281f-92de-43cd-b14d-8b8b5464affe")
	)
	(wire
		(pts
			(xy 96.52 68.58) (xy 101.6 68.58)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a0d5e4d0-17d1-41f9-8300-1d244d7d26e5")
	)
	(wire
		(pts
			(xy 106.68 68.58) (xy 106.68 64.77)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a66717c0-415b-452d-9017-58927394bf50")
	)
	(wire
		(pts
			(xy 101.6 85.09) (xy 101.6 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "a75722d2-a897-4beb-9be9-9845b5ee0207")
	)
	(wire
		(pts
			(xy 120.65 85.09) (xy 111.76 85.09)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "abb8c625-3471-4db8-802a-d7aeb8db0552")
	)
	(wire
		(pts
			(xy 120.65 68.58) (xy 111.76 68.58)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "b103c2b3-b5bf-4e12-9669-dc1e20859b08")
	)
	(wire
		(pts
			(xy 96.52 85.09) (xy 96.52 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "cfc3f340-568c-4597-9a48-13ded87f3699")
	)
	(wire
		(pts
			(xy 91.44 85.09) (xy 91.44 88.9)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e10549e2-8281-4dad-a64a-bf06e5c6542f")
	)
	(wire
		(pts
			(xy 86.36 68.58) (xy 91.44 68.58)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "e1a6c92f-245b-46bb-9fd5-11c2f26f759d")
	)
	(wire
		(pts
			(xy 96.52 68.58) (xy 96.52 64.77)
		)
		(stroke
			(width 0)
			(type default)
		)
		(uuid "fc856079-6bc9-436e-a46c-4ffab2362859")
	)
	(symbol
		(lib_id "power:GND")
		(at 106.68 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "187f23b9-58d1-473f-ba55-f22ee9dd0dbf")
		(property "Reference" "#PWR05"
			(at 106.68 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 106.68 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 106.68 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 106.68 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 106.68 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c52a2094-c449-4394-9bb9-af6d8a8818e2")
		)
		(instances
			(project ""
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR05")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 111.76 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "3545a458-a38c-43c1-ab77-3b23d4e2aafd")
		(property "Reference" "#PWR06"
			(at 111.76 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 111.76 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 111.76 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 111.76 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 111.76 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c531f886-d1d8-40ae-af50-b8a34ab54e8d")
		)
		(instances
			(project ""
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR06")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VCC")
		(at 96.52 64.77 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "36552470-5db5-43cc-bf05-304eb0f7158a")
		(property "Reference" "#PWR9"
			(at 96.52 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 96.52 59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 96.52 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 96.52 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VCC\""
			(at 96.52 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "eefd8e4c-c745-4157-bfaa-5c860cb0c64e")
		)
		(instances
			(project "erc_multiple_pin_to_pin"
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR9")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VCC")
		(at 111.76 64.77 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "37a1927f-b2f4-4311-84e0-0f2e66db7bad")
		(property "Reference" "#PWR12"
			(at 111.76 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 111.76 59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 111.76 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 111.76 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VCC\""
			(at 111.76 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b4caecda-6029-40f1-b96b-3a8683ede8d7")
		)
		(instances
			(project "erc_multiple_pin_to_pin"
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR12")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 101.6 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "39edd33a-556d-4204-bc19-7e9219bcb168")
		(property "Reference" "#PWR04"
			(at 101.6 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 101.6 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 101.6 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 101.6 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 101.6 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "2b351a74-af61-49a2-9289-6ad58d3c0fbd")
		)
		(instances
			(project ""
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR04")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector_Generic:Conn_01x01")
		(at 125.73 85.09 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "3cfa213e-b01a-41f6-92ad-2e991d50d214")
		(property "Reference" "J1"
			(at 128.27 83.8199 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "Conn_01x01"
			(at 128.27 86.3599 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 125.73 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 125.73 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connector, single row, 01x01, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 125.73 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "8dd39357-b8a0-43cb-ae06-69b3802e7301")
		)
		(instances
			(project ""
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "J1")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 86.36 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "3e289df7-dc13-4b08-85f8-937eeb5ec2a9")
		(property "Reference" "#PWR01"
			(at 86.36 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 86.36 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 86.36 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 86.36 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 86.36 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "62ff4fc9-4a71-4464-9fac-e42afa3b9ae2")
		)
		(instances
			(project ""
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR01")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 96.52 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "4c0ea90b-bf0a-4183-8c02-7a3eacecfbb6")
		(property "Reference" "#PWR03"
			(at 96.52 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 96.52 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 96.52 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 96.52 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 96.52 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "c8e179d3-c0c3-42c6-bc76-fed406e43061")
		)
		(instances
			(project ""
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR03")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VCC")
		(at 106.68 64.77 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "4ededa2d-ddd2-4a2c-94ed-9ef7ac98d340")
		(property "Reference" "#PWR11"
			(at 106.68 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 106.68 59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 106.68 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 106.68 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VCC\""
			(at 106.68 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "9e367e1c-5ed5-413c-bafa-833cb61a8d13")
		)
		(instances
			(project "erc_multiple_pin_to_pin"
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR11")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:PWR_FLAG")
		(at 86.36 68.58 90)
		(mirror x)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "68d7b657-7c92-4214-a4d5-580730e0b323")
		(property "Reference" "#FLG02"
			(at 84.455 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 82.55 68.5799 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 86.36 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 86.36 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Special symbol for telling ERC where power comes from"
			(at 86.36 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "8804aaff-b77e-46f4-8d89-e3da9642c398")
		)
		(instances
			(project "erc_multiple_pin_to_pin"
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#FLG02")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:PWR_FLAG")
		(at 86.36 85.09 90)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "7219516e-3c72-4062-98b8-ac232f202f81")
		(property "Reference" "#FLG01"
			(at 84.455 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "PWR_FLAG"
			(at 82.55 85.0899 90)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 86.36 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 86.36 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Special symbol for telling ERC where power comes from"
			(at 86.36 85.09 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "b3c8106c-c3a1-458f-89b5-ca23804e4ded")
		)
		(instances
			(project ""
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#FLG01")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VCC")
		(at 101.6 64.77 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "8ecf4d13-1bdd-4381-b913-1da6f1be91c3")
		(property "Reference" "#PWR10"
			(at 101.6 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 101.6 59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 101.6 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 101.6 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VCC\""
			(at 101.6 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "95134d6e-e80b-4352-9203-0e827e56de12")
		)
		(instances
			(project "erc_multiple_pin_to_pin"
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR10")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VCC")
		(at 86.36 64.77 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "a658e09d-f5f4-420d-806a-5bd64074b36a")
		(property "Reference" "#PWR7"
			(at 86.36 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 86.36 59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 86.36 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 86.36 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VCC\""
			(at 86.36 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "e0a7cc59-71a1-424d-aa17-d49a9daeffa9")
		)
		(instances
			(project "erc_multiple_pin_to_pin"
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR7")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:VCC")
		(at 91.44 64.77 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "d0254b2a-c59b-4750-b54b-b5df46110509")
		(property "Reference" "#PWR8"
			(at 91.44 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "VCC"
			(at 91.44 59.69 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 91.44 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 91.44 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"VCC\""
			(at 91.44 64.77 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "a6ce20e1-a567-49a5-a7c4-700a2f469fcc")
		)
		(instances
			(project "erc_multiple_pin_to_pin"
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR8")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "power:GND")
		(at 91.44 88.9 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "ea7b49fb-5519-46d9-98f3-d1c4682336ff")
		(property "Reference" "#PWR02"
			(at 91.44 95.25 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Value" "GND"
			(at 91.44 93.98 0)
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Footprint" ""
			(at 91.44 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" ""
			(at 91.44 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Power symbol creates a global label with name \"GND\" , ground"
			(at 91.44 88.9 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "d09d202a-3e95-4ba5-ae53-89ae638b398e")
		)
		(instances
			(project ""
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "#PWR02")
					(unit 1)
				)
			)
		)
	)
	(symbol
		(lib_id "Connector_Generic:Conn_01x01")
		(at 125.73 68.58 0)
		(unit 1)
		(exclude_from_sim no)
		(in_bom yes)
		(on_board yes)
		(dnp no)
		(fields_autoplaced yes)
		(uuid "f3197d36-2dba-4c4d-8c62-b92294427599")
		(property "Reference" "J2"
			(at 128.27 67.3099 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Value" "Conn_01x01"
			(at 128.27 69.8499 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(justify left)
			)
		)
		(property "Footprint" ""
			(at 125.73 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Datasheet" "~"
			(at 125.73 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(property "Description" "Generic connector, single row, 01x01, script generated (kicad-library-utils/schlib/autogen/connector/)"
			(at 125.73 68.58 0)
			(effects
				(font
					(size 1.27 1.27)
				)
				(hide yes)
			)
		)
		(pin "1"
			(uuid "41b05516-08b7-4132-9bf9-b29e6d04ae81")
		)
		(instances
			(project "erc_multiple_pin_to_pin"
				(path "/c9b0dfd9-0180-4a78-8afb-2260035ff793"
					(reference "J2")
					(unit 1)
				)
			)
		)
	)
	(sheet_instances
		(path "/"
			(page "1")
		)
	)
)
