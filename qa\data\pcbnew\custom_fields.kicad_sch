(kicad_sch (version 20210123) (generator eeschema)

  (paper "A4")

  (lib_symbols
    (symbol "Connector_Generic:Conn_01x02" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "J" (id 0) (at 0 2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "Conn_01x02" (id 1) (at 0 -5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "connector" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Generic connector, single row, 01x02, script generated (kicad-library-utils/schlib/autogen/connector/)" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Connector*:*_1x??_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Conn_01x02_1_1"
        (rectangle (start -1.27 -2.413) (end 0 -2.667)
          (stroke (width 0.1524)) (fill (type none))
        )
        (rectangle (start -1.27 0.127) (end 0 -0.127)
          (stroke (width 0.1524)) (fill (type none))
        )
        (rectangle (start -1.27 1.27) (end 1.27 -3.81)
          (stroke (width 0.254)) (fill (type background))
        )
        (pin passive line (at -5.08 0 0) (length 3.81)
          (name "Pin_1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 -2.54 0) (length 3.81)
          (name "Pin_2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254)) (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )


  (wire (pts (xy 125.73 90.17) (xy 142.24 90.17))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 142.24 90.17) (xy 142.24 92.71))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 142.24 95.25) (xy 142.24 97.79))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 142.24 97.79) (xy 125.73 97.79))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )

  (symbol (lib_id "Device:R") (at 125.73 93.98 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid "8e574303-eef9-471f-8bf9-dbda319f7a68")
    (property "Reference" "R1" (id 0) (at 127.5081 92.8306 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "R" (id 1) (at 127.5081 95.1293 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Resistor_SMD:R_1206_3216Metric" (id 2) (at 123.952 93.98 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 125.73 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )

  (symbol (lib_id "Connector_Generic:Conn_01x02") (at 147.32 92.71 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid "ebd12d3c-779b-42b9-beee-e5481760d2bb")
    (property "Reference" "J1" (id 0) (at 149.3521 92.8814 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "Conn_01x02" (id 1) (at 149.3521 95.1801 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Connector_JST:JST_XH_B2B-XH-A_1x02_P2.50mm_Vertical" (id 2) (at 147.32 92.71 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 147.32 92.71 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "myfield" "myvalue" (id 4) (at 147.32 92.71 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/ebd12d3c-779b-42b9-beee-e5481760d2bb"
      (reference "J1") (unit 1) (value "Conn_01x02") (footprint "Connector_JST:JST_XH_B2B-XH-A_1x02_P2.50mm_Vertical")
    )
    (path "/8e574303-eef9-471f-8bf9-dbda319f7a68"
      (reference "R1") (unit 1) (value "R") (footprint "Resistor_SMD:R_1206_3216Metric")
    )
  )
)
