/* XPM */
static char const * cursor_line_wire64_xpm[] = {
"64 64 4 1",
" 	c None",
".	c #FFFFFF",
"+	c #008000",
"@	c #000000",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                  ..            ",
"                                                 ....           ",
"                                                ..++..          ",
"                                               ..++++..         ",
"                                              ..+++++..         ",
"                                             ..+++++..          ",
"                                            ..+++++..           ",
"                                           ..+++++..            ",
"                                          ..+++++..             ",
"                                         ..+++++..              ",
"                                        ..+++++..               ",
"                                       ..+++++..                ",
"                                      ..+++++..                 ",
"                                     ..+++++..                  ",
"                                    ..+++++..                   ",
"                                   ..+++++..                    ",
"                                  ..+++++..                     ",
"                                 ..+++++..                      ",
"                                ..+++++..                       ",
"                               ..+++++..                        ",
"                              ..+++++..                         ",
"                             ..+++++..                          ",
"                            ..+++++..                           ",
"                           ..+++++..                            ",
"                          ..+++++..                             ",
"                         ..+++++..                              ",
"                        ..+++++..                               ",
"                       ..+++++..                                ",
"                      ..+++++..                                 ",
"                     ..+++++..                                  ",
"                    ..+++++..                                   ",
"                   ..+++++..                                    ",
"                   ..++++..                                     ",
"                    ..++..                                      ",
"          ..         ....                                       ",
"         .@@.         ..                                        ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"  ........@@........                                            ",
" .@@@@@@@@  @@@@@@@@.                                           ",
" .@@@@@@@@  @@@@@@@@.                                           ",
"  ........@@........                                            ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"         .@@.                                                   ",
"          ..                                                    ",
"                                                                "};
