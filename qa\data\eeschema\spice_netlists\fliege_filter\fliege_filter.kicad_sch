(kicad_sch (version 20221004) (generator eeschema)

  (uuid 1bb7f68e-09b1-4448-8403-97b70f8d3680)

  (paper "A4")

  (lib_symbols
    (symbol "Amplifier_Operational:LM358" (pin_names (offset 0.127)) (in_bom yes) (on_board yes)
      (property "Reference" "U" (at 0 5.08 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "LM358" (at 0 -5.08 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "http://www.ti.com/lit/ds/symlink/lm2904-n.pdf" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_locked" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_keywords" "dual opamp" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Low-Power, Dual Operational Amplifiers, DIP-8/SOIC-8/TO-99-8" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "SOIC*3.9x4.9mm*P1.27mm* DIP*W7.62mm* TO*99* OnSemi*Micro8* TSSOP*3x3mm*P0.65mm* TSSOP*4.4x3mm*P0.65mm* MSOP*3x3mm*P0.65mm* SSOP*3.9x4.9mm*P0.635mm* LFCSP*2x2mm*P0.5mm* *SIP* SOIC*5.3x6.2mm*P1.27mm*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "LM358_1_1"
        (polyline
          (pts
            (xy -5.08 5.08)
            (xy 5.08 0)
            (xy -5.08 -5.08)
            (xy -5.08 5.08)
          )
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
        (pin output line (at 7.62 0 180) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -7.62 -2.54 0) (length 2.54)
          (name "-" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -7.62 2.54 0) (length 2.54)
          (name "+" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "LM358_2_1"
        (polyline
          (pts
            (xy -5.08 5.08)
            (xy 5.08 0)
            (xy -5.08 -5.08)
            (xy -5.08 5.08)
          )
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
        (pin input line (at -7.62 2.54 0) (length 2.54)
          (name "+" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -7.62 -2.54 0) (length 2.54)
          (name "-" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
        (pin output line (at 7.62 0 180) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "7" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "LM358_3_1"
        (pin power_in line (at -2.54 -7.62 90) (length 3.81)
          (name "V-" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -2.54 7.62 270) (length 3.81)
          (name "V+" (effects (font (size 1.27 1.27))))
          (number "8" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:C" (pin_numbers hide) (pin_names (offset 0.254)) (in_bom yes) (on_board yes)
      (property "Reference" "C" (at 0.635 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "C" (at 0.635 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0.9652 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "cap capacitor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Unpolarized capacitor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "C_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "C_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy 2.032 -0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
      )
      (symbol "C_1_1"
        (pin passive line (at 0 3.81 270) (length 2.794)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 2.794)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VDC" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VDC" (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "dc(1)" (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, DC" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VDC_0_0"
        (polyline
          (pts
            (xy -1.27 0.254)
            (xy 1.27 0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.254)
            (xy -1.27 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.254 -0.254)
            (xy -0.254 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.254)
            (xy 0.762 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VDC_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VDC_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VSIN" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VSIN" (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "sin(0 1 1k)" (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, sinusoidal" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VSIN_0_0"
        (arc (start 0 0) (mid -0.635 0.6323) (end -1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0) (mid 0.635 -0.6323) (end 1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VSIN_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VSIN_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 196.85 91.44) (diameter 0) (color 0 0 0 0)
    (uuid 080466a8-2b6e-4731-acda-1fadc288005d)
  )
  (junction (at 158.75 101.6) (diameter 0) (color 0 0 0 0)
    (uuid 19db5023-8a0b-403e-9a68-0a4560dae79b)
  )
  (junction (at 196.85 120.65) (diameter 0) (color 0 0 0 0)
    (uuid 31a8cd39-d498-41a8-a7de-2caac1e12a5a)
  )
  (junction (at 196.85 101.6) (diameter 0) (color 0 0 0 0)
    (uuid 42254e74-b577-460a-a13c-3a763fe4e706)
  )
  (junction (at 158.75 88.9) (diameter 0) (color 0 0 0 0)
    (uuid 8cd86a5c-7555-4a0a-b919-ad6efe55fa24)
  )
  (junction (at 139.7 101.6) (diameter 0) (color 0 0 0 0)
    (uuid 8dcb9c69-be28-4d0e-a448-d61de1fc5938)
  )
  (junction (at 177.8 101.6) (diameter 0) (color 0 0 0 0)
    (uuid a73e3f0e-ed44-4e3a-9746-ef83d265bf9c)
  )
  (junction (at 152.4 88.9) (diameter 0) (color 0 0 0 0)
    (uuid d0bc1c7a-60be-4781-a712-eaca9f324757)
  )
  (junction (at 152.4 101.6) (diameter 0) (color 0 0 0 0)
    (uuid d1797249-03a3-4019-bdaf-036c7aad4518)
  )
  (junction (at 139.7 88.9) (diameter 0) (color 0 0 0 0)
    (uuid e58eb144-2e8a-4bf6-8dad-a4700fb9ae8f)
  )
  (junction (at 114.3 127) (diameter 0) (color 0 0 0 0)
    (uuid fbe146e9-bba2-43a7-beec-73ef9f809f2c)
  )

  (wire (pts (xy 158.75 118.11) (xy 158.75 101.6))
    (stroke (width 0) (type default))
    (uuid 00738f8e-35a5-4a99-a157-dae374485f01)
  )
  (wire (pts (xy 196.85 139.7) (xy 139.7 139.7))
    (stroke (width 0) (type default))
    (uuid 016a2784-fbeb-473e-9a6a-c73e4e87c153)
  )
  (wire (pts (xy 167.64 101.6) (xy 158.75 101.6))
    (stroke (width 0) (type default))
    (uuid 17b420aa-01cf-4ddb-8c1e-91683be3459f)
  )
  (wire (pts (xy 162.56 118.11) (xy 158.75 118.11))
    (stroke (width 0) (type default))
    (uuid 1af365ce-42bc-4369-9284-67a5493fde28)
  )
  (wire (pts (xy 139.7 139.7) (xy 139.7 101.6))
    (stroke (width 0) (type default))
    (uuid 2537f553-874e-464b-a506-452ddba61455)
  )
  (wire (pts (xy 139.7 101.6) (xy 139.7 88.9))
    (stroke (width 0) (type default))
    (uuid 32333cae-6755-471c-aa16-6b8dfb1acf0d)
  )
  (wire (pts (xy 114.3 127) (xy 114.3 128.27))
    (stroke (width 0) (type default))
    (uuid 32f273f9-d82f-47d0-87f8-e9ff5c8db830)
  )
  (wire (pts (xy 158.75 88.9) (xy 177.8 88.9))
    (stroke (width 0) (type default))
    (uuid 3f709bf1-7156-4e11-9008-eaa442c050b2)
  )
  (wire (pts (xy 152.4 88.9) (xy 158.75 88.9))
    (stroke (width 0) (type default))
    (uuid 4e3558bf-fc63-45ec-8074-618cdcf95d49)
  )
  (wire (pts (xy 114.3 88.9) (xy 139.7 88.9))
    (stroke (width 0) (type default))
    (uuid 5490b38b-f125-48d6-b5e1-e1fad031ffbd)
  )
  (wire (pts (xy 114.3 139.7) (xy 114.3 138.43))
    (stroke (width 0) (type default))
    (uuid 56f9b8e8-80c8-459b-b88c-************)
  )
  (wire (pts (xy 149.86 101.6) (xy 152.4 101.6))
    (stroke (width 0) (type default))
    (uuid 63d4fba3-1a53-4689-bc2f-95bf92abbb66)
  )
  (wire (pts (xy 114.3 125.73) (xy 114.3 127))
    (stroke (width 0) (type default))
    (uuid 6d517c61-e085-47f5-8bd1-58fc4f4f8138)
  )
  (wire (pts (xy 177.8 101.6) (xy 175.26 101.6))
    (stroke (width 0) (type default))
    (uuid 76013af2-5421-4782-be44-9b1f1afb44b4)
  )
  (wire (pts (xy 196.85 137.16) (xy 196.85 139.7))
    (stroke (width 0) (type default))
    (uuid 7bc0919d-469d-4609-bf7a-ef806419abc1)
  )
  (wire (pts (xy 114.3 114.3) (xy 127 114.3))
    (stroke (width 0) (type default))
    (uuid 7e358a59-5bf9-4a0a-b875-9e9f7ce99a56)
  )
  (wire (pts (xy 114.3 115.57) (xy 114.3 114.3))
    (stroke (width 0) (type default))
    (uuid 85016c6e-14ea-4056-863c-b679a97822bb)
  )
  (wire (pts (xy 149.86 88.9) (xy 152.4 88.9))
    (stroke (width 0) (type default))
    (uuid 896fff07-1b0a-4511-85b9-f5aacb45b1fe)
  )
  (wire (pts (xy 142.24 101.6) (xy 139.7 101.6))
    (stroke (width 0) (type default))
    (uuid 8d7f8989-9f65-493d-bdcb-0bd02a8459d6)
  )
  (wire (pts (xy 196.85 101.6) (xy 196.85 91.44))
    (stroke (width 0) (type default))
    (uuid 960c7add-b922-405f-80ff-7753b5414de0)
  )
  (wire (pts (xy 196.85 120.65) (xy 196.85 129.54))
    (stroke (width 0) (type default))
    (uuid 96a5a1cd-eb13-4c35-9d3a-3c8cb03a31e7)
  )
  (wire (pts (xy 152.4 101.6) (xy 152.4 88.9))
    (stroke (width 0) (type default))
    (uuid 9b691bae-b477-4602-98ce-54020634e0d2)
  )
  (wire (pts (xy 196.85 111.76) (xy 196.85 120.65))
    (stroke (width 0) (type default))
    (uuid 9c8724b4-3120-43b7-8065-8b88018a79a2)
  )
  (wire (pts (xy 177.8 101.6) (xy 177.8 93.98))
    (stroke (width 0) (type default))
    (uuid 9f051ed8-08e4-4b24-8ada-f285400e14e3)
  )
  (wire (pts (xy 177.8 115.57) (xy 177.8 101.6))
    (stroke (width 0) (type default))
    (uuid aa548a6e-a6e5-4520-a51a-33350c68b8b1)
  )
  (wire (pts (xy 152.4 101.6) (xy 152.4 104.14))
    (stroke (width 0) (type default))
    (uuid b66ef834-090c-4222-9206-ecd27687cd03)
  )
  (wire (pts (xy 196.85 91.44) (xy 203.2 91.44))
    (stroke (width 0) (type default))
    (uuid c38afeb5-04e6-44c1-852c-a3b39d975bb2)
  )
  (wire (pts (xy 158.75 91.44) (xy 158.75 88.9))
    (stroke (width 0) (type default))
    (uuid c867d611-eaf6-40e7-b1a6-ddf96912d105)
  )
  (wire (pts (xy 114.3 90.17) (xy 114.3 88.9))
    (stroke (width 0) (type default))
    (uuid cb556b15-f116-4789-b943-ef4a2ed92482)
  )
  (wire (pts (xy 139.7 88.9) (xy 142.24 88.9))
    (stroke (width 0) (type default))
    (uuid d64b6a1f-ea0c-42e2-9566-c613f37bef1e)
  )
  (wire (pts (xy 113.03 127) (xy 114.3 127))
    (stroke (width 0) (type default))
    (uuid d93974d7-798b-40f6-85b5-9775fe1cdd2d)
  )
  (wire (pts (xy 196.85 104.14) (xy 196.85 101.6))
    (stroke (width 0) (type default))
    (uuid e16d7343-515c-44b3-b5a3-07739fe2ef9f)
  )
  (wire (pts (xy 127 114.3) (xy 127 119.38))
    (stroke (width 0) (type default))
    (uuid ebfcd534-0c9e-47f6-918d-49e05e410dcd)
  )
  (wire (pts (xy 127 139.7) (xy 114.3 139.7))
    (stroke (width 0) (type default))
    (uuid ed29747f-2d3c-49c4-896c-fd41c72f03ce)
  )
  (wire (pts (xy 158.75 101.6) (xy 158.75 99.06))
    (stroke (width 0) (type default))
    (uuid ef9d21b8-15b5-461c-9c38-463eab4dda8e)
  )
  (wire (pts (xy 177.8 120.65) (xy 196.85 120.65))
    (stroke (width 0) (type default))
    (uuid f23ef421-5680-41b2-af34-d1ed61d67699)
  )
  (wire (pts (xy 187.96 101.6) (xy 196.85 101.6))
    (stroke (width 0) (type default))
    (uuid f3d25fd6-9a75-4202-82df-9348977ad0e5)
  )
  (wire (pts (xy 196.85 91.44) (xy 193.04 91.44))
    (stroke (width 0) (type default))
    (uuid f9589ccf-7be4-467a-b7a5-3d7df30a34fe)
  )
  (wire (pts (xy 127 134.62) (xy 127 139.7))
    (stroke (width 0) (type default))
    (uuid fc0195cf-f58c-4a52-be4d-8cb7e968bbeb)
  )
  (wire (pts (xy 177.8 101.6) (xy 180.34 101.6))
    (stroke (width 0) (type default))
    (uuid fc0fbb0c-6c18-4763-aa53-63bd0fe0f39b)
  )

  (text ".ac oct 10000 500 2k" (at 88.9 139.7 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 39be915e-60a8-490f-bf5e-fac8c4f56539)
  )

  (label "in" (at 114.3 88.9 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 1b822112-ddc6-4f05-bec2-86149eac0259)
  )
  (label "out" (at 203.2 91.44 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 70e292cd-d259-455c-b0cc-9821f699c572)
  )

  (symbol (lib_id "Amplifier_Operational:LM358") (at 170.18 118.11 180) (unit 2)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 1f9535b1-6668-419a-8162-68da61e10385)
    (property "Reference" "U1" (at 170.18 109.22 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "LM358" (at 170.18 111.76 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 170.18 118.11 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "http://www.ti.com/lit/ds/symlink/lm2904-n.pdf" (at 170.18 118.11 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Name" "uopamp_lvl2_2x" (at 185.42 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Library" "uopamp.lib.spice" (at 185.42 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "SUBCKT" (at 185.42 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Pins" "1=OUT1 2=-IN1 3=+IN1 4=VEE 5=+IN2 6=-IN2 7=OUT2 8=VCC" (at 170.18 118.11 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 0a29384e-5eae-4f6a-8dd8-bcb7fde77582))
    (pin "2" (uuid f4822b70-a40a-48e4-b1fe-93961c727978))
    (pin "3" (uuid 333b2942-3abd-4b04-b462-3f60313d7d5c))
    (pin "5" (uuid 27f8c948-353a-4e62-b0f4-6e9d2caf36b6))
    (pin "6" (uuid 1da44f03-c3bc-46fb-8565-760c3d3847b8))
    (pin "7" (uuid 3f62936b-f8db-42c6-bf32-9967c0bb59ed))
    (pin "4" (uuid a2661ef9-a7e5-4a82-acc4-498069251b27))
    (pin "8" (uuid 28267ce4-7d05-4b5b-b250-9717f9aadad6))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "U1") (unit 2) (value "LM358") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 113.03 127 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 32efad03-1f41-49f9-b4ea-1c10d6f8b290)
    (property "Reference" "#PWR0102" (at 106.68 127 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 109.22 127.635 90)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 113.03 127 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 113.03 127 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b8698b4b-789d-433e-9e3d-58855afeb989))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "#PWR0102") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 146.05 101.6 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 40ae5997-e7ff-4435-9011-995b9329a072)
    (property "Reference" "R1" (at 146.05 96.52 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "1M" (at 146.05 99.06 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 146.05 103.378 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 146.05 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 146.05 101.6 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 40f49b82-6de7-4c0a-9ce9-c928e90dcee8))
    (pin "2" (uuid 927de06d-c030-4513-ae73-2a546edceb92))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "R1") (unit 1) (value "1M") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 114.3 100.33 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 417cb032-b0dd-48f9-ba4c-b3e4ab34caa0)
    (property "Reference" "#PWR0103" (at 114.3 106.68 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 114.3 105.41 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 114.3 100.33 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 114.3 100.33 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5574af62-a1d4-48a1-8d60-098f735cbc13))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "#PWR0103") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 152.4 111.76 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 4a5a1b77-1f24-4735-af02-661a95efa347)
    (property "Reference" "#PWR0101" (at 152.4 118.11 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 152.4 116.84 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 152.4 111.76 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 152.4 111.76 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 285f130f-c9a9-447d-91e9-c9eb4bad4b7a))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "#PWR0101") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 196.85 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 4e83ce78-5afa-4877-a190-a11a77e2315b)
    (property "Reference" "R6" (at 199.39 132.715 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100k" (at 199.39 135.255 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 195.072 133.35 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 196.85 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 196.85 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 083905bc-5d2a-4620-b997-d57f2d5bc810))
    (pin "2" (uuid 985c8f1c-8267-45f9-9760-47c9aa413bce))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "R6") (unit 1) (value "100k") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 114.3 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 7156d318-64c1-44a4-9222-3a9a47aa2372)
    (property "Reference" "V5" (at 118.11 132.715 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "5" (at 118.11 135.255 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 114.3 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 114.3 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 114.3 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d23993af-9614-478b-984f-e6808785e08a))
    (pin "2" (uuid 908cef22-da54-4813-a573-d8e538412d66))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "V5") (unit 1) (value "5") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 184.15 101.6 270) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 7a2f7d50-7fd2-45fc-8549-13a411cd678a)
    (property "Reference" "R4" (at 184.15 96.52 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "15k" (at 184.15 99.06 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 184.15 99.822 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 184.15 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 184.15 101.6 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid b939b2fc-aa38-4699-b76d-3476fb7b1b91))
    (pin "2" (uuid 340595d2-3297-4e03-bc9e-7b5530d12845))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "R4") (unit 1) (value "15k") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Amplifier_Operational:LM358") (at 124.46 127 0) (mirror y) (unit 3)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 87dd20a3-7a20-4d68-b835-ffed52bb6407)
    (property "Reference" "U1" (at 128.27 127.635 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "LM358" (at 128.27 125.095 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 124.46 127 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "http://www.ti.com/lit/ds/symlink/lm2904-n.pdf" (at 124.46 127 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Name" "uopamp_lvl2_2x" (at 185.42 149.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Library" "uopamp.lib.spice" (at 185.42 149.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "SUBCKT" (at 185.42 149.86 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Pins" "1=OUT1 2=-IN1 3=+IN1 4=VEE 5=+IN2 6=-IN2 7=OUT2 8=VCC" (at 124.46 127 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5299360d-4e38-4601-9b73-8e19e0ac8f7e))
    (pin "2" (uuid 350b908f-0f88-41e6-ae62-566d1b148cb6))
    (pin "3" (uuid 871312bc-5c95-4083-a3f3-77e176925cf4))
    (pin "5" (uuid 04a3f5df-511c-4551-be3d-0f36514b9d79))
    (pin "6" (uuid 505a0463-ab55-4d25-a322-9c1bc4d556d4))
    (pin "7" (uuid 6c8a5b15-19b8-4f7f-9391-fd0ae8577955))
    (pin "4" (uuid f8419e99-6cb4-4149-8f18-58b8a114b817))
    (pin "8" (uuid 104db1a6-2ee0-424b-8a10-83f39fbdda9d))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "U1") (unit 3) (value "LM358") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VSIN") (at 114.3 95.25 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 88021970-b98d-4b3b-8504-80e76499ab7b)
    (property "Reference" "V1" (at 118.11 93.345 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "" (at 118.11 95.885 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 114.3 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 114.3 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 114.3 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "ac=1" (at 120.65 97.79 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid d18a7be3-befb-4f82-9812-85b576c6ba5e))
    (pin "2" (uuid 9a1a9ed4-ff64-4de9-9d48-d4c40f1612a0))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "V1") (unit 1) (value "") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 171.45 101.6 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 915357ab-3b0a-400b-9348-4398e83e2f0d)
    (property "Reference" "C2" (at 171.45 95.25 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "10n" (at 171.45 97.79 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 175.26 100.6348 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 171.45 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "C" (at 171.45 101.6 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 701a271c-8e57-419b-a6f9-2cfcaf4ee7b0))
    (pin "2" (uuid 8acd99f5-756e-400c-8bc0-8ad070a55b1c))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "C2") (unit 1) (value "10n") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 114.3 120.65 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 9505d7a9-59aa-4162-bfe0-189552aad72d)
    (property "Reference" "V4" (at 118.11 118.745 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "5" (at 118.11 121.285 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 114.3 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 114.3 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 114.3 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid d2a7eba0-60d5-4840-8c92-caa7d1cde951))
    (pin "2" (uuid 265a4239-56c8-4d73-80d5-54f8b8513d40))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "V4") (unit 1) (value "5") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Amplifier_Operational:LM358") (at 185.42 91.44 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid b7badb24-bf38-4cfb-b723-95e3c247d571)
    (property "Reference" "U1" (at 185.42 82.55 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "LM358" (at 185.42 85.09 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 185.42 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "http://www.ti.com/lit/ds/symlink/lm2904-n.pdf" (at 185.42 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Name" "uopamp_lvl2_2x" (at 185.42 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Library" "uopamp.lib.spice" (at 185.42 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "SUBCKT" (at 185.42 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Pins" "1=OUT1 2=-IN1 3=+IN1 4=VEE 5=+IN2 6=-IN2 7=OUT2 8=VCC" (at 185.42 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid dd60fb5d-009c-4abb-9bfa-85c9da58e712))
    (pin "2" (uuid 119b97a6-2663-4af3-8985-755444efebf8))
    (pin "3" (uuid 4aa8a087-5c13-4214-a267-06743f36fd28))
    (pin "5" (uuid 9f9bfbff-a698-4166-bfdb-67470ac1ef24))
    (pin "6" (uuid cf69309c-5a55-4110-be51-f85795c3cda1))
    (pin "7" (uuid 44bbf19f-6e7b-4432-9a21-df368cc78b2b))
    (pin "4" (uuid a892482c-6810-40f9-8824-e192d9d63f7f))
    (pin "8" (uuid 5bab44f3-7d2e-4aad-be27-b6e5b1dde982))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "U1") (unit 1) (value "LM358") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 158.75 95.25 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid da754588-7440-43c1-8ef3-7b66c12d6a5a)
    (property "Reference" "R3" (at 161.29 94.615 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "15k" (at 161.29 97.155 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 160.528 95.25 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 158.75 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 158.75 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 0f79286a-8c35-4ebd-bb87-7c2598c14e47))
    (pin "2" (uuid bbd3f277-d91d-4c7c-a69e-c57524a68d74))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "R3") (unit 1) (value "15k") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:C") (at 146.05 88.9 90) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid dbe16bdd-a219-4911-8bf3-fe440c368714)
    (property "Reference" "C1" (at 146.05 82.55 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "10n" (at 146.05 85.09 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 149.86 87.9348 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 146.05 88.9 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "C" (at 146.05 88.9 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 390f428e-6f41-4201-872f-ad881ee3dac9))
    (pin "2" (uuid eaf1c2d2-52fe-4763-afe1-6117fb0605a1))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "C1") (unit 1) (value "10n") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 196.85 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid e279ce6e-6053-4f70-b6fc-c5b6590e8e68)
    (property "Reference" "R5" (at 199.39 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100k" (at 199.39 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 195.072 107.95 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 196.85 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 196.85 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 17e2e487-1812-4496-ab4b-2a31981593f1))
    (pin "2" (uuid 60c540bd-d514-4344-b841-1ce0bbdaccec))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "R5") (unit 1) (value "100k") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 152.4 107.95 180) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid f1b3385a-18cc-443d-986d-324b49f10808)
    (property "Reference" "R2" (at 146.05 106.68 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "1M" (at 146.05 109.22 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 154.178 107.95 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 152.4 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 152.4 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 0641ce34-fa4f-4111-a092-55b991bc6841))
    (pin "2" (uuid 8088142a-b276-4d71-995a-28a0e4dbefbe))
    (instances
      (project "fliege_filter"
        (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680"
          (reference "R2") (unit 1) (value "1M") (footprint "")
        )
      )
    )
  )

  (sheet_instances
    (path "/1bb7f68e-09b1-4448-8403-97b70f8d3680" (page "1"))
  )
)
