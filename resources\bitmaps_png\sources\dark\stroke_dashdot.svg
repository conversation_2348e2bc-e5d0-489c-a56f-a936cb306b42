<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="100%"
   height="100%"
   viewBox="0 0 64 16"
   version="1.1"
   xml:space="preserve"
   style="fill-rule:evenodd;clip-rule:evenodd;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:1.5;"
   id="svg840"
   sodipodi:docname="stroke_dashdot.svg"
   inkscape:version="1.0.2 (e86c8708, 2021-01-15)"
   inkscape:export-filename="/Users/<USER>/kicad_dev/kicad/bitmaps_png/png/stroke_dashdot.png"
   inkscape:export-xdpi="96"
   inkscape:export-ydpi="96"><defs
   id="defs844" /><sodipodi:namedview
   pagecolor="#ffffff"
   bordercolor="#666666"
   borderopacity="1"
   objecttolerance="10"
   gridtolerance="10"
   guidetolerance="10"
   inkscape:pageopacity="0"
   inkscape:pageshadow="2"
   inkscape:window-width="1440"
   inkscape:window-height="800"
   id="namedview842"
   showgrid="false"
   inkscape:zoom="10.679189"
   inkscape:cx="32"
   inkscape:cy="8"
   inkscape:window-x="0"
   inkscape:window-y="23"
   inkscape:window-maximized="1"
   inkscape:current-layer="svg840"
   inkscape:document-rotation="0" />
     <metadata
   id="metadata43">
          <rdf:RDF>
               <cc:Work
   rdf:about="">
                    <dc:format>image/svg+xml</dc:format>
                    <dc:type
   rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
                    <dc:title />
                    <cc:license
   rdf:resource="http://creativecommons.org/licenses/by-sa/4.0/" />
               </cc:Work>
               
          <cc:License
   rdf:about="http://creativecommons.org/licenses/by-sa/4.0/"><cc:permits
     rdf:resource="http://creativecommons.org/ns#Reproduction" /><cc:permits
     rdf:resource="http://creativecommons.org/ns#Distribution" /><cc:requires
     rdf:resource="http://creativecommons.org/ns#Notice" /><cc:requires
     rdf:resource="http://creativecommons.org/ns#Attribution" /><cc:permits
     rdf:resource="http://creativecommons.org/ns#DerivativeWorks" /><cc:requires
     rdf:resource="http://creativecommons.org/ns#ShareAlike" /></cc:License></rdf:RDF>
     </metadata>
     <path
   d="m 35.735,8 c 0,-1.656 -1.344,-3 -3,-3 h -1 c -1.656,0 -3,1.344 -3,3 0,1.656 1.344,3 3,3 h 1 c 1.656,0 3,-1.344 3,-3 z"
   style="fill:#DED3DD;fill-opacity:1;stroke:#000000;stroke-width:0.09px"
   id="path834"
   sodipodi:nodetypes="sssssss" />
     <path
   d="m 19.5,8 c 0,-1.656 -1.344,-3 -3,-3 H 4 C 2.344,5 1,6.344 1,8 c 0,1.656 1.344,3 3,3 h 12.5 c 1.656,0 3,-1.344 3,-3 z"
   style="fill:#DED3DD;fill-opacity:1;stroke:#000000;stroke-width:0.09px"
   id="path836"
   sodipodi:nodetypes="sssssss" />
     <path
   d="M 63,8 C 63,6.344 61.656,5 60,5 H 47.5 c -1.656,0 -3,1.344 -3,3 0,1.656 1.344,3 3,3 H 60 c 1.656,0 3,-1.344 3,-3 z"
   style="fill:#DED3DD;fill-opacity:1;stroke:#000000;stroke-width:0.09px"
   id="path838"
   sodipodi:nodetypes="sssssss" />
</svg>
