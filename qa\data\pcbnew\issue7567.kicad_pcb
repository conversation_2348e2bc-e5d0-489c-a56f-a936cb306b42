(kicad_pcb (version 20220621) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.<PERSON>hes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (color "Green") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "core") (thickness 1.51) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (color "Green") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "None")
      (dielectric_constraints no)
    )
    (pad_to_mask_clearance 0)
    (aux_axis_origin 96.52 144.78)
    (grid_origin 96.52 144.78)
    (pcbplotparams
      (layerselection 0x00000fc_80000001)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes false)
      (usegerberadvancedattributes false)
      (creategerberjobfile false)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 6)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "GND")
  (net 2 "+5V")
  (net 3 "+3V3")
  (net 4 "GPIO_SPI_CS#")
  (net 5 "GPIO_UART1_TXD")
  (net 6 "GPIO_SPI_MISO")
  (net 7 "GPIO_UART1_RXD")
  (net 8 "GPIO_SPI_MOSI")
  (net 9 "GPIO_UART1_CTS")
  (net 10 "GPIO_SPI_CLK")
  (net 11 "GPIO_UART1_RTS")
  (net 12 "GPIO_I2C_SCL")
  (net 13 "GPIO_I2S_CLK")
  (net 14 "GPIO_I2C_SDA")
  (net 15 "GPIO_I2S_FRM")
  (net 16 "GPIO_UART2_TXD")
  (net 17 "GPIO_I2S_DO")
  (net 18 "GPIO_UART2_RXD")
  (net 19 "GPIO_I2S_DI")
  (net 20 "GPIO_S5_0")
  (net 21 "GPIO_PWM0")
  (net 22 "GPIO_S5_1")
  (net 23 "GPIO_PWM1")
  (net 24 "GPIO_S5_2")
  (net 25 "I2SMCLK_GPIO")

  (footprint "Pin_Headers:Pin_Header_Straight_2x13" locked (layer "F.Cu")
    (tstamp 00000000-0000-0000-0000-000057710c62)
    (at 109.855 142.24 90)
    (descr "Through hole pin header")
    (tags "pin header")
    (path "/00000000-0000-0000-0000-0000576c994f")
    (attr through_hole)
    (fp_text reference "P1" (at 1.27 -3.175 90) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp df6ea7ea-4c97-406e-b3d5-74ea4b56d842)
    )
    (fp_text value "CONN_02X13" (at 5.08 3.175 180) (layer "F.Fab") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 0521fa67-7953-4a88-8d72-df0659d06791)
    )
    (fp_line (start -1.55 -1.55) (end -1.55 0)
      (stroke (width 0.15) (type solid)) (layer "F.SilkS") (tstamp 520abb36-f693-4622-b926-5fd5e9d9c40f))
    (fp_line (start -1.27 1.27) (end -1.27 31.75)
      (stroke (width 0.15) (type solid)) (layer "F.SilkS") (tstamp 4ec22dd7-3cbb-4efa-95a5-9b85d3a13668))
    (fp_line (start 0 -1.55) (end -1.55 -1.55)
      (stroke (width 0.15) (type solid)) (layer "F.SilkS") (tstamp 76480a74-d043-445b-9be1-34656ab0d843))
    (fp_line (start 1.27 -1.27) (end 1.27 1.27)
      (stroke (width 0.15) (type solid)) (layer "F.SilkS") (tstamp fe0079fa-49e6-44bd-858a-d6eca245c70a))
    (fp_line (start 1.27 1.27) (end -1.27 1.27)
      (stroke (width 0.15) (type solid)) (layer "F.SilkS") (tstamp 8ca8e6ad-651f-477d-a59c-8edaf8b2b42c))
    (fp_line (start 3.81 -1.27) (end 1.27 -1.27)
      (stroke (width 0.15) (type solid)) (layer "F.SilkS") (tstamp dbee2a3c-b78c-4689-9dd1-8914940b4295))
    (fp_line (start 3.81 -1.27) (end 3.81 31.75)
      (stroke (width 0.15) (type solid)) (layer "F.SilkS") (tstamp 3167fff6-db1d-4c63-8d24-a011d71fcd96))
    (fp_line (start 3.81 31.75) (end -1.27 31.75)
      (stroke (width 0.15) (type solid)) (layer "F.SilkS") (tstamp 30162e69-e978-4092-8abd-fb8cf99fe5ab))
    (fp_line (start -1.75 -1.75) (end -1.75 32.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 21911c5e-dd6e-443e-bc92-e82c3328f409))
    (fp_line (start -1.75 -1.75) (end 4.3 -1.75)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp ce51c7f4-52d7-4410-99e2-85e15b2e1ca9))
    (fp_line (start -1.75 32.25) (end 4.3 32.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp cbcc1b9b-48ca-474c-acf7-2372da4972d7))
    (fp_line (start 4.3 -1.75) (end 4.3 32.25)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 77f54041-c0e8-49fd-9556-2a085e8bb35a))
    (pad "1" thru_hole rect locked (at 0 0 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 1 "GND") (tstamp 50e46035-d009-4bc9-9144-01a2b287fa35))
    (pad "2" thru_hole oval locked (at 2.54 0 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 1 "GND") (tstamp da748863-9757-41e5-820a-7f527a6b67f4))
    (pad "3" thru_hole oval locked (at 0 2.54 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 2 "+5V") (tstamp 5e5f556f-b431-451a-97db-c239bfdb6b76))
    (pad "4" thru_hole oval locked (at 2.54 2.54 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 3 "+3V3") (tstamp 3549d9b3-8d6e-45c8-9dc8-155d51b3f1f4))
    (pad "5" thru_hole oval locked (at 0 5.08 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 4 "GPIO_SPI_CS#") (tstamp bb0fbcdb-2c6a-42d9-b77d-949cc4808960))
    (pad "6" thru_hole oval locked (at 2.54 5.08 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 5 "GPIO_UART1_TXD") (tstamp 12b020e9-d4e0-43a5-806a-e9106735ab66))
    (pad "7" thru_hole oval locked (at 0 7.62 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 6 "GPIO_SPI_MISO") (tstamp 7693013d-863f-442e-99fb-6039d8b588de))
    (pad "8" thru_hole oval locked (at 2.54 7.62 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 7 "GPIO_UART1_RXD") (tstamp bc5ffcde-e33e-441d-b6ac-3e6afc4d94e7))
    (pad "9" thru_hole oval locked (at 0 10.16 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 8 "GPIO_SPI_MOSI") (tstamp 7f45ed09-d0b5-4fac-9eb4-20ee6c6f07a5))
    (pad "10" thru_hole oval locked (at 2.54 10.16 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 9 "GPIO_UART1_CTS") (tstamp 98154aac-31dc-461f-bfc2-29e338d032b8))
    (pad "11" thru_hole oval locked (at 0 12.7 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 10 "GPIO_SPI_CLK") (tstamp fe7b12bd-1172-47eb-89b1-4e16aee85fab))
    (pad "12" thru_hole oval locked (at 2.54 12.7 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 11 "GPIO_UART1_RTS") (tstamp f205cafc-4bc4-4231-937b-c32e5ee0917f))
    (pad "13" thru_hole oval locked (at 0 15.24 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 12 "GPIO_I2C_SCL") (tstamp 0d650c51-94a2-4a05-b514-dcca6c661b70))
    (pad "14" thru_hole oval locked (at 2.54 15.24 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 13 "GPIO_I2S_CLK") (tstamp 62385088-5982-4c59-83ee-a3e784b8dcc6))
    (pad "15" thru_hole oval locked (at 0 17.78 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 14 "GPIO_I2C_SDA") (tstamp 13faa25d-676b-4b52-bb43-20b12e4ee67b))
    (pad "16" thru_hole oval locked (at 2.54 17.78 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 15 "GPIO_I2S_FRM") (tstamp 12e0b964-6275-4d6d-bc8e-1115d1e67dd0))
    (pad "17" thru_hole oval locked (at 0 20.32 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 16 "GPIO_UART2_TXD") (tstamp e6f77aad-8f77-428b-b3da-8b4736e0a396))
    (pad "18" thru_hole oval locked (at 2.54 20.32 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 17 "GPIO_I2S_DO") (tstamp 456b7b6c-732b-4dd9-bb8c-bbfb8fd8ea8c))
    (pad "19" thru_hole oval locked (at 0 22.86 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 18 "GPIO_UART2_RXD") (tstamp 6d5620cd-9778-4c77-be23-94a57f004611))
    (pad "20" thru_hole oval locked (at 2.54 22.86 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 19 "GPIO_I2S_DI") (tstamp 31a6a5b8-e9b4-41b0-be22-3a2ae05ecc51))
    (pad "21" thru_hole oval locked (at 0 25.4 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 20 "GPIO_S5_0") (tstamp 5426cde5-01bb-4e04-b1c2-1eb7426cbec8))
    (pad "22" thru_hole oval locked (at 2.54 25.4 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 21 "GPIO_PWM0") (tstamp 4c4613cf-50b8-4d30-b330-d44b7505f7a5))
    (pad "23" thru_hole oval locked (at 0 27.94 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 22 "GPIO_S5_1") (tstamp 040a8c56-a2c5-489c-8167-8b7186664393))
    (pad "24" thru_hole oval locked (at 2.54 27.94 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 23 "GPIO_PWM1") (tstamp 376a7697-de73-4228-b597-7420574cb8f4))
    (pad "25" thru_hole oval locked (at 0 30.48 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 24 "GPIO_S5_2") (tstamp ad3c0573-42eb-4ab4-acec-a839887126ca))
    (pad "26" thru_hole oval locked (at 2.54 30.48 90) (size 1.7272 1.7272) (drill 1.016) (layers *.Cu *.Mask "F.SilkS")
      (net 25 "I2SMCLK_GPIO") (tstamp 874ff977-dbaa-4613-abf6-c2d848b5b3f0))
    (model "Pin_Headers.3dshapes/Pin_Header_Straight_2x13.wrl"
      (offset (xyz 1.269999981 -15.23999977 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 90))
    )
  )

  (footprint "Mounting_Holes:MountingHole_3-5mm" locked (layer "F.Cu")
    (tstamp 00000000-0000-0000-0000-0000577da462)
    (at 191.77 140.97)
    (descr "Mounting hole, Befestigungsbohrung, 3,5mm, No Annular, Kein Restring,")
    (tags "Mounting hole, Befestigungsbohrung, 3,5mm, No Annular, Kein Restring,")
    (path "/00000000-0000-0000-0000-000058633372")
    (fp_text reference "MK2" (at 0 -4.50088) (layer "F.SilkS") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ff4093c6-37e5-4062-80ab-cee0613ea782)
    )
    (fp_text value "Mounting_Hole" (at 0 5.00126) (layer "F.Fab") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 36b1a5f8-8293-4311-8bd7-8513e6eb0138)
    )
    (fp_circle (center 0 0) (end 3.5 0)
      (stroke (width 0.381) (type solid)) (fill none) (layer "Cmts.User") (tstamp 4c0a7678-dfed-4c31-b09a-348c5d219b65))
    (pad "" np_thru_hole circle locked (at 0 0) (size 3.5 3.5) (drill 3.5) (layers F&B.Cu *.Mask) (tstamp 86ce1d0e-da25-49eb-ae42-604350131569))
  )

  (footprint "Mounting_Holes:MountingHole_3-5mm" locked (layer "F.Cu")
    (tstamp 00000000-0000-0000-0000-0000577f1cae)
    (at 100.33 140.97)
    (descr "Mounting hole, Befestigungsbohrung, 3,5mm, No Annular, Kein Restring,")
    (tags "Mounting hole, Befestigungsbohrung, 3,5mm, No Annular, Kein Restring,")
    (path "/00000000-0000-0000-0000-000058633409")
    (fp_text reference "MK1" (at 0 -4.50088) (layer "F.SilkS") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp fdb9b92b-133b-4b47-8202-34e9c2a71818)
    )
    (fp_text value "Mounting_Hole" (at 0 5.00126) (layer "F.Fab") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ca7648fb-bd9d-45ae-8e68-a244b40b89bf)
    )
    (fp_circle (center 0 0) (end 3.5 0)
      (stroke (width 0.381) (type solid)) (fill none) (layer "Cmts.User") (tstamp cfd355b8-7252-45d7-a9a6-e63b1c0f609e))
    (pad "" np_thru_hole circle locked (at 0 0) (size 3.5 3.5) (drill 3.5) (layers F&B.Cu *.Mask) (tstamp 342f95af-1da3-4ffa-a9ef-dbc51ce2b75b))
  )

  (footprint "Mounting_Holes:MountingHole_3-5mm" locked (layer "F.Cu")
    (tstamp 00000000-0000-0000-0000-0000577f1cb9)
    (at 100.33 74.93)
    (descr "Mounting hole, Befestigungsbohrung, 3,5mm, No Annular, Kein Restring,")
    (tags "Mounting hole, Befestigungsbohrung, 3,5mm, No Annular, Kein Restring,")
    (path "/00000000-0000-0000-0000-00005863348e")
    (fp_text reference "MK4" (at 0 -4.50088) (layer "F.SilkS") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 89f8f381-144b-4f6d-97c0-9d50963af224)
    )
    (fp_text value "Mounting_Hole" (at 0 5.00126) (layer "F.Fab") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ff263f3e-bbfb-4282-8c12-7e0a8c90d9df)
    )
    (fp_circle (center 0 0) (end 3.5 0)
      (stroke (width 0.381) (type solid)) (fill none) (layer "Cmts.User") (tstamp 76b47b06-f231-4716-8dcb-54ce4ae94eba))
    (pad "" np_thru_hole circle locked (at 0 0) (size 3.5 3.5) (drill 3.5) (layers F&B.Cu *.Mask) (tstamp 6cb52416-0698-4f08-a129-189ae148bc82))
  )

  (footprint "Mounting_Holes:MountingHole_3-5mm" locked (layer "F.Cu")
    (tstamp 00000000-0000-0000-0000-0000577f1cc4)
    (at 191.77 74.93)
    (descr "Mounting hole, Befestigungsbohrung, 3,5mm, No Annular, Kein Restring,")
    (tags "Mounting hole, Befestigungsbohrung, 3,5mm, No Annular, Kein Restring,")
    (path "/00000000-0000-0000-0000-000058633454")
    (fp_text reference "MK3" (at 0 -4.50088) (layer "F.SilkS") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ec3af793-b9a1-4776-987a-62dc3ac44e4a)
    )
    (fp_text value "Mounting_Hole" (at 0 5.00126) (layer "F.Fab") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 8c0e2f03-e8db-4bd4-ad91-3d9de85fd0f2)
    )
    (fp_circle (center 0 0) (end 3.5 0)
      (stroke (width 0.381) (type solid)) (fill none) (layer "Cmts.User") (tstamp 66a6fefc-396c-447b-beb4-eae4fe6b8a65))
    (pad "" np_thru_hole circle locked (at 0 0) (size 3.5 3.5) (drill 3.5) (layers F&B.Cu *.Mask) (tstamp 93ef87f2-708b-4e52-b54c-2cbd6740e799))
  )

  (footprint "Resistor_SMD:R_0201_0603Metric" (layer "F.Cu")
    (tstamp c6d657fd-5fb9-4795-b35b-b8b571ac9973)
    (at 108.52 103.78)
    (descr "Resistor SMD 0201 (0603 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: https://www.vishay.com/docs/20052/crcw0201e3.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (attr smd)
    (fp_text reference "REF**" (at 0 -1.05) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp b6a6c6e2-647b-497b-8063-30c5dc6ae238)
    )
    (fp_text value "R_0201_0603Metric" (at 0 1.05) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp c2ff6069-851b-4586-b813-0d29f1cbb91b)
    )
    (fp_text user "${REFERENCE}" (at 0 -0.68) (layer "F.Fab")
        (effects (font (size 0.25 0.25) (thickness 0.04)))
      (tstamp 0f52edb2-e666-4472-ab5f-bb943a6d007e)
    )
    (fp_line (start -0.7 -0.35) (end 0.7 -0.35)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp d056470e-03a4-4f41-8395-855673becbea))
    (fp_line (start -0.7 0.35) (end -0.7 -0.35)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 45f6e2c1-c089-4110-a24d-4249fde80857))
    (fp_line (start 0.7 -0.35) (end 0.7 0.35)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 7ec5d1ba-e069-46ae-9fc3-70f2e9b2906e))
    (fp_line (start 0.7 0.35) (end -0.7 0.35)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp fb1761cf-0ef1-4597-8f11-48d5c123d152))
    (fp_line (start -0.3 -0.15) (end 0.3 -0.15)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e04c0cff-46c6-4a93-a0a4-b1521960a47e))
    (fp_line (start -0.3 0.15) (end -0.3 -0.15)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp fd9b1b23-22c5-4c46-83f1-64f36867c37d))
    (fp_line (start 0.3 -0.15) (end 0.3 0.15)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3d91e15e-8b12-4a4b-b942-ea0234aad4d4))
    (fp_line (start 0.3 0.15) (end -0.3 0.15)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 19f10a3f-3d39-4d89-b75d-d9a5d401cf26))
    (pad "" smd roundrect locked (at -0.345 0) (size 0.318 0.36) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp 05de845e-b7f4-43e8-bc7c-621f0a3d63eb))
    (pad "" smd roundrect locked (at 0.345 0) (size 0.318 0.36) (layers "F.Paste") (roundrect_rratio 0.25) (tstamp f3ca4cf6-bb49-45aa-bbc0-df85cd44b58f))
    (pad "1" smd roundrect locked (at -0.32 0) (size 0.46 0.4) (layers "F.Cu" "F.Mask") (roundrect_rratio 0.25) (tstamp a2d80f69-5f89-4bbd-a0e6-bd1f2450f16f))
    (pad "2" smd roundrect locked (at 0.32 0) (size 0.46 0.4) (layers "F.Cu" "F.Mask") (roundrect_rratio 0.25) (tstamp 377331a3-75db-48e6-8254-e27dc43ff779))
    (model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0201_0603Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_line (start 195.58 71.12) (end 96.52 71.12)
    (stroke (width 0.15) (type solid)) (layer "Edge.Cuts") (tstamp 2668eae5-e838-4dec-a87d-2b40a098a0a2))
  (gr_line (start 96.52 144.78) (end 195.58 144.78)
    (stroke (width 0.15) (type solid)) (layer "Edge.Cuts") (tstamp 4db5a4c9-4072-41cd-a257-fadcefa47294))
  (gr_line (start 195.58 144.78) (end 195.58 71.12)
    (stroke (width 0.15) (type solid)) (layer "Edge.Cuts") (tstamp c5eebce5-78a3-4142-8902-e211c6cd5c8f))
  (gr_line (start 96.52 71.12) (end 96.52 144.78)
    (stroke (width 0.15) (type solid)) (layer "Edge.Cuts") (tstamp deb9b139-58fd-49d8-9b12-09ad44d4f842))
  (target plus (at 96.52 144.78) (size 5) (width 0.15) (layer "Edge.Cuts") (tstamp 0656fbe8-d593-4158-9bf3-80d6a1b2e6a0))

  (zone (net 0) (net_name "") (layer "B.Cu") (tstamp 7ecf5a8d-7053-4645-8dde-8ebe3d86d387) (name "NoBottomFootprints") (hatch full 0.508)
    (connect_pads (clearance 0))
    (min_thickness 0.254) (filled_areas_thickness no)
    (keepout (tracks allowed) (vias allowed) (pads allowed) (copperpour allowed) (footprints allowed))
    (fill (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 114.96 110.15)
        (xy 96.52 110.15)
        (xy 96.52 92.05)
        (xy 114.96 92.05)
      )
    )
  )
  (zone (net 0) (net_name "") (layer "B.Cu") (tstamp a318326d-41b9-4ad1-a1b1-e90c920d74a2) (name "NoBottomFootprints") (hatch full 0.508)
    (connect_pads (clearance 0))
    (min_thickness 0.254) (filled_areas_thickness no)
    (keepout (tracks allowed) (vias allowed) (pads allowed) (copperpour allowed) (footprints not_allowed))
    (fill (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 195.58 112.02)
        (xy 178.86 112.02)
        (xy 178.86 96.83)
        (xy 195.58 96.83)
      )
    )
  )
)
