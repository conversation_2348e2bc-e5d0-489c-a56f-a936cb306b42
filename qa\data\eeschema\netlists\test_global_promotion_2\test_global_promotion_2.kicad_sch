(kicad_sch (version 20200512) (host eeschema "(5.99.0-1770-gd6a274607)")

  (page 1 3)

  (paper "A4")

  (lib_symbols
    (symbol "Connector:Conn_01x03_Male" (pin_names hide)
      (property "Reference" "J" (id 0) (at 0 5.08 0))
      (property "Value" "Conn_01x03_Male" (id 1) (at 0 -5.08 0))
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "connector" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Generic connector, single row, 01x03, script generated (kicad-library-utils/schlib/autogen/connector/)" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Connector*:*_1x??_*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "Conn_01x03_Male_1_1"
        (rectangle (start 0.8636 -2.413) (end 0 -2.667)(fill (type outline)))
        (rectangle (start 0.8636 0.127) (end 0 -0.127)(fill (type outline)))
        (rectangle (start 0.8636 2.667) (end 0 2.413)(fill (type outline)))
        (polyline
          (pts (xy 1.27 -2.54) (xy 0.8636 -2.54))
        )
        (polyline
          (pts (xy 1.27 0) (xy 0.8636 0))
        )
        (polyline
          (pts (xy 1.27 2.54) (xy 0.8636 2.54))
        )
        (pin passive line (at 5.08 2.54 180) (length 3.81) (name "Pin_1") (number "1"))
        (pin passive line (at 5.08 0 180) (length 3.81) (name "Pin_2") (number "2"))
        (pin passive line (at 5.08 -2.54 180) (length 3.81) (name "Pin_3") (number "3"))
      )
    )
  )

  (junction (at 115.57 82.55))

  (wire (pts (xy 96.52 58.42) (xy 130.81 58.42)))
  (wire (pts (xy 97.79 77.47) (xy 127 77.47)))
  (wire (pts (xy 97.79 80.01) (xy 115.57 80.01)))
  (wire (pts (xy 115.57 80.01) (xy 115.57 82.55)))
  (wire (pts (xy 115.57 82.55) (xy 115.57 107.95)))
  (wire (pts (xy 115.57 82.55) (xy 134.62 82.55)))
  (wire (pts (xy 115.57 107.95) (xy 134.62 107.95)))
  (wire (pts (xy 127 77.47) (xy 127 100.33)))
  (wire (pts (xy 127 100.33) (xy 134.62 100.33)))
  (wire (pts (xy 130.81 58.42) (xy 130.81 104.14)))
  (wire (pts (xy 130.81 104.14) (xy 134.62 104.14)))
  (wire (pts (xy 133.35 55.88) (xy 96.52 55.88)))
  (wire (pts (xy 133.35 78.74) (xy 133.35 55.88)))
  (wire (pts (xy 134.62 74.93) (xy 97.79 74.93)))
  (wire (pts (xy 134.62 78.74) (xy 133.35 78.74)))

  (label "LIVE_2" (at 107.95 77.47 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
  )

  (global_label "LIVE" (shape input) (at 133.35 55.88 0)
    (effects (font (size 1.27 1.27)) (justify left))
  )

  (symbol (lib_id "Connector:Conn_01x03_Male") (at 91.44 58.42 0) (unit 1)
    (uuid "00000000-0000-0000-0000-00005ccf482d")
    (property "Reference" "J1" (id 0) (at 91.44 53.34 0))
    (property "Value" "Conn_01x03_Male" (id 1) (at 91.44 63.5 0))
    (property "Footprint" "Connector_PinHeader_2.54mm:PinHeader_1x03_P2.54mm_Vertical" (id 2) (at 91.44 58.42 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 91.44 58.42 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )

  (symbol (lib_id "Connector:Conn_01x03_Male") (at 92.71 77.47 0) (unit 1)
    (uuid "00000000-0000-0000-0000-00005cc1686d")
    (property "Reference" "J2" (id 0) (at 92.71 72.39 0))
    (property "Value" "Conn_01x03_Male" (id 1) (at 92.71 82.55 0))
    (property "Footprint" "Connector_PinHeader_2.54mm:PinHeader_1x03_P2.54mm_Vertical" (id 2) (at 92.71 77.47 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 92.71 77.47 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )

  (sheet (at 134.62 71.12) (size 27.94 17.78)
    (stroke (width 0.1524) (color 174 129 255 1))
    (fill (color 255 255 255 0.0000))
    (uuid 00000000-0000-0000-0000-00005cc15ef9)
    (property "Sheet name" "Sheet5CC15EF8" (id 0) (at 134.62 70.4845 0)
      (effects (font (size 1.27 1.27)) (justify left bottom))
    )
    (property "Sheet file" "subsheet.kicad_sch" (id 1) (at 134.62 89.4085 0)
      (effects (font (size 1.27 1.27)) (justify left top))
    )
    (pin "LIVE" input (at 134.62 74.93 180)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (pin "NEUTRAL" input (at 134.62 82.55 180)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (pin "LIVE_1" input (at 134.62 78.74 180)
      (effects (font (size 1.27 1.27)) (justify left))
    )
  )

  (sheet (at 134.62 96.52) (size 27.94 17.78)
    (stroke (width 0.1524) (color 174 129 255 1))
    (fill (color 255 255 255 0.0000))
    (uuid 00000000-0000-0000-0000-00005cc165f1)
    (property "Sheet name" "sheet5CC165F1" (id 0) (at 134.62 95.8845 0)
      (effects (font (size 1.27 1.27)) (justify left bottom))
    )
    (property "Sheet file" "subsheet.kicad_sch" (id 1) (at 134.62 114.8085 0)
      (effects (font (size 1.27 1.27)) (justify left top))
    )
    (pin "LIVE" input (at 134.62 100.33 180)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (pin "NEUTRAL" input (at 134.62 107.95 180)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (pin "LIVE_1" input (at 134.62 104.14 180)
      (effects (font (size 1.27 1.27)) (justify left))
    )
  )

  (symbol_instances
    (path "/00000000-0000-0000-0000-00005ccf482d" (reference "J1") (unit 1))
    (path "/00000000-0000-0000-0000-00005cc1686d" (reference "J2") (unit 1))
    (path "/00000000-0000-0000-0000-00005cc15ef9/00000000-0000-0000-0000-00005cc15f6f" (reference "R1") (unit 1))
    (path "/00000000-0000-0000-0000-00005cc15ef9/00000000-0000-0000-0000-00005ce0545a" (reference "R2") (unit 1))
    (path "/00000000-0000-0000-0000-00005cc165f1/00000000-0000-0000-0000-00005cc15f6f" (reference "R3") (unit 1))
    (path "/00000000-0000-0000-0000-00005cc165f1/00000000-0000-0000-0000-00005ce0545a" (reference "R4") (unit 1))
  )
)
