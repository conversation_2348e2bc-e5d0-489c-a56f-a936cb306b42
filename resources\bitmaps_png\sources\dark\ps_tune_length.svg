<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   id="Слой_1"
   data-name="Слой 1"
   viewBox="0 0 24 24"
   version="1.1"
   sodipodi:docname="ps_tune_length.svg"
   inkscape:version="1.0.1 (3bc2e813f5, 2020-09-07)">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1609"
     inkscape:window-height="1286"
     id="namedview30"
     showgrid="true"
     inkscape:zoom="27.961538"
     inkscape:cx="3.3259971"
     inkscape:cy="13"
     inkscape:window-x="0"
     inkscape:window-y="37"
     inkscape:window-maximized="0"
     inkscape:document-rotation="0"
     inkscape:current-layer="Слой_1">
    <inkscape:grid
       type="xygrid"
       id="grid_kicad"
       spacingx="0.5"
       spacingy="0.5"
       color="#9999ff"
       opacity="0.13"
       empspacing="2" />
  </sodipodi:namedview>
  <metadata
     id="metadata43">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/4.0/" />
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>add_arc</dc:title>
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/4.0/">
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Reproduction" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Distribution" />
        <cc:requires
           rdf:resource="http://creativecommons.org/ns#Notice" />
        <cc:requires
           rdf:resource="http://creativecommons.org/ns#Attribution" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#DerivativeWorks" />
        <cc:requires
           rdf:resource="http://creativecommons.org/ns#ShareAlike" />
      </cc:License>
    </rdf:RDF>
  </metadata>
  <defs
     id="defs26735">
    <style
       id="style26733">.cls-1{fill:none;stroke:#42B8EB;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style>
  </defs>
  <title
     id="title26737">ps_tune_length</title>
  <path
     class="cls-1"
     d="m 1.0058,13.0061 h 0.9986 a 1,1 0 0 1 1,0.9956 L 3.0156,16.52 a 1.49,1.49 0 0 0 1.49,1.49 h 0.0176 a 1.49,1.49 0 0 0 1.49,-1.49 L 6.0025,7.9925 A 1.99,1.99 0 0 1 7.9925,6 h 0.0162 a 1.99,1.99 0 0 1 1.99,1.99 V 14 a 1,1 0 0 0 1,1 H 13 a 1,1 0 0 0 1,-1 V 7.98 a 1.99,1.99 0 0 1 1.99,-1.99 h 0.02 A 1.99,1.99 0 0 1 18,7.98 V 14 a 1,1 0 0 0 1,1 h 1 a 1,1 0 0 0 1,-1 v -1 a 1,1 0 0 1 1,-1 h 0.9942"
     id="path26739" />
</svg>
