/* XPM */
static char const * cursor_measure64_xpm[] = {
"64 64 3 1",
" 	c None",
".	c #000000",
"+	c #FFFFFF",
"                                                                ",
"        ++                                                      ",
"       +..+                                                     ",
"       +..+                                                     ",
"       +..+                                                     ",
"       +..+                                                     ",
"       +..+                                                     ",
"  ++++++..++++++                                                ",
" +......  ......+                                               ",
" +......  ......+                                               ",
"  ++++++..++++++                                                ",
"       +..+                                                     ",
"       +..+                                                     ",
"       +..+                                                     ",
"       +..+           ++++                                      ",
"       +..+          ++..++                                     ",
"        ++          ++....++                                    ",
"                   ++..++..++                                   ",
"                  ++..++++..++                                  ",
"                 ++..++++++..++                                 ",
"                ++..++++++++..++                                ",
"               ++..++++++++++..++                               ",
"              ++..++++..++++++..++                              ",
"              +..++++...+++++++..++                             ",
"              +..+++...+++++++++..++                            ",
"              ++..+...+++++++++++..++                           ",
"               ++....+++++++++++++..++                          ",
"                ++..+++++++++++++++..++                         ",
"                 ++..+++..++++++++++..++                        ",
"                  ++..+...+++++++++++..++                       ",
"                   ++....+++++..++++++..++                      ",
"                    ++..+++++...+++++++..++                     ",
"                     ++..+++...+++++++++..++                    ",
"                      ++..+...+++++++++++..++                   ",
"                       ++....+++++++++++++..++                  ",
"                        ++..+++++++++++++++..++                 ",
"                         ++..+++..++++++++++..++                ",
"                          ++..+...+++++++++++..++               ",
"                           ++....+++++..++++++..++              ",
"                            ++..+++++...+++++++..++             ",
"                             ++..+++...+++++++++..++            ",
"                              ++..+...+++++++++++..++           ",
"                               ++....+++++++++++++..++          ",
"                                ++..+++++++++++++++..++         ",
"                                 ++..+++..++++++++++..++        ",
"                                  ++..+...++++++++++..++        ",
"                                   ++....+++++..+++..++         ",
"                                    ++..+++++...++..++          ",
"                                     ++..+++...++..++           ",
"                                      ++..+...++..++            ",
"                                       ++....++..++             ",
"                                        ++..++..++              ",
"                                         ++....++               ",
"                                          ++..++                ",
"                                           ++++                 ",
"                                            ++                  ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                "};
