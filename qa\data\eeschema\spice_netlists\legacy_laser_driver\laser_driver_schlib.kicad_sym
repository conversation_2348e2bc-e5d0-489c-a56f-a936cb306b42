(kicad_symbol_lib (version 20220331) (generator kicad_symbol_editor)
  (symbol "C" (pin_numbers hide) (pin_names (offset 0.254)) (in_bom yes) (on_board yes)
    (property "Reference" "C" (id 0) (at 0.635 2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "C" (id 1) (at 0.635 -2.54 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 0.9652 -3.81 0)
      (effects (font (size 0.762 0.762)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "ki_fp_filters" "C? C_????_* C_???? SMD*_c Capacitor*" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "C_0_1"
      (polyline
        (pts
          (xy -2.032 -0.762)
          (xy 2.032 -0.762)
        )
        (stroke (width 0.508) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy -2.032 0.762)
          (xy 2.032 0.762)
        )
        (stroke (width 0.508) (type default))
        (fill (type none))
      )
    )
    (symbol "C_1_1"
      (pin passive line (at 0 3.81 270) (length 2.794)
        (name "~" (effects (font (size 1.016 1.016))))
        (number "1" (effects (font (size 1.016 1.016))))
      )
      (pin passive line (at 0 -3.81 90) (length 2.794)
        (name "~" (effects (font (size 1.016 1.016))))
        (number "2" (effects (font (size 1.016 1.016))))
      )
    )
  )
  (symbol "GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "#PWR" (id 0) (at 0 -6.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 0 -3.81 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.524 1.524)))
    )
    (symbol "GND_0_1"
      (polyline
        (pts
          (xy 0 0)
          (xy 0 -1.27)
          (xy 1.27 -1.27)
          (xy 0 -2.54)
          (xy -1.27 -1.27)
          (xy 0 -1.27)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "GND_1_1"
      (pin power_in line (at 0 0 270) (length 0) hide
        (name "GND" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "Generic_Opamp" (in_bom yes) (on_board yes)
    (property "Reference" "U" (id 0) (at 0 6.35 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "Generic_Opamp" (id 1) (at 0 3.81 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at -2.54 -2.54 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (symbol "Generic_Opamp_0_1"
      (polyline
        (pts
          (xy -5.08 5.08)
          (xy 5.08 0)
          (xy -5.08 -5.08)
          (xy -5.08 5.08)
        )
        (stroke (width 0.254) (type default))
        (fill (type background))
      )
    )
    (symbol "Generic_Opamp_1_1"
      (pin input line (at -7.62 2.54 0) (length 2.54)
        (name "+" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -7.62 -2.54 0) (length 2.54)
        (name "-" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -2.54 7.62 270) (length 3.81)
        (name "V+" (effects (font (size 1.27 1.27))))
        (number "3" (effects (font (size 1.27 1.27))))
      )
      (pin power_in line (at -2.54 -7.62 90) (length 3.81)
        (name "V-" (effects (font (size 1.27 1.27))))
        (number "4" (effects (font (size 1.27 1.27))))
      )
      (pin output line (at 7.62 0 180) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "5" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "LED" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
    (property "Reference" "D" (id 0) (at 0 2.54 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "LED" (id 1) (at 0 -2.54 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "ki_fp_filters" "LED-3MM LED-5MM LED-10MM LED-0603 LED-0805 LED-1206 LEDV" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "LED_0_1"
      (polyline
        (pts
          (xy -1.27 1.27)
          (xy -1.27 -1.27)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy -2.032 -0.635)
          (xy -3.175 -1.651)
          (xy -3.048 -1.016)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy -1.651 -1.016)
          (xy -2.794 -2.032)
          (xy -2.667 -1.397)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 1.27 1.27)
          (xy -1.27 0)
          (xy 1.27 -1.27)
        )
        (stroke (width 0) (type default))
        (fill (type outline))
      )
    )
    (symbol "LED_1_1"
      (pin passive line (at -5.08 0 0) (length 3.81)
        (name "K" (effects (font (size 1.016 1.016))))
        (number "1" (effects (font (size 1.016 1.016))))
      )
      (pin passive line (at 5.08 0 180) (length 3.81)
        (name "A" (effects (font (size 1.016 1.016))))
        (number "2" (effects (font (size 1.016 1.016))))
      )
    )
  )
  (symbol "Q_NPN_CBE" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
    (property "Reference" "Q" (id 0) (at 7.62 1.27 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "Q_NPN_CBE" (id 1) (at 15.24 -1.27 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (id 2) (at 5.08 2.54 0)
      (effects (font (size 0.7366 0.7366)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.524 1.524)))
    )
    (symbol "Q_NPN_CBE_0_1"
      (polyline
        (pts
          (xy 0.635 0.635)
          (xy 2.54 2.54)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 0.635 -0.635)
          (xy 2.54 -2.54)
          (xy 2.54 -2.54)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 0.635 1.905)
          (xy 0.635 -1.905)
          (xy 0.635 -1.905)
        )
        (stroke (width 0.508) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 1.27 -1.778)
          (xy 1.778 -1.27)
          (xy 2.286 -2.286)
          (xy 1.27 -1.778)
          (xy 1.27 -1.778)
        )
        (stroke (width 0) (type default))
        (fill (type outline))
      )
      (circle (center 1.27 0) (radius 2.8194)
        (stroke (width 0.254) (type default))
        (fill (type none))
      )
    )
    (symbol "Q_NPN_CBE_1_1"
      (pin passive line (at 2.54 5.08 270) (length 2.54)
        (name "C" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at -5.08 0 0) (length 5.715)
        (name "B" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
      (pin passive line (at 2.54 -5.08 90) (length 2.54)
        (name "E" (effects (font (size 1.27 1.27))))
        (number "3" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "R" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
    (property "Reference" "R" (id 0) (at 2.032 0 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "R" (id 1) (at 0 0 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at -1.778 0 90)
      (effects (font (size 0.762 0.762)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 0.762 0.762)))
    )
    (property "ki_fp_filters" "R_* Resistor_*" (id 4) (at 0 0 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (symbol "R_0_1"
      (rectangle (start -1.016 -2.54) (end 1.016 2.54)
        (stroke (width 0.254) (type default))
        (fill (type none))
      )
    )
    (symbol "R_1_1"
      (pin passive line (at 0 3.81 270) (length 1.27)
        (name "1" (effects (font (size 0.508 0.508))))
        (number "1" (effects (font (size 0.508 0.508))))
      )
      (pin passive line (at 0 -3.81 90) (length 1.27)
        (name "2" (effects (font (size 0.508 0.508))))
        (number "2" (effects (font (size 0.508 0.508))))
      )
    )
  )
  (symbol "VDD" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "#PWR" (id 0) (at 0 -3.81 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "VDD" (id 1) (at 0 3.81 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.524 1.524)))
    )
    (symbol "VDD_0_1"
      (polyline
        (pts
          (xy 0 0)
          (xy 0 1.27)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (circle (center 0 1.905) (radius 0.635)
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "VDD_1_1"
      (pin power_in line (at 0 0 90) (length 0) hide
        (name "VDD" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "VSOURCE" (pin_names (offset 1.016)) (in_bom yes) (on_board yes)
    (property "Reference" "V" (id 0) (at 5.08 5.08 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "VSOURCE" (id 1) (at 6.35 2.54 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (id 4) (at 0 0 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Node_Sequence" "1 2" (id 6) (at -7.62 5.08 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (symbol "VSOURCE_0_1"
      (polyline
        (pts
          (xy 0 -1.905)
          (xy 0 1.905)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (polyline
        (pts
          (xy 0 1.905)
          (xy -0.635 0.635)
          (xy 0.635 0.635)
          (xy 0 1.905)
        )
        (stroke (width 0) (type default))
        (fill (type outline))
      )
      (circle (center 0 0) (radius 2.54)
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "VSOURCE_1_1"
      (pin input line (at 0 5.08 270) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
      (pin input line (at 0 -5.08 90) (length 2.54)
        (name "~" (effects (font (size 1.27 1.27))))
        (number "2" (effects (font (size 1.27 1.27))))
      )
    )
  )
  (symbol "VSS" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
    (property "Reference" "#PWR" (id 0) (at 0 -3.81 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "VSS" (id 1) (at 0 3.81 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 0 0 0)
      (effects (font (size 1.524 1.524)))
    )
    (property "Datasheet" "" (id 3) (at 0 0 0)
      (effects (font (size 1.524 1.524)))
    )
    (symbol "VSS_0_1"
      (polyline
        (pts
          (xy 0 0)
          (xy 0 1.27)
        )
        (stroke (width 0) (type default))
        (fill (type none))
      )
      (circle (center 0 1.905) (radius 0.635)
        (stroke (width 0) (type default))
        (fill (type none))
      )
    )
    (symbol "VSS_1_1"
      (pin power_in line (at 0 0 90) (length 0) hide
        (name "VSS" (effects (font (size 1.27 1.27))))
        (number "1" (effects (font (size 1.27 1.27))))
      )
    )
  )
)
