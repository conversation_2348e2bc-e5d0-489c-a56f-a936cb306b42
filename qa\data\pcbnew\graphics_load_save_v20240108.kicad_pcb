(kicad_pcb
	(version 20240108)
	(generator "pcbnew")
	(generator_version "8.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(31 "B.Cu" signal)
		(32 "B.Adhes" user "B.Adhesive")
		(33 "F<PERSON>hes" user "F.Adhesive")
		(34 "B.Paste" user)
		(35 "F.Paste" user)
		(36 "B.SilkS" user "B.Silkscreen")
		(37 "F.<PERSON>" user "F.Silkscreen")
		(38 "B.Mask" user)
		(39 "F.Mask" user)
		(40 "Dwgs.User" user "User.Drawings")
		(41 "Cmts.User" user "User.Comments")
		(42 "Eco1.User" user "User.Eco1")
		(43 "Eco2.User" user "User.Eco2")
		(44 "Edge.Cuts" user)
		(45 "Margin" user)
		(46 "B.CrtYd" user "B.Courtyard")
		(47 "F.CrtYd" user "F.Courtyard")
		(48 "B.Fab" user)
		(49 "F.Fab" user)
		(50 "User.1" user)
		(51 "User.2" user)
		(52 "User.3" user)
		(53 "User.4" user)
		(54 "User.5" user)
		(55 "User.6" user)
		(56 "User.7" user)
		(57 "User.8" user)
		(58 "User.9" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(pcbplotparams
			(layerselection 0x00010fc_ffffffff)
			(plot_on_all_layers_selection 0x0000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(viasonmask no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotreference yes)
			(plotvalue yes)
			(plotfptext yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(gr_poly
		(pts
			(xy 145 85) (xy 160 85) (xy 160 100) (xy 145 100)
		)
		(stroke
			(width 0.2)
			(type solid)
		)
		(fill solid)
		(layer "F.Cu")
		(uuid "65596b4f-7b03-48e9-8be7-5824316ea7fd")
	)
	(gr_poly
		(pts
			(xy 125 85) (xy 135 85) (xy 135 95) (xy 125 95)
		)
		(stroke
			(width 0.2)
			(type solid)
		)
		(fill none)
		(layer "F.Cu")
		(uuid "cf265305-49c9-43d8-bb2a-ad34997b22d6")
	)
	(gr_rect
		(start 145 65)
		(end 160 80)
		(stroke
			(width 0.2)
			(type solid)
		)
		(fill solid)
		(layer "F.Cu")
		(uuid "d0669ae2-442f-427f-af0f-bc3008af779a")
	)
	(gr_rect
		(start 125 65)
		(end 135 75)
		(stroke
			(width 0.2)
			(type default)
		)
		(fill none)
		(layer "F.Cu")
		(uuid "fd1649a3-9a92-4dd3-96b4-88469cb257ba")
	)
	(gr_text "Fill"
		(at 150 60 0)
		(layer "Cmts.User")
		(uuid "435629a4-8d9c-4e90-ada3-ce9fb5233170")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3)
				(bold yes)
			)
			(justify bottom)
		)
	)
	(gr_text "No fill"
		(at 130 60 0)
		(layer "Cmts.User")
		(uuid "ef55c2a1-fcfc-4a00-81d3-37a24bf772bc")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3)
				(bold yes)
			)
			(justify bottom)
		)
	)
)
