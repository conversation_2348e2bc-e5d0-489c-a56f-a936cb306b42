<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wxFormBuilder_Project>
  <FileVersion major="1" minor="17"/>
  <object class="Project" expanded="true">
    <property name="class_decoration">; </property>
    <property name="code_generation">C++</property>
    <property name="disconnect_events">1</property>
    <property name="disconnect_mode">source_name</property>
    <property name="disconnect_php_events">0</property>
    <property name="disconnect_python_events">0</property>
    <property name="embedded_files_path">res</property>
    <property name="encoding">UTF-8</property>
    <property name="event_generation">connect</property>
    <property name="file">pns_log_viewer_frame_base</property>
    <property name="first_id">1000</property>
    <property name="help_provider">none</property>
    <property name="image_path_wrapper_function_name"></property>
    <property name="indent_with_spaces"></property>
    <property name="internationalize">0</property>
    <property name="name">PNS_LOG_VIEWER</property>
    <property name="namespace"></property>
    <property name="path">.</property>
    <property name="precompiled_header"></property>
    <property name="relative_path">1</property>
    <property name="skip_lua_events">1</property>
    <property name="skip_php_events">1</property>
    <property name="skip_python_events">1</property>
    <property name="ui_table">UI</property>
    <property name="use_array_enum">0</property>
    <property name="use_enum">0</property>
    <property name="use_microsoft_bom">0</property>
    <object class="Frame" expanded="true">
      <property name="aui_managed">0</property>
      <property name="aui_manager_style">wxAUI_MGR_DEFAULT</property>
      <property name="bg"></property>
      <property name="center">wxBOTH</property>
      <property name="context_help"></property>
      <property name="context_menu">1</property>
      <property name="drag_accept_files">0</property>
      <property name="enabled">1</property>
      <property name="event_handler">impl_virtual</property>
      <property name="extra_style"></property>
      <property name="fg"></property>
      <property name="font"></property>
      <property name="hidden">0</property>
      <property name="id">wxID_ANY</property>
      <property name="maximum_size"></property>
      <property name="minimum_size"></property>
      <property name="name">PNS_LOG_VIEWER_FRAME_BASE</property>
      <property name="pos"></property>
      <property name="size">1045,574</property>
      <property name="style">wxDEFAULT_FRAME_STYLE</property>
      <property name="subclass">; ; forward_declare</property>
      <property name="title">P&amp;S Log Viewer</property>
      <property name="tooltip"></property>
      <property name="two_step_creation">0</property>
      <property name="window_extra_style"></property>
      <property name="window_name"></property>
      <property name="window_style">wxTAB_TRAVERSAL</property>
      <property name="xrc_skip_sizer">1</property>
      <object class="wxMenuBar" expanded="true">
        <property name="bg"></property>
        <property name="context_help"></property>
        <property name="context_menu">1</property>
        <property name="drag_accept_files">0</property>
        <property name="enabled">1</property>
        <property name="fg"></property>
        <property name="font"></property>
        <property name="hidden">0</property>
        <property name="id">wxID_ANY</property>
        <property name="maximum_size"></property>
        <property name="minimum_size"></property>
        <property name="name">m_menubar1</property>
        <property name="permission">protected</property>
        <property name="pos"></property>
        <property name="size"></property>
        <property name="style"></property>
        <property name="subclass">; ; forward_declare</property>
        <property name="tooltip"></property>
        <property name="window_extra_style"></property>
        <property name="window_name"></property>
        <property name="window_style"></property>
        <object class="wxMenu" expanded="true">
          <property name="label">File</property>
          <property name="name">m_menuFile</property>
          <property name="permission">protected</property>
          <object class="wxMenuItem" expanded="true">
            <property name="bitmap"></property>
            <property name="checked">0</property>
            <property name="enabled">1</property>
            <property name="help"></property>
            <property name="id">wxID_ANY</property>
            <property name="kind">wxITEM_NORMAL</property>
            <property name="label">Open</property>
            <property name="name">m_menuOpenLogfile</property>
            <property name="permission">none</property>
            <property name="shortcut"></property>
            <property name="unchecked_bitmap"></property>
            <event name="OnMenuSelection">onOpen</event>
          </object>
          <object class="wxMenuItem" expanded="true">
            <property name="bitmap"></property>
            <property name="checked">0</property>
            <property name="enabled">1</property>
            <property name="help"></property>
            <property name="id">wxID_ANY</property>
            <property name="kind">wxITEM_NORMAL</property>
            <property name="label">Open</property>
            <property name="name">m_menuOpenTestcase</property>
            <property name="permission">none</property>
            <property name="shortcut"></property>
            <property name="unchecked_bitmap"></property>
            <event name="OnMenuSelection">onOpen</event>
          </object>
          <object class="wxMenuItem" expanded="true">
            <property name="bitmap"></property>
            <property name="checked">0</property>
            <property name="enabled">1</property>
            <property name="help"></property>
            <property name="id">wxID_ANY</property>
            <property name="kind">wxITEM_NORMAL</property>
            <property name="label">Save Testcase</property>
            <property name="name">m_menuSaveTestcase</property>
            <property name="permission">none</property>
            <property name="shortcut"></property>
            <property name="unchecked_bitmap"></property>
            <event name="OnMenuSelection">onSaveAs</event>
          </object>
          <object class="wxMenuItem" expanded="false">
            <property name="bitmap"></property>
            <property name="checked">0</property>
            <property name="enabled">1</property>
            <property name="help"></property>
            <property name="id">wxID_ANY</property>
            <property name="kind">wxITEM_NORMAL</property>
            <property name="label">Exit</property>
            <property name="name">m_menuExit</property>
            <property name="permission">none</property>
            <property name="shortcut"></property>
            <property name="unchecked_bitmap"></property>
            <event name="OnMenuSelection">onExit</event>
          </object>
        </object>
        <object class="wxMenu" expanded="true">
          <property name="label">View</property>
          <property name="name">m_menuView</property>
          <property name="permission">protected</property>
          <object class="wxMenuItem" expanded="true">
            <property name="bitmap"></property>
            <property name="checked">1</property>
            <property name="enabled">1</property>
            <property name="help"></property>
            <property name="id">wxID_ANY</property>
            <property name="kind">wxITEM_CHECK</property>
            <property name="label">Show PREVIEW_ITEMS</property>
            <property name="name">m_menuShowRPIs</property>
            <property name="permission">none</property>
            <property name="shortcut"></property>
            <property name="unchecked_bitmap"></property>
            <event name="OnMenuSelection">onShowRPIsChecked</event>
          </object>
          <object class="wxMenuItem" expanded="true">
            <property name="bitmap"></property>
            <property name="checked">0</property>
            <property name="enabled">1</property>
            <property name="help"></property>
            <property name="id">wxID_ANY</property>
            <property name="kind">wxITEM_CHECK</property>
            <property name="label">Override line width</property>
            <property name="name">m_menuOverrideLineWidth</property>
            <property name="permission">none</property>
            <property name="shortcut"></property>
            <property name="unchecked_bitmap"></property>
            <event name="OnMenuSelection">onShowThinLinesChecked</event>
          </object>
          <object class="wxMenuItem" expanded="true">
            <property name="bitmap"></property>
            <property name="checked">0</property>
            <property name="enabled">1</property>
            <property name="help"></property>
            <property name="id">wxID_ANY</property>
            <property name="kind">wxITEM_CHECK</property>
            <property name="label">Show Vertex Numbers</property>
            <property name="name">m_menuShowVertexNumbers</property>
            <property name="permission">none</property>
            <property name="shortcut"></property>
            <property name="unchecked_bitmap"></property>
            <event name="OnMenuSelection">onShowVerticesChecked</event>
          </object>
        </object>
      </object>
      <object class="wxBoxSizer" expanded="true">
        <property name="minimum_size"></property>
        <property name="name">m_mainSizer</property>
        <property name="orient">wxVERTICAL</property>
        <property name="permission">protected</property>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag"></property>
          <property name="proportion">0</property>
          <object class="wxFlexGridSizer" expanded="true">
            <property name="cols">11</property>
            <property name="flexible_direction">wxBOTH</property>
            <property name="growablecols"></property>
            <property name="growablerows"></property>
            <property name="hgap">0</property>
            <property name="minimum_size"></property>
            <property name="name">m_topBarSizer</property>
            <property name="non_flexible_grow_mode">wxFLEX_GROWMODE_SPECIFIED</property>
            <property name="permission">protected</property>
            <property name="rows">3</property>
            <property name="vgap">0</property>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL|wxALIGN_RIGHT</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Rewind: </property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_rewindText</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL</property>
              <property name="proportion">0</property>
              <object class="wxButton" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">&lt;</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size">50,-1</property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_rewindLeft</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">50,-1</property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">onBtnRewindLeft</event>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL</property>
              <property name="proportion">0</property>
              <object class="wxSlider" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="maxValue">100</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="minValue">0</property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size">200,-1</property>
                <property name="moveable">1</property>
                <property name="name">m_rewindSlider</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">200,-1</property>
                <property name="style">wxSL_HORIZONTAL</property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="value">50</property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnScroll">onRewindScroll</event>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL</property>
              <property name="proportion">0</property>
              <object class="wxButton" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="auth_needed">0</property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="bitmap"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="current"></property>
                <property name="default">0</property>
                <property name="default_pane">0</property>
                <property name="disabled"></property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="focus"></property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">&gt;</property>
                <property name="margins"></property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size">50,-1</property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_rewindRight</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="position"></property>
                <property name="pressed"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">50,-1</property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnButtonClick">onBtnRewindRight</event>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL</property>
              <property name="proportion">0</property>
              <object class="wxTextCtrl" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size">50,-1</property>
                <property name="maxlength"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_rewindPos</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size">50,-1</property>
                <property name="style">wxTE_PROCESS_ENTER</property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="value"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnText">onRewindCountText2</event>
                <event name="OnTextEnter">onRewindCountText</event>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">Filter:</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_staticText2</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL</property>
              <property name="proportion">0</property>
              <object class="wxTextCtrl" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="maxlength"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_filterString</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="value"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <event name="OnText">onFilterText</event>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALL|wxALIGN_CENTER_VERTICAL</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font">,90,92,-1,70,0</property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">MyLabel</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_algoStatus</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxEXPAND</property>
              <property name="proportion">1</property>
              <object class="spacer" expanded="false">
                <property name="height">0</property>
                <property name="permission">protected</property>
                <property name="width">0</property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxALL</property>
              <property name="proportion">0</property>
              <object class="wxStaticText" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font">,90,92,-1,70,0</property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="label">IDE:</property>
                <property name="markup">0</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_ideLabel</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos">-1,-1</property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
                <property name="wrap">-1</property>
              </object>
            </object>
            <object class="sizeritem" expanded="false">
              <property name="border">5</property>
              <property name="flag">wxALIGN_CENTER_VERTICAL|wxALL</property>
              <property name="proportion">0</property>
              <object class="wxChoice" expanded="false">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="choices">&quot;VS Code&quot; &quot;Visual Studio (full)&quot; &quot;CLion&quot; &quot;Emacs&quot;</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_ideChoice</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="selection">0</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="style"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip">Select IDE for go to line functionality</property>
                <property name="validator_data_type"></property>
                <property name="validator_style">wxFILTER_NONE</property>
                <property name="validator_type">wxDefaultValidator</property>
                <property name="validator_variable"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style"></property>
              </object>
            </object>
          </object>
        </object>
        <object class="sizeritem" expanded="true">
          <property name="border">5</property>
          <property name="flag">wxEXPAND</property>
          <property name="proportion">1</property>
          <object class="wxSplitterWindow" expanded="true">
            <property name="BottomDockable">1</property>
            <property name="LeftDockable">1</property>
            <property name="RightDockable">1</property>
            <property name="TopDockable">1</property>
            <property name="aui_layer"></property>
            <property name="aui_name"></property>
            <property name="aui_position"></property>
            <property name="aui_row"></property>
            <property name="best_size"></property>
            <property name="bg"></property>
            <property name="caption"></property>
            <property name="caption_visible">1</property>
            <property name="center_pane">0</property>
            <property name="close_button">1</property>
            <property name="context_help"></property>
            <property name="context_menu">1</property>
            <property name="default_pane">0</property>
            <property name="dock">Dock</property>
            <property name="dock_fixed">0</property>
            <property name="docking">Left</property>
            <property name="drag_accept_files">0</property>
            <property name="enabled">1</property>
            <property name="fg"></property>
            <property name="floatable">1</property>
            <property name="font"></property>
            <property name="gripper">0</property>
            <property name="hidden">0</property>
            <property name="id">wxID_ANY</property>
            <property name="max_size"></property>
            <property name="maximize_button">0</property>
            <property name="maximum_size"></property>
            <property name="min_pane_size">150</property>
            <property name="min_size"></property>
            <property name="minimize_button">0</property>
            <property name="minimum_size"></property>
            <property name="moveable">1</property>
            <property name="name">m_mainSplitter</property>
            <property name="pane_border">1</property>
            <property name="pane_position"></property>
            <property name="pane_size"></property>
            <property name="permission">protected</property>
            <property name="pin_button">1</property>
            <property name="pos"></property>
            <property name="resize">Resizable</property>
            <property name="sashgravity">0.0</property>
            <property name="sashpos">0</property>
            <property name="sashsize">-1</property>
            <property name="show">1</property>
            <property name="size"></property>
            <property name="splitmode">wxSPLIT_HORIZONTAL</property>
            <property name="style">wxSP_3D</property>
            <property name="subclass">; ; forward_declare</property>
            <property name="toolbar_pane">0</property>
            <property name="tooltip"></property>
            <property name="window_extra_style"></property>
            <property name="window_name"></property>
            <property name="window_style"></property>
            <object class="splitteritem" expanded="true">
              <object class="wxPanel" expanded="true">
                <property name="BottomDockable">1</property>
                <property name="LeftDockable">1</property>
                <property name="RightDockable">1</property>
                <property name="TopDockable">1</property>
                <property name="aui_layer"></property>
                <property name="aui_name"></property>
                <property name="aui_position"></property>
                <property name="aui_row"></property>
                <property name="best_size"></property>
                <property name="bg"></property>
                <property name="caption"></property>
                <property name="caption_visible">1</property>
                <property name="center_pane">0</property>
                <property name="close_button">1</property>
                <property name="context_help"></property>
                <property name="context_menu">1</property>
                <property name="default_pane">0</property>
                <property name="dock">Dock</property>
                <property name="dock_fixed">0</property>
                <property name="docking">Left</property>
                <property name="drag_accept_files">0</property>
                <property name="enabled">1</property>
                <property name="fg"></property>
                <property name="floatable">1</property>
                <property name="font"></property>
                <property name="gripper">0</property>
                <property name="hidden">0</property>
                <property name="id">wxID_ANY</property>
                <property name="max_size"></property>
                <property name="maximize_button">0</property>
                <property name="maximum_size"></property>
                <property name="min_size"></property>
                <property name="minimize_button">0</property>
                <property name="minimum_size"></property>
                <property name="moveable">1</property>
                <property name="name">m_panelProps</property>
                <property name="pane_border">1</property>
                <property name="pane_position"></property>
                <property name="pane_size"></property>
                <property name="permission">protected</property>
                <property name="pin_button">1</property>
                <property name="pos"></property>
                <property name="resize">Resizable</property>
                <property name="show">1</property>
                <property name="size"></property>
                <property name="subclass">; ; forward_declare</property>
                <property name="toolbar_pane">0</property>
                <property name="tooltip"></property>
                <property name="window_extra_style"></property>
                <property name="window_name"></property>
                <property name="window_style">wxTAB_TRAVERSAL</property>
                <object class="wxBoxSizer" expanded="true">
                  <property name="minimum_size"></property>
                  <property name="name">bSizer5</property>
                  <property name="orient">wxVERTICAL</property>
                  <property name="permission">none</property>
                  <object class="sizeritem" expanded="true">
                    <property name="border">5</property>
                    <property name="flag">wxEXPAND | wxALL</property>
                    <property name="proportion">1</property>
                    <object class="wxNotebook" expanded="true">
                      <property name="BottomDockable">1</property>
                      <property name="LeftDockable">1</property>
                      <property name="RightDockable">1</property>
                      <property name="TopDockable">1</property>
                      <property name="aui_layer"></property>
                      <property name="aui_name"></property>
                      <property name="aui_position"></property>
                      <property name="aui_row"></property>
                      <property name="best_size"></property>
                      <property name="bg"></property>
                      <property name="bitmapsize"></property>
                      <property name="caption"></property>
                      <property name="caption_visible">1</property>
                      <property name="center_pane">0</property>
                      <property name="close_button">1</property>
                      <property name="context_help"></property>
                      <property name="context_menu">1</property>
                      <property name="default_pane">0</property>
                      <property name="dock">Dock</property>
                      <property name="dock_fixed">0</property>
                      <property name="docking">Left</property>
                      <property name="drag_accept_files">0</property>
                      <property name="enabled">1</property>
                      <property name="fg"></property>
                      <property name="floatable">1</property>
                      <property name="font"></property>
                      <property name="gripper">0</property>
                      <property name="hidden">0</property>
                      <property name="id">wxID_ANY</property>
                      <property name="max_size"></property>
                      <property name="maximize_button">0</property>
                      <property name="maximum_size"></property>
                      <property name="min_size"></property>
                      <property name="minimize_button">0</property>
                      <property name="minimum_size"></property>
                      <property name="moveable">1</property>
                      <property name="name">m_propsNotebook</property>
                      <property name="pane_border">1</property>
                      <property name="pane_position"></property>
                      <property name="pane_size"></property>
                      <property name="permission">protected</property>
                      <property name="pin_button">1</property>
                      <property name="pos"></property>
                      <property name="resize">Resizable</property>
                      <property name="show">1</property>
                      <property name="size"></property>
                      <property name="style"></property>
                      <property name="subclass">; ; forward_declare</property>
                      <property name="toolbar_pane">0</property>
                      <property name="tooltip"></property>
                      <property name="window_extra_style"></property>
                      <property name="window_name"></property>
                      <property name="window_style"></property>
                      <object class="notebookpage" expanded="false">
                        <property name="bitmap"></property>
                        <property name="label">Geometry</property>
                        <property name="select">0</property>
                        <object class="wxPanel" expanded="false">
                          <property name="BottomDockable">1</property>
                          <property name="LeftDockable">1</property>
                          <property name="RightDockable">1</property>
                          <property name="TopDockable">1</property>
                          <property name="aui_layer"></property>
                          <property name="aui_name"></property>
                          <property name="aui_position"></property>
                          <property name="aui_row"></property>
                          <property name="best_size"></property>
                          <property name="bg"></property>
                          <property name="caption"></property>
                          <property name="caption_visible">1</property>
                          <property name="center_pane">0</property>
                          <property name="close_button">1</property>
                          <property name="context_help"></property>
                          <property name="context_menu">1</property>
                          <property name="default_pane">0</property>
                          <property name="dock">Dock</property>
                          <property name="dock_fixed">0</property>
                          <property name="docking">Left</property>
                          <property name="drag_accept_files">0</property>
                          <property name="enabled">1</property>
                          <property name="fg"></property>
                          <property name="floatable">1</property>
                          <property name="font"></property>
                          <property name="gripper">0</property>
                          <property name="hidden">0</property>
                          <property name="id">wxID_ANY</property>
                          <property name="max_size"></property>
                          <property name="maximize_button">0</property>
                          <property name="maximum_size"></property>
                          <property name="min_size"></property>
                          <property name="minimize_button">0</property>
                          <property name="minimum_size"></property>
                          <property name="moveable">1</property>
                          <property name="name">m_panelListView</property>
                          <property name="pane_border">1</property>
                          <property name="pane_position"></property>
                          <property name="pane_size"></property>
                          <property name="permission">protected</property>
                          <property name="pin_button">1</property>
                          <property name="pos"></property>
                          <property name="resize">Resizable</property>
                          <property name="show">1</property>
                          <property name="size"></property>
                          <property name="subclass">; ; forward_declare</property>
                          <property name="toolbar_pane">0</property>
                          <property name="tooltip"></property>
                          <property name="window_extra_style"></property>
                          <property name="window_name"></property>
                          <property name="window_style">wxTAB_TRAVERSAL</property>
                          <object class="wxBoxSizer" expanded="false">
                            <property name="minimum_size"></property>
                            <property name="name">bSizer6</property>
                            <property name="orient">wxVERTICAL</property>
                            <property name="permission">none</property>
                            <object class="sizeritem" expanded="false">
                              <property name="border">5</property>
                              <property name="flag">wxALL|wxEXPAND</property>
                              <property name="proportion">1</property>
                              <object class="wxTreeListCtrl" expanded="false">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="default_pane">0</property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="drag_accept_files">0</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_itemList</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size"></property>
                                <property name="style">wxTL_CHECKBOX|wxTL_DEFAULT_STYLE|wxTL_MULTIPLE</property>
                                <property name="subclass">; ; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                              </object>
                            </object>
                          </object>
                        </object>
                      </object>
                      <object class="notebookpage" expanded="true">
                        <property name="bitmap"></property>
                        <property name="label">Console</property>
                        <property name="select">1</property>
                        <object class="wxPanel" expanded="true">
                          <property name="BottomDockable">1</property>
                          <property name="LeftDockable">1</property>
                          <property name="RightDockable">1</property>
                          <property name="TopDockable">1</property>
                          <property name="aui_layer"></property>
                          <property name="aui_name"></property>
                          <property name="aui_position"></property>
                          <property name="aui_row"></property>
                          <property name="best_size"></property>
                          <property name="bg"></property>
                          <property name="caption"></property>
                          <property name="caption_visible">1</property>
                          <property name="center_pane">0</property>
                          <property name="close_button">1</property>
                          <property name="context_help"></property>
                          <property name="context_menu">1</property>
                          <property name="default_pane">0</property>
                          <property name="dock">Dock</property>
                          <property name="dock_fixed">0</property>
                          <property name="docking">Left</property>
                          <property name="drag_accept_files">0</property>
                          <property name="enabled">1</property>
                          <property name="fg"></property>
                          <property name="floatable">1</property>
                          <property name="font"></property>
                          <property name="gripper">0</property>
                          <property name="hidden">0</property>
                          <property name="id">wxID_ANY</property>
                          <property name="max_size"></property>
                          <property name="maximize_button">0</property>
                          <property name="maximum_size"></property>
                          <property name="min_size"></property>
                          <property name="minimize_button">0</property>
                          <property name="minimum_size"></property>
                          <property name="moveable">1</property>
                          <property name="name">m_panelConsole</property>
                          <property name="pane_border">1</property>
                          <property name="pane_position"></property>
                          <property name="pane_size"></property>
                          <property name="permission">protected</property>
                          <property name="pin_button">1</property>
                          <property name="pos"></property>
                          <property name="resize">Resizable</property>
                          <property name="show">1</property>
                          <property name="size"></property>
                          <property name="subclass">; ; forward_declare</property>
                          <property name="toolbar_pane">0</property>
                          <property name="tooltip"></property>
                          <property name="window_extra_style"></property>
                          <property name="window_name"></property>
                          <property name="window_style">wxTAB_TRAVERSAL</property>
                          <object class="wxBoxSizer" expanded="true">
                            <property name="minimum_size"></property>
                            <property name="name">bSizer7</property>
                            <property name="orient">wxVERTICAL</property>
                            <property name="permission">none</property>
                            <object class="sizeritem" expanded="false">
                              <property name="border">5</property>
                              <property name="flag">wxALL|wxEXPAND</property>
                              <property name="proportion">1</property>
                              <object class="wxTextCtrl" expanded="false">
                                <property name="BottomDockable">1</property>
                                <property name="LeftDockable">1</property>
                                <property name="RightDockable">1</property>
                                <property name="TopDockable">1</property>
                                <property name="aui_layer"></property>
                                <property name="aui_name"></property>
                                <property name="aui_position"></property>
                                <property name="aui_row"></property>
                                <property name="best_size"></property>
                                <property name="bg"></property>
                                <property name="caption"></property>
                                <property name="caption_visible">1</property>
                                <property name="center_pane">0</property>
                                <property name="close_button">1</property>
                                <property name="context_help"></property>
                                <property name="context_menu">1</property>
                                <property name="default_pane">0</property>
                                <property name="dock">Dock</property>
                                <property name="dock_fixed">0</property>
                                <property name="docking">Left</property>
                                <property name="drag_accept_files">0</property>
                                <property name="enabled">1</property>
                                <property name="fg"></property>
                                <property name="floatable">1</property>
                                <property name="font"></property>
                                <property name="gripper">0</property>
                                <property name="hidden">0</property>
                                <property name="id">wxID_ANY</property>
                                <property name="max_size"></property>
                                <property name="maximize_button">0</property>
                                <property name="maximum_size"></property>
                                <property name="maxlength"></property>
                                <property name="min_size"></property>
                                <property name="minimize_button">0</property>
                                <property name="minimum_size"></property>
                                <property name="moveable">1</property>
                                <property name="name">m_consoleText</property>
                                <property name="pane_border">1</property>
                                <property name="pane_position"></property>
                                <property name="pane_size"></property>
                                <property name="permission">protected</property>
                                <property name="pin_button">1</property>
                                <property name="pos"></property>
                                <property name="resize">Resizable</property>
                                <property name="show">1</property>
                                <property name="size"></property>
                                <property name="style">wxTE_MULTILINE|wxTE_READONLY|wxTE_RICH</property>
                                <property name="subclass">; ; forward_declare</property>
                                <property name="toolbar_pane">0</property>
                                <property name="tooltip"></property>
                                <property name="validator_data_type"></property>
                                <property name="validator_style">wxFILTER_NONE</property>
                                <property name="validator_type">wxDefaultValidator</property>
                                <property name="validator_variable"></property>
                                <property name="value"></property>
                                <property name="window_extra_style"></property>
                                <property name="window_name"></property>
                                <property name="window_style"></property>
                              </object>
                            </object>
                          </object>
                        </object>
                      </object>
                    </object>
                  </object>
                </object>
              </object>
            </object>
          </object>
        </object>
      </object>
      <object class="wxStatusBar" expanded="false">
        <property name="bg"></property>
        <property name="context_help"></property>
        <property name="context_menu">1</property>
        <property name="drag_accept_files">0</property>
        <property name="enabled">1</property>
        <property name="fg"></property>
        <property name="fields">1</property>
        <property name="font"></property>
        <property name="hidden">0</property>
        <property name="id">wxID_ANY</property>
        <property name="maximum_size"></property>
        <property name="minimum_size"></property>
        <property name="name">m_statusBar</property>
        <property name="permission">protected</property>
        <property name="pos"></property>
        <property name="size"></property>
        <property name="style">wxSTB_SIZEGRIP</property>
        <property name="subclass">; ; forward_declare</property>
        <property name="tooltip"></property>
        <property name="window_extra_style"></property>
        <property name="window_name"></property>
        <property name="window_style"></property>
      </object>
    </object>
  </object>
</wxFormBuilder_Project>
