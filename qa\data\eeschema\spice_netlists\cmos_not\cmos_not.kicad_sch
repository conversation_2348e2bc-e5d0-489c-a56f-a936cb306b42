(kicad_sch (version 20221004) (generator eeschema)

  (uuid 569fb493-5024-4abd-9aba-73198e12c7f2)

  (paper "A4")

  (lib_symbols
    (symbol "Device:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at -1.778 0 90)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "R res resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Resistor" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "R_*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VDC" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VDC" (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "dc(1)" (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, DC" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VDC_0_0"
        (polyline
          (pts
            (xy -1.27 0.254)
            (xy 1.27 0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.254)
            (xy -1.27 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.254 -0.254)
            (xy -0.254 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.254)
            (xy 0.762 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VDC_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VDC_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VSIN" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VSIN" (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "sin(0 1 1k)" (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, sinusoidal" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VSIN_0_0"
        (arc (start 0 0) (mid -0.635 0.6323) (end -1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (arc (start 0 0) (mid 0.635 -0.6323) (end 1.27 0)
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VSIN_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VSIN_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:+5V" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "+5V" (at 0 3.556 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"+5V\"" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "+5V_0_1"
        (polyline
          (pts
            (xy -0.762 1.27)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 0)
            (xy 0 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 2.54)
            (xy 0.762 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "+5V_1_1"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "+5V" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "pspice:MNMOS" (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "M" (at 7.62 1.27 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "MNMOS" (at 7.62 -1.27 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at -0.635 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at -0.635 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "mosfet nmos simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "N-channel MOSFET symbol for simulation only" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "MNMOS_0_1"
        (polyline
          (pts
            (xy -1.27 -2.54)
            (xy -1.27 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.635 -2.54)
            (xy 2.54 -2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 2.54 2.54)
            (xy -0.635 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 5.08 0)
            (xy -0.635 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.635 0)
            (xy 1.27 0.635)
            (xy 1.27 -0.635)
            (xy -0.635 0)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
      )
      (symbol "MNMOS_1_1"
        (polyline
          (pts
            (xy -0.635 -2.54)
            (xy -0.635 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (pin passive line (at 2.54 5.08 270) (length 2.54)
          (name "D" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -5.08 0 0) (length 3.81)
          (name "G" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 2.54 -5.08 90) (length 2.54)
          (name "S" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at 5.08 -5.08 90) (length 5.08)
          (name "B" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "pspice:MPMOS" (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "M" (at 7.62 1.27 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "MPMOS" (at 7.62 -1.27 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "mosfet pmos simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "P-channel MOSFET symbol for simulation only" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "MPMOS_0_1"
        (polyline
          (pts
            (xy -1.27 2.54)
            (xy -1.27 -2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.635 -2.54)
            (xy 2.54 -2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 2.54 2.54)
            (xy -0.635 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 5.08 0)
            (xy -0.635 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 5.08 0)
            (xy 3.175 0.635)
            (xy 3.175 -0.635)
            (xy 5.08 0)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
      )
      (symbol "MPMOS_1_1"
        (polyline
          (pts
            (xy -0.635 -2.54)
            (xy -0.635 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (pin passive line (at 2.54 -5.08 90) (length 2.54)
          (name "D" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -5.08 0 0) (length 3.81)
          (name "G" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 2.54 5.08 270) (length 2.54)
          (name "S" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at 5.08 5.08 270) (length 5.08)
          (name "B" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 139.7 101.6) (diameter 0) (color 0 0 0 0)
    (uuid 5742ce0f-bced-44cd-b9c4-b2cb55de89b9)
  )
  (junction (at 152.4 88.9) (diameter 0) (color 0 0 0 0)
    (uuid 98adbecd-b6c5-4cef-b4a6-81637bb1b9a3)
  )
  (junction (at 152.4 101.6) (diameter 0) (color 0 0 0 0)
    (uuid 9dc3e1b3-f11a-4ddf-bc8d-7ce8b126b456)
  )
  (junction (at 152.4 114.3) (diameter 0) (color 0 0 0 0)
    (uuid a2d689e7-0644-40c0-a4c6-d032b880f653)
  )

  (wire (pts (xy 152.4 102.87) (xy 152.4 101.6))
    (stroke (width 0) (type default))
    (uuid 060430e0-bc0a-4827-960e-5f85b72a6457)
  )
  (wire (pts (xy 152.4 101.6) (xy 165.1 101.6))
    (stroke (width 0) (type default))
    (uuid 08156fd4-65bb-41f5-9013-62c40afcb827)
  )
  (wire (pts (xy 152.4 114.3) (xy 154.94 114.3))
    (stroke (width 0) (type default))
    (uuid 0a05e2d5-7f50-429a-8ca9-924de6eaebe8)
  )
  (wire (pts (xy 114.3 101.6) (xy 114.3 102.87))
    (stroke (width 0) (type default))
    (uuid 1ea27a77-c716-4a5a-89e2-bbaffb486919)
  )
  (wire (pts (xy 165.1 104.14) (xy 165.1 101.6))
    (stroke (width 0) (type default))
    (uuid 2e50a728-2cdd-4ceb-a00d-d94bb38156b4)
  )
  (wire (pts (xy 88.9 101.6) (xy 88.9 102.87))
    (stroke (width 0) (type default))
    (uuid 3b797fff-2ac7-41f0-a395-8aaf3a524007)
  )
  (wire (pts (xy 114.3 113.03) (xy 114.3 114.3))
    (stroke (width 0) (type default))
    (uuid 419c72ec-f2b4-4f9b-8cc9-8794b44aa682)
  )
  (wire (pts (xy 114.3 101.6) (xy 139.7 101.6))
    (stroke (width 0) (type default))
    (uuid 4a43d5d8-ff71-46f5-b0ba-c5b76bc20a4f)
  )
  (wire (pts (xy 144.78 95.25) (xy 139.7 95.25))
    (stroke (width 0) (type default))
    (uuid 57835233-702a-4f7d-8c1b-e403adcb7c32)
  )
  (wire (pts (xy 139.7 107.95) (xy 144.78 107.95))
    (stroke (width 0) (type default))
    (uuid 76c6ebda-5ce5-428f-9d9f-bbee5ec79055)
  )
  (wire (pts (xy 165.1 111.76) (xy 165.1 114.3))
    (stroke (width 0) (type default))
    (uuid 79657d39-ee46-4093-8f3f-01f7c8038780)
  )
  (wire (pts (xy 154.94 90.17) (xy 154.94 88.9))
    (stroke (width 0) (type default))
    (uuid 81ddf59a-d7d3-42b1-a686-35987829afea)
  )
  (wire (pts (xy 154.94 113.03) (xy 154.94 114.3))
    (stroke (width 0) (type default))
    (uuid 99ae1e4e-170d-459b-a05b-55ff0dbb23f7)
  )
  (wire (pts (xy 152.4 101.6) (xy 152.4 100.33))
    (stroke (width 0) (type default))
    (uuid a70a5b59-e0c6-432e-a0d2-7ff851c1c39d)
  )
  (wire (pts (xy 88.9 113.03) (xy 88.9 114.3))
    (stroke (width 0) (type default))
    (uuid adc81cdb-b973-4454-ac81-be86add74dc7)
  )
  (wire (pts (xy 139.7 95.25) (xy 139.7 101.6))
    (stroke (width 0) (type default))
    (uuid bbca9498-5be5-4fa7-a4c4-e858c2c16225)
  )
  (wire (pts (xy 154.94 88.9) (xy 152.4 88.9))
    (stroke (width 0) (type default))
    (uuid e307bb18-7b3b-4d6b-9d5b-8b11dd7be21a)
  )
  (wire (pts (xy 139.7 101.6) (xy 139.7 107.95))
    (stroke (width 0) (type default))
    (uuid e47b52b0-179d-4e4c-9cf0-3810fb1dd8e2)
  )
  (wire (pts (xy 152.4 88.9) (xy 152.4 90.17))
    (stroke (width 0) (type default))
    (uuid e63bac26-dd3f-463f-8714-3d52e9d4a7d7)
  )
  (wire (pts (xy 152.4 114.3) (xy 152.4 113.03))
    (stroke (width 0) (type default))
    (uuid f6ee31af-9695-441d-a88d-c489d0532ac7)
  )

  (text ".tran 1u 5m" (at 118.11 118.11 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 5059b404-ed17-4f01-b25c-cdf1b3f4f6f2)
  )
  (text ";dc VSIN_IN1 0 5 0.1" (at 118.11 120.65 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid b2f00048-49ec-48d8-b4c5-af0c3eae3108)
  )

  (label "out" (at 165.1 101.6 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 42f02777-a1c5-4941-93fd-5c96f6476ad4)
  )
  (label "in" (at 114.3 101.6 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid e2726d64-6d91-4d46-aff6-a0df255f4f26)
  )

  (symbol (lib_id "power:GND") (at 165.1 114.3 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 309f5b6c-5522-46e6-beb8-0f66078434e8)
    (property "Reference" "#PWR06" (at 165.1 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 165.1 119.38 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 165.1 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 165.1 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 1ce343a2-30b8-45a5-b000-b0ce866c2c3e))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "#PWR06") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VSIN") (at 114.3 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 3a1b7444-3efb-420b-9894-dd518dacb8d6)
    (property "Reference" "VIN1" (at 118.11 107.95 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "${Sim.Device} ${Sim.Type}" (at 118.11 110.49 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 114.3 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 114.3 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 114.3 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Type" "SIN" (at 114.3 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "dc=2.5 ampl=2.5 f=1k" (at 129.54 113.03 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid caf5bcc4-a42f-4b60-b1a8-22e72909f97a))
    (pin "2" (uuid c527511d-c2c7-4e42-b131-b52b3277b5f0))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "VIN1") (unit 1) (value "${Sim.Device} ${Sim.Type}") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 114.3 114.3 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 44524c5a-8811-406b-9c1e-5bfbdede6dc2)
    (property "Reference" "#PWR03" (at 114.3 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 114.3 119.38 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 114.3 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 114.3 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7d28774b-0419-4663-aa50-0aae271310cd))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "#PWR03") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Device:R") (at 165.1 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 56f58e0b-1e61-490a-ab82-fbcd7ac0c68a)
    (property "Reference" "R1" (at 167.64 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "10M" (at 167.64 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 163.322 107.95 90)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 165.1 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 165.1 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 2f230156-e43a-4ffc-bfcb-5ad7a95b7eed))
    (pin "2" (uuid 94ba9eaf-e7c2-4392-86f1-973077f847d3))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "R1") (unit 1) (value "10M") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 88.9 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 9d0dbe0b-bffe-4850-bca8-ea409a0c68f9)
    (property "Reference" "VPWR1" (at 92.71 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "VPWR1" (at 92.71 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 88.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 88.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 88.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "dc=5" (at 88.9 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 56899bdb-9863-4906-be1d-e9bb08728b22))
    (pin "2" (uuid 08f7d465-4068-43ce-8d61-3bfc307d1d38))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "VPWR1") (unit 1) (value "VPWR1") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:+5V") (at 88.9 101.6 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid ac3a72ae-3363-4210-a491-3b73cc72153f)
    (property "Reference" "#PWR01" (at 88.9 105.41 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+5V" (at 88.9 97.79 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 88.9 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 88.9 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 9bd1f5d8-44cd-411c-bfca-33d6ac5234d1))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "#PWR01") (unit 1) (value "+5V") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 88.9 114.3 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid b2a35254-63de-4740-881f-adff693564f5)
    (property "Reference" "#PWR02" (at 88.9 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 88.9 119.38 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 88.9 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 88.9 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 75138b54-b286-4dd1-98d8-fa4dd93ce31c))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "#PWR02") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "pspice:MPMOS") (at 149.86 95.25 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid c027df38-6423-4d76-b056-ff104cd95f06)
    (property "Reference" "M1" (at 156.21 94.615 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "PMOS" (at 156.21 97.155 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 149.86 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 149.86 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Name" "pmos" (at 149.86 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Library" "cmos_not.lib.spice" (at 149.86 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "PMOS" (at 149.86 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Type" "MOS1" (at 149.86 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Pins" "1=D 2=G 3=S 4=B" (at 149.86 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "l=1u w=1.4u" (at 149.86 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 7bcb579b-80ed-4fb7-8b71-8a99bb3f6676))
    (pin "2" (uuid 07a1543f-3c98-4737-a397-46ca723ed60b))
    (pin "3" (uuid 35d10dbf-1fe3-475d-90db-a0921d822464))
    (pin "4" (uuid 9919be22-f896-4ed1-b26a-976e7ad5e210))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "M1") (unit 1) (value "PMOS") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 152.4 114.3 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid cd10bce4-35cc-4c52-81ad-dd3a953af38e)
    (property "Reference" "#PWR05" (at 152.4 120.65 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 152.4 119.38 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 152.4 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 152.4 114.3 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid c5d35e5d-598d-45fa-8279-c67ceb842257))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "#PWR05") (unit 1) (value "GND") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "power:+5V") (at 152.4 88.9 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid cdba6f47-7af5-49cd-bffb-3a4d3be46299)
    (property "Reference" "#PWR04" (at 152.4 92.71 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "+5V" (at 152.4 85.09 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 152.4 88.9 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 152.4 88.9 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 6f5b4a55-d21f-46e6-bc7d-d18c4df6dbca))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "#PWR04") (unit 1) (value "+5V") (footprint "")
        )
      )
    )
  )

  (symbol (lib_id "pspice:MNMOS") (at 149.86 107.95 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid ee873f1d-104f-442b-8e2a-a39c5a935c08)
    (property "Reference" "M2" (at 156.21 107.315 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "NMOS" (at 156.21 109.855 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 149.225 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 149.225 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Name" "nmos" (at 149.86 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Library" "cmos_not.lib.spice" (at 149.86 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "NMOS" (at 149.86 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Type" "MOS1" (at 149.86 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Pins" "1=D 2=G 3=S 4=B" (at 149.86 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "l=1u w=1u" (at 149.86 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e2b5a27e-4687-4bdb-851b-931f64e28381))
    (pin "2" (uuid 2c8fd632-e3e1-4ab4-ae31-69c8d87f7cf1))
    (pin "3" (uuid 18bfd48e-41c8-4eda-9849-955d36ff4543))
    (pin "4" (uuid b8798492-7b64-4fef-8620-8f6c1d58dc74))
    (instances
      (project "cmos_not"
        (path "/569fb493-5024-4abd-9aba-73198e12c7f2"
          (reference "M2") (unit 1) (value "NMOS") (footprint "")
        )
      )
    )
  )

  (sheet_instances
    (path "/569fb493-5024-4abd-9aba-73198e12c7f2" (page "1"))
  )
)
