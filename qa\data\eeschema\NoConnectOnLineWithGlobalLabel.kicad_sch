(kicad_sch (version 20211123) (generator eeschema)

  (uuid a42261a8-90bd-4a35-939b-632c3d8e595c)

  (paper "A4")

  (lib_symbols
    (symbol "Connector:TestPoint" (pin_numbers hide) (pin_names (offset 0.762) hide) (in_bom yes) (on_board yes)
      (property "Reference" "TP" (id 0) (at 0 6.858 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "TestPoint" (id 1) (at 0 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 5.08 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 5.08 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "test point tp" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "test point" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Pin* Test*" (id 6) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "TestPoint_0_1"
        (circle (center 0 3.302) (radius 0.762)
          (stroke (width 0) (type default) (color 0 0 0 0))
          (fill (type none))
        )
      )
      (symbol "TestPoint_1_1"
        (pin passive line (at 0 0 90) (length 2.54)
          (name "1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 29.21 30.48) (diameter 0.9144) (color 0 0 0 0)
    (uuid b80f780b-ddd7-486d-b8bc-c7ae3b0d378a)
  )

  (no_connect (at 29.21 36.83) (uuid 2dcc0ade-e5ab-4a90-8195-ec5478b70816))
  (no_connect (at 60.96 30.48) (uuid d115e9ba-d4f0-4386-a27b-ff1d739d696d))

  (wire (pts (xy 29.21 25.4) (xy 29.21 30.48))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid 2660ab9a-04ca-42fa-8de1-6167aa524155)
  )
  (wire (pts (xy 29.21 30.48) (xy 29.21 36.83))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid 2660ab9a-04ca-42fa-8de1-6167aa524156)
  )
  (wire (pts (xy 60.96 30.48) (xy 63.5 30.48))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid 2ece1ceb-2c6e-4363-a86f-fd0e243797e6)
  )
  (wire (pts (xy 29.21 30.48) (xy 31.75 30.48))
    (stroke (width 0) (type solid) (color 0 0 0 0))
    (uuid *************-4eaf-b087-a3b8575f4ed2)
  )

  (text "1 error (no pin)" (at 74.93 35.56 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
    (uuid 1e94bc46-c2a0-4923-a475-d60aecf0a8c3)
  )
  (text "No error with pin" (at 26.67 39.37 90)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid eb4d03e2-e08c-4736-982e-e674fd4662b3)
  )

  (global_label "test_OK" (shape input) (at 31.75 30.48 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid 4fd16d2c-2ba1-4d44-b54e-675a1f4522ca)
    (property "Intersheet References" "${INTERSHEET_REFS}" (id 0) (at 41.2993 30.4006 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )
  (global_label "test_err" (shape input) (at 63.5 30.48 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left))
    (uuid 64c16521-77d5-4277-bc21-128bcc9264fb)
    (property "Intersheet References" "${INTERSHEET_REFS}" (id 0) (at 73.1098 30.4006 0)
      (effects (font (size 1.27 1.27)) (justify left) hide)
    )
  )

  (symbol (lib_id "Connector:TestPoint") (at 29.21 25.4 0) (unit 1)
    (in_bom yes) (on_board yes) (fields_autoplaced)
    (uuid cb761d07-e4ee-408d-b193-63cb98747f85)
    (property "Reference" "TP1" (id 0) (at 31.75 22.2884 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "TestPoint" (id 1) (at 31.75 24.8284 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 34.29 25.4 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 34.29 25.4 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f15f35a2-a7d2-4351-a319-800789db39e5))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/cb761d07-e4ee-408d-b193-63cb98747f85"
      (reference "TP1") (unit 1) (value "TestPoint") (footprint "")
    )
  )
)
