/* XPM */
static char const * cursor_text64_xpm[] = {
"64 64 60 1",
" 	c None",
".	c #FFFFFF",
"+	c #000000",
"@	c #868686",
"#	c #FEFEFE",
"$	c #1C1C1C",
"%	c #CFCFCF",
"&	c #9A9A9A",
"*	c #3D3D3D",
"=	c #313131",
"-	c #DCDCDC",
";	c #9D9D9D",
">	c #ABABAB",
",	c #262626",
"'	c #D7D7D7",
")	c #5C5C5C",
"!	c #767676",
"~	c #F0F0F0",
"{	c #E7E7E7",
"]	c #939393",
"^	c #999999",
"/	c #BBBBBB",
"(	c #BEBEBE",
"_	c #C5C5C5",
":	c #787878",
"<	c #E2E2E2",
"[	c #EBEBEB",
"}	c #F1F1F1",
"|	c #EAEAEA",
"1	c #C8C8C8",
"2	c #9C9C9C",
"3	c #FCFCFC",
"4	c #8E8E8E",
"5	c #BFBFBF",
"6	c #C3C3C3",
"7	c #F9F9F9",
"8	c #2A2A2A",
"9	c #DEDEDE",
"0	c #FAFAFA",
"a	c #D5D5D5",
"b	c #A1A1A1",
"c	c #FDFDFD",
"d	c #494949",
"e	c #E0E0E0",
"f	c #B2B2B2",
"g	c #686868",
"h	c #303030",
"i	c #373737",
"j	c #ECECEC",
"k	c #818181",
"l	c #8D8D8D",
"m	c #C0C0C0",
"n	c #D8D8D8",
"o	c #828282",
"p	c #F7F7F7",
"q	c #F5F5F5",
"r	c #161616",
"s	c #464646",
"t	c #F3F3F3",
"u	c #484848",
"                                                                ",
"              ..                                                ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"  ............++............                                    ",
" .++++++++++++  ++++++++++++.                                   ",
" .++++++++++++  ++++++++++++.                                   ",
"  ............++............                                    ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.                                               ",
"             .++.         .........                             ",
"             .++.        @+++++++++@                            ",
"             .++.       #$+++++++++$#                           ",
"             .++.       %+++++++++++%                           ",
"              ..        &+++++++++++&                           ",
"                        *+++++=+++++*                           ",
"                       -++++++;++++++-                          ",
"                       >+++++,',+++++>                          ",
"                       )+++++!~!+++++)                          ",
"                      {++++++].^++++++{                         ",
"                      /++++++(._++++++/                         ",
"                      :++++++< [++++++:                         ",
"                     }++++++)|  )++++++}                        ",
"                     1++++++23  ;++++++1                        ",
"                     4++++++5   6++++++4                        ",
"                    78++++++9707<++++++87                       ",
"                    a+++++++++++++++++++a                       ",
"                    b+++++++++++++++++++b                       ",
"                   cd+++++++++++++++++++dc                      ",
"                   e+++++++++++++++++++++e                      ",
"                   f+++++++++++++++++++++f                      ",
"                  .g++++++h.......i++++++g.                     ",
"                  j+++++++k.     .l+++++++j                     ",
"                 .m+++++++n       9+++++++m.                    ",
"                 .o+++++++}       p+++++++o.                    ",
"                 qr++++++st       .u++++++rq                    ",
"                 .........         .........                    ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                "};
