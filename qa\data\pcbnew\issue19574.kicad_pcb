(kicad_pcb
	(version 20241228)
	(generator "pcbnew")
	(generator_version "9.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(2 "B.Cu" signal)
		(9 "F.Adhes" user "F.Adhesive")
		(11 "B.Adhes" user "B.Adhesive")
		(13 "F.Paste" user)
		(15 "B.Paste" user)
		(5 "F.SilkS" user "F.Silkscreen")
		(7 "B.SilkS" user "B.Silkscreen")
		(1 "F.Mask" user)
		(3 "B.Mask" user)
		(17 "Dwgs.User" user "User.Drawings")
		(19 "Cmts.User" user "User.Comments")
		(21 "Eco1.User" user "User.Eco1")
		(23 "Eco2.User" user "User.Eco2")
		(25 "Edge.Cuts" user)
		(27 "Margin" user)
		(31 "F.CrtYd" user "F.Courtyard")
		(29 "B.CrtYd" user "B.Courtyard")
		(35 "F.Fab" user)
		(33 "B.Fab" user)
		(39 "User.1" auxiliary)
		(41 "User.2" auxiliary)
		(43 "User.3" auxiliary)
		(45 "User.4" auxiliary)
		(47 "User.5" auxiliary)
		(49 "User.6" auxiliary)
		(51 "User.7" auxiliary)
		(53 "User.8" auxiliary)
		(55 "User.9" auxiliary)
		(57 "User.10" user)
		(59 "User.11" user)
		(61 "User.12" user)
		(63 "User.13" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(tenting front back)
		(pcbplotparams
			(layerselection 0x55555555_5755f5ff)
			(plot_on_all_layers_selection 0x00000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(pdf_metadata yes)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(plotpadnumbers no)
			(hidednponfab no)
			(sketchdnponfab yes)
			(crossoutdnponfab yes)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(net 1 "a")
	(gr_rect
		(start 108.5 85.75)
		(end 151.75 106)
		(stroke
			(width 0.05)
			(type default)
		)
		(fill no)
		(layer "Edge.Cuts")
		(uuid "6cc7062d-8557-4531-9a24-90817ee944f8")
	)
	(segment
		(start 129.75 89.75)
		(end 131.5 89.75)
		(width 0.2)
		(layer "F.Cu")
		(net 1)
		(uuid "0ff9e6a3-108e-4607-b6ff-56672c126bd8")
	)
	(segment
		(start 129.75 89.75)
		(end 134.25 89.75)
		(width 0.2)
		(layer "F.Cu")
		(net 1)
		(uuid "1e0c28e8-73f2-471a-a467-dc9f8684ff26")
	)
	(segment
		(start 131.5 89.75)
		(end 134.25 89.75)
		(width 0.2)
		(layer "F.Cu")
		(net 1)
		(uuid "937ad9f8-dc51-406f-9002-f1dd3fef9acb")
	)
	(segment
		(start 129.75 89)
		(end 131.5 89)
		(width 0.2)
		(layer "F.Cu")
		(net 1)
		(uuid "b5cb5fcb-d68c-4b62-8896-488a7b02b01c")
	)
	(segment
		(start 129.75 88.5)
		(end 134.25 88.5)
		(width 0.2)
		(layer "F.Cu")
		(net 1)
		(uuid "f2c320e0-1554-4312-ac4e-30075908acba")
	)
	(segment
		(start 131.5 88.75)
		(end 134.25 88.75)
		(width 0.2)
		(layer "F.Cu")
		(net 1)
		(uuid "f7140b20-6822-4a9f-ab21-98f073f5f2df")
	)
	(embedded_fonts no)
)
