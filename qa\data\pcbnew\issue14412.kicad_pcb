(kicad_pcb (version 20221018) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.Adhes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
    (50 "User.1" user)
    (51 "User.2" user)
    (52 "User.3" user)
    (53 "User.4" user)
    (54 "User.5" user)
    (55 "User.6" user)
    (56 "User.7" user)
    (57 "User.8" user)
    (58 "User.9" user)
  )

  (setup
    (pad_to_mask_clearance 0)
    (solder_mask_min_width 0.5)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 4)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")

  (footprint "modules:SolderJumper-2_Bridged" (layer "F.Cu")
    (tstamp a9251a00-5177-4c76-ad38-af2e7990b6de)
    (at 129.54 60.96)
    (descr "SMD Solder Jumper, 1x1.5mm Pads, 0.3mm gap, bridged with 1 copper strip")
    (tags "net tie solder jumper open")
    (attr exclude_from_pos_files exclude_from_bom)
    (net_tie_pad_groups "1, 2")
    (fp_text reference "REF**" (at 0 -1.8) (layer "F.SilkS")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 0cd571c6-2398-4900-9b0a-420c3413da46)
    )
    (fp_text value "SolderJumper-2_Bridged" (at 0 1.9) (layer "F.Fab")
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp a8f01197-168b-4d6b-a965-2c8587f743dc)
    )
    (fp_poly
      (pts
        (xy -0.25 -0.3)
        (xy 0.25 -0.3)
        (xy 0.25 0.3)
        (xy -0.25 0.3)
      )

      (stroke (width 0) (type solid)) (fill solid) (layer "F.Cu") (tstamp 1be5ebff-f702-4477-86ef-7ad1efd17bba))
    (fp_poly
      (pts
        (xy -0.25 -0.3)
        (xy 0.25 -0.3)
        (xy 0.25 0.3)
        (xy -0.25 0.3)
      )

      (stroke (width 0) (type solid)) (fill solid) (layer "F.Mask") (tstamp e277389f-ed35-4929-90c4-b87753e93544))
    (fp_line (start -1.65 -1.016) (end -1.65 1.016)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp ef2fc944-472d-4147-8fd8-278a767a52c1))
    (fp_line (start -1.65 -1.016) (end 1.65 -1.016)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 3f45c6bf-a59a-44d8-a700-af9f839cf9c0))
    (fp_line (start 1.65 1.016) (end -1.65 1.016)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 029f40fc-f223-4b10-bdea-7170bcbd9dba))
    (fp_line (start 1.65 1.016) (end 1.65 -1.016)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 00f5b64d-3c69-4d71-a902-23d49a84d0a1))
    (pad "1" smd rect (at -0.65 0) (size 1 1) (layers "F.Cu" "F.Mask") (tstamp 59d96ae7-86a2-405e-8950-663d05d8f8b7))
    (pad "2" smd rect (at 0.65 0) (size 1 1) (layers "F.Cu" "F.Mask") (tstamp 278eb6bc-6e76-4e3c-8a43-caa01c28b437))
  )

  (gr_rect (start 118.69 55) (end 140.04 72.5)
    (stroke (width 0.1) (type default)) (fill none) (layer "Edge.Cuts") (tstamp a276e979-35c3-42d7-8a7a-d853672c89d8))

)
