(kicad_pcb (version 20230620) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.<PERSON>hes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 6)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (pdf_front_fp_property_popups true)
      (pdf_back_fp_property_popups true)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "A")
  (net 2 "B")

  (footprint "Connector_PinHeader_2.54mm:PinHeader_1x01_P2.54mm_Vertical" (layer "F.Cu")
    (tstamp 29726c99-8e0f-4994-acd9-20f8edd59f2f)
    (at 125 75)
    (descr "Through hole straight pin header, 1x01, 2.54mm pitch, single row")
    (tags "Through hole pin header THT 1x01 2.54mm single row")
    (property "Reference" "J2" (at 0 -2.33 0) (layer "F.SilkS") (tstamp 04b8f53b-0650-4e4e-b534-307b2478074b)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "Conn_01x01" (at 0 2.33 0) (layer "F.Fab") (tstamp 70cb3b58-866c-49c8-9663-797580d0ad3d)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp cadc913e-5e97-4436-a246-745b06f8ceab)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 2adac99f-cc1f-4ef8-90f5-bd17837db009)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp c9effd1f-6cd4-4f8b-92ce-45ce0b2482b7)
      (effects (font (size 1.27 1.27)))
    )
    (path "/6d3104a4-7acb-423a-9890-85f903110171")
    (sheetfile "/home/<USER>/kicad-shorted-zone-test/kicad-shorted-zone-test/kicad-shorted-zone-test.kicad_sch")
    (attr through_hole)
    (fp_line (start -1.33 -1.33) (end 0 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 865ea47c-6880-48b9-83a1-df5df3b15c7f))
    (fp_line (start -1.33 0) (end -1.33 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 65ab38d8-9a4f-4b84-b94e-9178c6a18989))
    (fp_line (start -1.33 1.27) (end -1.33 1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 8f6b1f49-9797-40af-953e-ed63e723e77c))
    (fp_line (start -1.33 1.27) (end 1.33 1.27)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 93faaa20-fa00-4e7e-b9e2-669d043d7374))
    (fp_line (start -1.33 1.33) (end 1.33 1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp c6dcdb50-4040-422c-9a13-fa95da174da8))
    (fp_line (start 1.33 1.27) (end 1.33 1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp a98bf551-ccaf-4cdc-978b-75a58b50d4e8))
    (fp_line (start -1.8 -1.8) (end -1.8 1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 705d8972-9071-49e7-bb82-352b7baee711))
    (fp_line (start -1.8 1.8) (end 1.8 1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 395c6039-b4b3-42cc-a47b-33ed255d229c))
    (fp_line (start 1.8 -1.8) (end -1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 16ea1f69-eb6e-469e-a98b-85fbe6904dd6))
    (fp_line (start 1.8 1.8) (end 1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 0e5a77cb-4892-47ce-8ad3-dddce4b78dda))
    (fp_line (start -1.27 -0.635) (end -0.635 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2bf501b7-fd31-4ba7-a5fa-ab0bfd5d978c))
    (fp_line (start -1.27 1.27) (end -1.27 -0.635)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c88ccd7c-dfc0-4f18-94d3-436c0f1b7f10))
    (fp_line (start -0.635 -1.27) (end 1.27 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 394c4262-cdf7-4e40-b9c9-acff837628f6))
    (fp_line (start 1.27 -1.27) (end 1.27 1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3473a68e-7ee9-4569-996a-6b85f135736f))
    (fp_line (start 1.27 1.27) (end -1.27 1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9be0821f-53a7-4a6b-b20f-46cc43abb045))
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab") (tstamp 7933e25f-2e86-4ed0-9f12-510275b3fef7)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad "1" thru_hole rect (at 0 0) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 2 "B") (pinfunction "Pin_1")
      (tstamp 39ddc8c5-658a-4354-adac-e784c48a1e49)
    )
    (model "${KISYS3DMOD}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x01_P2.54mm_Vertical.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Connector_PinHeader_2.54mm:PinHeader_1x01_P2.54mm_Vertical" (layer "F.Cu")
    (tstamp 6b9198d5-ffb6-446c-9bd9-58087491bfdf)
    (at 133 75)
    (descr "Through hole straight pin header, 1x01, 2.54mm pitch, single row")
    (tags "Through hole pin header THT 1x01 2.54mm single row")
    (property "Reference" "J1" (at 0 -2.33 0) (layer "F.SilkS") (tstamp bd5dee96-9f3c-435b-9c0b-051e216e3ede)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "Conn_01x01" (at 0 2.33 0) (layer "F.Fab") (tstamp c4faab9a-5b64-4949-b7e8-4b8510c0c677)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 550168d1-b69c-4cac-8807-9113e988eb18)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 49744dbc-3b49-4938-8e46-e9397dbcc037)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp b6f9a893-9be9-4469-a117-aade54e171de)
      (effects (font (size 1.27 1.27)))
    )
    (path "/da07c2f2-9cfc-4123-b598-021ac8524413")
    (sheetfile "/home/<USER>/kicad-shorted-zone-test/kicad-shorted-zone-test/kicad-shorted-zone-test.kicad_sch")
    (attr through_hole)
    (fp_line (start -1.33 -1.33) (end 0 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 07744fd1-8232-46fa-a8e1-aedbee408df7))
    (fp_line (start -1.33 0) (end -1.33 -1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 60eed028-d53e-4449-870a-dd0223dd2708))
    (fp_line (start -1.33 1.27) (end -1.33 1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp cc36fc52-529a-4bb6-b591-4a369c8a36eb))
    (fp_line (start -1.33 1.27) (end 1.33 1.27)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 2f68829f-c401-4cbe-88e5-3795a2a9ebf2))
    (fp_line (start -1.33 1.33) (end 1.33 1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp efe5303e-b972-43f3-bc48-7899b111a46c))
    (fp_line (start 1.33 1.27) (end 1.33 1.33)
      (stroke (width 0.12) (type solid)) (layer "F.SilkS") (tstamp 10ad763f-c0c7-4ddd-aea2-1aba53cb9f70))
    (fp_line (start -1.8 -1.8) (end -1.8 1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp f1f5e747-2bbf-43cb-a6aa-7fa21fc6087b))
    (fp_line (start -1.8 1.8) (end 1.8 1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 404e8f9b-0d2f-483e-bfd6-bd8d62412459))
    (fp_line (start 1.8 -1.8) (end -1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 70086051-bcbe-4955-ada4-bfa6ca8668cf))
    (fp_line (start 1.8 1.8) (end 1.8 -1.8)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b01318f4-394d-420d-a0d3-724173857223))
    (fp_line (start -1.27 -0.635) (end -0.635 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ea95dced-49b6-44b6-984e-4c92327d4344))
    (fp_line (start -1.27 1.27) (end -1.27 -0.635)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7773b789-8d8f-46fb-a762-26160e44999e))
    (fp_line (start -0.635 -1.27) (end 1.27 -1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c40a4561-e42c-42c3-bc13-b6319a59b623))
    (fp_line (start 1.27 -1.27) (end 1.27 1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 236bf148-c32f-4267-b326-420692e18708))
    (fp_line (start 1.27 1.27) (end -1.27 1.27)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bed4c854-9234-4ee7-8fb3-8f182e3eaaa2))
    (fp_text user "${REFERENCE}" (at 0 0 90) (layer "F.Fab") (tstamp d5a60db9-2741-4283-884b-91161528e798)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (pad "1" thru_hole rect (at 0 0) (size 1.7 1.7) (drill 1) (layers "*.Cu" "*.Mask")
      (net 1 "A") (pinfunction "Pin_1")
      (tstamp df639234-11cd-4ef1-a107-9aef4bdb6632)
    )
    (model "${KISYS3DMOD}/Connector_PinHeader_2.54mm.3dshapes/PinHeader_1x01_P2.54mm_Vertical.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_rect (start 120 70) (end 140 80)
    (stroke (width 0.1) (type solid)) (fill none) (layer "Edge.Cuts") (tstamp 39ee3aa0-8792-48bd-9c1d-4738549f4f8e))

  (zone (net 2) (net_name "B") (layers "F&B.Cu") (tstamp 3afa8ea5-827c-4708-b518-e9bf7f8dd20e) (hatch edge 0.508)
    (connect_pads (clearance 0.508))
    (min_thickness 0.254) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 140 80)
        (xy 120 80)
        (xy 120 70)
        (xy 140 70)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 139.441621 70.520502)
        (xy 139.488114 70.574158)
        (xy 139.4995 70.6265)
        (xy 139.4995 79.3735)
        (xy 139.479498 79.441621)
        (xy 139.425842 79.488114)
        (xy 139.3735 79.4995)
        (xy 120.6265 79.4995)
        (xy 120.558379 79.479498)
        (xy 120.511886 79.425842)
        (xy 120.5005 79.3735)
        (xy 120.5005 75.898597)
        (xy 123.642 75.898597)
        (xy 123.648505 75.959093)
        (xy 123.699555 76.095964)
        (xy 123.699555 76.095965)
        (xy 123.787095 76.212904)
        (xy 123.904034 76.300444)
        (xy 124.040906 76.351494)
        (xy 124.101402 76.357999)
        (xy 124.101415 76.358)
        (xy 124.746 76.358)
        (xy 124.745999 75.614033)
        (xy 124.766001 75.545912)
        (xy 124.819657 75.499419)
        (xy 124.889926 75.489315)
        (xy 124.964237 75.5)
        (xy 125.035763 75.5)
        (xy 125.110068 75.489316)
        (xy 125.18034 75.499419)
        (xy 125.233996 75.545911)
        (xy 125.253999 75.614031)
        (xy 125.253999 75.614033)
        (xy 125.254 76.358)
        (xy 125.898585 76.358)
        (xy 125.898597 76.357999)
        (xy 125.959093 76.351494)
        (xy 126.095964 76.300444)
        (xy 126.095965 76.300444)
        (xy 126.212904 76.212904)
        (xy 126.300444 76.095965)
        (xy 126.300444 76.095964)
        (xy 126.351494 75.959093)
        (xy 126.357993 75.898649)
        (xy 131.6415 75.898649)
        (xy 131.648009 75.959196)
        (xy 131.648011 75.959204)
        (xy 131.69911 76.096202)
        (xy 131.699112 76.096207)
        (xy 131.786738 76.213261)
        (xy 131.903792 76.300887)
        (xy 131.903794 76.300888)
        (xy 131.903796 76.300889)
        (xy 131.962875 76.322924)
        (xy 132.040795 76.351988)
        (xy 132.040803 76.35199)
        (xy 132.10135 76.358499)
        (xy 132.101355 76.358499)
        (xy 132.101362 76.3585)
        (xy 132.101368 76.3585)
        (xy 133.898632 76.3585)
        (xy 133.898638 76.3585)
        (xy 133.898645 76.358499)
        (xy 133.898649 76.358499)
        (xy 133.959196 76.35199)
        (xy 133.959199 76.351989)
        (xy 133.959201 76.351989)
        (xy 134.096204 76.300889)
        (xy 134.096799 76.300444)
        (xy 134.213261 76.213261)
        (xy 134.300887 76.096207)
        (xy 134.300887 76.096206)
        (xy 134.300889 76.096204)
        (xy 134.351989 75.959201)
        (xy 134.3585 75.898638)
        (xy 134.3585 74.101362)
        (xy 134.352001 74.040906)
        (xy 134.35199 74.040803)
        (xy 134.351988 74.040795)
        (xy 134.300978 73.904035)
        (xy 134.300889 73.903796)
        (xy 134.300888 73.903794)
        (xy 134.300887 73.903792)
        (xy 134.213261 73.786738)
        (xy 134.096207 73.699112)
        (xy 134.096202 73.69911)
        (xy 133.959204 73.648011)
        (xy 133.959196 73.648009)
        (xy 133.898649 73.6415)
        (xy 133.898638 73.6415)
        (xy 132.101362 73.6415)
        (xy 132.10135 73.6415)
        (xy 132.040803 73.648009)
        (xy 132.040795 73.648011)
        (xy 131.903797 73.69911)
        (xy 131.903792 73.699112)
        (xy 131.786738 73.786738)
        (xy 131.699112 73.903792)
        (xy 131.69911 73.903797)
        (xy 131.648011 74.040795)
        (xy 131.648009 74.040803)
        (xy 131.6415 74.10135)
        (xy 131.6415 75.898649)
        (xy 126.357993 75.898649)
        (xy 126.357999 75.898597)
        (xy 126.358 75.898585)
        (xy 126.358 75.254)
        (xy 125.614844 75.254)
        (xy 125.546723 75.233998)
        (xy 125.50023 75.180342)
        (xy 125.490126 75.110068)
        (xy 125.493947 75.092504)
        (xy 125.5 75.071888)
        (xy 125.5 74.928111)
        (xy 125.493947 74.907496)
        (xy 125.493948 74.8365)
        (xy 125.532333 74.776774)
        (xy 125.596914 74.747282)
        (xy 125.614844 74.746)
        (xy 126.358 74.746)
        (xy 126.358 74.101414)
        (xy 126.357999 74.101402)
        (xy 126.351494 74.040906)
        (xy 126.300444 73.904035)
        (xy 126.300444 73.904034)
        (xy 126.212904 73.787095)
        (xy 126.095965 73.699555)
        (xy 125.959093 73.648505)
        (xy 125.898597 73.642)
        (xy 125.254 73.642)
        (xy 125.254 74.385966)
        (xy 125.233998 74.454087)
        (xy 125.180342 74.50058)
        (xy 125.110069 74.510683)
        (xy 125.110068 74.510683)
        (xy 125.035768 74.5)
        (xy 125.035763 74.5)
        (xy 124.964237 74.5)
        (xy 124.964231 74.5)
        (xy 124.889932 74.510683)
        (xy 124.819658 74.50058)
        (xy 124.766002 74.454087)
        (xy 124.746 74.385966)
        (xy 124.746 73.642)
        (xy 124.101402 73.642)
        (xy 124.040906 73.648505)
        (xy 123.904035 73.699555)
        (xy 123.904034 73.699555)
        (xy 123.787095 73.787095)
        (xy 123.699555 73.904034)
        (xy 123.699555 73.904035)
        (xy 123.648505 74.040906)
        (xy 123.642 74.101402)
        (xy 123.642 74.746)
        (xy 124.385156 74.746)
        (xy 124.453277 74.766002)
        (xy 124.49977 74.819658)
        (xy 124.509874 74.889932)
        (xy 124.506053 74.907496)
        (xy 124.5 74.928111)
        (xy 124.5 75.071888)
        (xy 124.506053 75.092504)
        (xy 124.506052 75.1635)
        (xy 124.467667 75.223226)
        (xy 124.403086 75.252718)
        (xy 124.385156 75.254)
        (xy 123.642 75.254)
        (xy 123.642 75.898597)
        (xy 120.5005 75.898597)
        (xy 120.5005 70.6265)
        (xy 120.520502 70.558379)
        (xy 120.574158 70.511886)
        (xy 120.6265 70.5005)
        (xy 139.3735 70.5005)
      )
    )
    (filled_polygon
      (layer "B.Cu")
      (pts
        (xy 139.441621 70.520502)
        (xy 139.488114 70.574158)
        (xy 139.4995 70.6265)
        (xy 139.4995 79.3735)
        (xy 139.479498 79.441621)
        (xy 139.425842 79.488114)
        (xy 139.3735 79.4995)
        (xy 120.6265 79.4995)
        (xy 120.558379 79.479498)
        (xy 120.511886 79.425842)
        (xy 120.5005 79.3735)
        (xy 120.5005 75.898597)
        (xy 123.642 75.898597)
        (xy 123.648505 75.959093)
        (xy 123.699555 76.095964)
        (xy 123.699555 76.095965)
        (xy 123.787095 76.212904)
        (xy 123.904034 76.300444)
        (xy 124.040906 76.351494)
        (xy 124.101402 76.357999)
        (xy 124.101415 76.358)
        (xy 124.746 76.358)
        (xy 124.745999 75.614033)
        (xy 124.766001 75.545912)
        (xy 124.819657 75.499419)
        (xy 124.889926 75.489315)
        (xy 124.964237 75.5)
        (xy 125.035763 75.5)
        (xy 125.110068 75.489316)
        (xy 125.18034 75.499419)
        (xy 125.233996 75.545911)
        (xy 125.253999 75.614031)
        (xy 125.253999 75.614033)
        (xy 125.254 76.358)
        (xy 125.898585 76.358)
        (xy 125.898597 76.357999)
        (xy 125.959093 76.351494)
        (xy 126.095964 76.300444)
        (xy 126.095965 76.300444)
        (xy 126.212904 76.212904)
        (xy 126.300444 76.095965)
        (xy 126.300444 76.095964)
        (xy 126.351494 75.959093)
        (xy 126.357993 75.898649)
        (xy 131.6415 75.898649)
        (xy 131.648009 75.959196)
        (xy 131.648011 75.959204)
        (xy 131.69911 76.096202)
        (xy 131.699112 76.096207)
        (xy 131.786738 76.213261)
        (xy 131.903792 76.300887)
        (xy 131.903794 76.300888)
        (xy 131.903796 76.300889)
        (xy 131.962875 76.322924)
        (xy 132.040795 76.351988)
        (xy 132.040803 76.35199)
        (xy 132.10135 76.358499)
        (xy 132.101355 76.358499)
        (xy 132.101362 76.3585)
        (xy 132.101368 76.3585)
        (xy 133.898632 76.3585)
        (xy 133.898638 76.3585)
        (xy 133.898645 76.358499)
        (xy 133.898649 76.358499)
        (xy 133.959196 76.35199)
        (xy 133.959199 76.351989)
        (xy 133.959201 76.351989)
        (xy 134.096204 76.300889)
        (xy 134.096799 76.300444)
        (xy 134.213261 76.213261)
        (xy 134.300887 76.096207)
        (xy 134.300887 76.096206)
        (xy 134.300889 76.096204)
        (xy 134.351989 75.959201)
        (xy 134.3585 75.898638)
        (xy 134.3585 74.101362)
        (xy 134.352001 74.040906)
        (xy 134.35199 74.040803)
        (xy 134.351988 74.040795)
        (xy 134.300978 73.904035)
        (xy 134.300889 73.903796)
        (xy 134.300888 73.903794)
        (xy 134.300887 73.903792)
        (xy 134.213261 73.786738)
        (xy 134.096207 73.699112)
        (xy 134.096202 73.69911)
        (xy 133.959204 73.648011)
        (xy 133.959196 73.648009)
        (xy 133.898649 73.6415)
        (xy 133.898638 73.6415)
        (xy 132.101362 73.6415)
        (xy 132.10135 73.6415)
        (xy 132.040803 73.648009)
        (xy 132.040795 73.648011)
        (xy 131.903797 73.69911)
        (xy 131.903792 73.699112)
        (xy 131.786738 73.786738)
        (xy 131.699112 73.903792)
        (xy 131.69911 73.903797)
        (xy 131.648011 74.040795)
        (xy 131.648009 74.040803)
        (xy 131.6415 74.10135)
        (xy 131.6415 75.898649)
        (xy 126.357993 75.898649)
        (xy 126.357999 75.898597)
        (xy 126.358 75.898585)
        (xy 126.358 75.254)
        (xy 125.614844 75.254)
        (xy 125.546723 75.233998)
        (xy 125.50023 75.180342)
        (xy 125.490126 75.110068)
        (xy 125.493947 75.092504)
        (xy 125.5 75.071888)
        (xy 125.5 74.928111)
        (xy 125.493947 74.907496)
        (xy 125.493948 74.8365)
        (xy 125.532333 74.776774)
        (xy 125.596914 74.747282)
        (xy 125.614844 74.746)
        (xy 126.358 74.746)
        (xy 126.358 74.101414)
        (xy 126.357999 74.101402)
        (xy 126.351494 74.040906)
        (xy 126.300444 73.904035)
        (xy 126.300444 73.904034)
        (xy 126.212904 73.787095)
        (xy 126.095965 73.699555)
        (xy 125.959093 73.648505)
        (xy 125.898597 73.642)
        (xy 125.254 73.642)
        (xy 125.254 74.385966)
        (xy 125.233998 74.454087)
        (xy 125.180342 74.50058)
        (xy 125.110069 74.510683)
        (xy 125.110068 74.510683)
        (xy 125.035768 74.5)
        (xy 125.035763 74.5)
        (xy 124.964237 74.5)
        (xy 124.964231 74.5)
        (xy 124.889932 74.510683)
        (xy 124.819658 74.50058)
        (xy 124.766002 74.454087)
        (xy 124.746 74.385966)
        (xy 124.746 73.642)
        (xy 124.101402 73.642)
        (xy 124.040906 73.648505)
        (xy 123.904035 73.699555)
        (xy 123.904034 73.699555)
        (xy 123.787095 73.787095)
        (xy 123.699555 73.904034)
        (xy 123.699555 73.904035)
        (xy 123.648505 74.040906)
        (xy 123.642 74.101402)
        (xy 123.642 74.746)
        (xy 124.385156 74.746)
        (xy 124.453277 74.766002)
        (xy 124.49977 74.819658)
        (xy 124.509874 74.889932)
        (xy 124.506053 74.907496)
        (xy 124.5 74.928111)
        (xy 124.5 75.071888)
        (xy 124.506053 75.092504)
        (xy 124.506052 75.1635)
        (xy 124.467667 75.223226)
        (xy 124.403086 75.252718)
        (xy 124.385156 75.254)
        (xy 123.642 75.254)
        (xy 123.642 75.898597)
        (xy 120.5005 75.898597)
        (xy 120.5005 70.6265)
        (xy 120.520502 70.558379)
        (xy 120.574158 70.511886)
        (xy 120.6265 70.5005)
        (xy 139.3735 70.5005)
      )
    )
  )
  (zone (net 1) (net_name "A") (layer "B.Cu") (tstamp d349fea2-2e3a-456a-aad1-1fdfcae16bff) (hatch edge 0.508)
    (connect_pads (clearance 0.508))
    (min_thickness 0.254) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.508) (thermal_bridge_width 0.508) (island_removal_mode 1) (island_area_min 10))
    (polygon
      (pts
        (xy 137.57933 78.081171)
        (xy 121.57933 78.081171)
        (xy 121.57933 72.081171)
        (xy 137.57933 72.081171)
      )
    )
    (filled_polygon
      (layer "B.Cu")
      (pts
        (xy 137.183178 72.261612)
        (xy 137.229671 72.315268)
        (xy 137.241057 72.36761)
        (xy 137.241057 77.626162)
        (xy 137.221055 77.694283)
        (xy 137.167399 77.740776)
        (xy 137.115057 77.752162)
        (xy 121.95693 77.752162)
        (xy 121.888809 77.73216)
        (xy 121.842316 77.678504)
        (xy 121.83093 77.626162)
        (xy 121.83093 72.36761)
        (xy 121.850932 72.299489)
        (xy 121.904588 72.252996)
        (xy 121.95693 72.24161)
        (xy 137.115057 72.24161)
      )
    )
  )
)
