EESchema-LIBRARY Version  3/5/2007-08:10:50
#
#
# 74LS125
#
DEF 74LS125 U 0 30 Y Y 4 F N
F0 "U" 0 100 50 H V L B
F1 "74LS125" 50 -150 40 H V L T
DRAW
P 4 0 1 0  -150 150  -150 -150  150 0  -150 150 N
X VCC 14 -150 150 0 D 50 30 0 0 W N
X O 8 450 0 300 L 50 30 3 1 T
X O 6 450 0 300 L 50 30 2 1 T
X O 3 450 0 300 L 50 30 1 1 T
X O 11 450 0 300 L 50 30 4 1 T
X D 2 -450 0 300 R 50 30 1 1 I
X D 5 -450 0 300 R 50 30 2 1 I
X D 9 -450 0 300 R 50 30 3 1 I
X D 12 -450 0 300 R 50 30 4 1 I
X GND 7 -150 -150 0 U 50 30 0 0 W N
X E 10 0 -300 220 U 50 30 3 0 I I
X E 4 0 -300 220 U 50 30 2 0 I I
X E 1 0 -300 220 U 50 30 1 0 I I
X E 13 0 -300 220 U 50 30 4 0 I I
ENDDRAW
ENDDEF
#
# C
#
DEF C C 0 10 N Y 1 F N
F0 "C" 50 100 50 H V L C
F1 "C" 50 -100 50 H V L C
$FPLIST
 C?
 SM*
$ENDFPLIST
DRAW
P 2 0 1 8  -100 -30  100 -30 N
P 2 0 1 8  -100 30  100 30 N
X ~ 1 0 200 170 D 40 40 1 1 P
X ~ 2 0 -200 170 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# CONN_6
#
DEF CONN_6 P 0 30 Y N 1 F N
F0 "P" -50 0 60 V V C C
F1 "CONN_6" 50 0 60 V V C C
DRAW
S -100 300 100 -300 0 1 0 N
X 6 6 -350 -250 250 R 60 60 1 1 P I
X 5 5 -350 -150 250 R 60 60 1 1 P I
X 4 4 -350 -50 250 R 60 60 1 1 P I
X 3 3 -350 50 250 R 60 60 1 1 P I
X 2 2 -350 150 250 R 60 60 1 1 P I
X 1 1 -350 250 250 R 60 60 1 1 P I
ENDDRAW
ENDDEF
#
# CP
#
DEF CP C 0 10 N N 1 F N
F0 "C" 50 100 50 H V L C
F1 "CP" 50 -100 50 H V L C
ALIAS CAPAPOL
$FPLIST
 CP*
 SM*
$ENDFPLIST
DRAW
P 4 0 1 0  -50 50  -50 -20  50 -20  50 50 F
P 4 0 1 8  -100 50  -100 -50  100 -50  100 50 N
X ~ 1 0 200 150 D 40 40 1 1 P
X ~ 2 0 -200 150 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# DB25
#
DEF DB25 J 0 40 Y N 1 F N
F0 "J" 50 1350 70 H V C C
F1 "DB25" -50 -1350 70 H V C C
$FPLIST
 DB25*
$ENDFPLIST
DRAW
P 2 0 1 0  -150 -600  -100 -600 N
A 117 1170 32 664 1 0 1 8 N 130 1199 149 1170
P 2 0 1 8  150 -1170  150 1170 N
C 50 700 30 0 1 0 N
P 2 0 1 0  -150 400  -100 400 N
C -70 -200 30 0 1 0 N
P 2 0 1 0  -150 1100  20 1100 N
P 2 0 1 0  -150 -800  -100 -800 N
P 2 0 1 8  -100 -1300  130 -1200 N
C -70 1000 30 0 1 0 N
C 50 1100 30 0 1 0 N
P 2 0 1 0  -150 100  20 100 N
C -70 -400 30 0 1 0 N
P 2 0 1 0  -150 600  -100 600 N
P 2 0 1 0  -150 -500  20 -500 N
P 2 0 1 8  130 1200  -100 1310 N
P 2 0 1 0  -150 -100  20 -100 N
C -70 -1200 30 0 1 0 N
P 2 0 1 0  -150 1000  -100 1000 N
C -70 -600 30 0 1 0 N
P 2 0 1 0  -150 1200  -100 1200 N
C 50 100 30 0 1 0 N
P 2 0 1 0  -150 0  -100 0 N
P 2 0 1 0  -150 -1000  -100 -1000 N
P 2 0 1 0  -150 300  20 300 N
C -70 -800 30 0 1 0 N
P 2 0 1 0  -150 -1200  -100 -1200 N
C -70 1200 30 0 1 0 N
P 2 0 1 0  -150 -700  20 -700 N
C 50 -1100 30 0 1 0 N
P 2 0 1 0  -150 900  20 900 N
P 2 0 1 8  -150 -1260  -150 1270 N
P 2 0 1 0  -150 700  20 700 N
P 2 0 1 0  -150 -1100  20 -1100 N
A -108 -1259 42 -1787 -788 0 1 8 N -150 -1260 -100 -1300
P 2 0 1 0  -150 200  -100 200 N
A 116 -1169 34 -657 -15 0 1 8 N 130 -1200 150 -1170
C -70 200 30 0 1 0 N
P 2 0 1 0  -150 800  -100 800 N
P 2 0 1 0  -150 -400  -100 -400 N
C 50 -700 30 0 1 0 N
C 50 500 30 0 1 0 N
C 50 900 30 0 1 0 N
C 50 -100 30 0 1 0 N
C 50 -300 30 0 1 0 N
C 50 300 30 0 1 0 N
C -70 -1000 30 0 1 0 N
C 50 -900 30 0 1 0 N
P 2 0 1 0  -150 -200  -100 -200 N
C -70 600 30 0 1 0 N
C 50 -500 30 0 1 0 N
C -70 0 30 0 1 0 N
C -70 400 30 0 1 0 N
A -109 1270 41 1799 774 0 1 8 N -150 1270 -100 1310
C -70 800 30 0 1 0 N
P 2 0 1 0  -150 500  20 500 N
P 2 0 1 0  -150 -300  20 -300 N
P 2 0 1 0  -150 -900  20 -900 N
X 1 1 -450 -1200 300 R 60 60 1 1 P
X P14 14 -450 -1100 300 R 60 60 1 1 P
X 2 2 -450 -1000 300 R 60 60 1 1 P
X P15 15 -450 -900 300 R 60 60 1 1 P
X 3 3 -450 -800 300 R 60 60 1 1 P
X P16 16 -450 -700 300 R 60 60 1 1 P
X 4 4 -450 -600 300 R 60 60 1 1 P
X P17 17 -450 -500 300 R 60 60 1 1 P
X 5 5 -450 -400 300 R 60 60 1 1 P
X P18 18 -450 -300 300 R 60 60 1 1 P
X 6 6 -450 -200 300 R 60 60 1 1 P
X P19 19 -450 -100 300 R 60 60 1 1 P
X 7 7 -450 0 300 R 60 60 1 1 P
X P20 20 -450 100 300 R 60 60 1 1 P
X 8 8 -450 200 300 R 60 60 1 1 P
X P21 21 -450 300 300 R 60 60 1 1 P
X 9 9 -450 400 300 R 60 60 1 1 P
X P22 22 -450 500 300 R 60 60 1 1 P
X 10 10 -450 600 300 R 60 60 1 1 P
X P23 23 -450 700 300 R 60 60 1 1 P
X 11 11 -450 800 300 R 60 60 1 1 P
X P24 24 -450 900 300 R 60 60 1 1 P
X 12 12 -450 1000 300 R 60 60 1 1 P
X P25 25 -450 1100 300 R 60 60 1 1 P
X 13 13 -450 1200 300 R 60 60 1 1 P
ENDDRAW
ENDDEF
#
# DB9
#
DEF DB9 J 0 40 Y N 1 F N
F0 "J" 0 550 70 H V C C
F1 "DB9" 0 -550 70 H V C C
$FPLIST
 DB9*
$ENDFPLIST
DRAW
P 2 0 1 8  -140 470  -150 460 N
P 2 0 1 8  129 390  150 370 N
P 2 0 1 0  -150 -200  -100 -200 N
C -70 0 30 0 1 0 N
P 2 0 1 8  140 -409  -50 -490 N
P 2 0 1 0  -150 300  20 300 N
C -70 400 30 0 1 0 N
P 2 0 1 0  -150 -100  20 -100 N
P 2 0 1 8  -140 -470  -110 -490 N
P 2 0 1 8  -140 470  -100 490 N
P 2 0 1 8  -100 490  -70 490 N
P 2 0 1 0  -150 -300  20 -300 N
P 2 0 1 8  150 370  150 -390 N
P 2 0 1 0  -150 -400  -100 -400 N
P 2 0 1 0  -150 100  20 100 N
P 2 0 1 0  -150 0  -100 0 N
P 2 0 1 8  -150 -460  -150 460 N
P 2 0 1 0  -150 200  -100 200 N
C 50 -100 30 0 1 0 N
P 2 0 1 0  -150 400  -100 400 N
C -70 200 30 0 1 0 N
C -70 -400 30 0 1 0 N
C 50 100 30 0 1 0 N
C 50 300 30 0 1 0 N
P 2 0 1 8  129 390  -70 490 N
P 2 0 1 8  -150 -459  -140 -470 N
P 2 0 1 8  150 -390  140 -409 N
P 2 0 1 8  -110 -490  -50 -490 N
C -70 -200 30 0 1 0 N
C 50 -300 30 0 1 0 N
X 1 1 -450 -400 300 R 60 60 1 1 P
X P6 6 -450 -300 300 R 60 60 1 1 P
X 2 2 -450 -200 300 R 60 60 1 1 P
X P7 7 -450 -100 300 R 60 60 1 1 P
X 3 3 -450 0 300 R 60 60 1 1 P
X P8 8 -450 100 300 R 60 60 1 1 P
X 4 4 -450 200 300 R 60 60 1 1 P
X P9 9 -450 300 300 R 60 60 1 1 P
X 5 5 -450 400 300 R 60 60 1 1 P
ENDDRAW
ENDDEF
#
# DIODESCH
#
DEF DIODESCH D 0 40 N N 1 F N
F0 "D" 0 100 40 H V C C
F1 "DIODESCH" 0 -100 40 H V C C
DRAW
P 3 0 1 0  -50 50  50 0  -50 -50 F
P 6 0 1 8  75 25  75 50  50 50  50 -50  25 -50  25 -25 N
X K 2 200 0 150 L 40 40 1 1 P
X A 1 -200 0 150 R 40 40 1 1 P
ENDDRAW
ENDDEF
#
# GND
#
DEF ~GND #PWR 0 0 Y Y 1 F P
F0 "#PWR" 0 0 30 H I C C
F1 "GND" 0 -70 30 H I C C
DRAW
P 4 0 1 4  -50 0  0 -50  50 0  -50 0 N
X GND 1 0 0 0 U 30 30 1 1 W N
ENDDRAW
ENDDEF
#
# PWR_FLAG
#
DEF PWR_FLAG #FLG 0 0 N N 1 F P
F0 "#FLG" 0 270 30 H I C C
F1 "PWR_FLAG" 0 230 30 H V C C
DRAW
P 3 0 1 0  0 0  0 100  0 100 N
P 5 0 1 0  0 100  -100 150  0 200  100 150  0 100 N
X pwr 1 0 0 0 U 20 20 0 0 w
ENDDRAW
ENDDEF
#
# R
#
DEF R R 0 0 N Y 1 F N
F0 "R" 80 0 50 V V C C
F1 "R" 0 0 50 V V C C
$FPLIST
 R?
 SM0603
 SM0805
$ENDFPLIST
DRAW
S -40 150 40 -150 0 1 8 N
X ~ 1 0 250 100 D 60 60 1 1 P
X ~ 2 0 -250 100 U 60 60 1 1 P
ENDDRAW
ENDDEF
#
# VCC
#
DEF VCC #PWR 0 0 Y Y 1 F P
F0 "#PWR" 0 100 30 H I C C
F1 "VCC" 0 100 30 H V C C
DRAW
P 3 0 1 4  0 0  0 30  0 30 N
C 0 50 20 0 1 4 N
X VCC 1 0 0 0 U 20 20 0 0 W N
ENDDRAW
ENDDEF
#
#EndLibrary
