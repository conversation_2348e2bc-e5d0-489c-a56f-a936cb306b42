(footprint "NEO-M8P" (version 20231007) (generator pcbnew)
  (layer "F.Cu")
  (property "Reference" "REF**" (at -6.1 -8.5 0 unlocked) (layer "F.SilkS") (tstamp da2fab9b-8c65-4da6-bdf0-663cbc3d8301)
    (effects (font (size 0.46736 0.46736) (thickness 0.04064)) (justify left bottom))
  )
  (property "Value" "NEO-M8P" (at -6.1 9 0 unlocked) (layer "F.Fab") (tstamp b1d0c25f-3145-4505-99b8-95c7a578791a)
    (effects (font (size 0.46736 0.46736) (thickness 0.04064)) (justify left bottom))
  )
  (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 60d0f303-bb0e-4bd2-aac4-7cecebe3b93c)
    (effects (font (size 1.27 1.27)))
  )
  (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 1cae8763-3c53-436d-8f67-9cb7e26d739d)
    (effects (font (size 1.27 1.27)))
  )
  (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 11bd8791-a3fb-4c0d-9d2a-2811efa2c0f8)
    (effects (font (size 1.27 1.27)))
  )
  (fp_poly
    (pts
      (xy -6.1 -7.4)
      (xy -7.3 -7.4)
      (xy -7.3 -6.6)
      (xy -6.1 -6.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7c0c57ec-d78c-40ec-a808-ed4d3e7e657a))
  (fp_poly
    (pts
      (xy -6.1 -6.3)
      (xy -7.3 -6.3)
      (xy -7.3 -5.5)
      (xy -6.1 -5.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp babd188f-0d12-408b-b9b7-78d07246261c))
  (fp_poly
    (pts
      (xy -6.1 -5.2)
      (xy -7.3 -5.2)
      (xy -7.3 -4.4)
      (xy -6.1 -4.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp ccc405e7-2a2a-48b5-ba40-a57ff0247fe3))
  (fp_poly
    (pts
      (xy -6.1 -4.1)
      (xy -7.3 -4.1)
      (xy -7.3 -3.3)
      (xy -6.1 -3.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp dcfbe9be-916a-4db6-849a-12e0c1056a2e))
  (fp_poly
    (pts
      (xy -6.1 -3)
      (xy -7.3 -3)
      (xy -7.3 -2.2)
      (xy -6.1 -2.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp f7744c2d-9fb6-4e76-ab97-84f5031298e3))
  (fp_poly
    (pts
      (xy -6.1 0)
      (xy -7.3 0)
      (xy -7.3 0.8)
      (xy -6.1 0.8)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 84253358-ebc0-4802-bbb9-b44682f34153))
  (fp_poly
    (pts
      (xy -6.1 1.1)
      (xy -7.3 1.1)
      (xy -7.3 1.9)
      (xy -6.1 1.9)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 814866d9-5df6-4dd1-b751-0eb7412f7101))
  (fp_poly
    (pts
      (xy -6.1 2.2)
      (xy -7.3 2.2)
      (xy -7.3 3)
      (xy -6.1 3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp e27eeabe-062e-4a50-894d-56a0cf1887ef))
  (fp_poly
    (pts
      (xy -6.1 3.3)
      (xy -7.3 3.3)
      (xy -7.3 4.1)
      (xy -6.1 4.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp b6fde2c8-0d96-49d5-a125-5ce9d4aee40b))
  (fp_poly
    (pts
      (xy -6.1 4.4)
      (xy -7.3 4.4)
      (xy -7.3 5.2)
      (xy -6.1 5.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp cf4805ee-5c9d-410d-a4b4-c11d5eee705d))
  (fp_poly
    (pts
      (xy -6.1 5.5)
      (xy -7.3 5.5)
      (xy -7.3 6.3)
      (xy -6.1 6.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 584a9a71-35c5-4551-a690-388d8d8fa6e8))
  (fp_poly
    (pts
      (xy -6.1 6.6)
      (xy -7.3 6.6)
      (xy -7.3 7.4)
      (xy -6.1 7.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp b5f8ee46-e929-40d0-be0e-05c6593243f6))
  (fp_poly
    (pts
      (xy -5.2 -7.3)
      (xy -6.1 -7.3)
      (xy -6.1 -6.7)
      (xy -5.2 -6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp d1b6c907-2246-46fe-b433-be82e953c8d8))
  (fp_poly
    (pts
      (xy -5.2 -6.2)
      (xy -6.1 -6.2)
      (xy -6.1 -5.6)
      (xy -5.2 -5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp a355c69d-b086-4ca9-bc3a-a267bf95c440))
  (fp_poly
    (pts
      (xy -5.2 -5.1)
      (xy -6.1 -5.1)
      (xy -6.1 -4.5)
      (xy -5.2 -4.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 2c172f30-99ab-4be5-a72d-03ca7eb994d8))
  (fp_poly
    (pts
      (xy -5.2 -4)
      (xy -6.1 -4)
      (xy -6.1 -3.4)
      (xy -5.2 -3.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp d079d058-2461-40f9-93cb-a7293279d869))
  (fp_poly
    (pts
      (xy -5.2 -2.9)
      (xy -6.1 -2.9)
      (xy -6.1 -2.3)
      (xy -5.2 -2.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp a09295c2-3425-4d11-99dd-2b9899199c40))
  (fp_poly
    (pts
      (xy -5.2 0.1)
      (xy -6.1 0.1)
      (xy -6.1 0.7)
      (xy -5.2 0.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 45483748-3458-4117-86a6-1f73b668d5cf))
  (fp_poly
    (pts
      (xy -5.2 1.2)
      (xy -6.1 1.2)
      (xy -6.1 1.8)
      (xy -5.2 1.8)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp af9b7fcd-5725-48b8-861a-0e33b023cee6))
  (fp_poly
    (pts
      (xy -5.2 2.3)
      (xy -6.1 2.3)
      (xy -6.1 2.9)
      (xy -5.2 2.9)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp a64c0911-f240-4c1e-a4cc-0bab968ba29d))
  (fp_poly
    (pts
      (xy -5.2 3.4)
      (xy -6.1 3.4)
      (xy -6.1 4)
      (xy -5.2 4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp acbfa014-6804-4b60-949b-e68b488b5c4e))
  (fp_poly
    (pts
      (xy -5.2 4.5)
      (xy -6.1 4.5)
      (xy -6.1 5.1)
      (xy -5.2 5.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 946a2294-0704-4f14-ab0f-379cb6e5f7f6))
  (fp_poly
    (pts
      (xy -5.2 5.6)
      (xy -6.1 5.6)
      (xy -6.1 6.2)
      (xy -5.2 6.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7ca0a50a-9d3e-4284-bf1e-947e426e3ab6))
  (fp_poly
    (pts
      (xy -5.2 6.7)
      (xy -6.1 6.7)
      (xy -6.1 7.3)
      (xy -5.2 7.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 4dc6a363-0eb0-4003-bb88-7cd1abde2582))
  (fp_poly
    (pts
      (xy 5.2 -6.7)
      (xy 6.1 -6.7)
      (xy 6.1 -7.3)
      (xy 5.2 -7.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 9cb36117-c38d-4c19-ba38-90ce31890fab))
  (fp_poly
    (pts
      (xy 5.2 -5.6)
      (xy 6.1 -5.6)
      (xy 6.1 -6.2)
      (xy 5.2 -6.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 4d16b23f-a2c8-419e-b0e7-3323b2497638))
  (fp_poly
    (pts
      (xy 5.2 -4.5)
      (xy 6.1 -4.5)
      (xy 6.1 -5.1)
      (xy 5.2 -5.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 1e4a78e2-ed93-4788-a1a9-b3a7d1d15308))
  (fp_poly
    (pts
      (xy 5.2 -3.4)
      (xy 6.1 -3.4)
      (xy 6.1 -4)
      (xy 5.2 -4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 559c6ba3-898f-4689-b250-84f6a46143c7))
  (fp_poly
    (pts
      (xy 5.2 -2.3)
      (xy 6.1 -2.3)
      (xy 6.1 -2.9)
      (xy 5.2 -2.9)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7bb7dd3c-2b7a-4c9c-a0b6-5798fce10654))
  (fp_poly
    (pts
      (xy 5.2 0.7)
      (xy 6.1 0.7)
      (xy 6.1 0.1)
      (xy 5.2 0.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 593a353e-d264-4e42-b5b7-2144bcc20445))
  (fp_poly
    (pts
      (xy 5.2 1.8)
      (xy 6.1 1.8)
      (xy 6.1 1.2)
      (xy 5.2 1.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 952ca3ff-7676-48bf-a523-34be7c1fdf49))
  (fp_poly
    (pts
      (xy 5.2 2.9)
      (xy 6.1 2.9)
      (xy 6.1 2.3)
      (xy 5.2 2.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 1bb9e158-05a4-49d3-bb05-d71dcd1d2878))
  (fp_poly
    (pts
      (xy 5.2 4)
      (xy 6.1 4)
      (xy 6.1 3.4)
      (xy 5.2 3.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 89a3f558-3a49-4014-9275-4cc1f1d4d7b1))
  (fp_poly
    (pts
      (xy 5.2 5.1)
      (xy 6.1 5.1)
      (xy 6.1 4.5)
      (xy 5.2 4.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp f1b5787f-0ed7-4dcf-b794-c6dc6babab78))
  (fp_poly
    (pts
      (xy 5.2 6.2)
      (xy 6.1 6.2)
      (xy 6.1 5.6)
      (xy 5.2 5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 52e6aa5c-01d4-44a7-8d12-4a0ee70800b3))
  (fp_poly
    (pts
      (xy 5.2 7.3)
      (xy 6.1 7.3)
      (xy 6.1 6.7)
      (xy 5.2 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp dfe91f21-9201-4a8b-bdad-3fbe48c39cd0))
  (fp_poly
    (pts
      (xy 6.1 -6.6)
      (xy 7.3 -6.6)
      (xy 7.3 -7.4)
      (xy 6.1 -7.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp cd1fdf01-a589-4a73-9c70-6a80b15e2f3b))
  (fp_poly
    (pts
      (xy 6.1 -5.5)
      (xy 7.3 -5.5)
      (xy 7.3 -6.3)
      (xy 6.1 -6.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp c6a39d2d-e296-49f2-9b99-c3d1e0a6e57d))
  (fp_poly
    (pts
      (xy 6.1 -4.4)
      (xy 7.3 -4.4)
      (xy 7.3 -5.2)
      (xy 6.1 -5.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 502d4096-0dc6-48b0-9831-1708f90c6bb6))
  (fp_poly
    (pts
      (xy 6.1 -3.3)
      (xy 7.3 -3.3)
      (xy 7.3 -4.1)
      (xy 6.1 -4.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp c41e5a5f-10b4-4147-b793-5d3d4016b23a))
  (fp_poly
    (pts
      (xy 6.1 -2.2)
      (xy 7.3 -2.2)
      (xy 7.3 -3)
      (xy 6.1 -3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 4a5c4d5c-05fe-4442-bb43-22a965a8e4e9))
  (fp_poly
    (pts
      (xy 6.1 0.8)
      (xy 7.3 0.8)
      (xy 7.3 0)
      (xy 6.1 0)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 30b8fa11-f080-408c-a5e8-176f6ecd566b))
  (fp_poly
    (pts
      (xy 6.1 1.9)
      (xy 7.3 1.9)
      (xy 7.3 1.1)
      (xy 6.1 1.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 90244fd2-734c-4a5d-a826-525fd1c5d2cc))
  (fp_poly
    (pts
      (xy 6.1 3)
      (xy 7.3 3)
      (xy 7.3 2.2)
      (xy 6.1 2.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 47cd3f3e-2085-4a98-8845-755572df6fa0))
  (fp_poly
    (pts
      (xy 6.1 4.1)
      (xy 7.3 4.1)
      (xy 7.3 3.3)
      (xy 6.1 3.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp d7ff373a-4deb-48cb-b4ea-630012b9d091))
  (fp_poly
    (pts
      (xy 6.1 5.2)
      (xy 7.3 5.2)
      (xy 7.3 4.4)
      (xy 6.1 4.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 015ff156-28cf-4872-8ce7-5abadcde92f8))
  (fp_poly
    (pts
      (xy 6.1 6.3)
      (xy 7.3 6.3)
      (xy 7.3 5.5)
      (xy 6.1 5.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp c510acd2-bf73-4ca1-a669-4f7612541231))
  (fp_poly
    (pts
      (xy 6.1 7.4)
      (xy 7.3 7.4)
      (xy 7.3 6.6)
      (xy 6.1 6.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 3a7defe4-9fdf-4061-9007-ccb244c44e07))
  (fp_line (start -6.35 -8.25) (end -4.88 -8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 840f2dda-f995-434a-8cfe-81ae8808db99))
  (fp_line (start -6.35 -6.4) (end -6.35 -8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp a6fd6900-5529-4184-b780-7da8b8aab9b8))
  (fp_line (start -6.35 8.25) (end -6.35 6.4)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 2d7276e1-e267-4e90-ad48-a776553e6e29))
  (fp_line (start -4.88 8.25) (end -6.35 8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 252befe5-b870-4f0d-90c3-b95ebc226006))
  (fp_line (start 4.88 -8.25) (end 6.35 -8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp b3e9a123-ffd8-4cdb-a72d-5fb63c15ae7d))
  (fp_line (start 6.35 -8.25) (end 6.35 -6.4)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 86e4defc-8cb1-41c4-9abf-79de0ff454cc))
  (fp_line (start 6.35 6.4) (end 6.35 8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp d83a3712-5da3-47d3-b339-15f7837cd1e7))
  (fp_line (start 6.35 8.25) (end 4.88 8.25)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 809d69b8-3905-4f33-a767-9f40d0e761bf))
  (fp_circle (center 6.7 8) (end 6.770709 8)
    (stroke (width 0.141418) (type solid)) (fill solid) (layer "F.SilkS") (tstamp 12391ce0-20d4-4aac-b23e-efb52b5ef4be))
  (fp_line (start -6.1 -8) (end 6.1 -8)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp addbfa89-11dc-4a5b-88de-4e5b3770a33d))
  (fp_line (start -6.1 8) (end -6.1 -8)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 914ce94b-ba69-4a09-ad4c-2f9b8d12a193))
  (fp_line (start 6.1 -8) (end 6.1 8)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 2570731d-5b11-4940-9cb7-f333e93305cf))
  (fp_line (start 6.1 8) (end -6.1 8)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 1a5e6224-8563-4d55-ad37-cabd1c26f34f))
  (fp_poly
    (pts
      (xy -6.1 -6.6)
      (xy -5.2 -6.6)
      (xy -5.2 -7.4)
      (xy -6.1 -7.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 7caa6905-4a77-499b-b23d-f0e514f7c27a))
  (fp_poly
    (pts
      (xy -6.1 -5.5)
      (xy -5.2 -5.5)
      (xy -5.2 -6.3)
      (xy -6.1 -6.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 6adc07b5-f120-45da-9a43-a58420a43c6d))
  (fp_poly
    (pts
      (xy -6.1 -4.4)
      (xy -5.2 -4.4)
      (xy -5.2 -5.2)
      (xy -6.1 -5.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 77c3cf9f-be75-4eaf-b5ef-5992efdbe043))
  (fp_poly
    (pts
      (xy -6.1 -3.3)
      (xy -5.2 -3.3)
      (xy -5.2 -4.1)
      (xy -6.1 -4.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp c9f6b496-99e7-46f3-9f30-b14a15ed730c))
  (fp_poly
    (pts
      (xy -6.1 -2.2)
      (xy -5.2 -2.2)
      (xy -5.2 -3)
      (xy -6.1 -3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp cf664ad0-4216-4806-bb15-f6ead8ca1a69))
  (fp_poly
    (pts
      (xy -6.1 0.8)
      (xy -5.2 0.8)
      (xy -5.2 0)
      (xy -6.1 0)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp ab79a31b-da13-43d1-8fc0-5293a7b0283f))
  (fp_poly
    (pts
      (xy -6.1 1.9)
      (xy -5.2 1.9)
      (xy -5.2 1.1)
      (xy -6.1 1.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp f550ef58-2af6-46b9-9d7b-55dedccc52e3))
  (fp_poly
    (pts
      (xy -6.1 3)
      (xy -5.2 3)
      (xy -5.2 2.2)
      (xy -6.1 2.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 0861041e-3dc4-4a6f-abfd-539a0cd33124))
  (fp_poly
    (pts
      (xy -6.1 4.1)
      (xy -5.2 4.1)
      (xy -5.2 3.3)
      (xy -6.1 3.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 68117d5a-13ad-4846-ac8a-07868a0fd8f6))
  (fp_poly
    (pts
      (xy -6.1 5.2)
      (xy -5.2 5.2)
      (xy -5.2 4.4)
      (xy -6.1 4.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 64a5c208-d9c9-4dce-9b22-3684b5f9df5e))
  (fp_poly
    (pts
      (xy -6.1 6.3)
      (xy -5.2 6.3)
      (xy -5.2 5.5)
      (xy -6.1 5.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp e5bf065d-4d74-4dc5-8ad4-aae77018d994))
  (fp_poly
    (pts
      (xy -6.1 7.4)
      (xy -5.2 7.4)
      (xy -5.2 6.6)
      (xy -6.1 6.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 502ba89a-361d-44f8-b8c4-0ac98c8528a4))
  (fp_poly
    (pts
      (xy 5.2 -6.6)
      (xy 6.1 -6.6)
      (xy 6.1 -7.4)
      (xy 5.2 -7.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp c47321e9-5a98-4038-b849-b3bc937e2bb1))
  (fp_poly
    (pts
      (xy 5.2 -5.5)
      (xy 6.1 -5.5)
      (xy 6.1 -6.3)
      (xy 5.2 -6.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp e6767c9a-6659-441e-868f-dbe5b9f7f6dd))
  (fp_poly
    (pts
      (xy 5.2 -4.4)
      (xy 6.1 -4.4)
      (xy 6.1 -5.2)
      (xy 5.2 -5.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 91d7a817-9a29-4feb-9a73-24579bd34608))
  (fp_poly
    (pts
      (xy 5.2 -3.3)
      (xy 6.1 -3.3)
      (xy 6.1 -4.1)
      (xy 5.2 -4.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 094a5583-1fe1-4379-975d-5d79a8d3e70f))
  (fp_poly
    (pts
      (xy 5.2 -2.2)
      (xy 6.1 -2.2)
      (xy 6.1 -3)
      (xy 5.2 -3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 048bcd16-b499-4342-8e53-a891ee786115))
  (fp_poly
    (pts
      (xy 5.2 0.8)
      (xy 6.1 0.8)
      (xy 6.1 0)
      (xy 5.2 0)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 307a7b75-e14a-4390-a3dd-8f8d8cb89950))
  (fp_poly
    (pts
      (xy 5.2 1.9)
      (xy 6.1 1.9)
      (xy 6.1 1.1)
      (xy 5.2 1.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp c6d5cedc-f66c-4da7-9080-2eeb6ad526b7))
  (fp_poly
    (pts
      (xy 5.2 3)
      (xy 6.1 3)
      (xy 6.1 2.2)
      (xy 5.2 2.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp bbbf07ce-e4f3-4ecd-95d6-b754cb875434))
  (fp_poly
    (pts
      (xy 5.2 4.1)
      (xy 6.1 4.1)
      (xy 6.1 3.3)
      (xy 5.2 3.3)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp f3ca7079-19ae-4899-81e1-e65406804b00))
  (fp_poly
    (pts
      (xy 5.2 5.2)
      (xy 6.1 5.2)
      (xy 6.1 4.4)
      (xy 5.2 4.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 12929f15-39c4-4805-b955-b5664d18f9d8))
  (fp_poly
    (pts
      (xy 5.2 6.3)
      (xy 6.1 6.3)
      (xy 6.1 5.5)
      (xy 5.2 5.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 6821cdf1-fdff-46b2-89e1-d4f02a8e3804))
  (fp_poly
    (pts
      (xy 5.2 7.4)
      (xy 6.1 7.4)
      (xy 6.1 6.6)
      (xy 5.2 6.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 5d3ce67f-3413-4132-abf9-2f690ec2bce7))
  (pad "1" smd rect (at 6 7) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 79bcc3d7-1c9c-4001-ac17-97a7ed645ce7)
  )
  (pad "2" smd rect (at 6 5.9) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 61c6b0c5-3669-46fe-9029-2bdf92d8d651)
  )
  (pad "3" smd rect (at 6 4.8) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 40da561b-fae4-47a2-bc1d-5782ba1688e2)
  )
  (pad "4" smd rect (at 6 3.7) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 208dafa8-ed36-4742-a3ad-51e9cdb378fd)
  )
  (pad "5" smd rect (at 6 2.6) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 8410ec96-543f-49fe-89ad-5ddbd55c9780)
  )
  (pad "6" smd rect (at 6 1.5) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 43c88e96-b1c1-4617-98df-6bd35aaad69a)
  )
  (pad "7" smd rect (at 6 0.4) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 09658c6a-a60f-456d-9878-11701776bdfc)
  )
  (pad "8" smd rect (at 6 -2.6) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 23fa8e7b-4485-4567-821b-adddf8b0982a)
  )
  (pad "9" smd rect (at 6 -3.7) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 45d37721-b538-4028-a3c5-43502cb5622a)
  )
  (pad "10" smd rect (at 6 -4.8) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 6f151d55-c758-44aa-a7fe-bf373bef047d)
  )
  (pad "11" smd rect (at 6 -5.9) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 3e6ed51c-c9b9-4ba5-a283-928a30d6f62f)
  )
  (pad "12" smd rect (at 6 -7) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 09f25058-c2e8-49f6-acf5-73c79adc7d55)
  )
  (pad "13" smd rect (at -6 -7 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 5c1131e0-68bb-43a3-a909-c2c4a1f41a08)
  )
  (pad "14" smd rect (at -6 -5.9 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp aabb13ac-08c2-415c-bf31-d3458f34388a)
  )
  (pad "15" smd rect (at -6 -4.8 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 91e1491c-e275-4831-a332-3f74dc9596de)
  )
  (pad "16" smd rect (at -6 -3.7 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b17733dd-d072-4d3b-b685-ab05650815be)
  )
  (pad "17" smd rect (at -6 -2.6 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp c8f9740c-30da-4883-a785-c0ee8eef060f)
  )
  (pad "18" smd rect (at -6 0.4 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 0a1cda43-6447-4ea1-a2ae-e57d85ce62d0)
  )
  (pad "19" smd rect (at -6 1.5 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 1b62baa1-83e6-4d82-b87f-ead7054535df)
  )
  (pad "20" smd rect (at -6 2.6 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp f6cfeff6-85bb-47a2-8617-90336a300bc7)
  )
  (pad "21" smd rect (at -6 3.7 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 104f0b30-1830-4528-8690-f2030f8e8eb6)
  )
  (pad "22" smd rect (at -6 4.8 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 5bb9c6d1-2d40-4e2d-871f-d20eadd1749c)
  )
  (pad "23" smd rect (at -6 5.9 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 59618fc3-8abb-42a1-9d21-d442eb41826a)
  )
  (pad "24" smd rect (at -6 7 180) (size 1.8 0.8) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp f9cc9a06-eec1-46ba-91e5-0851a855b752)
  )
)
