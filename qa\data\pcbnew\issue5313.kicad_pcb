(kicad_pcb (version 20230620) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B.Adhes" user "B.Adhesive")
    (33 "F.<PERSON>hes" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (plot_on_all_layers_selection 0x0000000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 6)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (pdf_front_fp_property_popups true)
      (pdf_back_fp_property_popups true)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "Net-(R1-Pad2)")
  (net 2 "Net-(R1-Pad1)")
  (net 3 "Net-(R2-Pad1)")

  (footprint "Resistor_SMD:R_0402_1005Metric" (layer "F.Cu")
    (tstamp 2e35e56e-39f2-4e13-a89c-33e461318e9a)
    (at 111.506 80.814)
    (descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: http://www.tortai-tech.com/upload/download/2011102023233369053.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R2" (at 0 -1.17 0) (layer "F.SilkS") (tstamp 2608013f-c60b-4903-8df3-ce47a61306c4)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "R_Small" (at 0 1.17 0) (layer "F.Fab") (tstamp 15494b9a-f470-4197-ad3b-0b6d97808b23)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp a5abcc28-1c39-450a-8448-e3e1cd674795)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp f5a06cd1-1c67-4033-beb3-8a0eee768cdd)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp bb46e987-bf20-45cc-8d9c-7075c31d5d46)
      (effects (font (size 1.27 1.27)))
    )
    (path "/1d21652e-0afd-4478-9568-7a8f5cff3100")
    (sheetfile "/home/<USER>/Downloads/test/test.kicad_sch")
    (attr smd)
    (fp_line (start -0.93 -0.47) (end 0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 08f25d02-c11d-4b34-8011-ab383f95d7a1))
    (fp_line (start -0.93 0.47) (end -0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 7aee03de-bbcd-41a7-974e-22f66503898e))
    (fp_line (start 0.93 -0.47) (end 0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 84118b63-1ccc-431c-8f94-70cc152141fd))
    (fp_line (start 0.93 0.47) (end -0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 9b5b55f1-cdfc-46ee-9072-40ab8e269d54))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b2a0f501-c07d-4d4d-8d12-bb2b32044306))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8f617df1-1490-4d84-9cb4-a56f4057882c))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3d4c44b3-a350-4f64-820e-e2815c9c62d3))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bea4d8e9-184c-47c5-85e1-c6b4d14d07b6))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 34e274c3-95e2-4821-920e-a3048fcf9646)
      (effects (font (size 0.25 0.25) (thickness 0.04)))
    )
    (pad "1" smd roundrect (at -0.485 0) (size 0.59 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "Net-(R2-Pad1)")
      (tstamp 08634bb5-252a-4644-975b-554b5d0b7e90)
    )
    (pad "2" smd roundrect (at 0.485 0) (size 0.59 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "Net-(R1-Pad2)")
      (tstamp 049859c8-78ff-420a-9dca-24bbecdc6125)
    )
    (model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0402_1005Metric" (layer "F.Cu")
    (tstamp 67a9540f-ec44-4aea-b54f-9e5d1b243986)
    (at 111.506 82.804)
    (descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: http://www.tortai-tech.com/upload/download/2011102023233369053.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R1" (at 0 -1.17 0) (layer "F.SilkS") (tstamp c5e9515e-fe30-406b-946f-78aa2287a2b2)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "R_Small" (at 0 1.17 0) (layer "F.Fab") (tstamp 843d10ec-0bf7-43d3-9c40-ec5b295ab15f)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp b7756c3f-99f1-49e5-8b80-f590e6c8f985)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp bb15da91-f43f-4784-a38e-d1dc188597a7)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 5733d4e0-fe09-42b4-8e12-7252a4705dd3)
      (effects (font (size 1.27 1.27)))
    )
    (path "/274977f8-da1c-43c6-8429-27b99eb50048")
    (sheetfile "/home/<USER>/Downloads/test/test.kicad_sch")
    (attr smd)
    (fp_line (start -0.93 -0.47) (end 0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 1709022a-1afb-4388-80b7-c1622545d5b5))
    (fp_line (start -0.93 0.47) (end -0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp d9f0b932-20d5-4274-a8e4-b885e3d96e69))
    (fp_line (start 0.93 -0.47) (end 0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 982f110a-e2dd-4179-88fc-7d0232e6cc06))
    (fp_line (start 0.93 0.47) (end -0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b8ba2f0a-f67a-46f8-a399-fafd449dee82))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 21709458-2485-4531-885d-d7c22cc8e7e6))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d4712823-4f65-44d7-b341-808aae111cba))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 69786e70-889b-425d-990f-50d0ce763842))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b6f109b1-697d-4408-9828-1e7e1a7cfd42))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 07fa7917-52dd-4b3f-95b2-a651606cd61c)
      (effects (font (size 0.25 0.25) (thickness 0.04)))
    )
    (pad "1" smd roundrect (at -0.485 0) (size 0.59 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "Net-(R1-Pad1)")
      (tstamp 647ba693-108a-46de-b52d-f093de185a75)
    )
    (pad "2" smd roundrect (at 0.485 0) (size 0.59 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "Net-(R1-Pad2)")
      (tstamp 9e4243f1-8573-419b-93f6-69fab259a67e)
    )
    (model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Resistor_SMD:R_0402_1005Metric" (layer "F.Cu")
    (tstamp f67218b8-fb92-4fe7-84fe-804d327d48d1)
    (at 114.416 80.814)
    (descr "Resistor SMD 0402 (1005 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: http://www.tortai-tech.com/upload/download/2011102023233369053.pdf), generated with kicad-footprint-generator")
    (tags "resistor")
    (property "Reference" "R3" (at 0 -1.17 0) (layer "F.SilkS") (tstamp 8c78bde3-f538-4fda-94d9-4151c6489f47)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Value" "R_Small" (at 0 1.17 0) (layer "F.Fab") (tstamp b8627364-1346-41a2-97de-6a59023bfbe1)
      (effects (font (size 1 1) (thickness 0.15)))
    )
    (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 2eb1b13f-91e7-4ffe-a278-f251b52473aa)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp a7d611da-8cb3-45ea-97e4-261becee964a)
      (effects (font (size 1.27 1.27)))
    )
    (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 7c59b7e7-dbf8-4f82-8166-221f6629c17a)
      (effects (font (size 1.27 1.27)))
    )
    (path "/2e14a2a0-1c04-42c3-9911-e34ceb1f9ad8")
    (sheetfile "/home/<USER>/Downloads/test/test.kicad_sch")
    (attr smd)
    (fp_line (start -0.93 -0.47) (end 0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp e4c6bfb5-dda1-4669-bc12-5d2df7558304))
    (fp_line (start -0.93 0.47) (end -0.93 -0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp a7912eb2-40ab-432e-b2a1-2f941927b43c))
    (fp_line (start 0.93 -0.47) (end 0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp 630e66d9-7009-4548-934e-83ab633f8b98))
    (fp_line (start 0.93 0.47) (end -0.93 0.47)
      (stroke (width 0.05) (type solid)) (layer "F.CrtYd") (tstamp b5a83110-cf69-42b3-981a-4d97509bbe30))
    (fp_line (start -0.5 -0.25) (end 0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 762b8aec-70d1-49eb-a871-8cc0e8f849ea))
    (fp_line (start -0.5 0.25) (end -0.5 -0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 112478b0-aff4-4ae0-8c96-08d8c6f3dc47))
    (fp_line (start 0.5 -0.25) (end 0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 67a2f96f-6ee4-4fa4-b57f-f6af959a7bec))
    (fp_line (start 0.5 0.25) (end -0.5 0.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 20aeb361-93b0-4262-bf53-ea45301eb3e6))
    (fp_text user "${REFERENCE}" (at 0 0 0) (layer "F.Fab") (tstamp 76ec4073-6e0a-4968-b6ad-e5bd1ac12146)
      (effects (font (size 0.25 0.25) (thickness 0.04)))
    )
    (pad "1" smd roundrect (at -0.485 0) (size 0.59 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "Net-(R2-Pad1)")
      (tstamp 1dec6977-7edb-4e58-9aaf-965641602fe2)
    )
    (pad "2" smd roundrect (at 0.485 0) (size 0.59 0.64) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "Net-(R1-Pad2)")
      (tstamp 64e6a021-8cf3-4cdf-bb07-67aa282a1721)
    )
    (model "${KISYS3DMOD}/Resistor_SMD.3dshapes/R_0402_1005Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_rect (start 73.8378 65.3288) (end 165.5064 114.554)
    (stroke (width 0.1) (type solid)) (fill none) (layer "Edge.Cuts") (tstamp 3a43118e-4b4b-4961-8500-c8729a204e86))

  (segment (start 114.8588 78.613) (end 114.192 78.613) (width 0.25) (layer "F.Cu") (net 1) (tstamp 1019e655-7cba-45e0-b987-d2115c5676f8))
  (segment (start 114.192 78.613) (end 111.991 80.814) (width 0.25) (layer "F.Cu") (net 1) (tstamp 241fb8f4-0106-4da2-95e1-08924968743f))
  (segment (start 114.901 78.6552) (end 114.8588 78.613) (width 0.25) (layer "F.Cu") (net 1) (tstamp 5f443ecc-6dc8-4d65-b2d4-2702bf1cb330))
  (segment (start 114.901 80.814) (end 114.901 78.6552) (width 0.25) (layer "F.Cu") (net 1) (tstamp ffad814b-0ff2-41ae-90f6-2ece8b4df7ab))
  (segment (start 109.728 81.661) (end 109.728 83.1342) (width 0.25) (layer "F.Cu") (net 3) (tstamp 0aeb92fb-b7d4-4745-af62-7f8840eb9dc7))
  (segment (start 112.8776 83.82) (end 113.931 82.7666) (width 0.25) (layer "F.Cu") (net 3) (tstamp 1f8a8106-f867-43c0-8011-c4329d79c359))
  (segment (start 109.728 83.1342) (end 110.4138 83.82) (width 0.25) (layer "F.Cu") (net 3) (tstamp 552d2183-6b4a-48ac-b7a2-83e116b69a88))
  (segment (start 113.931 82.7666) (end 113.931 80.814) (width 0.25) (layer "F.Cu") (net 3) (tstamp 593326f5-d1c7-40f2-a9a6-7fb3932bd744))
  (segment (start 110.4138 83.82) (end 112.8776 83.82) (width 0.25) (layer "F.Cu") (net 3) (tstamp 90d8961f-6a71-4b3b-b328-ce0412ab50ca))
  (segment (start 110.575 80.814) (end 109.728 81.661) (width 0.25) (layer "F.Cu") (net 3) (tstamp 988b05d5-906a-4504-9abb-d49688a4d719))
  (segment (start 111.021 80.814) (end 110.575 80.814) (width 0.25) (layer "F.Cu") (net 3) (tstamp fa845dd2-b8cf-4b8f-9b36-bf547bd2b518))

  (zone (net 3) (net_name "Net-(R2-Pad1)") (layer "F.Cu") (tstamp cede45cc-3345-43eb-a4ae-2346c54fbdc8) (hatch edge 0.508)
    (connect_pads (clearance 0.508))
    (min_thickness 0.254) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.508) (thermal_bridge_width 0.508))
    (polygon
      (pts
        (xy 119.126 76.7334)
        (xy 118.6434 88.9254)
        (xy 105.5878 89.1794)
        (xy 105.8672 76.8858)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy 119.061752 76.754142)
        (xy 119.108858 76.80726)
        (xy 119.120755 76.8659)
        (xy 118.648097 88.806718)
        (xy 118.625417 88.873994)
        (xy 118.569964 88.918328)
        (xy 118.524647 88.92771)
        (xy 105.719204 89.176843)
        (xy 105.650707 89.15817)
        (xy 105.603179 89.105429)
        (xy 105.590786 89.048004)
        (xy 105.59352 88.92771)
        (xy 105.738121 82.56525)
        (xy 110.2175 82.56525)
        (xy 110.217501 83.042748)
        (xy 110.220426 83.079935)
        (xy 110.266669 83.239104)
        (xy 110.351036 83.381761)
        (xy 110.351042 83.381768)
        (xy 110.468231 83.498957)
        (xy 110.468238 83.498963)
        (xy 110.610895 83.58333)
        (xy 110.610898 83.58333)
        (xy 110.6109 83.583332)
        (xy 110.770063 83.629573)
        (xy 110.807253 83.6325)
        (xy 111.234746 83.632499)
        (xy 111.234748 83.632499)
        (xy 111.253341 83.631036)
        (xy 111.271937 83.629573)
        (xy 111.4311 83.583332)
        (xy 111.441859 83.576968)
        (xy 111.510671 83.559508)
        (xy 111.570138 83.576967)
        (xy 111.5809 83.583332)
        (xy 111.740063 83.629573)
        (xy 111.777253 83.6325)
        (xy 112.204746 83.632499)
        (xy 112.204748 83.632499)
        (xy 112.223341 83.631036)
        (xy 112.241937 83.629573)
        (xy 112.4011 83.583332)
        (xy 112.401102 83.58333)
        (xy 112.401104 83.58333)
        (xy 112.543761 83.498963)
        (xy 112.54376 83.498963)
        (xy 112.543763 83.498962)
        (xy 112.660962 83.381763)
        (xy 112.745332 83.2391)
        (xy 112.791573 83.079937)
        (xy 112.7945 83.042747)
        (xy 112.794499 82.565254)
        (xy 112.791573 82.528063)
        (xy 112.745332 82.3689)
        (xy 112.74533 82.368898)
        (xy 112.74533 82.368895)
        (xy 112.660963 82.226238)
        (xy 112.660957 82.226231)
        (xy 112.543768 82.109042)
        (xy 112.543761 82.109036)
        (xy 112.401104 82.024669)
        (xy 112.241942 81.978428)
        (xy 112.241938 81.978427)
        (xy 112.241937 81.978427)
        (xy 112.241935 81.978426)
        (xy 112.24193 81.978426)
        (xy 112.204749 81.9755)
        (xy 112.204747 81.9755)
        (xy 111.990784 81.9755)
        (xy 111.777252 81.975501)
        (xy 111.740062 81.978427)
        (xy 111.580902 82.024666)
        (xy 111.580899 82.024667)
        (xy 111.570136 82.031033)
        (xy 111.501319 82.048491)
        (xy 111.441861 82.031032)
        (xy 111.431101 82.024668)
        (xy 111.271942 81.978428)
        (xy 111.271938 81.978427)
        (xy 111.271937 81.978427)
        (xy 111.271935 81.978426)
        (xy 111.27193 81.978426)
        (xy 111.234749 81.9755)
        (xy 111.234747 81.9755)
        (xy 111.021056 81.9755)
        (xy 110.807252 81.975501)
        (xy 110.770064 81.978426)
        (xy 110.610895 82.024669)
        (xy 110.468238 82.109036)
        (xy 110.468231 82.109042)
        (xy 110.351042 82.226231)
        (xy 110.351036 82.226238)
        (xy 110.266669 82.368895)
        (xy 110.220428 82.528057)
        (xy 110.220426 82.528069)
        (xy 110.2175 82.56525)
        (xy 105.738121 82.56525)
        (xy 105.77215 81.068)
        (xy 110.219204 81.068)
        (xy 110.220923 81.089855)
        (xy 110.220923 81.089856)
        (xy 110.26713 81.248901)
        (xy 110.351436 81.391453)
        (xy 110.351439 81.391457)
        (xy 110.468542 81.50856)
        (xy 110.468546 81.508563)
        (xy 110.611098 81.592869)
        (xy 110.766999 81.638162)
        (xy 110.767 81.068)
        (xy 110.219204 81.068)
        (xy 105.77215 81.068)
        (xy 105.783348 80.57525)
        (xy 111.1875 80.57525)
        (xy 111.187501 81.052748)
        (xy 111.190427 81.089937)
        (xy 111.236666 81.249097)
        (xy 111.257453 81.284246)
        (xy 111.274999 81.348384)
        (xy 111.274999 81.638163)
        (xy 111.430899 81.59287)
        (xy 111.441367 81.586679)
        (xy 111.510183 81.569218)
        (xy 111.569645 81.586676)
        (xy 111.5809 81.593332)
        (xy 111.740063 81.639573)
        (xy 111.777253 81.6425)
        (xy 112.204746 81.642499)
        (xy 112.204748 81.642499)
        (xy 112.223341 81.641036)
        (xy 112.241937 81.639573)
        (xy 112.4011 81.593332)
        (xy 112.401102 81.59333)
        (xy 112.401104 81.59333)
        (xy 112.543761 81.508963)
        (xy 112.54376 81.508963)
        (xy 112.543763 81.508962)
        (xy 112.660962 81.391763)
        (xy 112.689085 81.344208)
        (xy 112.74533 81.249104)
        (xy 112.74533 81.249102)
        (xy 112.745332 81.2491)
        (xy 112.791573 81.089937)
        (xy 112.7933 81.068)
        (xy 113.129204 81.068)
        (xy 113.130923 81.089855)
        (xy 113.130923 81.089856)
        (xy 113.17713 81.248901)
        (xy 113.261436 81.391453)
        (xy 113.261439 81.391457)
        (xy 113.378542 81.50856)
        (xy 113.378546 81.508563)
        (xy 113.521097 81.592868)
        (xy 113.677 81.638162)
        (xy 113.677 81.068)
        (xy 113.129204 81.068)
        (xy 112.7933 81.068)
        (xy 112.7945 81.052747)
        (xy 112.794499 80.958592)
        (xy 112.8145 80.890473)
        (xy 112.831395 80.869507)
        (xy 113.104 80.596902)
        (xy 113.16631 80.562879)
        (xy 113.193093 80.56)
        (xy 113.677 80.56)
        (xy 113.677 80.076094)
        (xy 113.697002 80.007973)
        (xy 113.713905 79.986999)
        (xy 114.052405 79.648499)
        (xy 114.114717 79.614473)
        (xy 114.185532 79.619538)
        (xy 114.242368 79.662085)
        (xy 114.267179 79.728605)
        (xy 114.2675 79.737594)
        (xy 114.2675 79.864935)
        (xy 114.247498 79.933056)
        (xy 114.217152 79.965696)
        (xy 114.185 79.989836)
        (xy 114.185 80.279614)
        (xy 114.167454 80.343752)
        (xy 114.146668 80.378898)
        (xy 114.100428 80.538057)
        (xy 114.100426 80.538069)
        (xy 114.0975 80.57525)
        (xy 114.097501 81.052748)
        (xy 114.100426 81.089935)
        (xy 114.146669 81.249104)
        (xy 114.167453 81.284247)
        (xy 114.185 81.348386)
        (xy 114.184999 81.638162)
        (xy 114.340899 81.59287)
        (xy 114.351367 81.586679)
        (xy 114.420183 81.569218)
        (xy 114.479645 81.586676)
        (xy 114.4909 81.593332)
        (xy 114.650063 81.639573)
        (xy 114.687253 81.6425)
        (xy 115.114746 81.642499)
        (xy 115.114748 81.642499)
        (xy 115.133341 81.641036)
        (xy 115.151937 81.639573)
        (xy 115.3111 81.593332)
        (xy 115.311102 81.59333)
        (xy 115.311104 81.59333)
        (xy 115.453761 81.508963)
        (xy 115.45376 81.508963)
        (xy 115.453763 81.508962)
        (xy 115.570962 81.391763)
        (xy 115.599085 81.344208)
        (xy 115.65533 81.249104)
        (xy 115.65533 81.249102)
        (xy 115.655332 81.2491)
        (xy 115.701573 81.089937)
        (xy 115.7045 81.052747)
        (xy 115.704499 80.575254)
        (xy 115.701573 80.538063)
        (xy 115.655332 80.3789)
        (xy 115.65533 80.378898)
        (xy 115.65533 80.378895)
        (xy 115.570963 80.236238)
        (xy 115.566101 80.22997)
        (xy 115.567253 80.229076)
        (xy 115.537379 80.174367)
        (xy 115.5345 80.147584)
        (xy 115.5345 79.614473)
        (xy 115.534499 78.739043)
        (xy 115.536249 78.723211)
        (xy 115.535956 78.723184)
        (xy 115.536702 78.715291)
        (xy 115.5345 78.645228)
        (xy 115.5345 78.61535)
        (xy 115.5345 78.615344)
        (xy 115.53362 78.608382)
        (xy 115.533156 78.602493)
        (xy 115.531674 78.555311)
        (xy 115.526017 78.535842)
        (xy 115.522012 78.516498)
        (xy 115.519474 78.496403)
        (xy 115.5021 78.452522)
        (xy 115.500181 78.446916)
        (xy 115.487018 78.401607)
        (xy 115.476706 78.38417)
        (xy 115.46801 78.366421)
        (xy 115.460552 78.347583)
        (xy 115.432812 78.309403)
        (xy 115.429564 78.304458)
        (xy 115.405542 78.263838)
        (xy 115.391214 78.24951)
        (xy 115.378384 78.234489)
        (xy 115.366472 78.218093)
        (xy 115.344685 78.200069)
        (xy 115.341715 78.197451)
        (xy 115.299718 78.158013)
        (xy 115.278577 78.136871)
        (xy 115.278572 78.136866)
        (xy 115.273025 78.132563)
        (xy 115.268517 78.128712)
        (xy 115.234125 78.096417)
        (xy 115.234119 78.096413)
        (xy 115.216363 78.086651)
        (xy 115.199847 78.075802)
        (xy 115.183841 78.063386)
        (xy 115.153089 78.050078)
        (xy 115.14054 78.044648)
        (xy 115.135208 78.042036)
        (xy 115.093861 78.019305)
        (xy 115.074236 78.014266)
        (xy 115.055536 78.007864)
        (xy 115.036945 77.999819)
        (xy 115.036943 77.999818)
        (xy 115.036942 77.999818)
        (xy 114.990342 77.992437)
        (xy 114.984529 77.991233)
        (xy 114.93883 77.9795)
        (xy 114.918576 77.9795)
        (xy 114.898866 77.977949)
        (xy 114.878857 77.97478)
        (xy 114.878856 77.97478)
        (xy 114.831883 77.97922)
        (xy 114.82595 77.9795)
        (xy 114.275853 77.9795)
        (xy 114.260011 77.97775)
        (xy 114.259984 77.978044)
        (xy 114.252092 77.977298)
        (xy 114.252091 77.977298)
        (xy 114.182042 77.9795)
        (xy 114.152144 77.9795)
        (xy 114.15214 77.9795)
        (xy 114.15213 77.979501)
        (xy 114.145179 77.980379)
        (xy 114.139267 77.980844)
        (xy 114.092113 77.982326)
        (xy 114.092111 77.982327)
        (xy 114.072656 77.987978)
        (xy 114.053303 77.991986)
        (xy 114.033211 77.994524)
        (xy 114.033204 77.994525)
        (xy 114.033203 77.994526)
        (xy 114.033201 77.994526)
        (xy 114.0332 77.994527)
        (xy 113.989339 78.011892)
        (xy 113.983724 78.013815)
        (xy 113.938407 78.026982)
        (xy 113.920964 78.037297)
        (xy 113.903218 78.04599)
        (xy 113.884382 78.053448)
        (xy 113.846209 78.081181)
        (xy 113.841248 78.08444)
        (xy 113.800638 78.108458)
        (xy 113.786311 78.122784)
        (xy 113.771285 78.135617)
        (xy 113.754895 78.147525)
        (xy 113.754893 78.147527)
        (xy 113.724808 78.183892)
        (xy 113.720812 78.188283)
        (xy 111.960498 79.948595)
        (xy 111.898186 79.982621)
        (xy 111.871404 79.9855)
        (xy 111.777252 79.985501)
        (xy 111.740062 79.988427)
        (xy 111.580906 80.034665)
        (xy 111.580902 80.034667)
        (xy 111.569641 80.041326)
        (xy 111.500823 80.058781)
        (xy 111.44137 80.041322)
        (xy 111.430901 80.03513)
        (xy 111.274999 79.989836)
        (xy 111.274999 80.279614)
        (xy 111.257454 80.343751)
        (xy 111.236666 80.378901)
        (xy 111.236666 80.378902)
        (xy 111.190429 80.538054)
        (xy 111.190426 80.538069)
        (xy 111.1875 80.57525)
        (xy 105.783348 80.57525)
        (xy 105.783695 80.56)
        (xy 110.219204 80.56)
        (xy 110.767 80.56)
        (xy 110.766999 79.989836)
        (xy 110.611097 80.035131)
        (xy 110.468546 80.119436)
        (xy 110.468542 80.119439)
        (xy 110.351439 80.236542)
        (xy 110.351436 80.236546)
        (xy 110.26713 80.379098)
        (xy 110.220923 80.538143)
        (xy 110.220923 80.538144)
        (xy 110.219204 80.56)
        (xy 105.783695 80.56)
        (xy 105.864433 77.007527)
        (xy 105.885977 76.93988)
        (xy 105.940675 76.894619)
        (xy 105.988949 76.8844)
        (xy 118.993406 76.734924)
      )
    )
  )
)
