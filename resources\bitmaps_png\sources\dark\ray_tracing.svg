<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   id="Слой_1"
   data-name="Слой 1"
   viewBox="0 0 24 24"
   version="1.1"
   sodipodi:docname="ray_tracing.svg"
   inkscape:version="1.4 (e7c3feb, 2024-10-09)"
   xml:space="preserve"
   width="24"
   height="24"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:dc="http://purl.org/dc/elements/1.1/"><sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="2560"
     inkscape:window-height="1570"
     id="namedview30"
     showgrid="false"
     inkscape:zoom="4"
     inkscape:cx="1.5"
     inkscape:cy="66.75"
     inkscape:window-x="0"
     inkscape:window-y="30"
     inkscape:window-maximized="1"
     inkscape:document-rotation="0"
     inkscape:current-layer="Слой_1"
     inkscape:showpageshadow="2"
     inkscape:pagecheckerboard="0"
     inkscape:deskcolor="#d1d1d1"
     showguides="true"><inkscape:grid
       type="xygrid"
       id="grid_kicad"
       spacingx="0.5"
       spacingy="0.5"
       color="#9999ff"
       opacity="0.13"
       empspacing="2"
       originx="-102"
       originy="-102"
       units="px"
       visible="false" /><inkscape:page
       x="0"
       y="0"
       width="24"
       height="24"
       id="page89"
       margin="0"
       bleed="0" /></sodipodi:namedview><metadata
     id="metadata43"><rdf:RDF><cc:Work
         rdf:about=""><cc:license
           rdf:resource="http://creativecommons.org/licenses/by-sa/4.0/" /><dc:format>image/svg+xml</dc:format><dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" /><dc:title>about</dc:title></cc:Work><cc:License
         rdf:about="http://creativecommons.org/licenses/by-sa/4.0/"><cc:permits
           rdf:resource="http://creativecommons.org/ns#Reproduction" /><cc:permits
           rdf:resource="http://creativecommons.org/ns#Distribution" /><cc:requires
           rdf:resource="http://creativecommons.org/ns#Notice" /><cc:requires
           rdf:resource="http://creativecommons.org/ns#Attribution" /><cc:permits
           rdf:resource="http://creativecommons.org/ns#DerivativeWorks" /><cc:requires
           rdf:resource="http://creativecommons.org/ns#ShareAlike" /></cc:License></rdf:RDF></metadata><defs
     id="defs60"><style
       id="style58">.cls-1,.cls-2{fill:none;stroke:#ded3dd;stroke-linecap:round;stroke-linejoin:round;}.cls-1{stroke-width:2px;}.cls-2{stroke-width:4px;}</style><style
       id="style28017">.cls-1{fill:none;stroke:#ded3dd;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}.cls-2{fill:#42b8eb;}</style><style
       id="style220">.cls-1{fill:none;stroke:#f2647e;stroke-linecap:round;stroke-linejoin:round;}.cls-2{fill:#d8d8d8;}.cls-3{fill:#333;}.cls-4{fill:#ded3dd;}.cls-5{fill:#42b8eb;}.cls-6{fill:#848484;}.cls-7{fill:#f2647e;}</style><style
       id="style115733">.cls-1{fill:none;stroke:#ded3dd;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}.cls-2{fill:#42b8eb;}</style><style
       id="style115838">.cls-1{fill:#ded3dd;}.cls-2{fill:#42b8eb;}</style><style
       id="style944">.cls-1{fill:none;stroke:#f2647e;stroke-linecap:round;stroke-linejoin:round;}.cls-2{fill:#f2647e;}.cls-3{fill:#848484;}.cls-4{fill:#ded3dd;}</style><style
       id="style115920">.cls-1{fill:#42b8eb;}.cls-2{fill:#ded3dd;}</style><style
       id="style27838">.cls-1{fill:#848484;}.cls-2{fill:#ded3dd;}.cls-3,.cls-6{fill:none;}.cls-3{stroke:#848484;stroke-miterlimit:10;stroke-width:0.9553px;}.cls-4{fill:#42b8eb;}.cls-5{fill:#f2647e;}.cls-6{stroke:#f5f5f5;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style><style
       id="style116483">.cls-1{fill:#848484;}.cls-2,.cls-3,.cls-4,.cls-7{fill:none;stroke-linecap:round;stroke-linejoin:round;}.cls-2{stroke:#f2647e;}.cls-2,.cls-3,.cls-4{stroke-width:0.9549px;}.cls-3{stroke:#42b8eb;}.cls-4{stroke:#0f71a8;}.cls-5{fill:#42b8eb;}.cls-6{fill:#f2647e;}.cls-7{stroke:#f5f5f5;stroke-width:2px;}</style><style
       id="style24549">.cls-1{fill:#ded3dd;}</style><clipPath
       clipPathUnits="userSpaceOnUse"
       id="clipPath75"><rect
         style="fill:#000000;fill-opacity:1;stroke:none;stroke-width:1;stroke-linecap:butt;stroke-opacity:1"
         id="rect76"
         width="10.999999"
         height="20"
         x="23"
         y="46" /></clipPath><style
       id="style158807">.cls-1{fill:#848484;}.cls-2{fill:#f29100;}.cls-3{fill:#ded3dd;}</style><style
       id="style10156">.cls-1{fill:#ded3dd;}.cls-2{fill:#848484;}</style><style
       id="style2419">.cls-1{fill:#ededed;}.cls-2{fill:#d8d8d8;}.cls-3{fill:#848484;}.cls-4{fill:#42b8eb;}.cls-5,.cls-7{fill:none;stroke-linecap:round;stroke-linejoin:round;}.cls-5{stroke:#ded3dd;}.cls-6{fill:#f2647e;}.cls-7{stroke:#f5f5f5;stroke-width:2px;}</style><style
       id="style117073">.cls-1{fill:#848484;}.cls-2{fill:#ded3dd;}.cls-3{fill:#f2647e;}.cls-4{fill:none;stroke:#f5f5f5;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style><style
       id="style117073-1">.cls-1{fill:#848484;}.cls-2{fill:#ded3dd;}.cls-3{fill:#f2647e;}.cls-4{fill:none;stroke:#f5f5f5;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}</style><style
       id="style2419-6">.cls-1{fill:#ededed;}.cls-2{fill:#d8d8d8;}.cls-3{fill:#848484;}.cls-4{fill:#42b8eb;}.cls-5,.cls-7{fill:none;stroke-linecap:round;stroke-linejoin:round;}.cls-5{stroke:#ded3dd;}.cls-6{fill:#f2647e;}.cls-7{stroke:#f5f5f5;stroke-width:2px;}</style><style
       id="style27079">.cls-1{fill:#848484;}.cls-2{fill:none;stroke:#42b8eb;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}.cls-3{fill:#fff;}.cls-4{fill:#f2647e;}</style><style
       id="style27997">.cls-1{fill:#8f8f8f;stroke:#DED3DD;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}.cls-2{fill:#42B8EB;}</style><style
       id="style2419-62">.cls-1{fill:#ededed;}.cls-2{fill:#d8d8d8;}.cls-3{fill:#8f8f8f;}.cls-4{fill:#42B8EB;}.cls-5,.cls-7{fill:none;stroke-linecap:round;stroke-linejoin:round;}.cls-5{stroke:#DED3DD;}.cls-6{fill:#f2647e;}.cls-7{stroke:#ded3dd;stroke-width:2px;}</style><style
       id="style158484">.cls-1{fill:#ededed;}.cls-2{fill:#d8d8d8;}.cls-3{fill:#8f8f8f;}.cls-4{fill:#42B8EB;}.cls-5,.cls-7{fill:none;stroke-linecap:round;stroke-linejoin:round;}.cls-5{stroke:#DED3DD;}.cls-6{fill:#f2647e;}.cls-7{stroke:#ded3dd;stroke-width:2px;}</style><style
       id="style24549-2">.cls-1{fill:#DED3DD;}</style></defs><title
     id="title62">about</title><style
     type="text/css"
     id="style833">
	.st0{fill:#ded3dd;}
	.st1{fill:none;stroke:#ded3dd;}
</style><g
     id="g2"
     transform="translate(190,-112)"
     style="display:inline" /><g
     id="g61"
     transform="translate(190,-112)"
     style="display:inline" /><g
     id="g62"
     transform="translate(190,-112)"
     style="display:inline" /><g
     id="g63"
     transform="translate(190,-112)"
     style="display:inline" /><rect
     style="display:inline;fill:#ded3dd;fill-opacity:1;stroke-width:1.71153"
     id="rect2"
     width="7.9999995"
     height="7.9999995"
     x="112"
     y="120"
     transform="translate(-112,-112)" /><g
     id="g5"
     clip-path="url(#clipPath75)"
     transform="translate(-10,-44)"
     style="display:inline"><path
       style="fill:#848484;fill-opacity:1;stroke:#42b8eb;stroke-width:1;stroke-linecap:butt;stroke-opacity:1"
       d="M 16,56 36,66"
       id="path2"
       sodipodi:nodetypes="cc" /><path
       style="fill:#848484;fill-opacity:1;stroke:#42b8eb;stroke-width:1;stroke-linecap:butt;stroke-opacity:1"
       d="M 16,56 36,46"
       id="path3"
       sodipodi:nodetypes="cc" /><path
       style="fill:#848484;fill-opacity:1;stroke:#42b8eb;stroke-width:1;stroke-linecap:butt;stroke-opacity:1"
       d="M 16,56 36,52.666667"
       id="path4"
       sodipodi:nodetypes="cc" /><path
       style="fill:#848484;fill-opacity:1;stroke:#42b8eb;stroke-width:1;stroke-linecap:butt;stroke-opacity:1"
       d="m 16,56 20,3.333333"
       id="path5"
       sodipodi:nodetypes="cc" /></g><path
     id="path6"
     style="display:inline;fill:#ded3dd;fill-opacity:1;stroke:none;stroke-width:1;stroke-linecap:round"
     d="M 124,120.44141 116.88281,124 124,127.55859 V 124 Z"
     transform="translate(-112,-112)" /><style
     type="text/css"
     id="style833-0">
	.st0{fill:#727272;stroke:#DED3DD;stroke-linecap:round;stroke-linejoin:bevel;}
	.st1{fill:#ded3dd;}
	.st2{fill:#DED3DD;}
	.st3{fill:none;stroke:#727272;stroke-linejoin:round;}
</style></svg>
