(kicad_pcb
	(version 20231212)
	(generator "pcbnew")
	(generator_version "7.99")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(31 "B.Cu" signal)
		(32 "B.Adhes" user "B.Adhesive")
		(33 "F<PERSON>hes" user "F.Adhesive")
		(34 "B.Paste" user)
		(35 "F.Paste" user)
		(36 "B.SilkS" user "B.Silkscreen")
		(37 "F.<PERSON>" user "F.Silkscreen")
		(38 "B.Mask" user)
		(39 "F.Mask" user)
		(40 "Dwgs.User" user "User.Drawings")
		(41 "Cmts.User" user "User.Comments")
		(42 "Eco1.User" user "User.Eco1")
		(43 "Eco2.User" user "User.Eco2")
		(44 "Edge.Cuts" user)
		(45 "Margin" user)
		(46 "B.CrtYd" user "B.Courtyard")
		(47 "F.CrtYd" user "F.Courtyard")
		(48 "B.Fab" user)
		(49 "F.Fab" user)
		(50 "User.1" user)
		(51 "User.2" user)
		(52 "User.3" user)
		(53 "User.4" user)
		(54 "User.5" user)
		(55 "User.6" user)
		(56 "User.7" user)
		(57 "User.8" user)
		(58 "User.9" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(pcbplotparams
			(layerselection 0x00010fc_ffffffff)
			(plot_on_all_layers_selection 0x0000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(viasonmask no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotreference yes)
			(plotvalue yes)
			(plotfptext yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(footprint "Capacitor_THT:CP_Axial_L10.0mm_D4.5mm_P15.00mm_Horizontal"
		(locked yes)
		(layer "F.Cu")
		(uuid "0775cd70-2e84-4592-a160-456c37a8f4f6")
		(at 100 100)
		(descr "CP, Axial series, Axial, Horizontal, pin pitch=15mm, , length*diameter=10*4.5mm^2, Electrolytic Capacitor, , http://www.vishay.com/docs/28325/021asm.pdf")
		(tags "CP Axial series Axial Horizontal pin pitch 15mm  length 10mm diameter 4.5mm Electrolytic Capacitor")
		(property "Reference" "REF**"
			(at 7.5 -3.37 0)
			(layer "F.SilkS")
			(uuid "c7796bf7-7f1d-4683-9284-a87c0fa50414")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "CP_Axial_L10.0mm_D4.5mm_P15.00mm_Horizontal"
			(at 7.5 3.37 0)
			(layer "F.Fab")
			(uuid "ece75a6c-bfca-4489-8477-2c64abeaee25")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Footprint" "Capacitor_THT:CP_Axial_L10.0mm_D4.5mm_P15.00mm_Horizontal"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "149443aa-7880-4c28-bc68-600b6a20727d")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "16b40231-b48b-439b-894f-c65a10c9d7ec")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "a4cd6670-1b9b-4328-a0cf-89b4f85c405d")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(attr through_hole)
		(fp_line
			(start 0.63 -2.2)
			(end 2.13 -2.2)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "f734bc6e-eade-463b-bbc5-8c9385488bb2")
		)
		(fp_line
			(start 1.24 0)
			(end 2.38 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4a49d950-d288-463b-bfd0-aa51dad75a83")
		)
		(fp_line
			(start 1.38 -2.95)
			(end 1.38 -1.45)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d89f467e-bfc4-4f07-917b-87b31ceb063b")
		)
		(fp_line
			(start 2.38 -2.37)
			(end 2.38 2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d0f40116-6f54-4f8f-850f-97e5e556a46c")
		)
		(fp_line
			(start 2.38 -2.37)
			(end 3.88 -2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "52c40338-2f64-4475-ab16-33fb441364d7")
		)
		(fp_line
			(start 2.38 2.37)
			(end 3.88 2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "da297c31-d1c0-4e69-ae37-fa59cb15d5c2")
		)
		(fp_line
			(start 3.88 -2.37)
			(end 4.63 -1.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "b5aeb1ba-7fe3-42e5-8e19-eb2e78ad1729")
		)
		(fp_line
			(start 3.88 2.37)
			(end 4.63 1.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "bfcfbfd9-5c54-43cf-94dc-64295d9f333f")
		)
		(fp_line
			(start 4.63 -1.62)
			(end 5.38 -2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "364977d2-d763-4431-a57d-713d42cde94b")
		)
		(fp_line
			(start 4.63 1.62)
			(end 5.38 2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "61edbc7c-fdb3-4f8a-9ec9-6258d80a2b97")
		)
		(fp_line
			(start 5.38 -2.37)
			(end 12.62 -2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e1aedee6-3ccf-4d72-af45-f6425676ae19")
		)
		(fp_line
			(start 5.38 2.37)
			(end 12.62 2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "42afa6f0-ff7b-4fea-8dc6-cfc6328950fe")
		)
		(fp_line
			(start 12.62 -2.37)
			(end 12.62 2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "341b7bd3-5bfe-4120-9b8b-d7ad08d9d22b")
		)
		(fp_line
			(start 13.76 0)
			(end 12.62 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8c498845-e340-4ce7-8687-59cd7f163d12")
		)
		(fp_line
			(start -1.25 -2.5)
			(end -1.25 2.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "a82813f4-5a82-46a5-b0ed-d99d270f2c50")
		)
		(fp_line
			(start -1.25 2.5)
			(end 16.25 2.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d16095a6-e7a5-4f9f-aa36-ac3efb1cddd5")
		)
		(fp_line
			(start 16.25 -2.5)
			(end -1.25 -2.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "e19ae057-cc01-4dd4-bc08-b66c2d970d1b")
		)
		(fp_line
			(start 16.25 2.5)
			(end 16.25 -2.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "aa169630-8b12-4006-8034-124e9a1aac1c")
		)
		(fp_line
			(start 0 0)
			(end 2.5 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "fa5b53b5-ed53-471e-81ae-fbfc65047ee4")
		)
		(fp_line
			(start 2.5 -2.25)
			(end 2.5 2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "62bb9c06-d53e-4303-8351-7bd8869260f6")
		)
		(fp_line
			(start 2.5 -2.25)
			(end 3.88 -2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "82c75686-44b6-4595-85cf-d45ce37c2ef6")
		)
		(fp_line
			(start 2.5 2.25)
			(end 3.88 2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "02f34a6a-50bb-42ae-a109-af0d77c34b81")
		)
		(fp_line
			(start 3.88 -2.25)
			(end 4.63 -1.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c4e242e8-aa46-4e77-a141-f6e002ab9dd0")
		)
		(fp_line
			(start 3.88 2.25)
			(end 4.63 1.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ff361eb8-bba5-4d69-8b8a-e6a31ccd04fa")
		)
		(fp_line
			(start 3.9 0)
			(end 5.4 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4968882e-8839-4c9a-8c24-1b7ee1766cf2")
		)
		(fp_line
			(start 4.63 -1.5)
			(end 5.38 -2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bbfbffed-27b0-46ba-a6e9-ab69e691c9db")
		)
		(fp_line
			(start 4.63 1.5)
			(end 5.38 2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6a302705-ffc0-4446-8463-0d52a79c0b34")
		)
		(fp_line
			(start 4.65 -0.75)
			(end 4.65 0.75)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "0e3259e2-91a3-4091-be7a-958c7975eae0")
		)
		(fp_line
			(start 5.38 -2.25)
			(end 12.5 -2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "542a98b5-2eb0-465c-91ee-fc6a62627285")
		)
		(fp_line
			(start 5.38 2.25)
			(end 12.5 2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bd87bfe8-864a-4c10-8d26-0796b3c2937e")
		)
		(fp_line
			(start 12.5 -2.25)
			(end 12.5 2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "067d8a2a-1e4c-40b8-a00e-bf381d69eb12")
		)
		(fp_line
			(start 15 0)
			(end 12.5 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "d1a50356-7868-4d84-8b9b-ae9a3a69475a")
		)
		(fp_text user "${REFERENCE}"
			(at 7.5 0 0)
			(layer "F.Fab")
			(uuid "dec732a7-ef82-4a0a-a417-eb90fc433e4d")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0)
			(size 2 2)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(uuid "8b8b2c19-35c8-4457-9957-3cf11d0cf41c")
		)
		(pad "2" thru_hole oval
			(at 15 0)
			(size 2 2)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(uuid "dc1273b8-0dfe-485c-87a9-74e7a65f6f0c")
		)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/CP_Axial_L10.0mm_D4.5mm_P15.00mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "Capacitor_THT:CP_Axial_L10.0mm_D4.5mm_P15.00mm_Horizontal"
		(layer "F.Cu")
		(uuid "898cf321-03c7-40bb-8d78-4bc5e52986c2")
		(at 100 80)
		(descr "CP, Axial series, Axial, Horizontal, pin pitch=15mm, , length*diameter=10*4.5mm^2, Electrolytic Capacitor, , http://www.vishay.com/docs/28325/021asm.pdf")
		(tags "CP Axial series Axial Horizontal pin pitch 15mm  length 10mm diameter 4.5mm Electrolytic Capacitor")
		(property "Reference" "REF**"
			(at 7.5 -3.37 0)
			(layer "F.SilkS")
			(uuid "c4cc01e3-4f4f-4a74-859f-4fd156f18bf8")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "CP_Axial_L10.0mm_D4.5mm_P15.00mm_Horizontal"
			(at 7.5 3.37 0)
			(layer "F.Fab")
			(uuid "189a9743-3373-4572-803a-1d303d95444c")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Footprint" "Capacitor_THT:CP_Axial_L10.0mm_D4.5mm_P15.00mm_Horizontal"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "dd1d8ff5-4507-41a6-a60b-fecc59829710")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "f2bca7a4-00f1-40f4-881f-2d0d8bc43b81")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "95622434-3cc9-4767-94b4-0c82c968cba8")
			(effects
				(font
					(size 1.27 1.27)
				)
			)
		)
		(attr through_hole)
		(fp_line
			(start 0.63 -2.2)
			(end 2.13 -2.2)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "1324508b-6b90-4eb8-92c6-cb88d8db1d7b")
		)
		(fp_line
			(start 1.24 0)
			(end 2.38 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c5c2de8c-bfb7-4892-95f4-************")
		)
		(fp_line
			(start 1.38 -2.95)
			(end 1.38 -1.45)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "24ef4f0f-b641-4dc9-8587-f651793d320e")
		)
		(fp_line
			(start 2.38 -2.37)
			(end 2.38 2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "088a2a73-0c9e-4485-af70-631bf726b35a")
		)
		(fp_line
			(start 2.38 -2.37)
			(end 3.88 -2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "3ae125c7-dc4f-4762-9901-4b64a01dc7e8")
		)
		(fp_line
			(start 2.38 2.37)
			(end 3.88 2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "6692fd81-ceee-4db2-9c23-b967b430f192")
		)
		(fp_line
			(start 3.88 -2.37)
			(end 4.63 -1.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "d96809a6-d074-4310-ae56-ea585b5e33f8")
		)
		(fp_line
			(start 3.88 2.37)
			(end 4.63 1.62)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "8f8bdb9a-406e-423e-895d-ddfeb74a5e15")
		)
		(fp_line
			(start 4.63 -1.62)
			(end 5.38 -2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "7faafeff-1e29-4d66-8ce4-498b90103b1d")
		)
		(fp_line
			(start 4.63 1.62)
			(end 5.38 2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "c0b3d98e-3edc-4e21-b76d-be084dcb2e57")
		)
		(fp_line
			(start 5.38 -2.37)
			(end 12.62 -2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e1644d30-249c-4e65-a541-6436a12b95c3")
		)
		(fp_line
			(start 5.38 2.37)
			(end 12.62 2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "a5164acb-591e-4c28-869b-f76fc495dc3c")
		)
		(fp_line
			(start 12.62 -2.37)
			(end 12.62 2.37)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "52ee1134-46b4-4519-994e-57edf9ce9fef")
		)
		(fp_line
			(start 13.76 0)
			(end 12.62 0)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e219f86d-e5a5-478f-9485-c310c2d5489c")
		)
		(fp_line
			(start -1.25 -2.5)
			(end -1.25 2.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "*************-480b-801c-6695d88149e2")
		)
		(fp_line
			(start -1.25 2.5)
			(end 16.25 2.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3b5348a3-ed36-4d27-ac75-b79431545f78")
		)
		(fp_line
			(start 16.25 -2.5)
			(end -1.25 -2.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "aea10a83-94cb-43dc-86ba-a65c6f58bdeb")
		)
		(fp_line
			(start 16.25 2.5)
			(end 16.25 -2.5)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "120c238f-ec00-43d6-bb27-4f1187858a12")
		)
		(fp_line
			(start 0 0)
			(end 2.5 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bde1fede-d0dc-4f10-acc4-156a2cb3dd9a")
		)
		(fp_line
			(start 2.5 -2.25)
			(end 2.5 2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "a075cc61-c7e7-434a-920e-d76a9e22c182")
		)
		(fp_line
			(start 2.5 -2.25)
			(end 3.88 -2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "80db50c1-517b-41b9-8d16-4e7f6a1c8e1c")
		)
		(fp_line
			(start 2.5 2.25)
			(end 3.88 2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "06260dbc-e262-4015-a2e4-9c0d76d5f0f6")
		)
		(fp_line
			(start 3.88 -2.25)
			(end 4.63 -1.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4bf9ecb3-440d-48a2-8832-6233ee4af6cb")
		)
		(fp_line
			(start 3.88 2.25)
			(end 4.63 1.5)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ce7f1fd5-1fcd-4c22-a8e7-5641f4cc84e4")
		)
		(fp_line
			(start 3.9 0)
			(end 5.4 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "86776dbb-106e-4b92-9f32-77a7ed93a31b")
		)
		(fp_line
			(start 4.63 -1.5)
			(end 5.38 -2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "bc794f73-3e6b-4e39-be88-30dc51c330ce")
		)
		(fp_line
			(start 4.63 1.5)
			(end 5.38 2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "2f71c7eb-1a9a-44a0-9191-8376eb6acf30")
		)
		(fp_line
			(start 4.65 -0.75)
			(end 4.65 0.75)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ed8fe065-069f-4c2a-b9e2-c1232722d458")
		)
		(fp_line
			(start 5.38 -2.25)
			(end 12.5 -2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "adf8dccd-c708-4c1a-93ca-d14686915226")
		)
		(fp_line
			(start 5.38 2.25)
			(end 12.5 2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "ecd654be-0975-48fa-8332-eff6b2ee917f")
		)
		(fp_line
			(start 12.5 -2.25)
			(end 12.5 2.25)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b3ab6e83-3462-48d2-abb3-76357de561d4")
		)
		(fp_line
			(start 15 0)
			(end 12.5 0)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "4105d46f-f763-440d-b61d-0cc3f0cca075")
		)
		(fp_text user "${REFERENCE}"
			(at 7.5 0 0)
			(layer "F.Fab")
			(uuid "33b068f5-e0d8-4486-af18-38edee8d7d10")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(pad "1" thru_hole rect
			(at 0 0)
			(size 2 2)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(uuid "48bb47ed-2e82-498b-a350-1d1f566a6fb3")
		)
		(pad "2" thru_hole oval
			(at 15 0)
			(size 2 2)
			(drill 1)
			(layers "*.Cu" "*.Mask")
			(remove_unused_layers no)
			(uuid "b7df9598-d979-433e-a990-d7e43c6a4361")
		)
		(model "${KICAD6_3DMODEL_DIR}/Capacitor_THT.3dshapes/CP_Axial_L10.0mm_D4.5mm_P15.00mm_Horizontal.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(gr_text "Locked"
		(at 104 95 0)
		(layer "Cmts.User")
		(uuid "01a84b3d-adea-4d88-8d1d-471b42fb7cb4")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3)
				(bold yes)
			)
			(justify left bottom)
		)
	)
	(gr_text "Normal"
		(at 104 75 0)
		(layer "Cmts.User")
		(uuid "42780ba8-3d95-4f59-b87d-b58970650c4a")
		(effects
			(font
				(size 1.5 1.5)
				(thickness 0.3)
				(bold yes)
			)
			(justify left bottom)
		)
	)
)