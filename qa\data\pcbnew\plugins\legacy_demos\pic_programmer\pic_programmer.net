# EESchema Netlist Version 1.1 created  16/5/2007-09:46:07
(
 ( 442A5056 CP10 C1 100uF
  ( 1 VCC )
  ( 2 GND )
 )
 ( 442A501D CP10 C2 220uF
  ( 1 N-000014 )
  ( 2 GND )
 )
 ( 442A584C CP8 C3 22uF/25V
  ( 1 VPP )
  ( 2 GND )
 )
 ( 442A5F61 C1-1 C4 0
  ( 1 N-000018 )
  ( 2 GND )
 )
 ( 442A58B1 C1-1 C5 10nF
  ( 1 N-000019 )
  ( 2 GND )
 )
 ( 442AA12B C1-1 C6 100nF
  ( 1 VCC )
  ( 2 GND )
 )
 ( 442AA145 C1-1 C7 100nF
  ( 1 VCC_PIC_2 )
  ( 2 GND )
 )
 ( 4639BE2C C1-1 C8 100nF
  ( 1 VCC_PIC_2 )
  ( 2 GND )
 )
 ( 464AD280 C1-1 C9 22OnF
  ( 1 VPP )
  ( 2 N-000026 )
 )
 ( 442A500B D5 D1 1N4004
  ( 1 N-000013 )
  ( 2 N-000014 )
 )
 ( 442A4D1B D3 D2 BAT43
  ( 1 N-000004 )
  ( 2 VCC )
 )
 ( 442A4D25 D3 D3 BAT43
  ( 1 GND )
  ( 2 N-000004 )
 )
 ( 442A4D5C D3 D4 BAT43
  ( 1 N-000005 )
  ( 2 VCC )
 )
 ( 442A4D5D D3 D5 BAT43
  ( 1 GND )
  ( 2 N-000005 )
 )
 ( 442A4D64 D3 D6 BAT43
  ( 1 N-000006 )
  ( 2 VCC )
 )
 ( 442A4D65 D3 D7 BAT43
  ( 1 GND )
  ( 2 N-000006 )
 )
 ( 442A4F5D LEDV D8 RED-LED
  ( 1 N-000010 )
  ( 2 GND )
 )
 ( 442A5084 LEDV D9 GREEN-LED
  ( 1 N-000015 )
  ( 2 GND )
 )
 ( 442A6026 D5 D10 SCHOTTKY
  ( 1 N-000017 )
  ( 2 VPP )
 )
 ( 4639BA28 D3 D11 BAT43
  ( 1 N-000024 )
  ( 2 N-000011 )
 )
 ( 4639B9EA LEDV D12 YELLOW-LED
  ( 1 N-000023 )
  ( 2 GND )
 )
 ( 442A4C93 DB9FC J1 DB9-FEMAL
  ( 1 ? )
  ( 2 ? )
  ( 3 VPP_ON_1 )
  ( 4 PC-DATA-OUT_1 )
  ( 5 GND )
  ( 6 ? )
  ( 7 PC-CLOCK-OUT_1 )
  ( 8 CTS_1 )
  ( 9 ? )
 )
 ( 4639BAF8 PIN_ARRAY_2X1 JP1 JUMPER
  ( 1 VCC )
  ( 2 VCC_PIC_2 )
 )
 ( 442A57BE INDUCTOR_V L1 22uH
  ( 1 N-000017 )
  ( 2 VCC )
 )
 ( 442A4FE7 bornier2 P1 CONN_2
  ( 1 GND )
  ( 2 N-000013 )
 )
 ( 4436967E 28DIP-ELL300 P2 SUPP28
  ( 1 VPP-MCLR_2 )
  ( 2 ? )
  ( 3 ? )
  ( 4 ? )
  ( 5 ? )
  ( 6 ? )
  ( 7 ? )
  ( 8 GND )
  ( 9 ? )
  ( 10 ? )
  ( 11 ? )
  ( 12 ? )
  ( 13 ? )
  ( 14 ? )
  ( 15 ? )
  ( 16 ? )
  ( 17 ? )
  ( 18 ? )
  ( 19 GND )
  ( 20 VCC_PIC_2 )
  ( 21 ? )
  ( 22 ? )
  ( 23 ? )
  ( 24 ? )
  ( 25 ? )
  ( 26 ? )
  ( 27 CLOCK-RB6_1 )
  ( 28 DATA-RB7_2 )
 )
 ( 442A88ED 40DIP-ELL600 P3 SUPP40
  ( 1 VPP-MCLR_2 )
  ( 2 ? )
  ( 3 ? )
  ( 4 ? )
  ( 5 ? )
  ( 6 ? )
  ( 7 ? )
  ( 8 GND )
  ( 9 ? )
  ( 10 ? )
  ( 11 VCC_PIC_2 )
  ( 12 GND )
  ( 13 ? )
  ( 14 ? )
  ( 15 ? )
  ( 16 ? )
  ( 17 ? )
  ( 18 ? )
  ( 19 ? )
  ( 20 ? )
  ( 21 ? )
  ( 22 ? )
  ( 23 ? )
  ( 24 ? )
  ( 25 ? )
  ( 26 ? )
  ( 27 ? )
  ( 28 ? )
  ( 29 ? )
  ( 30 ? )
  ( 31 GND )
  ( 32 VCC_PIC_2 )
  ( 33 ? )
  ( 34 ? )
  ( 35 ? )
  ( 36 ? )
  ( 37 ? )
  ( 38 ? )
  ( 39 CLOCK-RB6_1 )
  ( 40 DATA-RB7_2 )
 )
 ( 442A4EB9 TO92 Q1 BC237
  ( 1 GND )
  ( 2 N-000012 )
  ( 3 N-000011 )
 )
 ( 442A4F30 TO92 Q2 BC307
  ( 1 VPP )
  ( 2 N-000026 )
  ( 3 N-000002 )
 )
 ( 4639B996 TO92 Q3 BC307
  ( 1 VCC )
  ( 2 N-000003 )
  ( 3 VCC_PIC_2 )
 )
 ( 442A4CF4 R4 R1 10K
  ( 1 VPP_ON_1 )
  ( 2 N-000004 )
 )
 ( 442A4CFB R4 R2 10K
  ( 1 VPP_ON_1 )
  ( 2 GND )
 )
 ( 442A4D5A R4 R3 10K
  ( 1 PC-DATA-OUT_1 )
  ( 2 N-000005 )
 )
 ( 442A4D5B R4 R4 10K
  ( 1 PC-DATA-OUT_1 )
  ( 2 GND )
 )
 ( 442A4D62 R4 R5 10K
  ( 1 PC-CLOCK-OUT_1 )
  ( 2 N-000006 )
 )
 ( 442A4D63 R4 R6 10K
  ( 1 PC-CLOCK-OUT_1 )
  ( 2 GND )
 )
 ( 442A4F2A R4 R7 10K
  ( 1 VPP )
  ( 2 N-000026 )
 )
 ( 442A4D92 R4 R8 1K
  ( 1 N-000008 )
  ( 2 N-000012 )
 )
 ( 442A4F52 R4 R9 2.2K
  ( 1 N-000002 )
  ( 2 N-000010 )
 )
 ( 442A5F83 R4 R10 5,1K
  ( 1 N-000018 )
  ( 2 N-000019 )
 )
 ( 442A4F23 R4 R11 22K
  ( 1 N-000011 )
  ( 2 N-000026 )
 )
 ( 442A4D85 R4 R12 470
  ( 1 N-000007 )
  ( 2 DATA-RB7_2 )
 )
 ( 442A4D8D R4 R13 470
  ( 1 N-000009 )
  ( 2 CLOCK-RB6_1 )
 )
 ( 442A5083 R4 R14 470
  ( 1 VCC )
  ( 2 N-000015 )
 )
 ( 442A58D7 R4 R15 6.2K
  ( 1 N-000021 )
  ( 2 GND )
 )
 ( 442A58DC R4 R16 62K
  ( 1 N-000020 )
  ( 2 VPP )
 )
 ( 442A50BF R4 R17 22K
  ( 1 N-000002 )
  ( 2 GND )
 )
 ( 44369638 R4 R18 220
  ( 1 N-000002 )
  ( 2 VPP-MCLR_2 )
 )
 ( 4639B9B0 R4 R19 2.2K
  ( 1 N-000024 )
  ( 2 N-000003 )
 )
 ( 4639B9B3 R4 R20 2.2K
  ( 1 VCC )
  ( 2 N-000003 )
 )
 ( 4639B9E9 R4 R21 470
  ( 1 N-000023 )
  ( 2 VCC_PIC_2 )
 )
 ( 443D0101 RV2X4 RV1 1K
  ( 1 N-000021 )
  ( 2 N-000022 )
  ( 3 N-000020 )
 )
 ( 442A87F7 8DIP-ELL300 U1 24Cxx
  ( 1 GND )
  ( 2 GND )
  ( 3 GND )
  ( 4 GND )
  ( 5 DATA-RB7_2 )
  ( 6 CLOCK-RB6_1 )
  ( 7 ? )
  ( 8 VCC )
 )
 ( 442A4D6B 14DIP-ELL300 U2 74HC125
  ( 1 GND )
  ( 2 N-000004 )
  ( 3 N-000008 )
  ( 4 GND )
  ( 5 N-000005 )
  ( 6 N-000007 )
  ( 7 GND )
  ( 8 N-000009 )
  ( 9 N-000006 )
  ( 10 GND )
  ( 11 CTS_1 )
  ( 12 DATA-RB7_2 )
  ( 13 GND )
  ( 14 VCC )
 )
 ( 442A504A LM78XX U3 7805
  ( GND GND )
  ( VI N-000014 )
  ( VO VCC )
 )
 ( 442A5E20 8DIP-ELL300 U4 LT1373
  ( 1 N-000018 )
  ( 2 N-000022 )
  ( 3 ? )
  ( 4 ? )
  ( 5 VCC )
  ( 6 GND )
  ( 7 GND )
  ( 8 N-000017 )
 )
 ( 442A81A7 18DIP-ELL300 U5 PIC_18_PINS
  ( 1 ? )
  ( 2 ? )
  ( 3 ? )
  ( 4 VPP-MCLR_2 )
  ( 5 GND )
  ( 6 ? )
  ( 7 ? )
  ( 8 ? )
  ( 9 ? )
  ( 10 ? )
  ( 11 ? )
  ( 12 CLOCK-RB6_1 )
  ( 13 DATA-RB7_2 )
  ( 14 VCC_PIC_2 )
  ( 15 ? )
  ( 16 ? )
  ( 17 ? )
  ( 18 ? )
 )
 ( 442A81A5 8DIP-ELL300 U6 PIC_8_PINS
  ( 1 VCC_PIC_2 )
  ( 2 ? )
  ( 3 ? )
  ( 4 VPP-MCLR_2 )
  ( 5 ? )
  ( 6 CLOCK-RB6_1 )
  ( 7 DATA-RB7_2 )
  ( 8 GND )
 )
)
*
{ Allowed footprints by component:
$component C1
 CP*
 SM*
$endlist
$component C2
 CP*
 SM*
$endlist
$component C3
 CP*
 SM*
$endlist
$component C4
 SM*
 C?
 C1-1
$endlist
$component C5
 SM*
 C?
 C1-1
$endlist
$component C6
 SM*
 C?
 C1-1
$endlist
$component C7
 SM*
 C?
 C1-1
$endlist
$component C8
 SM*
 C?
 C1-1
$endlist
$component C9
 SM*
 C?
 C1-1
$endlist
$component D1
 D?
 S*
$endlist
$component D2
 D?
 S*
$endlist
$component D3
 D?
 S*
$endlist
$component D4
 D?
 S*
$endlist
$component D5
 D?
 S*
$endlist
$component D6
 D?
 S*
$endlist
$component D7
 D?
 S*
$endlist
$component D10
 D?
 S*
$endlist
$component D11
 D?
 S*
$endlist
$component J1
 DB9*
$endlist
$component R1
 R?
 SM0603
 SM0805
$endlist
$component R2
 R?
 SM0603
 SM0805
$endlist
$component R3
 R?
 SM0603
 SM0805
$endlist
$component R4
 R?
 SM0603
 SM0805
$endlist
$component R5
 R?
 SM0603
 SM0805
$endlist
$component R6
 R?
 SM0603
 SM0805
$endlist
$component R7
 R?
 SM0603
 SM0805
$endlist
$component R8
 R?
 SM0603
 SM0805
$endlist
$component R9
 R?
 SM0603
 SM0805
$endlist
$component R10
 R?
 SM0603
 SM0805
$endlist
$component R11
 R?
 SM0603
 SM0805
$endlist
$component R12
 R?
 SM0603
 SM0805
$endlist
$component R13
 R?
 SM0603
 SM0805
$endlist
$component R14
 R?
 SM0603
 SM0805
$endlist
$component R15
 R?
 SM0603
 SM0805
$endlist
$component R16
 R?
 SM0603
 SM0805
$endlist
$component R17
 R?
 SM0603
 SM0805
$endlist
$component R18
 R?
 SM0603
 SM0805
$endlist
$component R19
 R?
 SM0603
 SM0805
$endlist
$component R20
 R?
 SM0603
 SM0805
$endlist
$component R21
 R?
 SM0603
 SM0805
$endlist
$endfootprintlist
}
