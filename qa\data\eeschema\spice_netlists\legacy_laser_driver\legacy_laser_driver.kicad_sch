(kicad_sch (version 20220404) (generator eeschema)

  (uuid 345f09c0-9aae-452d-8e25-bb6665c6b8ac)

  (paper "A4")

  (lib_symbols
    (symbol "R_1" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R_1" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 0.762 0.762)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "ki_fp_filters" "R_* Resistor_*" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_1_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "R_2" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R_2" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 0.762 0.762)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "ki_fp_filters" "R_* Resistor_*" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_2_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_2_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "R_3" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R_3" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 0.762 0.762)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "ki_fp_filters" "R_* Resistor_*" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_3_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_3_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "R_4" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R_4" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 0.762 0.762)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "ki_fp_filters" "R_* Resistor_*" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_4_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_4_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "laser_driver_schlib:C" (pin_numbers hide) (pin_names (offset 0.254)) (in_bom yes) (on_board yes)
      (property "Reference" "C" (id 0) (at 0.635 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "C" (id 1) (at 0.635 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0.9652 -3.81 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "ki_fp_filters" "C? C_????_* C_???? SMD*_c Capacitor*" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "C_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy 2.032 -0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
      )
      (symbol "C_1_1"
        (pin passive line (at 0 3.81 270) (length 2.794)
          (name "~" (effects (font (size 1.016 1.016))))
          (number "1" (effects (font (size 1.016 1.016))))
        )
        (pin passive line (at 0 -3.81 90) (length 2.794)
          (name "~" (effects (font (size 1.016 1.016))))
          (number "2" (effects (font (size 1.016 1.016))))
        )
      )
    )
    (symbol "laser_driver_schlib:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (id 0) (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (id 1) (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "laser_driver_schlib:Generic_Opamp" (in_bom yes) (on_board yes)
      (property "Reference" "U" (id 0) (at 0 6.35 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "Generic_Opamp" (id 1) (at 0 3.81 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at -2.54 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (symbol "Generic_Opamp_0_1"
        (polyline
          (pts
            (xy -5.08 5.08)
            (xy 5.08 0)
            (xy -5.08 -5.08)
            (xy -5.08 5.08)
          )
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "Generic_Opamp_1_1"
        (pin input line (at -7.62 2.54 0) (length 2.54)
          (name "+" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -7.62 -2.54 0) (length 2.54)
          (name "-" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -2.54 7.62 270) (length 3.81)
          (name "V+" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin power_in line (at -2.54 -7.62 90) (length 3.81)
          (name "V-" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin output line (at 7.62 0 180) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "laser_driver_schlib:LED" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "D" (id 0) (at 0 2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "LED" (id 1) (at 0 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "ki_fp_filters" "LED-3MM LED-5MM LED-10MM LED-0603 LED-0805 LED-1206 LEDV" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "LED_0_1"
        (polyline
          (pts
            (xy -1.27 1.27)
            (xy -1.27 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 -0.635)
            (xy -3.175 -1.651)
            (xy -3.048 -1.016)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -1.651 -1.016)
            (xy -2.794 -2.032)
            (xy -2.667 -1.397)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 1.27)
            (xy -1.27 0)
            (xy 1.27 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
      )
      (symbol "LED_1_1"
        (pin passive line (at -5.08 0 0) (length 3.81)
          (name "K" (effects (font (size 1.016 1.016))))
          (number "1" (effects (font (size 1.016 1.016))))
        )
        (pin passive line (at 5.08 0 180) (length 3.81)
          (name "A" (effects (font (size 1.016 1.016))))
          (number "2" (effects (font (size 1.016 1.016))))
        )
      )
    )
    (symbol "laser_driver_schlib:Q_NPN_CBE" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "Q" (id 0) (at 7.62 1.27 0)
        (effects (font (size 1.27 1.27)) (justify right))
      )
      (property "Value" "Q_NPN_CBE" (id 1) (at 15.24 -1.27 0)
        (effects (font (size 1.27 1.27)) (justify right))
      )
      (property "Footprint" "" (id 2) (at 5.08 2.54 0)
        (effects (font (size 0.7366 0.7366)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "Q_NPN_CBE_0_1"
        (polyline
          (pts
            (xy 0.635 0.635)
            (xy 2.54 2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.635 -0.635)
            (xy 2.54 -2.54)
            (xy 2.54 -2.54)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.635 1.905)
            (xy 0.635 -1.905)
            (xy 0.635 -1.905)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -1.778)
            (xy 1.778 -1.27)
            (xy 2.286 -2.286)
            (xy 1.27 -1.778)
            (xy 1.27 -1.778)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
        (circle (center 1.27 0) (radius 2.8194)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "Q_NPN_CBE_1_1"
        (pin passive line (at 2.54 5.08 270) (length 2.54)
          (name "C" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at -5.08 0 0) (length 5.715)
          (name "B" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 2.54 -5.08 90) (length 2.54)
          (name "E" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "laser_driver_schlib:R" (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 0.762 0.762)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "ki_fp_filters" "R_* Resistor_*" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "2" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "laser_driver_schlib:VDD" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (id 0) (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "VDD" (id 1) (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "VDD_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 1.905) (radius 0.635)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "VDD_1_1"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "VDD" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "laser_driver_schlib:VSOURCE" (pin_names (offset 1.016)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 5.08 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "VSOURCE" (id 1) (at 6.35 2.54 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Fieldname" "Value" (id 4) (at 0 0 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (property "Spice_Node_Sequence" "1 2" (id 6) (at -7.62 5.08 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (symbol "VSOURCE_0_1"
        (polyline
          (pts
            (xy 0 -1.905)
            (xy 0 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 1.905)
            (xy -0.635 0.635)
            (xy 0.635 0.635)
            (xy 0 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "VSOURCE_1_1"
        (pin input line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "laser_driver_schlib:VSS" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (id 0) (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "VSS" (id 1) (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "VSS_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (circle (center 0 1.905) (radius 0.635)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "VSS_1_1"
        (pin power_in line (at 0 0 90) (length 0) hide
          (name "VSS" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:PWR_FLAG" (power) (pin_numbers hide) (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "#FLG" (id 0) (at 0 1.905 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "PWR_FLAG" (id 1) (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Special symbol for telling ERC where power comes from" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "PWR_FLAG_0_0"
        (pin power_out line (at 0 0 90) (length 0)
          (name "pwr" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "PWR_FLAG_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
            (xy -1.016 1.905)
            (xy 0 2.54)
            (xy 1.016 1.905)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
    )
  )

  (junction (at 238.76 53.34) (diameter 1.016) (color 0 0 0 0)
    (uuid 2458a5e4-a9ee-4559-8d60-8162624b29c1)
  )
  (junction (at 146.05 105.41) (diameter 1.016) (color 0 0 0 0)
    (uuid 5204e43b-805b-4cae-a883-b83959b4f2e9)
  )
  (junction (at 245.11 53.34) (diameter 1.016) (color 0 0 0 0)
    (uuid 635e5026-d111-4cb0-a089-ab5defcaede5)
  )
  (junction (at 162.56 95.25) (diameter 1.016) (color 0 0 0 0)
    (uuid 7d0a1349-e430-4d9e-9d9b-7ba102a3a011)
  )
  (junction (at 118.11 73.66) (diameter 1.016) (color 0 0 0 0)
    (uuid 846b9988-0da0-46c3-ba02-7f49cc1fe375)
  )
  (junction (at 245.11 66.04) (diameter 1.016) (color 0 0 0 0)
    (uuid 8486a017-47f7-457e-b511-ffa529b09927)
  )
  (junction (at 114.3 91.44) (diameter 1.016) (color 0 0 0 0)
    (uuid a9694e50-fbe0-4e59-91b8-32abdef47493)
  )
  (junction (at 129.54 105.41) (diameter 1.016) (color 0 0 0 0)
    (uuid c10e7980-7b96-4eb5-b8f3-5f29a0b2892d)
  )
  (junction (at 172.72 110.49) (diameter 1.016) (color 0 0 0 0)
    (uuid c89b34fa-cdac-4c14-88da-d05aa9dbb3c9)
  )
  (junction (at 162.56 110.49) (diameter 1.016) (color 0 0 0 0)
    (uuid ccec9c61-9f3f-4c93-931f-327d0dad5977)
  )
  (junction (at 245.11 40.64) (diameter 1.016) (color 0 0 0 0)
    (uuid eac94b5c-a2f4-4632-a9df-19fb163b696d)
  )

  (wire (pts (xy 162.56 110.49) (xy 162.56 119.38))
    (stroke (width 0) (type solid))
    (uuid 0635f3ba-592b-477b-b88b-85297d3f621f)
  )
  (wire (pts (xy 114.3 105.41) (xy 114.3 91.44))
    (stroke (width 0) (type solid))
    (uuid 09cce4a9-c373-4333-8a42-4ef53bf78c37)
  )
  (wire (pts (xy 146.05 111.76) (xy 146.05 105.41))
    (stroke (width 0) (type solid))
    (uuid 0f6f1664-b680-42f1-b77c-eff57dfe0984)
  )
  (wire (pts (xy 149.86 105.41) (xy 149.86 95.25))
    (stroke (width 0) (type solid))
    (uuid 258382be-4c95-41cb-9b29-e8783be56662)
  )
  (wire (pts (xy 67.31 106.68) (xy 67.31 95.25))
    (stroke (width 0) (type solid))
    (uuid 2f62686c-c711-4546-b23c-4b6a4d29062a)
  )
  (wire (pts (xy 160.02 73.66) (xy 177.8 73.66))
    (stroke (width 0) (type solid))
    (uuid 30d5788e-8d65-4809-8ac7-6ec3545dabe9)
  )
  (wire (pts (xy 129.54 105.41) (xy 133.35 105.41))
    (stroke (width 0) (type solid))
    (uuid 31121e75-3f90-48dd-9bcc-fe82a7c15bd6)
  )
  (wire (pts (xy 67.31 73.66) (xy 67.31 85.09))
    (stroke (width 0) (type solid))
    (uuid 322dbe27-b38a-43c8-b2e4-0b601ddf0a40)
  )
  (wire (pts (xy 114.3 105.41) (xy 129.54 105.41))
    (stroke (width 0) (type solid))
    (uuid 3fe25085-bf12-44f8-8b28-775467576a48)
  )
  (wire (pts (xy 233.68 53.34) (xy 233.68 54.61))
    (stroke (width 0) (type solid))
    (uuid 40ca5757-c2b4-4804-8836-548fab88b80a)
  )
  (wire (pts (xy 114.3 91.44) (xy 120.65 91.44))
    (stroke (width 0) (type solid))
    (uuid 45af3ada-6154-4556-8c0c-5e4cee5a8aab)
  )
  (wire (pts (xy 118.11 73.66) (xy 152.4 73.66))
    (stroke (width 0) (type solid))
    (uuid 472394d8-1b60-45f4-a851-7d55d0d0a80c)
  )
  (wire (pts (xy 245.11 53.34) (xy 245.11 54.61))
    (stroke (width 0) (type solid))
    (uuid 4ded3f87-dbb9-401e-8ced-54e2736ad9d6)
  )
  (wire (pts (xy 162.56 110.49) (xy 172.72 110.49))
    (stroke (width 0) (type solid))
    (uuid 50f630aa-9af4-478e-a635-d47f53a975e0)
  )
  (wire (pts (xy 245.11 40.64) (xy 245.11 41.91))
    (stroke (width 0) (type solid))
    (uuid 5291a971-be67-4846-87da-c226707249fb)
  )
  (wire (pts (xy 140.97 111.76) (xy 146.05 111.76))
    (stroke (width 0) (type solid))
    (uuid 54b16121-76e0-4ca1-835c-5db29d33b07c)
  )
  (wire (pts (xy 146.05 105.41) (xy 149.86 105.41))
    (stroke (width 0) (type solid))
    (uuid 64eb337e-6d35-409f-afb6-ace9e7d4da1c)
  )
  (wire (pts (xy 129.54 111.76) (xy 133.35 111.76))
    (stroke (width 0) (type solid))
    (uuid 687dd94f-159c-4b41-a9a6-803da892de1a)
  )
  (wire (pts (xy 172.72 110.49) (xy 177.8 110.49))
    (stroke (width 0) (type solid))
    (uuid 68adbf0e-0949-43a9-941a-5f772f7e784d)
  )
  (wire (pts (xy 140.97 105.41) (xy 146.05 105.41))
    (stroke (width 0) (type solid))
    (uuid 6b9265c1-5bf0-46c3-88c6-c28149d12696)
  )
  (wire (pts (xy 245.11 52.07) (xy 245.11 53.34))
    (stroke (width 0) (type solid))
    (uuid 6c6f452d-9afe-427f-b280-d0cb1d4662f9)
  )
  (wire (pts (xy 162.56 93.98) (xy 162.56 95.25))
    (stroke (width 0) (type solid))
    (uuid 6ea9f4a3-8276-410c-bd06-33a62131b7c7)
  )
  (wire (pts (xy 237.49 40.64) (xy 245.11 40.64))
    (stroke (width 0) (type solid))
    (uuid 7b3f87c9-f95a-4241-a517-23c734734fdb)
  )
  (wire (pts (xy 238.76 53.34) (xy 238.76 54.61))
    (stroke (width 0) (type solid))
    (uuid 83764fd0-95ca-4a7e-a3fd-1c010a2651ac)
  )
  (wire (pts (xy 149.86 95.25) (xy 162.56 95.25))
    (stroke (width 0) (type solid))
    (uuid 889710ec-1cc1-4a7d-9428-7425c964b8cb)
  )
  (wire (pts (xy 172.72 110.49) (xy 172.72 105.41))
    (stroke (width 0) (type solid))
    (uuid 8cfebc97-c8af-4a2f-bb8b-7066b20e4a81)
  )
  (wire (pts (xy 177.8 73.66) (xy 177.8 110.49))
    (stroke (width 0) (type solid))
    (uuid 8da935d3-7684-4d23-ac73-c3d5260ebaf0)
  )
  (wire (pts (xy 120.65 86.36) (xy 118.11 86.36))
    (stroke (width 0) (type solid))
    (uuid 8f20ba04-4e49-4bb2-9e9a-254d5856f67a)
  )
  (wire (pts (xy 238.76 53.34) (xy 245.11 53.34))
    (stroke (width 0) (type solid))
    (uuid 94d2a6c4-d3ac-4d5d-8010-fb21530d1e99)
  )
  (wire (pts (xy 234.95 66.04) (xy 245.11 66.04))
    (stroke (width 0) (type solid))
    (uuid 9ac32d1d-d0c7-408f-88e3-94a454ab86b9)
  )
  (wire (pts (xy 245.11 66.04) (xy 245.11 64.77))
    (stroke (width 0) (type solid))
    (uuid af0e6782-b6fa-4cd2-b70d-af94d8d31170)
  )
  (wire (pts (xy 129.54 105.41) (xy 129.54 111.76))
    (stroke (width 0) (type solid))
    (uuid b1048214-5e97-404d-a5e3-98e2567acc0d)
  )
  (wire (pts (xy 135.89 88.9) (xy 154.94 88.9))
    (stroke (width 0) (type solid))
    (uuid b5c2d9ea-893a-47dc-a632-0160f6aa7bbb)
  )
  (wire (pts (xy 106.68 73.66) (xy 67.31 73.66))
    (stroke (width 0) (type solid))
    (uuid b7e6771d-ff0f-4053-b551-25e26aefdaa6)
  )
  (wire (pts (xy 233.68 53.34) (xy 238.76 53.34))
    (stroke (width 0) (type solid))
    (uuid c0c7aa26-e750-49a6-975c-9dd4cb9e4b90)
  )
  (wire (pts (xy 109.22 91.44) (xy 114.3 91.44))
    (stroke (width 0) (type solid))
    (uuid c7f27d3c-e087-4244-b96b-ce2cbb2940a9)
  )
  (wire (pts (xy 162.56 105.41) (xy 162.56 110.49))
    (stroke (width 0) (type solid))
    (uuid d49d4c92-90e7-4e6d-9043-3d791cc32935)
  )
  (wire (pts (xy 162.56 95.25) (xy 162.56 97.79))
    (stroke (width 0) (type solid))
    (uuid d86d9276-e06d-4599-80e8-5d6386f77a0c)
  )
  (wire (pts (xy 172.72 95.25) (xy 172.72 97.79))
    (stroke (width 0) (type solid))
    (uuid db0bcd5a-e04f-4dd1-8af3-ebbac2f9904a)
  )
  (wire (pts (xy 162.56 95.25) (xy 172.72 95.25))
    (stroke (width 0) (type solid))
    (uuid dc089e65-ec1d-4f49-9103-ee8e3dafbf77)
  )
  (wire (pts (xy 118.11 86.36) (xy 118.11 73.66))
    (stroke (width 0) (type solid))
    (uuid ebe12a6f-6caf-45ab-a7b3-99e813d6b5df)
  )
  (wire (pts (xy 114.3 73.66) (xy 118.11 73.66))
    (stroke (width 0) (type solid))
    (uuid ec8ae3c7-b7c7-47bc-a29d-e531ccbf6f7c)
  )

  (text ".tran 10p 150n" (at 80.01 137.16 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid d2ede8b8-d346-47de-905f-e906f9cac1bf)
  )

  (label "in" (at 92.71 73.66 0) (fields_autoplaced)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid f31eebf6-0a5f-4b16-ad86-c4c03e7a5ba8)
  )
  (label "out" (at 166.37 110.49 0) (fields_autoplaced)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid f9f32c4c-d94f-45c3-bd48-0c84f87c4ae6)
  )

  (symbol (lib_id "laser_driver_schlib:VSOURCE") (at 67.31 90.17 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-000057336052)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 70.5612 89.0016 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "" (id 1) (at 70.5612 91.313 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 67.31 90.17 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 67.31 90.17 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Spice_Primitive" "V" (id 4) (at 67.31 90.17 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Model" "pulse(0 3 100n 1n 1n 20n 100n )" (id 5) (at 67.31 90.17 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 5197a420-36c0-4e94-837c-3869abda7adb))
    (pin "2" (uuid 963aa2ed-8174-4bfb-b58d-9c313f986efb))
  )

  (symbol (lib_id "laser_driver_schlib:Generic_Opamp") (at 128.27 88.9 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-00005788ff9f)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 128.27 85.09 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "" (id 1) (at 128.27 92.71 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 125.73 91.44 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 128.27 88.9 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (id 4) (at 128.27 88.9 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "X" (id 5) (at 128.27 88.9 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Model" "ad8009" (id 6) (at 128.27 88.9 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 7) (at 128.27 88.9 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Lib_File" "ad8009.lib" (id 8) (at 128.27 88.9 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid c5ddd1b2-802e-4d44-a9a8-5fd98b85169e))
    (pin "2" (uuid 80f4fcf7-6b65-4e5c-a550-c3831bb0b43b))
    (pin "3" (uuid 39576561-900c-4bb3-8e71-d4a42b9eec4d))
    (pin "4" (uuid 018b4743-3f9d-4a1a-a0ee-da99f89800e3))
    (pin "5" (uuid 7f0810ec-9686-4533-b099-7c8b8e4f9857))
  )

  (symbol (lib_id "laser_driver_schlib:VSOURCE") (at 245.11 46.99 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578900ba)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 248.3612 45.8216 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "" (id 1) (at 248.3612 48.133 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 245.11 46.99 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 245.11 46.99 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (id 4) (at 245.11 46.99 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 245.11 46.99 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Node_Sequence" "1 2" (id 6) (at 237.49 41.91 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 59def277-e482-49e4-add6-23c83a4a40f8))
    (pin "2" (uuid a39c4b2c-a516-4aca-bbb1-61566aa7552b))
  )

  (symbol (lib_id "laser_driver_schlib:VSOURCE") (at 245.11 59.69 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-000057890232)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 248.3612 58.5216 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "" (id 1) (at 248.3612 60.833 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 245.11 59.69 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 245.11 59.69 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (id 4) (at 245.11 59.69 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 245.11 59.69 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Node_Sequence" "1 2" (id 6) (at 237.49 54.61 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid dc35ef92-0606-4330-8cb1-d06edd284c72))
    (pin "2" (uuid 2948c442-fd64-48d5-9bf5-9bf1d6f9c96a))
  )

  (symbol (lib_id "laser_driver_schlib:GND") (at 238.76 54.61 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578902d2)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 238.76 60.96 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 238.887 59.0042 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 238.76 54.61 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 238.76 54.61 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 973adce4-432c-45dc-aefd-9e51b9ae1060))
  )

  (symbol (lib_id "laser_driver_schlib:VDD") (at 245.11 40.64 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578903c0)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 245.11 44.45 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 245.5418 36.2458 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 245.11 40.64 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 245.11 40.64 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 722ebe56-d0d7-47af-9bbc-ca40fbb8a7ee))
  )

  (symbol (lib_id "laser_driver_schlib:VSS") (at 245.11 66.04 180) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578903e2)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 245.11 62.23 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 244.6528 70.4342 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 245.11 66.04 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 245.11 66.04 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 7a8a6c3e-f769-4f48-8ff0-f26b07ff95a9))
  )

  (symbol (lib_id "laser_driver_schlib:VDD") (at 125.73 81.28 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-000057890425)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 125.73 85.09 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 126.1618 76.8858 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 125.73 81.28 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 125.73 81.28 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid f277988a-24c7-4136-ad01-294432da805b))
  )

  (symbol (lib_id "laser_driver_schlib:VSS") (at 125.73 96.52 180) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-000057890453)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 125.73 92.71 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 125.2728 100.9142 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 125.73 96.52 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 125.73 96.52 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 2d5126a4-7370-47a6-81c3-34bb24d58da1))
  )

  (symbol (lib_id "laser_driver_schlib:C") (at 172.72 101.6 180) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-00005789085b)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 169.799 100.4316 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "" (id 1) (at 169.799 102.743 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 171.7548 97.79 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 172.72 101.6 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 39dd3d6a-df9a-4d64-90ce-c63969bbe542))
    (pin "2" (uuid dd7f4245-8efb-4898-8e29-ff4c82d82dde))
  )

  (symbol (lib_id "laser_driver_schlib:R") (at 162.56 101.6 180) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578ea6d8)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 160.8074 100.4316 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "" (id 1) (at 160.8074 102.743 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 164.338 101.6 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 162.56 101.6 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 8180e012-9cef-4346-a906-5fe990559a19))
    (pin "2" (uuid 994f5fec-dfd3-4181-9a21-f981c98b3c7d))
  )

  (symbol (lib_name "R_1") (lib_id "laser_driver_schlib:R") (at 105.41 91.44 270) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578ea7ee)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 105.41 86.1822 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "" (id 1) (at 105.41 88.4936 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 105.41 89.662 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 105.41 91.44 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 9803fd69-1b2e-4769-95c8-844a47371d01))
    (pin "2" (uuid bb369ca0-70cb-4230-a5d0-282d5ee0cc56))
  )

  (symbol (lib_name "R_4") (lib_id "laser_driver_schlib:R") (at 137.16 105.41 270) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578ea8b4)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 137.16 100.1522 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "" (id 1) (at 137.16 102.4636 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 137.16 103.632 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 137.16 105.41 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 20bf1e7d-be19-4cd0-abb3-6ddda1a83b13))
    (pin "2" (uuid bceef02f-04ad-4959-a415-a1ed1485af31))
  )

  (symbol (lib_id "laser_driver_schlib:Q_NPN_CBE") (at 160.02 88.9 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578eadcc)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 164.8714 87.7316 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "" (id 1) (at 164.8714 90.043 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 165.1 86.36 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 160.02 88.9 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (id 4) (at 160.02 88.9 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "Q" (id 5) (at 160.02 88.9 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Model" "fzt1049a" (id 6) (at 160.02 88.9 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 7) (at 160.02 88.9 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Lib_File" "fzt1049a.lib" (id 8) (at 160.02 88.9 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 075a8c7c-8c08-4a89-bb61-0344115b2289))
    (pin "2" (uuid 19199317-6644-4fd5-bdba-2e53c0ea9083))
    (pin "3" (uuid fe56b655-ba64-4769-806c-0fea7a1f0c4c))
  )

  (symbol (lib_id "laser_driver_schlib:C") (at 137.16 111.76 90) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578eb076)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 137.16 115.824 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "" (id 1) (at 137.16 118.1354 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 140.97 110.7948 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 137.16 111.76 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 80bede86-f568-4a91-a8e5-05a2abc4a536))
    (pin "2" (uuid 09ca4b01-5705-46df-af32-4af5eac89e2c))
  )

  (symbol (lib_id "laser_driver_schlib:LED") (at 162.56 124.46 90) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578eb1e8)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 165.3032 123.2916 90)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "" (id 1) (at 165.3032 125.603 90)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (id 2) (at 162.56 124.46 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 162.56 124.46 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "qtlp690c" (id 4) (at 162.56 124.46 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "D" (id 5) (at 162.56 124.46 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Model" "laser" (id 6) (at 162.56 124.46 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 7) (at 162.56 124.46 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Lib_File" "laser.lib" (id 8) (at 162.56 124.46 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Node_Sequence" "2 1" (id 9) (at 162.56 124.46 90)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 017e0989-e00f-46cc-9694-ef1aa45bf136))
    (pin "2" (uuid 79f4aea1-0515-47a1-80f0-6296a3387ec7))
  )

  (symbol (lib_id "laser_driver_schlib:GND") (at 162.56 129.54 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578eb42d)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 162.56 135.89 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 162.687 133.9342 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 162.56 129.54 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 162.56 129.54 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 5e38ebe9-08ac-4584-94b8-eb9b4b4e0fd2))
  )

  (symbol (lib_name "R_3") (lib_id "laser_driver_schlib:R") (at 156.21 73.66 270) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578eba35)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 156.21 68.4022 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "" (id 1) (at 156.21 70.7136 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 156.21 71.882 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 156.21 73.66 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid b4a2ee67-e3f7-404f-9831-890c4347e933))
    (pin "2" (uuid eecd5373-3205-4d3a-903b-dc8800137c83))
  )

  (symbol (lib_name "R_2") (lib_id "laser_driver_schlib:R") (at 110.49 73.66 270) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578ebb39)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 110.49 68.4022 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "" (id 1) (at 110.49 70.7136 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 110.49 71.882 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 110.49 73.66 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid dec7857d-5997-49b3-9c15-455795db6f2e))
    (pin "2" (uuid b185344b-cb8e-4343-8bd6-e1bf9b957880))
  )

  (symbol (lib_id "laser_driver_schlib:GND") (at 101.6 91.44 270) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578ebbe4)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 95.25 91.44 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 97.2058 91.567 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 101.6 91.44 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 101.6 91.44 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 4acc58ba-17e5-439c-9da3-f728baadbd7b))
  )

  (symbol (lib_id "laser_driver_schlib:GND") (at 67.31 106.68 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000578ec19d)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 67.31 113.03 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 67.437 111.0742 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 67.31 106.68 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 67.31 106.68 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 9f3f68c6-8b3d-4e7b-8e81-a1a2634203b2))
  )

  (symbol (lib_id "laser_driver_schlib:VDD") (at 162.56 83.82 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-00005a0b5a9d)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 162.56 87.63 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 162.9918 79.4258 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 162.56 83.82 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 162.56 83.82 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid d19d6ae3-746e-4b6a-b3e0-4dd381515c17))
  )

  (symbol (lib_id "power:PWR_FLAG") (at 237.49 40.64 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 30468655-fd8b-4bef-8c43-09d939fcbff5)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 237.49 38.735 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 237.49 36.3156 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 237.49 40.64 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 237.49 40.64 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 45f1295a-cc7e-4b52-84df-1c723b876b60))
  )

  (symbol (lib_id "power:PWR_FLAG") (at 233.68 54.61 180) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 63fc4c18-7f39-4806-9185-c37efcf99579)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 233.68 56.515 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 233.68 58.9344 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 233.68 54.61 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 233.68 54.61 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid dbae8912-cf5e-49bd-99e6-85fb634a3299))
  )

  (symbol (lib_id "power:PWR_FLAG") (at 234.95 66.04 180) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid a8f4cb9f-5c44-4d92-b170-52e37c1ffbb3)
    (default_instance (reference "U") (unit 1) (value "") (footprint ""))
    (property "Reference" "U" (id 0) (at 234.95 67.945 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "" (id 1) (at 234.95 70.3644 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 234.95 66.04 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 234.95 66.04 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 5c70cd01-9984-486c-b999-f68f6bd9bdcc))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/30468655-fd8b-4bef-8c43-09d939fcbff5"
      (reference "#FLG0101") (unit 1) (value "PWR_FLAG") (footprint "")
    )
    (path "/a8f4cb9f-5c44-4d92-b170-52e37c1ffbb3"
      (reference "#FLG0102") (unit 1) (value "PWR_FLAG") (footprint "")
    )
    (path "/63fc4c18-7f39-4806-9185-c37efcf99579"
      (reference "#FLG0103") (unit 1) (value "PWR_FLAG") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578ec19d"
      (reference "#PWR01") (unit 1) (value "GND") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578ebbe4"
      (reference "#PWR02") (unit 1) (value "GND") (footprint "")
    )
    (path "/00000000-0000-0000-0000-000057890425"
      (reference "#PWR03") (unit 1) (value "VDD") (footprint "")
    )
    (path "/00000000-0000-0000-0000-000057890453"
      (reference "#PWR04") (unit 1) (value "VSS") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578eb42d"
      (reference "#PWR06") (unit 1) (value "GND") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578902d2"
      (reference "#PWR07") (unit 1) (value "GND") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578903c0"
      (reference "#PWR08") (unit 1) (value "VDD") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578903e2"
      (reference "#PWR09") (unit 1) (value "VSS") (footprint "")
    )
    (path "/00000000-0000-0000-0000-00005a0b5a9d"
      (reference "#PWR0101") (unit 1) (value "VDD") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578eb076"
      (reference "C1") (unit 1) (value "1p") (footprint "")
    )
    (path "/00000000-0000-0000-0000-00005789085b"
      (reference "C2") (unit 1) (value "1p") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578eb1e8"
      (reference "D1") (unit 1) (value "laser diode") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578eadcc"
      (reference "Q1") (unit 1) (value "fzt1049a") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578ea7ee"
      (reference "R1") (unit 1) (value "220") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578ebb39"
      (reference "R2") (unit 1) (value "160") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578ea8b4"
      (reference "R3") (unit 1) (value "220") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578eba35"
      (reference "R4") (unit 1) (value "220") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578ea6d8"
      (reference "R5") (unit 1) (value "2.5") (footprint "")
    )
    (path "/00000000-0000-0000-0000-00005788ff9f"
      (reference "U1") (unit 1) (value "AD8009") (footprint "")
    )
    (path "/00000000-0000-0000-0000-000057336052"
      (reference "V1") (unit 1) (value "pulse (check properties)") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000578900ba"
      (reference "V2") (unit 1) (value "DC 10") (footprint "")
    )
    (path "/00000000-0000-0000-0000-000057890232"
      (reference "V3") (unit 1) (value "DC 10") (footprint "")
    )
  )
)
