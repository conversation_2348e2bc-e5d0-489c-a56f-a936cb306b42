(kicad_sch (version 20230121) (generator eeschema)

  (uuid 1982c87f-95a1-4a40-9669-f13cd3c28543)

  (paper "A4")

  (lib_symbols
    (symbol "Device:R_Potentiometer" (pin_names (offset 1.016) hide) (in_bom yes) (on_board yes)
      (property "Reference" "RV" (at -4.445 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R_Potentiometer" (at -2.54 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "resistor variable" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Potentiometer" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_fp_filters" "Potentiometer*" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_Potentiometer_0_1"
        (polyline
          (pts
            (xy 2.54 0)
            (xy 1.524 0)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.143 0)
            (xy 2.286 0.508)
            (xy 2.286 -0.508)
            (xy 1.143 0)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
        (rectangle (start 1.016 2.54) (end -1.016 -2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_Potentiometer_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "1" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 3.81 0 180) (length 1.27)
          (name "2" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "3" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "Simulation_SPICE:VDC" (pin_numbers hide) (pin_names (offset 0.0254)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (at 2.54 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "VDC" (at 2.54 0 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Spice_Netlist_Enabled" "Y" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Primitive" "V" (at 0 0 0)
        (effects (font (size 1.27 1.27)) (justify left) hide)
      )
      (property "Spice_Model" "dc(1)" (at 2.54 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "ki_keywords" "simulation" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Voltage source, DC" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "VDC_0_0"
        (polyline
          (pts
            (xy -1.27 0.254)
            (xy 1.27 0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -0.762 -0.254)
            (xy -1.27 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0.254 -0.254)
            (xy -0.254 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 -0.254)
            (xy 0.762 -0.254)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (text "+" (at 0 1.905 0)
          (effects (font (size 1.27 1.27)))
        )
      )
      (symbol "VDC_0_1"
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0.254) (type default))
          (fill (type background))
        )
      )
      (symbol "VDC_1_1"
        (pin passive line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 127 50.8) (diameter 0) (color 0 0 0 0)
    (uuid 5a7710ba-4170-492d-bc32-90805ba7c000)
  )
  (junction (at 127 88.9) (diameter 0) (color 0 0 0 0)
    (uuid f58fe8e4-5110-45ec-ad08-4f65416c307e)
  )

  (wire (pts (xy 114.3 62.23) (xy 114.3 63.5))
    (stroke (width 0) (type default))
    (uuid 04439e70-b428-449c-8252-f1ae41187e35)
  )
  (wire (pts (xy 127 88.9) (xy 127 127))
    (stroke (width 0) (type default))
    (uuid 13012609-7ad9-472e-86c6-03fee6bbd1d7)
  )
  (wire (pts (xy 152.4 88.9) (xy 152.4 91.44))
    (stroke (width 0) (type default))
    (uuid 22edb9f6-6060-4359-acdf-fbaede805588)
  )
  (wire (pts (xy 114.3 50.8) (xy 114.3 52.07))
    (stroke (width 0) (type default))
    (uuid 37b4e902-bf1b-4443-ac9e-cb383b0fc168)
  )
  (wire (pts (xy 127 127) (xy 152.4 127))
    (stroke (width 0) (type default))
    (uuid 3c09a6f2-8250-4109-b583-729fd0ae6962)
  )
  (wire (pts (xy 152.4 127) (xy 152.4 129.54))
    (stroke (width 0) (type default))
    (uuid 440df16b-6ff5-4290-a397-7c8cb5cfa8f9)
  )
  (wire (pts (xy 152.4 139.7) (xy 152.4 137.16))
    (stroke (width 0) (type default))
    (uuid 501f3c0a-9c33-49a4-8f5c-dbfdd1280680)
  )
  (wire (pts (xy 127 50.8) (xy 152.4 50.8))
    (stroke (width 0) (type default))
    (uuid 7b442ca0-947a-4ba5-b5ca-d4993e9d8f1f)
  )
  (wire (pts (xy 152.4 101.6) (xy 152.4 99.06))
    (stroke (width 0) (type default))
    (uuid 7f0873af-4f4b-418f-bdf2-a98356f42411)
  )
  (wire (pts (xy 127 50.8) (xy 127 88.9))
    (stroke (width 0) (type default))
    (uuid *************-43e8-8787-797837904c0c)
  )
  (wire (pts (xy 114.3 50.8) (xy 127 50.8))
    (stroke (width 0) (type default))
    (uuid 8dfaa691-b0dd-464f-a3c8-e61d80a9e815)
  )
  (wire (pts (xy 127 88.9) (xy 152.4 88.9))
    (stroke (width 0) (type default))
    (uuid 9df50b3d-cd9c-4630-8fc2-9781e02cb99b)
  )
  (wire (pts (xy 152.4 63.5) (xy 152.4 60.96))
    (stroke (width 0) (type default))
    (uuid a27ce8ba-81bc-448d-92c5-43377b730c27)
  )
  (wire (pts (xy 156.21 95.25) (xy 165.1 95.25))
    (stroke (width 0) (type default))
    (uuid a58e2b02-3d81-4f91-9994-4ab346e73283)
  )
  (wire (pts (xy 152.4 50.8) (xy 152.4 53.34))
    (stroke (width 0) (type default))
    (uuid b9267405-05c0-4c72-be22-100cadee2870)
  )
  (wire (pts (xy 156.21 133.35) (xy 165.1 133.35))
    (stroke (width 0) (type default))
    (uuid c31a2e32-ab2e-436e-bc0c-f103cafa261e)
  )
  (wire (pts (xy 156.21 57.15) (xy 165.1 57.15))
    (stroke (width 0) (type default))
    (uuid ea67a019-8241-4451-ad06-3cd3ef842f0e)
  )

  (text ";.tran 1u 1m" (at 114.3 38.1 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid ab065b39-ac97-4ee9-aa1e-7c7c6434edbf)
  )
  (text ".op" (at 114.3 41.91 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid cce0b216-de6a-4ebf-b46d-164873cd2c63)
  )

  (label "out2" (at 165.1 95.25 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid a8326323-460c-4dca-9496-e55047e59867)
  )
  (label "out3" (at 165.1 133.35 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid b0ee660c-3c86-4cfc-a039-96dc5be7bc21)
  )
  (label "in" (at 114.3 50.8 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid b4a91548-af2d-4d7b-91b1-91e2c64a1188)
  )
  (label "out1" (at 165.1 57.15 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid ef8fea74-bc8a-49e2-9bda-219af90153ff)
  )

  (netclass_flag "" (length 2.54) (shape round) (at 134.62 50.8 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 0267b22e-f0f5-4503-afd3-8b8f8c8a2238)
    (property "op" "${OP}" (at 135.3185 48.26 0)
      (effects (font (size 1.27 1.27) italic) (justify left))
    )
  )
  (netclass_flag "" (length 2.54) (shape round) (at 158.75 95.25 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 19fb2bcd-aa04-4a99-bf53-4c0d6aba2f25)
    (property "op" "${OP.2V}" (at 159.4485 92.71 0)
      (effects (font (size 1.27 1.27) italic) (justify left))
    )
  )
  (netclass_flag "" (length 2.54) (shape round) (at 158.75 57.15 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid 2ec23ab4-fcb7-40c7-a748-e6a10b2808c5)
    (property "op" "${OP.3mV}" (at 159.4485 54.61 0)
      (effects (font (size 1.27 1.27) italic) (justify left))
    )
  )
  (netclass_flag "" (length 2.54) (shape round) (at 160.02 133.35 0) (fields_autoplaced)
    (effects (font (size 1.27 1.27)) (justify left bottom))
    (uuid bb66a009-cad9-4d11-a1ba-1101278f7a49)
    (property "op" "${OP}" (at 160.7185 130.81 0)
      (effects (font (size 1.27 1.27) italic) (justify left))
    )
  )

  (symbol (lib_id "power:GND") (at 152.4 63.5 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 714a5327-723b-4f39-a927-3d5170d8f38b)
    (property "Reference" "#PWR0102" (at 152.4 69.85 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 152.4 68.58 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 152.4 63.5 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 152.4 63.5 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 885e82e7-2b72-4ef9-93d1-5c4cfd58fe9a))
    (instances
      (project "potentiometers"
        (path "/1982c87f-95a1-4a40-9669-f13cd3c28543"
          (reference "#PWR0102") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R_Potentiometer") (at 152.4 133.35 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid 7d86509c-cffd-4987-9581-6c934a4e33c5)
    (property "Reference" "RV3" (at 149.86 132.715 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "1k" (at 149.86 135.255 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 152.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 152.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 152.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Type" "POT" (at 152.4 133.35 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "pos=0.1" (at 146.05 137.16 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid c77da26e-2c80-4310-84bf-e1a90dc2094f))
    (pin "2" (uuid 03dbec1f-269e-4720-a4dd-2cbbc854fa89))
    (pin "3" (uuid f76a306b-9f82-41eb-a2bd-27e1b85e66b9))
    (instances
      (project "potentiometers"
        (path "/1982c87f-95a1-4a40-9669-f13cd3c28543"
          (reference "RV3") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R_Potentiometer") (at 152.4 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 858d0f36-c16f-4513-940a-461fc88ba009)
    (property "Reference" "RV1" (at 149.86 56.515 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "1k" (at 149.86 59.055 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 152.4 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 152.4 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 152.4 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Type" "POT" (at 152.4 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 72f78aa8-0a10-4afc-bce0-380904d14d13))
    (pin "2" (uuid 23696eac-29f2-4bf5-8d36-b58dc31aade0))
    (pin "3" (uuid a088b589-4331-4a43-9692-eeae2c3927ce))
    (instances
      (project "potentiometers"
        (path "/1982c87f-95a1-4a40-9669-f13cd3c28543"
          (reference "RV1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 152.4 139.7 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 97c6566f-c80b-4dd7-b07e-c44615310263)
    (property "Reference" "#PWR0103" (at 152.4 146.05 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 152.4 144.78 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 152.4 139.7 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 152.4 139.7 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid bf00c644-2773-4f8d-b9db-5d94d618da85))
    (instances
      (project "potentiometers"
        (path "/1982c87f-95a1-4a40-9669-f13cd3c28543"
          (reference "#PWR0103") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Simulation_SPICE:VDC") (at 114.3 57.15 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid b53e13df-0a0c-4917-99d9-00f4258e8f24)
    (property "Reference" "V1" (at 118.11 55.245 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "1" (at 118.11 57.785 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (at 114.3 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 114.3 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "V" (at 114.3 57.15 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 4553662d-6271-4548-b150-dc1eb8221d40))
    (pin "2" (uuid 9ab1f350-5c0f-4459-b0df-abd174706379))
    (instances
      (project "potentiometers"
        (path "/1982c87f-95a1-4a40-9669-f13cd3c28543"
          (reference "V1") (unit 1)
        )
      )
      (project ""
        (path "/819ca68d-3774-4cc3-bc37-7f904ecae47c"
          (reference "V1") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 114.3 63.5 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid d310f44b-5ee6-401e-97cc-5e407654bb6b)
    (property "Reference" "#PWR0104" (at 114.3 69.85 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 114.3 68.58 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 114.3 63.5 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 114.3 63.5 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 4f818b0b-aa2e-44df-888c-470379561a5e))
    (instances
      (project "potentiometers"
        (path "/1982c87f-95a1-4a40-9669-f13cd3c28543"
          (reference "#PWR0104") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "Device:R_Potentiometer") (at 152.4 95.25 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no)
    (uuid e2f6f1d9-7fa5-40e4-8062-077093991555)
    (property "Reference" "RV2" (at 149.86 94.615 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "1k" (at 149.86 97.155 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (at 152.4 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (at 152.4 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Device" "R" (at 152.4 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Type" "POT" (at 152.4 95.25 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Sim.Params" "pos=0.3" (at 146.05 99.06 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid 506d640e-0a3d-4ba3-a5f0-80d908fad32d))
    (pin "2" (uuid dc9b9c37-5353-4880-87bb-8fc37017c528))
    (pin "3" (uuid db81ec35-ea18-4bf8-aefc-34db5616ae22))
    (instances
      (project "potentiometers"
        (path "/1982c87f-95a1-4a40-9669-f13cd3c28543"
          (reference "RV2") (unit 1)
        )
      )
      (project ""
        (path "/c77911d6-6148-44b5-abe9-6b8990dcab43"
          (reference "RV2") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 152.4 101.6 0) (unit 1)
    (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid ee756bad-e6db-46d4-99bc-20add3a7dccf)
    (property "Reference" "#PWR0101" (at 152.4 107.95 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 152.4 106.68 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 152.4 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 152.4 101.6 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid ad7da15a-fc57-429c-a864-0081f2445601))
    (instances
      (project "potentiometers"
        (path "/1982c87f-95a1-4a40-9669-f13cd3c28543"
          (reference "#PWR0101") (unit 1)
        )
      )
    )
  )

  (sheet_instances
    (path "/" (page "1"))
  )
)
