(kicad_pcb
	(version 20240108)
	(generator "pcbnew")
	(generator_version "8.0")
	(general
		(thickness 1.6)
		(legacy_teardrops no)
	)
	(paper "A4")
	(layers
		(0 "F.Cu" signal)
		(31 "B.Cu" signal)
		(32 "B.Adhes" user "B.Adhesive")
		(33 "F<PERSON>hes" user "F.Adhesive")
		(34 "B.Paste" user)
		(35 "F.Paste" user)
		(36 "B.SilkS" user "B.Silkscreen")
		(37 "F.<PERSON>" user "F.Silkscreen")
		(38 "B.Mask" user)
		(39 "F.Mask" user)
		(40 "Dwgs.User" user "User.Drawings")
		(41 "Cmts.User" user "User.Comments")
		(42 "Eco1.User" user "User.Eco1")
		(43 "Eco2.User" user "User.Eco2")
		(44 "Edge.Cuts" user)
		(45 "Margin" user)
		(46 "B.CrtYd" user "B.Courtyard")
		(47 "F.CrtYd" user "F.Courtyard")
		(48 "B.Fab" user)
		(49 "F.Fab" user)
		(50 "User.1" user)
		(51 "User.2" user)
		(52 "User.3" user)
		(53 "User.4" user)
		(54 "User.5" user)
		(55 "User.6" user)
		(56 "User.7" user)
		(57 "User.8" user)
		(58 "User.9" user)
	)
	(setup
		(pad_to_mask_clearance 0)
		(allow_soldermask_bridges_in_footprints no)
		(pcbplotparams
			(layerselection 0x00010fc_ffffffff)
			(plot_on_all_layers_selection 0x0000000_00000000)
			(disableapertmacros no)
			(usegerberextensions no)
			(usegerberattributes yes)
			(usegerberadvancedattributes yes)
			(creategerberjobfile yes)
			(dashed_line_dash_ratio 12.000000)
			(dashed_line_gap_ratio 3.000000)
			(svgprecision 4)
			(plotframeref no)
			(viasonmask no)
			(mode 1)
			(useauxorigin no)
			(hpglpennumber 1)
			(hpglpenspeed 20)
			(hpglpendiameter 15.000000)
			(pdf_front_fp_property_popups yes)
			(pdf_back_fp_property_popups yes)
			(dxfpolygonmode yes)
			(dxfimperialunits yes)
			(dxfusepcbnewfont yes)
			(psnegative no)
			(psa4output no)
			(plotreference yes)
			(plotvalue yes)
			(plotfptext yes)
			(plotinvisibletext no)
			(sketchpadsonfab no)
			(subtractmaskfromsilk no)
			(outputformat 1)
			(mirror no)
			(drillshape 1)
			(scaleselection 1)
			(outputdirectory "")
		)
	)
	(net 0 "")
	(net 1 "a")
	(footprint "test_thermal_spoke_count:C_0805_2012Metric_custom_spokes"
		(layer "F.Cu")
		(uuid "0244deae-bf20-4b88-9777-64b2d7691357")
		(at 115 110)
		(descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
		(tags "capacitor")
		(property "Reference" "REF**"
			(at 0 -1.68 0)
			(layer "F.SilkS")
			(uuid "bc682ff4-2b11-46e3-b1c2-3b6d25fcf846")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "C_0805_2012Metric_custom_spokes"
			(at 0 1.68 0)
			(layer "F.Fab")
			(uuid "072679ec-ac3a-4d1d-a5f7-3744f9ddb4fb")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Footprint" "test_thermal_spoke_count:C_0805_2012Metric_custom_spokes"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "d41501df-c1bb-4186-b903-cbd2995bbbfd")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "c00d6c15-5585-4547-92fe-189864da301a")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "ba30ed52-4e2e-4eb6-9902-9fba4d856030")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(attr smd)
		(fp_line
			(start -0.261252 -0.735)
			(end 0.261252 -0.735)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "de72caec-2256-4d78-bcc7-d40112bd0d81")
		)
		(fp_line
			(start -0.261252 0.735)
			(end 0.261252 0.735)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "4822adf0-3104-4d27-bb76-9d994ee9701c")
		)
		(fp_line
			(start -1.7 -0.98)
			(end 1.7 -0.98)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3ea6d36a-0032-48d1-8378-1f1d69d864f6")
		)
		(fp_line
			(start -1.7 0.98)
			(end -1.7 -0.98)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "2bc289a7-bd66-4c0d-a4ad-57ea85a1dcb6")
		)
		(fp_line
			(start 1.7 -0.98)
			(end 1.7 0.98)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "ec60335f-d34e-498f-a34d-ee6158b03f4e")
		)
		(fp_line
			(start 1.7 0.98)
			(end -1.7 0.98)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "3486fd80-33a9-41c3-97a9-fce5b670b16f")
		)
		(fp_line
			(start -1 -0.625)
			(end 1 -0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "b43c6a88-e2d7-4dbb-919b-dd2f205f049a")
		)
		(fp_line
			(start -1 0.625)
			(end -1 -0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "1e6222e4-17f4-48f4-9cf9-2659b6360c8a")
		)
		(fp_line
			(start 1 -0.625)
			(end 1 0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "6132f1e9-c2e7-4dab-86ef-0c2a1d07ead5")
		)
		(fp_line
			(start 1 0.625)
			(end -1 0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "c558181e-bdde-4a46-8372-85fce1a0cb17")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "2d56eea7-05eb-4805-9a07-fb60a362127e")
			(effects
				(font
					(size 0.5 0.5)
					(thickness 0.08)
				)
			)
		)
		(pad "1" smd custom
			(at -0.95 0)
			(size 1 1)
			(layers "F.Cu" "F.Paste" "F.Mask")
			(net 1 "a")
			(thermal_bridge_angle 90)
			(options
				(clearance outline)
				(anchor circle)
			)
			(primitives
				(gr_poly
					(pts
						(xy -0.5 -0.475) (xy -0.48097 -0.570671) (xy -0.426777 -0.651777) (xy -0.345671 -0.70597) (xy -0.25 -0.725)
						(xy 0.25 -0.725) (xy 0.345671 -0.70597) (xy 0.426777 -0.651777) (xy 0.48097 -0.570671) (xy 0.5 -0.475)
						(xy 0.5 0.475) (xy 0.48097 0.570671) (xy 0.426777 0.651777) (xy 0.345671 0.70597) (xy 0.25 0.725)
						(xy -0.25 0.725) (xy -0.345671 0.70597) (xy -0.426777 0.651777) (xy -0.48097 0.570671) (xy -0.5 0.475)
					)
					(width 0)
					(fill yes)
				)
				(gr_vector
					(start 0 0)
					(end 0 1.5)
				)
				(gr_vector
					(start 0 0)
					(end -1.55 0)
				)
				(gr_vector
					(start 0 0)
					(end 0 -1.5)
				)
			)
			(uuid "57856e2d-8127-4a1b-a518-b91d9a7d047b")
		)
		(pad "2" smd custom
			(at 0.95 0)
			(size 1 1)
			(layers "F.Cu" "F.Paste" "F.Mask")
			(net 1 "a")
			(thermal_bridge_angle 90)
			(options
				(clearance outline)
				(anchor circle)
			)
			(primitives
				(gr_poly
					(pts
						(xy -0.5 -0.475) (xy -0.48097 -0.570671) (xy -0.426777 -0.651777) (xy -0.345671 -0.70597) (xy -0.25 -0.725)
						(xy 0.25 -0.725) (xy 0.345671 -0.70597) (xy 0.426777 -0.651777) (xy 0.48097 -0.570671) (xy 0.5 -0.475)
						(xy 0.5 0.475) (xy 0.48097 0.570671) (xy 0.426777 0.651777) (xy 0.345671 0.70597) (xy 0.25 0.725)
						(xy -0.25 0.725) (xy -0.345671 0.70597) (xy -0.426777 0.651777) (xy -0.48097 0.570671) (xy -0.5 0.475)
					)
					(width 0)
					(fill yes)
				)
				(gr_vector
					(start 0.05 0)
					(end 1.55 0)
				)
			)
			(uuid "79e6da3c-a69f-45fb-993e-66f3776e8fb6")
		)
		(model "${KICAD8_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(footprint "test_thermal_spoke_count:C_0805_2012Metric"
		(layer "F.Cu")
		(uuid "7f0321db-d18d-4db3-814e-d3cbe9174170")
		(at 115 115)
		(descr "Capacitor SMD 0805 (2012 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf, https://docs.google.com/spreadsheets/d/1BsfQQcO9C6DZCsRaXUlFlo91Tg2WpOkGARC1WS5S8t0/edit?usp=sharing), generated with kicad-footprint-generator")
		(tags "capacitor")
		(property "Reference" "REF**"
			(at 0 -1.68 0)
			(layer "F.SilkS")
			(uuid "7620a590-f89d-4338-b569-b31a139cf2c6")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Value" "C_0805_2012Metric"
			(at 0 1.68 0)
			(layer "F.Fab")
			(uuid "98b0617c-b5d6-4d36-a22a-c269f6e7ab8a")
			(effects
				(font
					(size 1 1)
					(thickness 0.15)
				)
			)
		)
		(property "Footprint" "Capacitor_SMD:C_0805_2012Metric"
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "4e244502-c332-4674-a9f9-127ed7e2c613")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Datasheet" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "3a99221b-4466-4711-a20f-943b695fae94")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(property "Description" ""
			(at 0 0 0)
			(unlocked yes)
			(layer "F.Fab")
			(hide yes)
			(uuid "0ff62164-ea53-42e9-82fc-fbd2d12a6c22")
			(effects
				(font
					(size 1.27 1.27)
					(thickness 0.15)
				)
			)
		)
		(attr smd)
		(fp_line
			(start -0.261252 -0.735)
			(end 0.261252 -0.735)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "e96917d9-4815-46d8-a69b-************")
		)
		(fp_line
			(start -0.261252 0.735)
			(end 0.261252 0.735)
			(stroke
				(width 0.12)
				(type solid)
			)
			(layer "F.SilkS")
			(uuid "0bfdab70-dbf6-4782-a9e2-149880329c1b")
		)
		(fp_line
			(start -1.7 -0.98)
			(end 1.7 -0.98)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "b141feac-ac7f-44c1-b0b9-333393f5d977")
		)
		(fp_line
			(start -1.7 0.98)
			(end -1.7 -0.98)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "67ad3e16-d526-41a4-8481-5c7227eefda6")
		)
		(fp_line
			(start 1.7 -0.98)
			(end 1.7 0.98)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "d6dbbd74-3b67-47c1-8692-78970209fe2d")
		)
		(fp_line
			(start 1.7 0.98)
			(end -1.7 0.98)
			(stroke
				(width 0.05)
				(type solid)
			)
			(layer "F.CrtYd")
			(uuid "fb55b0d1-516b-454d-b643-f0997bb6c2db")
		)
		(fp_line
			(start -1 -0.625)
			(end 1 -0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "07045590-ab12-48d6-a987-618e487ea022")
		)
		(fp_line
			(start -1 0.625)
			(end -1 -0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "f48448bf-bf44-463d-8e03-9ee9b592249f")
		)
		(fp_line
			(start 1 -0.625)
			(end 1 0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "350fcc90-360d-4ed2-a8b9-07d8a2068ca3")
		)
		(fp_line
			(start 1 0.625)
			(end -1 0.625)
			(stroke
				(width 0.1)
				(type solid)
			)
			(layer "F.Fab")
			(uuid "15bfbddb-9283-4485-b44c-b323dce8bd05")
		)
		(fp_text user "${REFERENCE}"
			(at 0 0 0)
			(layer "F.Fab")
			(uuid "b8220eaf-1387-419f-ab7b-c4d751ed6ead")
			(effects
				(font
					(size 0.5 0.5)
					(thickness 0.08)
				)
			)
		)
		(pad "1" smd roundrect
			(at -0.95 0)
			(size 1 1.45)
			(layers "F.Cu" "F.Paste" "F.Mask")
			(roundrect_rratio 0.25)
			(net 1 "a")
			(uuid "88a94303-0d98-40f5-9430-aa0040cc0e14")
		)
		(pad "2" smd roundrect
			(at 0.95 0)
			(size 1 1.45)
			(layers "F.Cu" "F.Paste" "F.Mask")
			(roundrect_rratio 0.25)
			(net 1 "a")
			(uuid "32dc8534-4659-4a08-bc1c-d65ac79b10c2")
		)
		(model "${KICAD8_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_0805_2012Metric.wrl"
			(offset
				(xyz 0 0 0)
			)
			(scale
				(xyz 1 1 1)
			)
			(rotate
				(xyz 0 0 0)
			)
		)
	)
	(gr_line
		(start 105 120)
		(end 105 105)
		(stroke
			(width 0.05)
			(type default)
		)
		(layer "Edge.Cuts")
		(uuid "2d491e9d-9826-4802-9d04-148f1a1d0082")
	)
	(gr_line
		(start 105 105)
		(end 125 105)
		(stroke
			(width 0.05)
			(type default)
		)
		(layer "Edge.Cuts")
		(uuid "4f1fbf4a-901e-47ab-bcc0-f6b79bd079e0")
	)
	(gr_line
		(start 125 105)
		(end 125 120)
		(stroke
			(width 0.05)
			(type default)
		)
		(layer "Edge.Cuts")
		(uuid "5b3a6e6a-a5a4-4fa7-9aa3-61c213eaf61a")
	)
	(gr_line
		(start 125 120)
		(end 105 120)
		(stroke
			(width 0.05)
			(type default)
		)
		(layer "Edge.Cuts")
		(uuid "8dec62ec-2e2a-4ba3-b755-50add5a4189d")
	)
	(zone
		(net 1)
		(net_name "a")
		(layer "F.Cu")
		(uuid "681a4c00-defd-41da-8504-1bb039b834fa")
		(hatch edge 0.5)
		(connect_pads
			(clearance 0.5)
		)
		(min_thickness 0.25)
		(filled_areas_thickness no)
		(fill yes
			(thermal_gap 0.5)
			(thermal_bridge_width 0.5)
			(island_removal_mode 1)
			(island_area_min 10)
		)
		(polygon
			(pts
				(xy 105 109) (xy 115 109) (xy 115 105) (xy 125 105) (xy 125 116) (xy 115 116) (xy 115 120) (xy 105 120)
			)
		)
		(filled_polygon
			(layer "F.Cu")
			(pts
				(xy 124.442539 105.520185) (xy 124.488294 105.572989) (xy 124.4995 105.6245) (xy 124.4995 115.876)
				(xy 124.479815 115.943039) (xy 124.427011 115.988794) (xy 124.3755 116) (xy 116.979546 116) (xy 116.912507 115.980315)
				(xy 116.866752 115.927511) (xy 116.856808 115.858353) (xy 116.874007 115.810904) (xy 116.884354 115.794128)
				(xy 116.884358 115.794119) (xy 116.939505 115.627697) (xy 116.939506 115.62769) (xy 116.949999 115.524986)
				(xy 116.95 115.524973) (xy 116.95 115.25) (xy 114.3 115.25) (xy 114.3 116.224999) (xy 114.349972 116.224999)
				(xy 114.349986 116.224998) (xy 114.452697 116.214505) (xy 114.619119 116.159358) (xy 114.619124 116.159356)
				(xy 114.768345 116.067315) (xy 114.788319 116.047342) (xy 114.849642 116.013857) (xy 114.919334 116.018841)
				(xy 114.975267 116.060713) (xy 114.999684 116.126177) (xy 115 116.135023) (xy 115 119.3755) (xy 114.980315 119.442539)
				(xy 114.927511 119.488294) (xy 114.876 119.4995) (xy 105.6245 119.4995) (xy 105.557461 119.479815)
				(xy 105.511706 119.427011) (xy 105.5005 119.3755) (xy 105.5005 115.524986) (xy 113.050001 115.524986)
				(xy 113.060494 115.627697) (xy 113.115641 115.794119) (xy 113.115643 115.794124) (xy 113.207684 115.943345)
				(xy 113.331654 116.067315) (xy 113.480875 116.159356) (xy 113.48088 116.159358) (xy 113.647302 116.214505)
				(xy 113.647309 116.214506) (xy 113.750019 116.224999) (xy 113.799999 116.224998) (xy 113.8 116.224998)
				(xy 113.8 115.25) (xy 113.050001 115.25) (xy 113.050001 115.524986) (xy 105.5005 115.524986) (xy 105.5005 114.475013)
				(xy 113.05 114.475013) (xy 113.05 114.75) (xy 113.8 114.75) (xy 113.8 113.775) (xy 114.3 113.775)
				(xy 114.3 114.75) (xy 115.7 114.75) (xy 116.2 114.75) (xy 116.949999 114.75) (xy 116.949999 114.475028)
				(xy 116.949998 114.475013) (xy 116.939505 114.372302) (xy 116.884358 114.20588) (xy 116.884356 114.205875)
				(xy 116.792315 114.056654) (xy 116.668345 113.932684) (xy 116.519124 113.840643) (xy 116.519119 113.840641)
				(xy 116.352697 113.785494) (xy 116.35269 113.785493) (xy 116.249986 113.775) (xy 116.2 113.775)
				(xy 116.2 114.75) (xy 115.7 114.75) (xy 115.7 113.775) (xy 115.699999 113.774999) (xy 115.650029 113.775)
				(xy 115.650011 113.775001) (xy 115.547302 113.785494) (xy 115.38088 113.840641) (xy 115.380875 113.840643)
				(xy 115.231654 113.932684) (xy 115.107683 114.056655) (xy 115.107681 114.056658) (xy 115.105536 114.060136)
				(xy 115.103442 114.062018) (xy 115.103202 114.062323) (xy 115.10315 114.062281) (xy 115.053587 114.106858)
				(xy 114.984624 114.118078) (xy 114.920543 114.090232) (xy 114.894464 114.060136) (xy 114.892318 114.056658)
				(xy 114.892316 114.056655) (xy 114.768345 113.932684) (xy 114.619124 113.840643) (xy 114.619119 113.840641)
				(xy 114.452697 113.785494) (xy 114.45269 113.785493) (xy 114.349986 113.775) (xy 114.3 113.775)
				(xy 113.8 113.775) (xy 113.799999 113.774999) (xy 113.750029 113.775) (xy 113.750011 113.775001)
				(xy 113.647302 113.785494) (xy 113.48088 113.840641) (xy 113.480875 113.840643) (xy 113.331654 113.932684)
				(xy 113.207684 114.056654) (xy 113.115643 114.205875) (xy 113.115641 114.20588) (xy 113.060494 114.372302)
				(xy 113.060493 114.372309) (xy 113.05 114.475013) (xy 105.5005 114.475013) (xy 105.5005 110.474996)
				(xy 113.045 110.474996) (xy 113.054701 110.573507) (xy 113.054701 110.57351) (xy 113.073732 110.66919)
				(xy 113.073735 110.669202) (xy 113.0871 110.721565) (xy 113.087103 110.721573) (xy 113.149135 110.851231)
				(xy 113.14914 110.851238) (xy 113.20331 110.93231) (xy 113.203335 110.932345) (xy 113.235725 110.975616)
				(xy 113.342664 111.071672) (xy 113.342671 111.071677) (xy 113.423745 111.125849) (xy 113.423771 111.125866)
				(xy 113.470256 111.153445) (xy 113.470254 111.153445) (xy 113.60581 111.201267) (xy 113.701474 111.220295)
				(xy 113.701492 111.220298) (xy 113.799999 111.229999) (xy 113.8 111.229999) (xy 113.8 110.25) (xy 113.045 110.25)
				(xy 113.045 110.474996) (xy 105.5005 110.474996) (xy 105.5005 109.124) (xy 105.520185 109.056961)
				(xy 105.572989 109.011206) (xy 105.6245 109) (xy 113.019649 109) (xy 113.086688 109.019685) (xy 113.132443 109.072489)
				(xy 113.142387 109.141647) (xy 113.126292 109.187271) (xy 113.121554 109.195256) (xy 113.073732 109.33081)
				(xy 113.073732 109.330811) (xy 113.054704 109.426474) (xy 113.054701 109.426492) (xy 113.045 109.525003)
				(xy 113.045 109.75) (xy 113.926 109.75) (xy 113.993039 109.769685) (xy 114.038794 109.822489) (xy 114.05 109.874)
				(xy 114.05 110) (xy 114.176 110) (xy 114.243039 110.019685) (xy 114.288794 110.072489) (xy 114.3 110.124)
				(xy 114.3 111.229999) (xy 114.398507 111.220298) (xy 114.39851 111.220298) (xy 114.49419 111.201267)
				(xy 114.494202 111.201264) (xy 114.546565 111.187899) (xy 114.546573 111.187896) (xy 114.676231 111.125864)
				(xy 114.676238 111.125859) (xy 114.75731 111.071689) (xy 114.757345 111.071664) (xy 114.800616 111.039274)
				(xy 114.896671 110.932336) (xy 114.896889 110.932011) (xy 114.89699 110.931926) (xy 114.899393 110.928834)
				(xy 114.900064 110.929356) (xy 114.950495 110.887199) (xy 115.019819 110.878482) (xy 115.082851 110.908629)
				(xy 115.10227 110.931042) (xy 115.102365 110.930975) (xy 115.103048 110.93194) (xy 115.103119 110.932022)
				(xy 115.103335 110.932346) (xy 115.135725 110.975616) (xy 115.242664 111.071672) (xy 115.242671 111.071677)
				(xy 115.323745 111.125849) (xy 115.323771 111.125866) (xy 115.370256 111.153445) (xy 115.370254 111.153445)
				(xy 115.50581 111.201267) (xy 115.601474 111.220295) (xy 115.601492 111.220298) (xy 115.700003 111.23)
				(xy 116.199997 111.23) (xy 116.298507 111.220298) (xy 116.29851 111.220298) (xy 116.39419 111.201267)
				(xy 116.394202 111.201264) (xy 116.446565 111.187899) (xy 116.446573 111.187896) (xy 116.576231 111.125864)
				(xy 116.576238 111.125859) (xy 116.65731 111.071689) (xy 116.657345 111.071664) (xy 116.700616 111.039274)
				(xy 116.796672 110.932335) (xy 116.796677 110.932328) (xy 116.850849 110.851254) (xy 116.850866 110.851228)
				(xy 116.878445 110.804744) (xy 116.926267 110.669189) (xy 116.926267 110.669188) (xy 116.945295 110.573525)
				(xy 116.945298 110.573507) (xy 116.955 110.474996) (xy 116.955 110.25) (xy 116.124 110.25) (xy 116.056961 110.230315)
				(xy 116.011206 110.177511) (xy 116 110.126) (xy 116 109.874) (xy 116.019685 109.806961) (xy 116.072489 109.761206)
				(xy 116.124 109.75) (xy 116.955 109.75) (xy 116.955 109.525003) (xy 116.945298 109.426492) (xy 116.945298 109.426489)
				(xy 116.926267 109.330809) (xy 116.926264 109.330797) (xy 116.912899 109.278434) (xy 116.912896 109.278426)
				(xy 116.850864 109.148768) (xy 116.850859 109.148761) (xy 116.796689 109.067689) (xy 116.796664 109.067654)
				(xy 116.764274 109.024383) (xy 116.657335 108.928327) (xy 116.657328 108.928322) (xy 116.576254 108.87415)
				(xy 116.576228 108.874133) (xy 116.529743 108.846554) (xy 116.529745 108.846554) (xy 116.394189 108.798732)
				(xy 116.298525 108.779704) (xy 116.298507 108.779701) (xy 116.199997 108.77) (xy 115.700003 108.77)
				(xy 115.601492 108.779701) (xy 115.601489 108.779701) (xy 115.505809 108.798732) (xy 115.505797 108.798735)
				(xy 115.453434 108.8121) (xy 115.453426 108.812103) (xy 115.323768 108.874135) (xy 115.323761 108.87414)
				(xy 115.242689 108.92831) (xy 115.24266 108.928331) (xy 115.198308 108.961531) (xy 115.132843 108.985946)
				(xy 115.064571 108.971093) (xy 115.015166 108.921686) (xy 115 108.862262) (xy 115 105.6245) (xy 115.019685 105.557461)
				(xy 115.072489 105.511706) (xy 115.124 105.5005) (xy 124.3755 105.5005)
			)
		)
	)
)
