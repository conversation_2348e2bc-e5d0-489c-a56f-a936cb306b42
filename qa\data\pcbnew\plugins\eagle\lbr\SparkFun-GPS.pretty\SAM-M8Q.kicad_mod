(footprint "SAM-M8Q" (version 20231007) (generator pcbnew)
  (layer "F.Cu")
  (descr "SAM-M8Q-0-10 Physical Characteristics\n\n• 15x15x6.3mm\n• Module is a Chip Antenna/GPS Unit combined")
  (property "Reference" "REF**" (at -7.75 -8.25 0 unlocked) (layer "F.SilkS") (tstamp ff9f075c-af5c-4d1b-bcae-c092251a9631)
    (effects (font (size 0.46736 0.46736) (thickness 0.04064)) (justify left bottom))
  )
  (property "Value" "SAM-M8Q" (at -7.75 8.75 0 unlocked) (layer "F.Fab") (tstamp 04e6b5b2-45a6-4e0c-a09c-8e4767e481c3)
    (effects (font (size 0.46736 0.46736) (thickness 0.04064)) (justify left bottom))
  )
  (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp b4ef4d13-d569-4048-8adb-8c98d767d91d)
    (effects (font (size 1.27 1.27)))
  )
  (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp db76060d-8b68-4390-8f9a-7dc018bebf94)
    (effects (font (size 1.27 1.27)))
  )
  (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp fcf7555e-9f4e-4968-b087-90a9a66595ef)
    (effects (font (size 1.27 1.27)))
  )
  (fp_line (start -8 -8) (end -6.2 -8)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 0a9fca7d-5255-4bd3-89cb-2aa10276cea9))
  (fp_line (start -8 -6.2) (end -8 -8)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 71c9843d-000f-4f0a-b48c-c336ae35d1d4))
  (fp_line (start -8 8) (end -8 6.2)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 88cc864e-1d60-4059-b923-c05c2b09f42b))
  (fp_line (start -6.2 8) (end -8 8)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp cbc9f9a7-aae0-479c-8cb0-6e588de1c482))
  (fp_line (start 6.2 -8) (end 8 -8)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp e5c60204-e405-4b35-aa78-636c4d7fb71f))
  (fp_line (start 8 -8) (end 8 -6.2)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 11a6494e-9d04-4dfc-8ce9-6ae3d9077973))
  (fp_line (start 8 6.2) (end 8 8)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 07064619-2b9a-4075-9711-bafa999b63ae))
  (fp_line (start 8 8) (end 6.2 8)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 4c62a850-42b5-4e3e-af67-aa0b0021e295))
  (fp_circle (center -8.89 -7.62) (end -8.689197 -7.62)
    (stroke (width 0.401606) (type solid)) (fill solid) (layer "F.SilkS") (tstamp f6a38ce5-8378-4424-8d9d-0e1c80696c94))
  (fp_poly
    (pts
      (xy -5.08 5.08)
      (xy 5.08 5.08)
      (xy 5.08 -5.08)
      (xy -5.08 -5.08)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "B.CrtYd") (tstamp e14c545e-1a12-40b8-b3a3-0a517cfd508d))
  (fp_poly
    (pts
      (xy -5.08 5.08)
      (xy 5.08 5.08)
      (xy 5.08 -5.08)
      (xy -5.08 -5.08)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.CrtYd") (tstamp 4d8f2242-553f-4b5f-9c9e-3988377b0da6))
  (fp_line (start -7.75 -7.75) (end 7.75 -7.75)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 333c9438-2f61-4355-840b-8b2067483ddb))
  (fp_line (start -7.75 7.75) (end -7.75 -7.75)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 4cd10ee5-b1e7-441b-909b-aae3972bc853))
  (fp_line (start 7.75 -7.75) (end 7.75 7.75)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 5bb0338e-edf0-400c-989e-5743edb86e53))
  (fp_line (start 7.75 7.75) (end -7.75 7.75)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 2a63c45a-1184-492a-bfe7-3fd48fab065e))
  (fp_poly
    (pts
      (xy -7.65 -3.05)
      (xy -5.85 -3.05)
      (xy -5.85 -4.55)
      (xy -7.65 -4.55)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 6cacf536-5968-457c-a709-e1c9a3eb259c))
  (fp_poly
    (pts
      (xy -7.65 -1.15)
      (xy -5.85 -1.15)
      (xy -5.85 -2.65)
      (xy -7.65 -2.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 21d3646c-5385-4749-b791-cf081e412eaf))
  (fp_poly
    (pts
      (xy -7.65 0.75)
      (xy -5.85 0.75)
      (xy -5.85 -0.75)
      (xy -7.65 -0.75)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 82ea5b72-3bf8-4c19-b625-d7d3fad87108))
  (fp_poly
    (pts
      (xy -7.65 2.65)
      (xy -5.85 2.65)
      (xy -5.85 1.15)
      (xy -7.65 1.15)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp b98e8e6e-1d2e-49d5-9275-aa46cb762203))
  (fp_poly
    (pts
      (xy -7.65 4.55)
      (xy -5.85 4.55)
      (xy -5.85 3.05)
      (xy -7.65 3.05)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp aa7118cd-64e9-41a6-810d-08827c969caa))
  (fp_poly
    (pts
      (xy -4.55 -5.85)
      (xy -3.05 -5.85)
      (xy -3.05 -7.65)
      (xy -4.55 -7.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 81c8b15b-fd22-4644-b940-7015ab2e5736))
  (fp_poly
    (pts
      (xy -4.55 7.65)
      (xy -3.05 7.65)
      (xy -3.05 5.85)
      (xy -4.55 5.85)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp cd9439b6-2d0c-41d0-8c65-0546f7b1fd15))
  (fp_poly
    (pts
      (xy -2.65 -5.85)
      (xy -1.15 -5.85)
      (xy -1.15 -7.65)
      (xy -2.65 -7.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp ab164212-fca1-4742-9542-79d5b1c0327e))
  (fp_poly
    (pts
      (xy -2.65 7.65)
      (xy -1.15 7.65)
      (xy -1.15 5.85)
      (xy -2.65 5.85)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 44b472e7-0ff3-4aa2-bf09-a651040b0d62))
  (fp_poly
    (pts
      (xy -0.75 -5.85)
      (xy 0.75 -5.85)
      (xy 0.75 -7.65)
      (xy -0.75 -7.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 38c66c5b-db2c-430f-a4f3-f716009fab82))
  (fp_poly
    (pts
      (xy -0.75 7.65)
      (xy 0.75 7.65)
      (xy 0.75 5.85)
      (xy -0.75 5.85)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 02767383-e3f1-465f-a038-3202882eb82d))
  (fp_poly
    (pts
      (xy 1.15 -5.85)
      (xy 2.65 -5.85)
      (xy 2.65 -7.65)
      (xy 1.15 -7.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 6885d0bc-7f1d-4e49-92e7-4af25b1fb949))
  (fp_poly
    (pts
      (xy 1.15 7.65)
      (xy 2.65 7.65)
      (xy 2.65 5.85)
      (xy 1.15 5.85)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp efb1d18a-dcc8-4416-b659-d7cd7eb7e364))
  (fp_poly
    (pts
      (xy 3.05 -5.85)
      (xy 4.55 -5.85)
      (xy 4.55 -7.65)
      (xy 3.05 -7.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp df00a9e7-0b0c-4db3-9a5d-11ffb4386b02))
  (fp_poly
    (pts
      (xy 3.05 7.65)
      (xy 4.55 7.65)
      (xy 4.55 5.85)
      (xy 3.05 5.85)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 47b63a05-e83a-47e6-9633-ace4e284fb2a))
  (fp_poly
    (pts
      (xy 5.85 -3.05)
      (xy 7.65 -3.05)
      (xy 7.65 -4.55)
      (xy 5.85 -4.55)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp c589ec5e-1409-497d-a5b7-77d919d5115b))
  (fp_poly
    (pts
      (xy 5.85 -1.15)
      (xy 7.65 -1.15)
      (xy 7.65 -2.65)
      (xy 5.85 -2.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp f885c435-7f70-46f3-812b-35e083d5a0c1))
  (fp_poly
    (pts
      (xy 5.85 0.75)
      (xy 7.65 0.75)
      (xy 7.65 -0.75)
      (xy 5.85 -0.75)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 64d2557e-2123-4f58-aab0-aaca1bd5a6a5))
  (fp_poly
    (pts
      (xy 5.85 2.65)
      (xy 7.65 2.65)
      (xy 7.65 1.15)
      (xy 5.85 1.15)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp b03ef02a-a35c-4d5a-80eb-d1f88850d923))
  (fp_poly
    (pts
      (xy 5.85 4.55)
      (xy 7.65 4.55)
      (xy 7.65 3.05)
      (xy 5.85 3.05)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 421239cd-b6a0-4c5c-8116-daacbdacddaf))
  (pad "1" smd rect (at -7.125 -3.8 180) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 77c41b13-89fc-4656-8d9e-e613fb1e6c3b)
  )
  (pad "2" smd rect (at -7.125 -1.9 180) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 18b4d7b9-e313-44cd-97da-879609b46c6b)
  )
  (pad "3" smd rect (at -7.125 0 180) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 003d5aba-d469-42ee-91fa-a055d60e30a8)
  )
  (pad "4" smd rect (at -7.125 1.9 180) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp be450f72-766d-4e4a-a2ec-17f8af62f8b6)
  )
  (pad "5" smd rect (at -7.125 3.8 180) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 83761610-4dc3-49f2-941d-311cdc1e3f0f)
  )
  (pad "6" smd rect (at -3.8 7.125 90) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 6e63eb71-029d-4b5c-a494-5e2e5e620a9b)
  )
  (pad "7" smd rect (at -1.9 7.125 90) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 0fbe6295-6d7f-460e-a30e-0154ddd93e68)
  )
  (pad "8" smd rect (at 0 7.125 90) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp c3de6cdc-3ff0-4646-814a-75d93ca47a44)
  )
  (pad "9" smd rect (at 1.9 7.125 90) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp aa3e60a3-feb9-4422-88cd-a4a4553b5c3d)
  )
  (pad "10" smd rect (at 3.8 7.125 90) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp d3c7669c-3006-4e70-a26c-c1aae7909878)
  )
  (pad "11" smd rect (at 7.125 3.8) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b59a9513-d6a4-471f-905c-a12e8c832e75)
  )
  (pad "12" smd rect (at 7.125 1.9) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 08963018-e28f-4bee-a741-9f6a7d30cca0)
  )
  (pad "13" smd rect (at 7.125 0) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 3a2e52c3-addb-45d5-84ed-6a1f65ade793)
  )
  (pad "14" smd rect (at 7.125 -1.9) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp c8d96cbb-00ae-4675-9fdf-f7a50d4c2717)
  )
  (pad "15" smd rect (at 7.125 -3.8) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 0eba95b2-9479-4263-9fb2-1bec2fee1802)
  )
  (pad "16" smd rect (at 3.8 -7.125 270) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 27c57b09-456f-4a20-8bf1-4f52ce9c521d)
  )
  (pad "17" smd rect (at 1.9 -7.125 270) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 35fa7e68-d2fa-49f6-a0e6-b7283de163db)
  )
  (pad "18" smd rect (at 0 -7.125 270) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp c432612e-299b-40e0-9114-aa7dbd62d479)
  )
  (pad "19" smd rect (at -1.9 -7.125 270) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp ab4f444d-deae-4c50-8be3-f1c17eec7fd6)
  )
  (pad "20" smd rect (at -3.8 -7.125 270) (size 2.75 1.6) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp af768910-0695-4984-9e63-bfb33801ef70)
  )
)
