(kicad_pcb (version 20200628) (host pcbnew "(5.99.0-2386-g51f3b0bf76-dirty)")

  (general
    (thickness 1.6002)
    (drawings 0)
    (tracks 15)
    (modules 4)
    (nets 5)
  )

  (paper "A3")
  (title_block
    (title "KiCad demo")
    (date "2015-10-14")
    (rev "1.A")
  )

  (layers
    (0 "Composant" signal)
    (1 "GND_layer" signal)
    (2 "VCC_layer" signal)
    (31 "Cuivre" signal)
    (32 "B.Adhes" user)
    (33 "F.Adhes" user)
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user)
    (37 "F.SilkS" user)
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user)
    (41 "Cmts.User" user)
    (42 "Eco1.User" user)
    (43 "Eco2.User" user)
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user)
    (47 "F.CrtYd" user)
  )

  (setup
    (aux_axis_origin 40.9 173.1)
    (pcbplotparams
      (layerselection 0x010fc_80000007)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (svguseinch false)
      (svgprecision 6)
      (excludeedgelayer false)
      (linewidth 0.100000)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin true)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "plots")
    )
  )

  (net 0 "")
  (net 1 "Net-(C48-Pad2)")
  (net 2 "Net-(L6-Pad1)")
  (net 3 "Net-(L6-Pad2)")
  (net 4 "Net-(R10-Pad2)")

  (module "lib_smd:SM1206" (layer "Cuivre") (tedit 54019107) (tstamp cf6d41b6-be24-4142-a50b-7bf0419214d3)
    (at 77.31 42.02 90)
    (path "/00000000-0000-0000-0000-00004bf0367f/00000000-0000-0000-0000-000022760fbc")
    (attr smd)
    (fp_text reference "R11" (at 0 0 -90) (layer "B.SilkS")
      (effects (font (size 0.762 0.762) (thickness 0.127)) (justify mirror))
    )
    (fp_text value "1K" (at 0 0 -90) (layer "B.SilkS") hide
      (effects (font (size 0.762 0.762) (thickness 0.127)) (justify mirror))
    )
    (fp_line (start -0.889 1.143) (end -2.54 1.143) (layer "B.SilkS") (width 0.127))
    (fp_line (start 2.54 -1.143) (end 0.889 -1.143) (layer "B.SilkS") (width 0.127))
    (fp_line (start 2.54 1.143) (end 2.54 -1.143) (layer "B.SilkS") (width 0.127))
    (fp_line (start 0.889 1.143) (end 2.54 1.143) (layer "B.SilkS") (width 0.127))
    (fp_line (start -2.54 -1.143) (end -0.889 -1.143) (layer "B.SilkS") (width 0.127))
    (fp_line (start -2.54 1.143) (end -2.54 -1.143) (layer "B.SilkS") (width 0.127))
    (pad "2" smd rect (at 1.651 0 90) (size 1.524 2.032) (layers "Cuivre" "B.Paste" "B.Mask")
      (net 1 "Net-(C48-Pad2)") (tstamp 11cb9d3f-10e3-41e4-b53f-517fc40917f8))
    (pad "1" smd rect (at -1.651 0 90) (size 1.524 2.032) (layers "Cuivre" "B.Paste" "B.Mask")
      (net 3 "Net-(L6-Pad2)") (tstamp aa67310d-5884-4d05-8dcf-bf41060498ff))
    (model "SMD_Packages.3dshapes/SMD-1206.wrl"
      (at (xyz 0 0 0))
      (scale (xyz 0.17 0.16 0.16))
      (rotate (xyz 0 0 0))
    )
  )

  (module "lib_smd:SM1206" (layer "Cuivre") (tedit 54019107) (tstamp 7878478d-dbff-4e07-9acb-bbb7715fff75)
    (at 74.75 42.046 -90)
    (path "/00000000-0000-0000-0000-00004bf0367f/00000000-0000-0000-0000-000022760f80")
    (attr smd)
    (fp_text reference "R10" (at 0 0 -270) (layer "B.SilkS")
      (effects (font (size 0.762 0.762) (thickness 0.127)) (justify mirror))
    )
    (fp_text value "1K" (at 0 0 -270) (layer "B.SilkS") hide
      (effects (font (size 0.762 0.762) (thickness 0.127)) (justify mirror))
    )
    (fp_line (start -0.889 1.143) (end -2.54 1.143) (layer "B.SilkS") (width 0.127))
    (fp_line (start 2.54 -1.143) (end 0.889 -1.143) (layer "B.SilkS") (width 0.127))
    (fp_line (start 2.54 1.143) (end 2.54 -1.143) (layer "B.SilkS") (width 0.127))
    (fp_line (start 0.889 1.143) (end 2.54 1.143) (layer "B.SilkS") (width 0.127))
    (fp_line (start -2.54 -1.143) (end -0.889 -1.143) (layer "B.SilkS") (width 0.127))
    (fp_line (start -2.54 1.143) (end -2.54 -1.143) (layer "B.SilkS") (width 0.127))
    (pad "2" smd rect (at 1.651 0 270) (size 1.524 2.032) (layers "Cuivre" "B.Paste" "B.Mask")
      (net 4 "Net-(R10-Pad2)") (tstamp b1cfb053-add6-4c39-a602-ee8cec92ed6d))
    (pad "1" smd rect (at -1.651 0 270) (size 1.524 2.032) (layers "Cuivre" "B.Paste" "B.Mask")
      (net 2 "Net-(L6-Pad1)") (tstamp d85d64ec-0c45-4808-b66c-a6e95250cd7d))
    (model "SMD_Packages.3dshapes/SMD-1206.wrl"
      (at (xyz 0 0 0))
      (scale (xyz 0.17 0.16 0.16))
      (rotate (xyz 0 0 0))
    )
  )

  (module "lib_smd:SM1206" (layer "Composant") (tedit 54019107) (tstamp 00000000-0000-0000-0000-00005402cf81)
    (at 57.26 43.686 90)
    (path "/00000000-0000-0000-0000-00004bf0367f/00000000-0000-0000-0000-000022760f80")
    (attr smd)
    (fp_text reference "R10" (at 0 0 270) (layer "F.SilkS")
      (effects (font (size 0.762 0.762) (thickness 0.127)))
    )
    (fp_text value "1K" (at 0 0 270) (layer "F.SilkS") hide
      (effects (font (size 0.762 0.762) (thickness 0.127)))
    )
    (fp_line (start -2.54 -1.143) (end -2.54 1.143) (layer "F.SilkS") (width 0.127))
    (fp_line (start -2.54 1.143) (end -0.889 1.143) (layer "F.SilkS") (width 0.127))
    (fp_line (start 0.889 -1.143) (end 2.54 -1.143) (layer "F.SilkS") (width 0.127))
    (fp_line (start 2.54 -1.143) (end 2.54 1.143) (layer "F.SilkS") (width 0.127))
    (fp_line (start 2.54 1.143) (end 0.889 1.143) (layer "F.SilkS") (width 0.127))
    (fp_line (start -0.889 -1.143) (end -2.54 -1.143) (layer "F.SilkS") (width 0.127))
    (pad "1" smd rect (at -1.651 0 90) (size 1.524 2.032) (layers "Composant" "F.Paste" "F.Mask")
      (net 2 "Net-(L6-Pad1)") (tstamp d85d64ec-0c45-4808-b66c-a6e95250cd7d))
    (pad "2" smd rect (at 1.651 0 90) (size 1.524 2.032) (layers "Composant" "F.Paste" "F.Mask")
      (net 4 "Net-(R10-Pad2)") (tstamp b1cfb053-add6-4c39-a602-ee8cec92ed6d))
    (model "SMD_Packages.3dshapes/SMD-1206.wrl"
      (at (xyz 0 0 0))
      (scale (xyz 0.17 0.16 0.16))
      (rotate (xyz 0 0 0))
    )
  )

  (module "lib_smd:SM1206" (layer "Composant") (tedit 54019107) (tstamp 00000000-0000-0000-0000-00005402cf8c)
    (at 57.26 50.29 -90)
    (path "/00000000-0000-0000-0000-00004bf0367f/00000000-0000-0000-0000-000022760fbc")
    (attr smd)
    (fp_text reference "R11" (at 0 0 90) (layer "F.SilkS")
      (effects (font (size 0.762 0.762) (thickness 0.127)))
    )
    (fp_text value "1K" (at 0 0 90) (layer "F.SilkS") hide
      (effects (font (size 0.762 0.762) (thickness 0.127)))
    )
    (fp_line (start -2.54 -1.143) (end -2.54 1.143) (layer "F.SilkS") (width 0.127))
    (fp_line (start -2.54 1.143) (end -0.889 1.143) (layer "F.SilkS") (width 0.127))
    (fp_line (start 0.889 -1.143) (end 2.54 -1.143) (layer "F.SilkS") (width 0.127))
    (fp_line (start 2.54 -1.143) (end 2.54 1.143) (layer "F.SilkS") (width 0.127))
    (fp_line (start 2.54 1.143) (end 0.889 1.143) (layer "F.SilkS") (width 0.127))
    (fp_line (start -0.889 -1.143) (end -2.54 -1.143) (layer "F.SilkS") (width 0.127))
    (pad "1" smd rect (at -1.651 0 270) (size 1.524 2.032) (layers "Composant" "F.Paste" "F.Mask")
      (net 3 "Net-(L6-Pad2)") (tstamp aa67310d-5884-4d05-8dcf-bf41060498ff))
    (pad "2" smd rect (at 1.651 0 270) (size 1.524 2.032) (layers "Composant" "F.Paste" "F.Mask")
      (net 1 "Net-(C48-Pad2)") (tstamp 11cb9d3f-10e3-41e4-b53f-517fc40917f8))
    (model "SMD_Packages.3dshapes/SMD-1206.wrl"
      (at (xyz 0 0 0))
      (scale (xyz 0.17 0.16 0.16))
      (rotate (xyz 0 0 0))
    )
  )

  (segment (start 57.26 51.57) (end 61.7 47.13) (width 0.2032) (layer "Composant") (net 1) (tstamp f694334e-a9e7-4d5c-a90b-b15a26d06084))
  (segment (start 61.7 47.13) (end 61.7 46.24) (width 0.2032) (layer "Composant") (net 1) (tstamp 2911a49d-d9ac-4dad-8965-d7abcbd48932))
  (segment (start 61.7 46.24) (end 62.67077 45.26923) (width 0.2032) (layer "Composant") (net 1) (tstamp 78574693-36f7-41c3-8372-1beb483f4919))
  (segment (start 62.67077 45.26923) (end 65.01834 45.26923) (width 0.2032) (layer "Composant") (net 1) (tstamp 5223ac82-4207-4e56-8f80-fc8163229ff2))
  (segment (start 57.26 51.941) (end 57.26 51.57) (width 0.2032) (layer "Composant") (net 1) (tstamp 8a8d348d-69c0-4919-a6e5-75f8709b0d4b))
  (segment (start 65.01834 45.26923) (end 64.87 45.26923) (width 0.2032) (layer "Composant") (net 1) (tstamp f3f895af-921c-4405-9d2c-a994ff46a36e))
  (segment (start 61.03618 44.86282) (end 64.85 44.86282) (width 0.2032) (layer "Composant") (net 3) (tstamp eac47d05-6561-4d3b-88a5-073ca5967b09))
  (segment (start 57.26 48.639) (end 61.03618 44.86282) (width 0.2032) (layer "Composant") (net 3) (tstamp 18e8fb07-1048-4440-81bf-85c3063dee17))
  (segment (start 60.89166 44.45641) (end 65.95641 44.45641) (width 0.2032) (layer "Composant") (net 2) (tstamp efec1be9-cdeb-40da-a27f-a503d55d30a2))
  (segment (start 60.40359 44.45641) (end 60.89166 44.45641) (width 0.2032) (layer "Composant") (net 2) (tstamp 8e2d1065-302e-482f-81b6-3d31d0643e24))
  (segment (start 59.523 45.337) (end 60.40359 44.45641) (width 0.2032) (layer "Composant") (net 2) (tstamp c954b2e0-51d2-4950-8f2a-7143adff5865))
  (segment (start 57.26 45.337) (end 59.523 45.337) (width 0.2032) (layer "Composant") (net 2) (tstamp ef9660dc-199b-4386-8c7f-92cec3d85773))
  (segment (start 57.26 42.035) (end 59.045 42.035) (width 0.2032) (layer "Composant") (net 4) (tstamp 75276ab2-082f-49bf-89bf-cb2785abb992))
  (segment (start 59.045 42.035) (end 61.06 44.05) (width 0.2032) (layer "Composant") (net 4) (tstamp 6d7d1289-6535-423f-a199-f228105c6d91))
  (segment (start 61.06 44.05) (end 65.86 44.05) (width 0.2032) (layer "Composant") (net 4) (tstamp 84f97f24-934c-4869-a0a5-9b902def1b2b))

)
