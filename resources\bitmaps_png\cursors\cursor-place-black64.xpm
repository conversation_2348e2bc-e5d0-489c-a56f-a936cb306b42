/* XPM */
static char const * cursor_place_black64_xpm[] = {
"64 64 4 1",
" 	c None",
".	c #000000",
"+	c #008000",
"@	c #FFFFFF",
"                                                                ",
" @                                                              ",
" @@                                                             ",
" @@@                                                            ",
" @..@                                                           ",
" @...@                                                          ",
" @....@                                                         ",
" @.....@                                                        ",
" @......@                                                       ",
" @.......@                                                      ",
" @........@                                                     ",
" @.........@                                                    ",
" @..........@                                                   ",
" @...........@                                                  ",
" @............@                                                 ",
" @.............@                                                ",
" @..............@                                               ",
" @...............@                                              ",
" @................@                                             ",
" @.................@                                            ",
" @..................@                                           ",
" @..................@@                                          ",
" @............@@@@@@@                                           ",
" @............@                                                 ",
" @............@                                                 ",
" @............@                                                 ",
" @.....@@......@                                                ",
" @....@ @......@                                                ",
" @...@  @@......@                                               ",
" @..@    @......@                                               ",
" @@@      @......@                                              ",
" @@       @......@                                              ",
" @         @......@                                             ",
"           @.....@@                                             ",
"            @...@@                                              ",
"            @..@@                                               ",
"             @@@                                                ",
"                                          @@@@                  ",
"                                         @++++@                 ",
"                                         @++++@                 ",
"                                         @++++@                 ",
"                                         @++++@                 ",
"                                         @++++@                 ",
"                                    @@@@@@++++@@@@@@            ",
"                                   @++++++++++++++++@           ",
"                                   @++++++++++++++++@           ",
"                                   @++++++++++++++++@           ",
"                                   @++++++++++++++++@           ",
"                                    @@@@@@++++@@@@@@            ",
"                                         @++++@                 ",
"                                         @++++@                 ",
"                                         @++++@                 ",
"                                         @++++@                 ",
"                                         @++++@                 ",
"                                          @@@@                  ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                ",
"                                                                "};
