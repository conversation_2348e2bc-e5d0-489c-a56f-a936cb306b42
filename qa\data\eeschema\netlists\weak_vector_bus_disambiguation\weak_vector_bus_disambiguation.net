(export (version "E")
  (design
    (source "weak_vector_bus_disambiguation.kicad_sch")
    (date "Sun Feb 21 20:25:00 2021")
    (tool "Eeschema (5.99.0-9272-g68b81c8271-dirty)")
    (sheet (number "1") (name "/") (tstamps "/")
      (title_block
        (title)
        (company)
        (rev)
        (date)
        (source "weak_vector_bus_disambiguation.kicad_sch")
        (comment (number "1") (value ""))
        (comment (number "2") (value ""))
        (comment (number "3") (value ""))
        (comment (number "4") (value ""))
        (comment (number "5") (value ""))
        (comment (number "6") (value ""))
        (comment (number "7") (value ""))
        (comment (number "8") (value ""))
        (comment (number "9") (value ""))))
    (sheet (number "2") (name "/Sub1/") (tstamps "/911370ed-9eca-4408-af70-75f46ccb0cea/")
      (title_block
        (title)
        (company)
        (rev)
        (date)
        (source "sub1.kicad_sch")
        (comment (number "1") (value ""))
        (comment (number "2") (value ""))
        (comment (number "3") (value ""))
        (comment (number "4") (value ""))
        (comment (number "5") (value ""))
        (comment (number "6") (value ""))
        (comment (number "7") (value ""))
        (comment (number "8") (value ""))
        (comment (number "9") (value ""))))
    (sheet (number "3") (name "/Sub2/") (tstamps "/48e15bb3-68ef-4111-84c2-95e07dc0e8bf/")
      (title_block
        (title)
        (company)
        (rev)
        (date)
        (source "sub2.kicad_sch")
        (comment (number "1") (value ""))
        (comment (number "2") (value ""))
        (comment (number "3") (value ""))
        (comment (number "4") (value ""))
        (comment (number "5") (value ""))
        (comment (number "6") (value ""))
        (comment (number "7") (value ""))
        (comment (number "8") (value ""))
        (comment (number "9") (value ""))))
    (sheet (number "4") (name "/Merge/") (tstamps "/b4665eb8-ef65-4980-bc76-c5100cd8fd0b/")
      (title_block
        (title)
        (company)
        (rev)
        (date)
        (source "merge.kicad_sch")
        (comment (number "1") (value ""))
        (comment (number "2") (value ""))
        (comment (number "3") (value ""))
        (comment (number "4") (value ""))
        (comment (number "5") (value ""))
        (comment (number "6") (value ""))
        (comment (number "7") (value ""))
        (comment (number "8") (value ""))
        (comment (number "9") (value "")))))
  (components
    (comp (ref "R1")
      (value "R")
      (datasheet "~")
      (libsource (lib "Device") (part "R") (description "Resistor"))
      (property (name "Sheetname") (value "Sub1"))
      (property (name "Sheetfile") (value "sub1.kicad_sch"))
      (sheetpath (names "/Sub1/") (tstamps "/911370ed-9eca-4408-af70-75f46ccb0cea/"))
      (tstamps "549fed29-2fe3-4199-8781-d64e3d55522a"))
    (comp (ref "R2")
      (value "R")
      (datasheet "~")
      (libsource (lib "Device") (part "R") (description "Resistor"))
      (property (name "Sheetname") (value "Sub2"))
      (property (name "Sheetfile") (value "sub2.kicad_sch"))
      (sheetpath (names "/Sub2/") (tstamps "/48e15bb3-68ef-4111-84c2-95e07dc0e8bf/"))
      (tstamps "2099c994-a92c-409d-a12d-82f6c93cabc6"))
    (comp (ref "R3")
      (value "R")
      (datasheet "~")
      (libsource (lib "Device") (part "R") (description "Resistor"))
      (property (name "Sheetname") (value "Merge"))
      (property (name "Sheetfile") (value "merge.kicad_sch"))
      (sheetpath (names "/Merge/") (tstamps "/b4665eb8-ef65-4980-bc76-c5100cd8fd0b/"))
      (tstamps "a8053649-8fd9-4481-b56e-122d712ddeb5"))
    (comp (ref "R4")
      (value "R")
      (datasheet "~")
      (libsource (lib "Device") (part "R") (description "Resistor"))
      (property (name "Sheetname") (value "Merge"))
      (property (name "Sheetfile") (value "merge.kicad_sch"))
      (sheetpath (names "/Merge/") (tstamps "/b4665eb8-ef65-4980-bc76-c5100cd8fd0b/"))
      (tstamps "2eeee84e-974a-4768-b3a0-fdfaed463e15")))
  (libparts
    (libpart (lib "Device") (part "R")
      (description "Resistor")
      (docs "~")
      (footprints
        (fp "R_*"))
      (fields
        (field (name "Reference") "R")
        (field (name "Value") "R")
        (field (name "Datasheet") "~"))
      (pins
        (pin (num "1") (name "~") (type "passive"))
        (pin (num "2") (name "~") (type "passive")))))
  (libraries)
  (nets
    (net (code "1") (name "/Merge/A1")
      (node (ref "R1") (pin "1") (pintype "passive"))
      (node (ref "R3") (pin "1") (pintype "passive")))
    (net (code "2") (name "/Merge/A2")
      (node (ref "R1") (pin "2") (pintype "passive"))
      (node (ref "R3") (pin "2") (pintype "passive")))
    (net (code "4") (name "/Merge/D1")
      (node (ref "R2") (pin "1") (pintype "passive"))
      (node (ref "R4") (pin "1") (pintype "passive")))
    (net (code "5") (name "/Merge/D2")
      (node (ref "R2") (pin "2") (pintype "passive"))
      (node (ref "R4") (pin "2") (pintype "passive")))))
