(kicad_pcb (version 20210623) (generator pcbnew)

  (general
    (thickness 1.6)
  )

  (paper "A3")
  (layers
    (0 "F.Cu" signal)
    (31 "B.Cu" signal)
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B<PERSON>SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (color "Green") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "core") (thickness 1.51) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (color "Green") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "None")
      (dielectric_constraints no)
    )
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x00010fc_ffffffff)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (svguseinch false)
      (svgprecision 6)
      (excludeedgelayer true)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits true)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue true)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 1)
      (mirror false)
      (drillshape 1)
      (scaleselection 1)
      (outputdirectory "")
    )
  )

  (net 0 "")
  (net 1 "GND")
  (net 2 "ADC12_IN14")
  (net 3 "+3V3")
  (net 4 "ADC2_IN15")
  (net 5 "ADC3_IN5")
  (net 6 "unconnected-(IC2-Pad19)")
  (net 7 "unconnected-(IC2-Pad18)")
  (net 8 "~{RESET}")
  (net 9 "Net-(C27-Pad1)")
  (net 10 "Net-(C28-Pad1)")
  (net 11 "unconnected-(IC2-Pad22)")
  (net 12 "PA15_IN_DFM")
  (net 13 "PC15_IN_PICKUP")
  (net 14 "PC13_OUT_BTS723_MV")
  (net 15 "PC14_OUT_BTS723_ALARM")
  (net 16 "ADC12_IN1")
  (net 17 "unconnected-(IC2-Pad40)")
  (net 18 "ADC12_IN2")
  (net 19 "USART2_TX")
  (net 20 "USART2_RX")
  (net 21 "SPI1_~{SS}")
  (net 22 "SPI1_SCK")
  (net 23 "SPI1_MISO")
  (net 24 "TIM1_CH1N")
  (net 25 "PB0_OUT_SM_STEP")
  (net 26 "TIM8_CH3N")
  (net 27 "ADC1_IN11")
  (net 28 "ADC1_IN5")
  (net 29 "TIM1_CH1")
  (net 30 "USART1_TX")
  (net 31 "USART1_RX")
  (net 32 "CAN1_RX")
  (net 33 "CAN1_TX")
  (net 34 "SWDIO")
  (net 35 "SWDCLK")
  (net 36 "SPI1_MOSI")
  (net 37 "CAN2_RX")
  (net 38 "CAN2_TX")
  (net 39 "PB7_OUT_SM_~{ENABLE}")
  (net 40 "PB8_OUT__LED")
  (net 41 "TIM8_CH3")
  (net 42 "Net-(IC2-Pad20)")

  (footprint "0IBF_Crystal:Crystal_SMD_5032-2Pin_5.0x3.2mm_IBF" (layer "F.Cu")
    (tedit 60FD5043) (tstamp 0de9af20-f2c1-4152-8c27-34e4ce4bec78)
    (at 106.07 -86.88 90)
    (descr "SMD Crystal 5x3,2mm Quantek QC5CB / Interquip SMAC-5032 angepaßte Pads IBFEEW")
    (tags "SMD crystal")
    (property "Alternative" "Abracom ABM3-10.000MHZ-D2Y-T")
    (property "Bemerkung" "")
    (property "Farnell" "2508597")
    (property "MF" "Quantek")
    (property "MPN" "QC5CB10.0000F18B23M / QC5CB10.0000F18B23R")
    (property "RS" "813-6097")
    (property "Sheetfile" "controller.kicad_sch")
    (property "Sheetname" "Controller")
    (property "digikey" "")
    (property "mouser" "")
    (path "/21ecf582-2fe5-40fc-9946-89f54de57e7c/656de130-e9e8-49cd-b55f-f52948b09214")
    (attr smd)
    (fp_text reference "Q1" (at 0 -2.8 90) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 59ee8af3-c8d3-4c23-8657-70d98fa756b6)
    )
    (fp_text value "QC5CB10.000" (at 0 2.8 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 74fa71de-1c22-4758-b2dd-2c303737d498)
    )
    (fp_text user "${REFERENCE}" (at 3.82 0 90) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 67ae7ca2-ebc7-49a5-baf1-24115daa3026)
    )
    (fp_line (start -0.2 0) (end -0.5 0) (layer "F.SilkS") (width 0.07) (tstamp 02db9d19-a3cb-4be0-828b-49297dcf0845))
    (fp_line (start -2.8 1.7) (end 2.8 1.7) (layer "F.SilkS") (width 0.07) (tstamp 1492f2eb-82fb-460c-a712-3ecc1389d05d))
    (fp_line (start 2.8 -1.7) (end 2.8 -1.6) (layer "F.SilkS") (width 0.07) (tstamp 15c03324-3b1a-4c11-9142-f9c2b8a369aa))
    (fp_line (start -0.2 -0.7) (end -0.2 0.7) (layer "F.SilkS") (width 0.07) (tstamp 2ca4cd67-11e7-4bc4-b349-bec6803e66ed))
    (fp_line (start -2.8 1.6) (end -2.8 1.7) (layer "F.SilkS") (width 0.07) (tstamp 2dcf3f66-5257-4408-8283-570eeab368a6))
    (fp_line (start -2.8 -1.7) (end -2.8 -1.6) (layer "F.SilkS") (width 0.07) (tstamp 38b9d64d-8d84-4044-8396-27fb999961a9))
    (fp_line (start 0.1 -0.6) (end 0.1 0.6) (layer "F.SilkS") (width 0.07) (tstamp 48a896ae-a0f3-49b6-bdc7-a62b294294d4))
    (fp_line (start 0.1 0.6) (end -0.1 0.6) (layer "F.SilkS") (width 0.07) (tstamp 4d49a789-b0f1-4705-b19c-3809bec4414b))
    (fp_line (start -0.1 -0.6) (end 0.1 -0.6) (layer "F.SilkS") (width 0.07) (tstamp 7b374b50-4b0d-4c84-8586-a22780c1d664))
    (fp_line (start 2.8 -1.7) (end -2.8 -1.7) (layer "F.SilkS") (width 0.07) (tstamp a43f91a1-116a-4d40-a54b-8d5db52826c0))
    (fp_line (start 0.2 0) (end 0.6 0) (layer "F.SilkS") (width 0.07) (tstamp a4c12ca8-54d1-4a5a-b361-295a2d47471d))
    (fp_line (start 0.2 -0.7) (end 0.2 0.7) (layer "F.SilkS") (width 0.07) (tstamp a5118d1b-d818-4c65-8549-73ad4ccf7443))
    (fp_line (start 2.8 1.6) (end 2.8 1.7) (layer "F.SilkS") (width 0.07) (tstamp b65f86ac-bb27-4ddd-8769-0a03778d1811))
    (fp_line (start -0.1 0.6) (end -0.1 -0.6) (layer "F.SilkS") (width 0.07) (tstamp ef9e4629-ffd8-459b-9c3a-c80fc1d215ac))
    (fp_line (start 3.2 -1.9) (end -3.2 -1.9) (layer "F.CrtYd") (width 0.05) (tstamp 2ceab9df-6986-47e7-9061-5955ccf38699))
    (fp_line (start -3.2 1.9) (end 3.2 1.9) (layer "F.CrtYd") (width 0.05) (tstamp 80afb5ab-4177-4551-8787-1c944430afa5))
    (fp_line (start -3.2 -1.9) (end -3.2 1.9) (layer "F.CrtYd") (width 0.05) (tstamp a13bea0c-8bdc-41f7-abc8-c973b0f0538f))
    (fp_line (start 3.2 1.9) (end 3.2 -1.9) (layer "F.CrtYd") (width 0.05) (tstamp fcb074a8-a44c-486a-994f-f5865e99191d))
    (fp_line (start -2.3 -1.6) (end 2.3 -1.6) (layer "F.Fab") (width 0.1) (tstamp 02e3b2da-ecd6-4ec8-b312-3f9af36cb135))
    (fp_line (start 0.2 0) (end 0.6 0) (layer "F.Fab") (width 0.07) (tstamp 0c45a563-5e27-4fce-8af5-06ee0a6f1a1c))
    (fp_line (start 0.2 -0.7) (end 0.2 0.7) (layer "F.Fab") (width 0.07) (tstamp 1d7c950b-4361-4599-b3de-d1aee7824db3))
    (fp_line (start -2.3 1.6) (end -2.5 1.4) (layer "F.Fab") (width 0.1) (tstamp 1de822db-a0f9-41b7-a613-3545751f2426))
    (fp_line (start -0.2 -0.7) (end -0.2 0.7) (layer "F.Fab") (width 0.07) (tstamp 2bc58434-cbc6-4696-8426-3f8878fbf4ab))
    (fp_line (start 2.5 -1.4) (end 2.5 1.4) (layer "F.Fab") (width 0.1) (tstamp 3e247a21-39b4-4045-88d0-cacdceeafc93))
    (fp_line (start 2.5 1.4) (end 2.3 1.6) (layer "F.Fab") (width 0.1) (tstamp 46e283c6-ce96-417e-9b2b-58b60b2baab7))
    (fp_line (start -0.1 0.6) (end -0.1 -0.6) (layer "F.Fab") (width 0.07) (tstamp 63b19816-716c-41bf-9351-2df40ea7b6bf))
    (fp_line (start 0.1 0.6) (end -0.1 0.6) (layer "F.Fab") (width 0.07) (tstamp 704fbe02-9a47-437d-a3c1-99ca8ad88c2a))
    (fp_line (start -2.5 -1.4) (end -2.3 -1.6) (layer "F.Fab") (width 0.1) (tstamp 86c41ca5-1020-4e15-885c-0f8595be64ec))
    (fp_line (start 2.3 -1.6) (end 2.5 -1.4) (layer "F.Fab") (width 0.1) (tstamp a5b78643-ac93-4ff0-be94-2f958608b528))
    (fp_line (start -0.1 -0.6) (end 0.1 -0.6) (layer "F.Fab") (width 0.07) (tstamp adb710b1-4972-41b1-b8a9-755c11c76257))
    (fp_line (start -0.2 0) (end -0.5 0) (layer "F.Fab") (width 0.07) (tstamp b3510168-8969-45c5-a9fa-60ad00689094))
    (fp_line (start -2.5 0.6) (end -1.5 1.6) (layer "F.Fab") (width 0.1) (tstamp b9e98e16-fd1f-41e0-9cc1-f6952e941337))
    (fp_line (start -2.5 1.4) (end -2.5 -1.4) (layer "F.Fab") (width 0.1) (tstamp c472a57f-95fe-4470-9816-70cb04bae7b8))
    (fp_line (start 2.3 1.6) (end -2.3 1.6) (layer "F.Fab") (width 0.1) (tstamp d8d793c6-7000-4cb5-9808-f318ebb543da))
    (fp_line (start 0.1 -0.6) (end 0.1 0.6) (layer "F.Fab") (width 0.07) (tstamp e2fa0db0-ddb8-41a9-93e0-8aa90030f56f))
    (pad "1" smd rect locked (at -2 0 90) (size 2.2 2.8) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 10 "Net-(C28-Pad1)") (pintype "passive") (tstamp da710f9d-5646-4d10-b982-08ce7f8a5a98))
    (pad "2" smd rect locked (at 2 0 90) (size 2.2 2.8) (layers "F.Cu" "F.Paste" "F.Mask")
      (net 9 "Net-(C27-Pad1)") (pintype "passive") (tstamp 4fca3028-c5d0-4de0-b6fe-98edbb026a3d))
    (model "${KICAD6_USER_LIBRARY_3D}/discrete/Crystal_SMD_5032_2pin_5x3mm.stp"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz -90 0 0))
    )
  )

  (footprint "0IBF_RCL:C_1206_3216Metric" (layer "F.Cu")
    (tedit 60FD2F22) (tstamp 5bf3f5fe-f86e-498f-acd9-70912b60481f)
    (at 104.47 -82.38 180)
    (descr "Capacitor SMD 1206 (3216 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Alternative" "")
    (property "Bemerkung" "")
    (property "Farnell" "")
    (property "MF" "")
    (property "MPN" "")
    (property "RS" "")
    (property "Sheetfile" "controller.kicad_sch")
    (property "Sheetname" "Controller")
    (property "digikey" "")
    (property "mouser" "")
    (path "/21ecf582-2fe5-40fc-9946-89f54de57e7c/8bab4fd9-c19a-40d4-a8f5-10dd898a0374")
    (attr smd)
    (fp_text reference "C24" (at 0 -1.85) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 8bee4d0e-2e50-4bfe-9074-b019f09f38a0)
    )
    (fp_text value "100nF" (at 0 1.85) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 3acc2d27-25b6-49e0-b17a-d4a93a83d63d)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
      (effects (font (size 0.8 0.8) (thickness 0.12)))
      (tstamp ca47df6a-677e-436e-883b-b1f2a15e1a9f)
    )
    (fp_line (start -0.711252 0.91) (end 0.711252 0.91) (layer "F.SilkS") (width 0.12) (tstamp 191bbb0c-ad8b-443a-9878-1deada5e1242))
    (fp_line (start -0.711252 -0.91) (end 0.711252 -0.91) (layer "F.SilkS") (width 0.12) (tstamp ad1d4a96-4ed0-437a-b49c-87fe2fca1ea1))
    (fp_rect (start -2.3 -1) (end 2.3 1) (layer "F.CrtYd") (width 0.05) (fill none) (tstamp 4dfcc27e-1317-4b75-aadc-d616bae9ca01))
    (fp_line (start -1.6 -0.8) (end 1.6 -0.8) (layer "F.Fab") (width 0.1) (tstamp 8c836167-9e65-475d-9a08-899d99608fc1))
    (fp_line (start 1.6 0.8) (end -1.6 0.8) (layer "F.Fab") (width 0.1) (tstamp 95812da0-380e-4d9d-852b-7abc215baeb2))
    (fp_line (start -1.6 0.8) (end -1.6 -0.8) (layer "F.Fab") (width 0.1) (tstamp 9f1bf3b9-1b9b-4e15-b572-94629150a7da))
    (fp_line (start 1.6 -0.8) (end 1.6 0.8) (layer "F.Fab") (width 0.1) (tstamp ef8f0823-2ec6-4501-b18f-17fe59cdafb7))
    (fp_rect (start -1.95 -0.7) (end -1.65 0.7) (layer "F.Fab") (width 0.01) (fill solid) (tstamp 26befa1e-139e-4fa8-bb61-48b2cd2f4e3a))
    (fp_rect (start 1.6 -0.7) (end 1.9 0.7) (layer "F.Fab") (width 0.01) (fill solid) (tstamp 56197b27-39c0-46ff-97f6-e5a8266c8d56))
    (pad "1" smd roundrect locked (at -1.475 0 180) (size 1.15 1.8) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.217391)
      (net 8 "~{RESET}") (pintype "passive") (tstamp 79f161af-b2ee-444e-b6ea-90702bbd06a6))
    (pad "2" smd roundrect locked (at 1.475 0 180) (size 1.15 1.8) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.217391)
      (net 1 "GND") (pintype "passive") (tstamp 297c662b-9710-4f76-b1a4-745eb9b402bc))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_1206_3216Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "Package_QFP:LQFP-48_7x7mm_P0.5mm" (layer "F.Cu")
    (tedit 5D9F72AF) (tstamp 833c05b7-f4dc-44b2-abd8-71e8d4f553c0)
    (at 115.6 -85.1)
    (descr "LQFP, 48 Pin (https://www.analog.com/media/en/technical-documentation/data-sheets/ltc2358-16.pdf), generated with kicad-footprint-generator ipc_gullwing_generator.py")
    (tags "LQFP QFP")
    (property "Alternative" "")
    (property "Bemerkung" "")
    (property "Farnell" "3648858")
    (property "MF" "ST")
    (property "MPN" "STM32G491CET6")
    (property "RS" "215-0839 / 215-0838")
    (property "Sheetfile" "controller.kicad_sch")
    (property "Sheetname" "Controller")
    (property "digikey" "497-STM32G491CET6-ND")
    (property "mouser" "511-STM32G491CET6")
    (path "/21ecf582-2fe5-40fc-9946-89f54de57e7c/e0722ab8-d89c-469b-9058-cb19d719e3c3")
    (attr smd)
    (fp_text reference "IC2" (at 0 -5.85) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp d5dd8c55-2f3d-45cd-975b-f4d1c199d065)
    )
    (fp_text value "STM32G491CET6" (at 0 5.85) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp d5b2bab8-9af0-427a-aa9b-86c5cb322947)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp ff37f4f6-3abf-4d5f-a83b-18dc759240a4)
    )
    (fp_line (start -3.61 -3.16) (end -4.9 -3.16) (layer "F.SilkS") (width 0.12) (tstamp 0db906d9-03bb-49e5-9d80-729b6ee9fedd))
    (fp_line (start 3.61 3.61) (end 3.61 3.16) (layer "F.SilkS") (width 0.12) (tstamp 1f5ce3e5-adf2-492d-ad2f-41ec787e1622))
    (fp_line (start 3.16 3.61) (end 3.61 3.61) (layer "F.SilkS") (width 0.12) (tstamp 220ef310-c9b6-423c-b7fe-825f1931baf1))
    (fp_line (start -3.61 3.61) (end -3.61 3.16) (layer "F.SilkS") (width 0.12) (tstamp 3ba9cd1a-65bb-4614-b341-667ac08356a8))
    (fp_line (start -3.61 -3.61) (end -3.61 -3.16) (layer "F.SilkS") (width 0.12) (tstamp 7089790e-7314-4851-9938-f9f9e4f7fce7))
    (fp_line (start 3.16 -3.61) (end 3.61 -3.61) (layer "F.SilkS") (width 0.12) (tstamp 9fda3c38-0565-4c93-9e16-71411c9c0af0))
    (fp_line (start -3.16 3.61) (end -3.61 3.61) (layer "F.SilkS") (width 0.12) (tstamp a09f1f34-3261-4016-bea2-d5bf8fbe5f15))
    (fp_line (start 3.61 -3.61) (end 3.61 -3.16) (layer "F.SilkS") (width 0.12) (tstamp c38dcaa0-dbc6-490b-9051-ab47f12dcef6))
    (fp_line (start -3.16 -3.61) (end -3.61 -3.61) (layer "F.SilkS") (width 0.12) (tstamp c59256f0-600d-4199-8b6e-ed4994afb9a0))
    (fp_line (start 3.15 -5.15) (end 3.15 -3.75) (layer "F.CrtYd") (width 0.05) (tstamp 012fa6ca-4897-4c7e-9e7c-f6a4fdf5837a))
    (fp_line (start -3.75 3.75) (end -3.75 3.15) (layer "F.CrtYd") (width 0.05) (tstamp 1149d8b1-dad0-4749-9e3c-53f3718f8384))
    (fp_line (start 5.15 -3.15) (end 5.15 0) (layer "F.CrtYd") (width 0.05) (tstamp 1317bb3d-69e7-484a-8293-cb5063f0e326))
    (fp_line (start 0 -5.15) (end -3.15 -5.15) (layer "F.CrtYd") (width 0.05) (tstamp 1ac8a3b1-aecf-4e57-a09f-aba989a27bad))
    (fp_line (start -3.75 3.15) (end -5.15 3.15) (layer "F.CrtYd") (width 0.05) (tstamp 299fe656-54f5-490c-95e5-039d00b48dfb))
    (fp_line (start 3.15 3.75) (end 3.75 3.75) (layer "F.CrtYd") (width 0.05) (tstamp 30f98e32-c13b-428f-a0c2-fae2cf663a2d))
    (fp_line (start 5.15 3.15) (end 5.15 0) (layer "F.CrtYd") (width 0.05) (tstamp 36a2caac-f810-4f89-80a3-aab70388c16d))
    (fp_line (start 0 -5.15) (end 3.15 -5.15) (layer "F.CrtYd") (width 0.05) (tstamp 46deab9f-236a-40ed-9543-5272a528f1b3))
    (fp_line (start 3.15 5.15) (end 3.15 3.75) (layer "F.CrtYd") (width 0.05) (tstamp 4acdd1bb-344f-4aea-be28-2a7a396c4b27))
    (fp_line (start 3.75 -3.75) (end 3.75 -3.15) (layer "F.CrtYd") (width 0.05) (tstamp 6c220254-8b7d-443c-9ddc-fa4ce7b751da))
    (fp_line (start -3.75 -3.15) (end -5.15 -3.15) (layer "F.CrtYd") (width 0.05) (tstamp 7d43b51b-418a-47e8-8fb0-8ea76c8c6556))
    (fp_line (start 3.75 3.75) (end 3.75 3.15) (layer "F.CrtYd") (width 0.05) (tstamp 7e4c8177-d37e-4b5e-9ef6-2b8ca64f35e1))
    (fp_line (start 3.75 3.15) (end 5.15 3.15) (layer "F.CrtYd") (width 0.05) (tstamp 7ebdebad-08ae-4ef7-8399-7482fee68f45))
    (fp_line (start -3.15 -5.15) (end -3.15 -3.75) (layer "F.CrtYd") (width 0.05) (tstamp 87896831-5d8c-4521-8724-bd7e758bc5f0))
    (fp_line (start 0 5.15) (end -3.15 5.15) (layer "F.CrtYd") (width 0.05) (tstamp 9490bef3-623e-4df3-98e4-1ba4877a9f5f))
    (fp_line (start 3.15 -3.75) (end 3.75 -3.75) (layer "F.CrtYd") (width 0.05) (tstamp 9a7efbbe-cef6-4a16-94cc-24b9c923f135))
    (fp_line (start 0 5.15) (end 3.15 5.15) (layer "F.CrtYd") (width 0.05) (tstamp ab1e36d7-dea4-41d9-9336-36d4512db9e9))
    (fp_line (start -3.15 -3.75) (end -3.75 -3.75) (layer "F.CrtYd") (width 0.05) (tstamp c454684a-9f60-4d46-8bf5-00f2cf2c78a7))
    (fp_line (start -3.75 -3.75) (end -3.75 -3.15) (layer "F.CrtYd") (width 0.05) (tstamp c79bd622-cd92-4a96-846f-4a8e1e806975))
    (fp_line (start -3.15 5.15) (end -3.15 3.75) (layer "F.CrtYd") (width 0.05) (tstamp d715a65f-32ed-4968-9170-aeaaffcba3a6))
    (fp_line (start 3.75 -3.15) (end 5.15 -3.15) (layer "F.CrtYd") (width 0.05) (tstamp da669de5-264f-478f-807b-b32b7cdc4490))
    (fp_line (start -5.15 -3.15) (end -5.15 0) (layer "F.CrtYd") (width 0.05) (tstamp e1806ad6-cb3f-4c1d-91ad-d20268040d85))
    (fp_line (start -3.15 3.75) (end -3.75 3.75) (layer "F.CrtYd") (width 0.05) (tstamp eea2c37f-9e65-46e4-8cd1-613a33eb97ef))
    (fp_line (start -5.15 3.15) (end -5.15 0) (layer "F.CrtYd") (width 0.05) (tstamp fb60249e-6a41-4a29-9ca5-75b6ecade8c4))
    (fp_line (start -3.5 3.5) (end -3.5 -2.5) (layer "F.Fab") (width 0.1) (tstamp 03ea9bfd-77ae-4ae0-be1a-ee0cd6120f15))
    (fp_line (start 3.5 3.5) (end -3.5 3.5) (layer "F.Fab") (width 0.1) (tstamp 646ee076-8221-4d17-8880-7fd365a872f2))
    (fp_line (start 3.5 -3.5) (end 3.5 3.5) (layer "F.Fab") (width 0.1) (tstamp 7a11f02a-5924-47b3-a142-67c316d3a351))
    (fp_line (start -3.5 -2.5) (end -2.5 -3.5) (layer "F.Fab") (width 0.1) (tstamp 892b195e-b8bc-4357-8433-51de0fb58e1f))
    (fp_line (start -2.5 -3.5) (end 3.5 -3.5) (layer "F.Fab") (width 0.1) (tstamp eabfab10-ac5b-4298-873d-ddd2f54ded57))
    (pad "1" smd roundrect locked (at -4.1625 -2.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pinfunction "Vbat") (pintype "input") (tstamp 5bf1cfbf-338c-4d72-84cd-e522eb0a0b71))
    (pad "2" smd roundrect locked (at -4.1625 -2.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 14 "PC13_OUT_BTS723_MV") (pinfunction "PC13") (pintype "passive") (tstamp cfa3a19d-3ebd-486a-b124-73675d2745fa))
    (pad "3" smd roundrect locked (at -4.1625 -1.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 15 "PC14_OUT_BTS723_ALARM") (pinfunction "PC14_OSC32") (pintype "passive") (tstamp 2bbe60b5-6b41-44d3-9b6e-bd68ac851971))
    (pad "4" smd roundrect locked (at -4.1625 -1.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 13 "PC15_IN_PICKUP") (pinfunction "PC15_OSC32") (pintype "passive") (tstamp 1b969bde-7183-439b-8efd-71d27dfdd927))
    (pad "5" smd roundrect locked (at -4.1625 -0.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 9 "Net-(C27-Pad1)") (pinfunction "PF0_OSC") (pintype "passive") (tstamp 51b823a2-e195-423d-b339-de02810729a7))
    (pad "6" smd roundrect locked (at -4.1625 -0.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 10 "Net-(C28-Pad1)") (pinfunction "PF1_OSC") (pintype "passive") (tstamp 02f2ec88-5fe8-49db-b520-7f45aac0aa7e))
    (pad "7" smd roundrect locked (at -4.1625 0.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 8 "~{RESET}") (pinfunction "PG10_~{RES}") (pintype "passive") (tstamp 012c0e72-2733-4542-a5b3-fb6993eacc9f))
    (pad "8" smd roundrect locked (at -4.1625 0.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 16 "ADC12_IN1") (pinfunction "PA0") (pintype "passive") (tstamp b8415513-aee7-46f1-ac6c-03101461533f))
    (pad "9" smd roundrect locked (at -4.1625 1.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 18 "ADC12_IN2") (pinfunction "PA1") (pintype "passive") (tstamp ffcadddd-c0b1-48d7-8063-5dbf1cb73b8c))
    (pad "10" smd roundrect locked (at -4.1625 1.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 19 "USART2_TX") (pinfunction "PA2") (pintype "passive") (tstamp 0d6223d0-a15e-4270-ab41-cf45ff605869))
    (pad "11" smd roundrect locked (at -4.1625 2.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 20 "USART2_RX") (pinfunction "PA3") (pintype "passive") (tstamp 0ed37356-a5e6-48d9-8862-7334f24c052d))
    (pad "12" smd roundrect locked (at -4.1625 2.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 21 "SPI1_~{SS}") (pinfunction "PA4") (pintype "passive") (tstamp 29058b5d-ca3f-4dd7-8af2-8854ac26acd1))
    (pad "13" smd roundrect locked (at -2.75 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 22 "SPI1_SCK") (pinfunction "PA5") (pintype "passive") (tstamp e70a7607-ad5c-44c8-9616-036e8cf7f514))
    (pad "14" smd roundrect locked (at -2.25 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 23 "SPI1_MISO") (pinfunction "PA6") (pintype "passive") (tstamp 6e6fd987-e913-4037-8bc2-226f34363455))
    (pad "15" smd roundrect locked (at -1.75 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 24 "TIM1_CH1N") (pinfunction "PA7") (pintype "passive") (tstamp ce64664b-9f33-4bb9-bcb6-72bcf3e89e2f))
    (pad "16" smd roundrect locked (at -1.25 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 25 "PB0_OUT_SM_STEP") (pinfunction "PB0") (pintype "passive") (tstamp b5fa5132-6380-488b-be0c-e66514207da3))
    (pad "17" smd roundrect locked (at -0.75 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 26 "TIM8_CH3N") (pinfunction "PB1") (pintype "passive") (tstamp 7c5dd425-3131-4874-989a-4ee6b01dbf04))
    (pad "18" smd roundrect locked (at -0.25 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 7 "unconnected-(IC2-Pad18)") (pinfunction "PB2") (pintype "passive") (tstamp 707dbb48-8caf-4c08-9f33-880bdf6f89f8))
    (pad "19" smd roundrect locked (at 0.25 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 6 "unconnected-(IC2-Pad19)") (pinfunction "VSSA") (pintype "input") (tstamp f32bca8c-2f2d-43aa-97c3-41f1f2f20f05))
    (pad "20" smd roundrect locked (at 0.75 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 42 "Net-(IC2-Pad20)") (pinfunction "VRef+") (pintype "input") (tstamp 2374a396-bbac-4269-8287-b23cf7cb4fcd))
    (pad "21" smd roundrect locked (at 1.25 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 42 "Net-(IC2-Pad20)") (pinfunction "Vdda") (pintype "input") (tstamp 700f2081-1a4d-468f-b358-3b02275e6554))
    (pad "22" smd roundrect locked (at 1.75 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 11 "unconnected-(IC2-Pad22)") (pinfunction "PB10") (pintype "passive") (tstamp 1b216e9c-bb33-4418-aaf0-7f0c9793ee1f))
    (pad "23" smd roundrect locked (at 2.25 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "VSS/GND") (pintype "input") (tstamp e32949db-2489-4c96-9722-3f6a012d5e66))
    (pad "24" smd roundrect locked (at 2.75 4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pinfunction "Vdd") (pintype "input") (tstamp b594f538-ed79-42cf-9c7f-01fe42485b77))
    (pad "25" smd roundrect locked (at 4.1625 2.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "ADC12_IN14") (pinfunction "PB11") (pintype "passive") (tstamp 2c87519d-5eda-4990-81c7-773fe9e55862))
    (pad "26" smd roundrect locked (at 4.1625 2.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 27 "ADC1_IN11") (pinfunction "PB12") (pintype "passive") (tstamp aab999ad-ce93-489b-878b-177cbb1ea618))
    (pad "27" smd roundrect locked (at 4.1625 1.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "ADC3_IN5") (pinfunction "PB13") (pintype "passive") (tstamp 196f1506-c627-4c52-b8e1-2cf2dcb6c720))
    (pad "28" smd roundrect locked (at 4.1625 1.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 28 "ADC1_IN5") (pinfunction "PB14") (pintype "passive") (tstamp 5b91a2ff-0ea5-4e16-9e05-afe2091e0b9a))
    (pad "29" smd roundrect locked (at 4.1625 0.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 4 "ADC2_IN15") (pinfunction "PB15") (pintype "passive") (tstamp 73633cb8-0eb2-433a-8452-dbf3758089bf))
    (pad "30" smd roundrect locked (at 4.1625 0.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 29 "TIM1_CH1") (pinfunction "PA8") (pintype "passive") (tstamp 1f1543f7-f39a-4a6b-96dd-09de60a49329))
    (pad "31" smd roundrect locked (at 4.1625 -0.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 30 "USART1_TX") (pinfunction "PA9") (pintype "passive") (tstamp dba97165-5dfa-44ac-bb77-1f218e8d451a))
    (pad "32" smd roundrect locked (at 4.1625 -0.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 31 "USART1_RX") (pinfunction "PA10") (pintype "passive") (tstamp 3755b813-6ef1-4064-8c43-cb3e1030baa4))
    (pad "33" smd roundrect locked (at 4.1625 -1.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 32 "CAN1_RX") (pinfunction "PA11") (pintype "passive") (tstamp e9b783a2-15fb-48a5-98db-a06a38874ca2))
    (pad "34" smd roundrect locked (at 4.1625 -1.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 33 "CAN1_TX") (pinfunction "PA12") (pintype "passive") (tstamp e5c7c980-6c3a-4373-978f-4476ed785cf9))
    (pad "35" smd roundrect locked (at 4.1625 -2.25) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "VSS/GND") (pintype "input") (tstamp a6b20249-42fa-4fb7-a76f-9ebccf646c57))
    (pad "36" smd roundrect locked (at 4.1625 -2.75) (size 1.475 0.3) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pinfunction "Vdd") (pintype "input") (tstamp 2427fbc4-dab5-41f3-8c14-a57477041f14))
    (pad "37" smd roundrect locked (at 2.75 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 34 "SWDIO") (pinfunction "SWDIO_PA13") (pintype "passive") (tstamp fdd17800-9245-402e-98bc-70ca258d1d5f))
    (pad "38" smd roundrect locked (at 2.25 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 35 "SWDCLK") (pinfunction "SWDCLK_PA14") (pintype "passive") (tstamp 3558f958-08a4-48fb-8949-b03a2b12b5b5))
    (pad "39" smd roundrect locked (at 1.75 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 12 "PA15_IN_DFM") (pinfunction "PA15") (pintype "passive") (tstamp 94abcd19-8f75-4408-a9a6-541351b19985))
    (pad "40" smd roundrect locked (at 1.25 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 17 "unconnected-(IC2-Pad40)") (pinfunction "SWO_PB3") (pintype "passive") (tstamp 842b5aec-9884-461a-8192-970f81bc21a5))
    (pad "41" smd roundrect locked (at 0.75 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 36 "SPI1_MOSI") (pinfunction "PB4") (pintype "passive") (tstamp 69d51732-0cd1-4424-8159-cdcff54d8d63))
    (pad "42" smd roundrect locked (at 0.25 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 37 "CAN2_RX") (pinfunction "PB5") (pintype "passive") (tstamp 43b2515d-40e1-4f18-8498-c2212bdee67c))
    (pad "43" smd roundrect locked (at -0.25 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 38 "CAN2_TX") (pinfunction "PB6") (pintype "passive") (tstamp 835ed08f-5346-4263-a1a0-a8a80928f96a))
    (pad "44" smd roundrect locked (at -0.75 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 39 "PB7_OUT_SM_~{ENABLE}") (pinfunction "PB7") (pintype "passive") (tstamp 26e8f4e7-9220-47f0-9ac9-56cb4bed2519))
    (pad "45" smd roundrect locked (at -1.25 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 40 "PB8_OUT__LED") (pinfunction "Boot0_PB8") (pintype "passive") (tstamp 2a31e86e-895d-49aa-be3f-b59761213409))
    (pad "46" smd roundrect locked (at -1.75 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 41 "TIM8_CH3") (pinfunction "PB9") (pintype "passive") (tstamp 4c8e0465-e0ce-4ed7-8297-1727ac343ba2))
    (pad "47" smd roundrect locked (at -2.25 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "VSS/GND") (pintype "input") (tstamp 4b9748b3-8ebd-4c02-8852-11697ffb14a2))
    (pad "48" smd roundrect locked (at -2.75 -4.1625) (size 0.3 1.475) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "+3V3") (pinfunction "Vdd") (pintype "input") (tstamp b40bf359-7abf-434a-a4c8-2da42a1aaa8f))
    (model "${KICAD6_3DMODEL_DIR}/Package_QFP.3dshapes/LQFP-48_7x7mm_P0.5mm.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "0IBF_RCL:C_1206_3216Metric" (layer "F.Cu")
    (tedit 60FD2F22) (tstamp d9428240-1e44-4605-82f0-03c995fa11d2)
    (at 101.67 -88.88 180)
    (descr "Capacitor SMD 1206 (3216 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Alternative" "")
    (property "Bemerkung" "")
    (property "Farnell" "")
    (property "MF" "")
    (property "MPN" "")
    (property "RS" "")
    (property "Sheetfile" "controller.kicad_sch")
    (property "Sheetname" "Controller")
    (property "digikey" "")
    (property "mouser" "")
    (path "/21ecf582-2fe5-40fc-9946-89f54de57e7c/0dce264e-9bca-40d6-9d61-9c4929a9100f")
    (attr smd)
    (fp_text reference "C27" (at 0 -1.85) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 8ff10208-cca1-4f72-92d3-e0696ffdcacf)
    )
    (fp_text value "18pF" (at 0 1.85) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 3918dfe7-8999-44bf-af7f-694fbf7031b8)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
      (effects (font (size 0.8 0.8) (thickness 0.12)))
      (tstamp 7f15ad17-07ba-48ba-b706-1381021bde77)
    )
    (fp_line (start -0.711252 0.91) (end 0.711252 0.91) (layer "F.SilkS") (width 0.12) (tstamp 11166f97-ed70-4cb7-81d7-7891e9e0b4eb))
    (fp_line (start -0.711252 -0.91) (end 0.711252 -0.91) (layer "F.SilkS") (width 0.12) (tstamp 56b791bd-4bd1-402a-add9-dd1b785e19c7))
    (fp_rect (start -2.3 -1) (end 2.3 1) (layer "F.CrtYd") (width 0.05) (fill none) (tstamp 88ad8fcc-9aae-40e2-838f-242cec68bd6a))
    (fp_line (start -1.6 0.8) (end -1.6 -0.8) (layer "F.Fab") (width 0.1) (tstamp 03b29997-e6cf-4f95-ab2f-48a96c25713e))
    (fp_line (start 1.6 0.8) (end -1.6 0.8) (layer "F.Fab") (width 0.1) (tstamp add057f3-9ba6-4d5e-bed8-38cfd7f58cad))
    (fp_line (start 1.6 -0.8) (end 1.6 0.8) (layer "F.Fab") (width 0.1) (tstamp c89209f8-f06d-4961-9460-9548a6d24554))
    (fp_line (start -1.6 -0.8) (end 1.6 -0.8) (layer "F.Fab") (width 0.1) (tstamp d44c9923-c9da-4e68-b750-f730efb92518))
    (fp_rect (start -1.95 -0.7) (end -1.65 0.7) (layer "F.Fab") (width 0.01) (fill solid) (tstamp 61572252-55f2-4259-82cc-e7fa5e51537a))
    (fp_rect (start 1.6 -0.7) (end 1.9 0.7) (layer "F.Fab") (width 0.01) (fill solid) (tstamp ed86fa80-21c4-430e-9fee-b311dba83db4))
    (pad "1" smd roundrect locked (at -1.475 0 180) (size 1.15 1.8) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.217391)
      (net 9 "Net-(C27-Pad1)") (pintype "passive") (tstamp b4716f7e-720f-4a40-94df-9dab415c2e09))
    (pad "2" smd roundrect locked (at 1.475 0 180) (size 1.15 1.8) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.217391)
      (net 1 "GND") (pintype "passive") (tstamp 4ab41e96-3896-429b-845c-c8b841245667))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_1206_3216Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (footprint "0IBF_RCL:C_1206_3216Metric" (layer "F.Cu")
    (tedit 60FD2F22) (tstamp e5eb927b-ab9b-4dfa-a350-78194cc42294)
    (at 101.67 -86.38 180)
    (descr "Capacitor SMD 1206 (3216 Metric), square (rectangular) end terminal, IPC_7351 nominal, (Body size source: IPC-SM-782 page 76, https://www.pcb-3d.com/wordpress/wp-content/uploads/ipc-sm-782a_amendment_1_and_2.pdf), generated with kicad-footprint-generator")
    (tags "capacitor")
    (property "Alternative" "")
    (property "Bemerkung" "")
    (property "Farnell" "")
    (property "MF" "")
    (property "MPN" "")
    (property "RS" "")
    (property "Sheetfile" "controller.kicad_sch")
    (property "Sheetname" "Controller")
    (property "digikey" "")
    (property "mouser" "")
    (path "/21ecf582-2fe5-40fc-9946-89f54de57e7c/dceb99f1-0f35-4081-8010-aaf5d778581f")
    (attr smd)
    (fp_text reference "C28" (at 0 -1.85) (layer "F.SilkS")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp e82dbd21-dd36-466f-8f06-703f80ab95c6)
    )
    (fp_text value "18pF" (at 0 1.85) (layer "F.Fab")
      (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 03d07f58-edb7-4e39-a342-f7dca32af450)
    )
    (fp_text user "${REFERENCE}" (at 0 0) (layer "F.Fab")
      (effects (font (size 0.8 0.8) (thickness 0.12)))
      (tstamp 5d0c1f5c-af2f-41b1-affe-4bac5f6e5128)
    )
    (fp_line (start -0.711252 0.91) (end 0.711252 0.91) (layer "F.SilkS") (width 0.12) (tstamp 4cf32712-d495-4992-aaf7-1cbd990dc530))
    (fp_line (start -0.711252 -0.91) (end 0.711252 -0.91) (layer "F.SilkS") (width 0.12) (tstamp 667681d2-df09-41b5-a700-b0de6b24a524))
    (fp_rect (start -2.3 -1) (end 2.3 1) (layer "F.CrtYd") (width 0.05) (fill none) (tstamp be2f800a-3ed6-4e0a-912d-a110cfdf6b4b))
    (fp_line (start -1.6 0.8) (end -1.6 -0.8) (layer "F.Fab") (width 0.1) (tstamp 12cf333d-1f68-4ae6-b553-70ba2f5f693e))
    (fp_line (start 1.6 0.8) (end -1.6 0.8) (layer "F.Fab") (width 0.1) (tstamp 306456f9-bce2-4cb0-ac6f-558637713ef6))
    (fp_line (start -1.6 -0.8) (end 1.6 -0.8) (layer "F.Fab") (width 0.1) (tstamp 3dc97e70-7e3a-4f78-9ff0-cd8142ad6b00))
    (fp_line (start 1.6 -0.8) (end 1.6 0.8) (layer "F.Fab") (width 0.1) (tstamp 46aa8dc6-7b8c-407f-856b-2760fb8dbfb4))
    (fp_rect (start 1.6 -0.7) (end 1.9 0.7) (layer "F.Fab") (width 0.01) (fill solid) (tstamp 5f49b3a8-a993-4520-ac7c-a3fce5822c61))
    (fp_rect (start -1.95 -0.7) (end -1.65 0.7) (layer "F.Fab") (width 0.01) (fill solid) (tstamp 60f0f474-6042-463f-9249-121adf9d7e7d))
    (pad "1" smd roundrect locked (at -1.475 0 180) (size 1.15 1.8) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.217391)
      (net 10 "Net-(C28-Pad1)") (pintype "passive") (tstamp dc885587-42ea-477f-b5a8-2ca9965e1b83))
    (pad "2" smd roundrect locked (at 1.475 0 180) (size 1.15 1.8) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.217391)
      (net 1 "GND") (pintype "passive") (tstamp 767a6593-1530-48ff-8f15-972e43ca7829))
    (model "${KICAD6_3DMODEL_DIR}/Capacitor_SMD.3dshapes/C_1206_3216Metric.wrl"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_poly
    (pts
      (xy 0 0)
      (xy 0 -100)
      (xy 150.7 -100)
      (xy 150.7 0)
    ) (layer "Edge.Cuts") (width 0.1) (fill none) (tstamp 8ce1d4b0-7fa5-46e0-adc9-cd5be18c10e9))

  (segment (start 111.4375 -87.85) (end 112.85 -89.2625) (width 0.2) (layer "F.Cu") (net 3) (tstamp 17b7dbb2-a831-4dea-b4e7-844bc97220de))
  (segment (start 112.395 -89.7175) (end 112.395 -93.38) (width 0.2) (layer "F.Cu") (net 3) (tstamp 1c6dd1df-dd62-4608-8d17-a93c33de3441))
  (segment (start 112.375 -93.4) (end 112.395 -93.38) (width 0.4) (layer "F.Cu") (net 3) (tstamp 4397afb6-b5ea-41b0-b807-1eb5b8bd07e5))
  (segment (start 112.85 -89.2625) (end 112.395 -89.7175) (width 0.2) (layer "F.Cu") (net 3) (tstamp ec95af03-6d36-42a0-b85e-09736135ea4b))
  (segment (start 119.7625 -84.35) (end 121.681337 -84.35) (width 0.2) (layer "F.Cu") (net 4) (tstamp 3873bce8-1765-4683-88fe-f282a2e046f8))
  (segment (start 121.681337 -84.35) (end 122.821555 -85.490218) (width 0.2) (layer "F.Cu") (net 4) (tstamp 70ecde91-2108-487b-9c92-184fbca2ff8a))
  (segment (start 122.821555 -85.490218) (end 122.821555 -85.646555) (width 0.2) (layer "F.Cu") (net 4) (tstamp acb536a5-b484-4ae7-9fb6-143a115cc3d8))
  (segment (start 104.73 -82.38) (end 104.7 -82.35) (width 0.2) (layer "F.Cu") (net 8) (tstamp 0274d653-1f7e-424e-800e-1a11a5d101ef))
  (segment (start 109.039022 -84.85) (end 106.569022 -82.38) (width 0.2) (layer "F.Cu") (net 8) (tstamp 1fbbefdb-0bfc-4131-bef7-59d7e3b32aeb))
  (segment (start 105.945 -82.38) (end 104.73 -82.38) (width 0.2) (layer "F.Cu") (net 8) (tstamp 3318ac82-3e51-4e49-80c5-aaae0671a1d7))
  (segment (start 111.4375 -84.85) (end 109.039022 -84.85) (width 0.2) (layer "F.Cu") (net 8) (tstamp 444f4ff4-45ae-46ad-83c3-c86b67250533))
  (segment (start 106.569022 -82.38) (end 105.945 -82.38) (width 0.2) (layer "F.Cu") (net 8) (tstamp b74cd295-71ed-4aaf-9c9f-6bc756bbe74f))
  (segment (start 107.22 -88.88) (end 106.07 -88.88) (width 0.2) (layer "F.Cu") (net 9) (tstamp 0093020a-49dd-421e-b9ba-e754c8e48002))
  (segment (start 103.145 -88.88) (end 106.07 -88.88) (width 0.2) (layer "F.Cu") (net 9) (tstamp 4b3735f5-4bc4-4ca5-8da5-a4d025ea0d37))
  (segment (start 111.4375 -85.85) (end 110.25 -85.85) (width 0.2) (layer "F.Cu") (net 9) (tstamp 6a1e7aae-88e8-4a5a-a259-e8ad9bb6bf0a))
  (segment (start 110.25 -85.85) (end 107.22 -88.88) (width 0.2) (layer "F.Cu") (net 9) (tstamp 918714e7-254e-49f8-95d7-2fbc42ee45fa))
  (segment (start 104.645 -84.88) (end 106.07 -84.88) (width 0.2) (layer "F.Cu") (net 10) (tstamp 21478b50-ec5c-4252-9065-9ab52dc1d215))
  (segment (start 111.4375 -85.35) (end 106.54 -85.35) (width 0.2) (layer "F.Cu") (net 10) (tstamp 6fdc8151-f8d3-4751-8651-6d7dd94ef438))
  (segment (start 106.54 -85.35) (end 106.07 -84.88) (width 0.2) (layer "F.Cu") (net 10) (tstamp b0ef9842-3a07-4bc7-92f1-e51d797e26bb))
  (segment (start 103.145 -86.38) (end 104.645 -84.88) (width 0.2) (layer "F.Cu") (net 10) (tstamp ff1fe589-f890-42d3-96d7-e0f4a39aa094))
  (segment (start 111.4375 -86.35) (end 112.175 -86.35) (width 0.2) (layer "F.Cu") (net 13) (tstamp 93905cf4-26f4-4adb-91d0-8eba4c43452b))
  (segment (start 110.5 -87.35) (end 110 -87.85) (width 0.2) (layer "F.Cu") (net 14) (tstamp 1e3be38e-7011-4661-81e8-bffb71d6b9e4))
  (segment (start 110 -87.85) (end 109.95 -87.9) (width 0.2) (layer "F.Cu") (net 14) (tstamp 9282658c-bc67-4c17-bed1-9557fa6ba9cd))
  (segment (start 111.4375 -87.35) (end 110.5 -87.35) (width 0.2) (layer "F.Cu") (net 14) (tstamp d4d78e80-ec6f-473e-b19c-a5b09c56ef66))
  (segment (start 109.95 -87.9) (end 109.95 -90.6005) (width 0.2) (layer "F.Cu") (net 14) (tstamp f3380420-beac-4952-aa3d-41d7fe9e0cdb))
  (via (at 109.95 -90.6005) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 14) (tstamp e5b3e736-ef16-4a48-b59f-7e97b69123e6))
  (segment (start 109.95 -90.75) (end 109.89952 -90.80048) (width 0.2) (layer "B.Cu") (net 14) (tstamp 5774fe3b-4f3f-4e4a-ac7f-379aaed3b41c))
  (segment (start 109.95 -90.6005) (end 109.95 -90.75) (width 0.2) (layer "B.Cu") (net 14) (tstamp ef8ea2e9-ead7-4023-a133-6e8575585adb))
  (segment (start 111.4375 -83.35) (end 110.7 -83.35) (width 0.2) (layer "F.Cu") (net 19) (tstamp 47ead742-42bf-4320-bb0f-438c75cff963))
  (segment (start 111.4375 -82.85) (end 110.7 -82.85) (width 0.2) (layer "F.Cu") (net 20) (tstamp 0efe3f7c-54c6-445e-b949-b22dc44a4303))
  (segment (start 109.3 -81.45) (end 109.3 -72.65) (width 0.2) (layer "F.Cu") (net 20) (tstamp 795b7d46-c389-4cdb-8eac-cf2e9ffe99b3))
  (segment (start 109.3 -72.65) (end 109.25 -72.6) (width 0.2) (layer "F.Cu") (net 20) (tstamp bc6118ec-4137-4856-8e1f-93f839210fbb))
  (segment (start 110.7 -82.85) (end 109.3 -81.45) (width 0.2) (layer "F.Cu") (net 20) (tstamp c79c41fe-7fbc-4fe6-b542-a517a2c25d22))
  (segment (start 109.8495 -80.762) (end 109.8495 -75.934832) (width 0.2) (layer "F.Cu") (net 21) (tstamp 85ede48d-2a1e-4c30-8101-4f77ac925674))
  (segment (start 111.4375 -82.35) (end 109.8495 -80.762) (width 0.2) (layer "F.Cu") (net 21) (tstamp 92e92b5a-d5b1-4c5f-af3b-26f27138e119))
  (via (at 109.8495 -75.934832) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 21) (tstamp 7c0f2b25-35b7-4875-9287-b6e3327af513))
  (segment (start 109.783332 -76.001) (end 109.8495 -75.934832) (width 0.2) (layer "B.Cu") (net 21) (tstamp 3dfb4965-d749-4a08-ac0f-c499420acb23))
  (segment (start 95.349 -76.001) (end 109.783332 -76.001) (width 0.2) (layer "B.Cu") (net 21) (tstamp c2347614-ddb8-412e-900a-9454ff0e3076))
  (segment (start 95.301 -76.001) (end 95.3 -76) (width 0.2) (layer "B.Cu") (net 21) (tstamp fad86278-e752-4050-a99e-ae442c50b3d5))
  (segment (start 112.85 -80.9375) (end 112.85 -79.35) (width 0.2) (layer "F.Cu") (net 22) (tstamp bf8993a8-267e-4a88-aaf7-43a916bc9b78))
  (segment (start 112.85 -79.35) (end 112.4 -78.9) (width 0.2) (layer "F.Cu") (net 22) (tstamp c9ca226e-9c9d-4ad6-83fe-a8d1e7fefac6))
  (via (at 112.4 -78.9) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 22) (tstamp 878ab63f-5cbe-4cb7-85b2-6dc45fcaa5c9))
  (segment (start 112.4 -78.9) (end 112.35 -78.85) (width 0.2) (layer "B.Cu") (net 22) (tstamp e1151690-cc80-4490-993c-d2d3f9a9deb0))
  (segment (start 113.35 -80.9375) (end 113.35 -80.241042) (width 0.2) (layer "F.Cu") (net 23) (tstamp 2cfcc0dc-4710-415d-abd2-23d1bddaecd9))
  (segment (start 113.553999 -79.7) (end 113.553999 -78.782576) (width 0.2) (layer "F.Cu") (net 24) (tstamp 5b5a6a35-41db-47c7-992e-d69b71c5c3a2))
  (segment (start 112.447799 -77.676376) (end 111.084251 -77.676376) (width 0.2) (layer "F.Cu") (net 24) (tstamp b39627ab-4f80-4579-a2fb-c9b2740726cb))
  (segment (start 113.553999 -78.782576) (end 112.447799 -77.676376) (width 0.2) (layer "F.Cu") (net 24) (tstamp b78d0291-5d5c-4e58-b537-6b296efb406a))
  (segment (start 113.85 -80.9375) (end 113.85 -79.996001) (width 0.2) (layer "F.Cu") (net 24) (tstamp d4f0a7f1-bbc6-49be-acf8-df2c902beee3))
  (segment (start 113.85 -79.996001) (end 113.553999 -79.7) (width 0.2) (layer "F.Cu") (net 24) (tstamp de3c173e-5ea6-48aa-af89-7a58d65dd859))
  (via (at 111.084251 -77.676376) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 24) (tstamp 2aeb9dbf-5e66-4b17-802a-fa0e16384958))
  (segment (start 96.923624 -77.676376) (end 96.9 -77.7) (width 0.2) (layer "B.Cu") (net 24) (tstamp 1e90f58d-6f36-48c2-a7b5-66ef4d8af4cf))
  (segment (start 111.084251 -77.676376) (end 96.923624 -77.676376) (width 0.2) (layer "B.Cu") (net 24) (tstamp a4c9c39f-7446-4e87-8670-15e211408330))
  (segment (start 112.6 -77.1) (end 111.731575 -77.1) (width 0.2) (layer "F.Cu") (net 25) (tstamp be041ff0-fc9f-4024-8b9b-7ee27227924b))
  (segment (start 114.35 -78.85) (end 112.6 -77.1) (width 0.2) (layer "F.Cu") (net 25) (tstamp c6e3042f-b41b-4cb9-8d31-3a6fe16b81bf))
  (segment (start 114.35 -80.9375) (end 114.35 -78.85) (width 0.2) (layer "F.Cu") (net 25) (tstamp ccc2f9c1-e2f7-4a65-a1ec-86cc5405b676))
  (segment (start 111.731575 -77.1) (end 111.704699 -77.126876) (width 0.2) (layer "F.Cu") (net 25) (tstamp f8ef742c-32dd-4d87-9b27-283e4bb106e5))
  (via (at 111.704699 -77.126876) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 25) (tstamp 94fd927c-5cba-40d7-b272-7163c37cd6c5))
  (segment (start 97.073124 -77.126876) (end 97 -77.2) (width 0.2) (layer "B.Cu") (net 25) (tstamp ed9f6e87-4b74-4372-878a-803ae31e61b7))
  (segment (start 111.704699 -77.126876) (end 97.073124 -77.126876) (width 0.2) (layer "B.Cu") (net 25) (tstamp ffa859cc-11c9-4bcb-9db6-0f0786afce7e))
  (segment (start 121.1 -84.85) (end 121.15 -84.9) (width 0.2) (layer "F.Cu") (net 29) (tstamp 95a687bf-2ded-45be-ac1d-d34df0df64b4))
  (segment (start 119.7625 -84.85) (end 121.1 -84.85) (width 0.2) (layer "F.Cu") (net 29) (tstamp 9f845ad7-4de4-4d3a-b411-34636302bed6))
  (segment (start 119.7625 -85.35) (end 119.862001 -85.449501) (width 0.2) (layer "F.Cu") (net 30) (tstamp f7c5e2ab-7d80-45ff-b56c-42253409aca7))
  (segment (start 121.627113 -85.85) (end 122.34634 -86.569227) (width 0.2) (layer "F.Cu") (net 31) (tstamp 53674539-2d8a-409e-aa2f-e53e91568d52))
  (segment (start 122.34634 -86.569227) (end 122.34634 -86.650999) (width 0.2) (layer "F.Cu") (net 31) (tstamp c26da0fa-b0f9-4537-9fd7-e438176f27fe))
  (segment (start 119.7625 -85.85) (end 121.627113 -85.85) (width 0.2) (layer "F.Cu") (net 31) (tstamp f87c16e4-9039-441e-b6fd-e87ea70d6aad))
  (via (at 122.34634 -86.650999) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 31) (tstamp aeeaf173-7068-406d-9683-46ded40476c6))
  (segment (start 124.922385 -86.749012) (end 125.020398 -86.650999) (width 0.2) (layer "B.Cu") (net 31) (tstamp 48f019c8-b7f0-47c5-8dc9-063e7878736f))
  (segment (start 122.444353 -86.749012) (end 124.922385 -86.749012) (width 0.2) (layer "B.Cu") (net 31) (tstamp 900920a5-8ef0-4baf-9b88-4098a94d231f))
  (segment (start 122.34634 -86.650999) (end 122.444353 -86.749012) (width 0.2) (layer "B.Cu") (net 31) (tstamp f4809eec-3416-466b-a626-a977c4aa4d14))
  (segment (start 119.7625 -86.35) (end 121.35 -86.35) (width 0.2) (layer "F.Cu") (net 32) (tstamp 4d7ae790-1d37-43a3-8ac3-a68006e38765))
  (segment (start 121.35 -86.35) (end 121.55 -86.55) (width 0.2) (layer "F.Cu") (net 32) (tstamp c3e02b29-bd93-422c-92f7-0bf197f3610d))
  (via (at 121.55 -86.55) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 32) (tstamp 643dc246-af22-474c-8fe2-476ee1d4316b))
  (segment (start 121.55 -86.55) (end 121.55 -86.7) (width 0.2) (layer "B.Cu") (net 32) (tstamp e149c6bb-9643-406f-a9ed-9336bd589f24))
  (segment (start 117.85 -89.2625) (end 117.85 -90) (width 0.2) (layer "F.Cu") (net 35) (tstamp 04dbe7ae-42d9-4eff-89ee-6ff462927d9b))
  (segment (start 118.9 -91.05) (end 119.05 -91.05) (width 0.2) (layer "F.Cu") (net 35) (tstamp 30c11fa2-4bc6-4b85-b4cb-f4ffacad1958))
  (segment (start 117.85 -90) (end 118.9 -91.05) (width 0.2) (layer "F.Cu") (net 35) (tstamp ce1f28a2-6de0-4185-ae31-da920f0815ca))
  (via (at 119.05 -91.05) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 35) (tstamp 6faa2a89-f526-41d3-9ecf-f4e660f719be))
  (segment (start 119.05 -91.05) (end 119.149501 -91.149501) (width 0.2) (layer "B.Cu") (net 35) (tstamp 0ef2acf4-3ebc-4541-8630-799b090476c8))
  (segment (start 115.85 -89.2625) (end 115.85 -90.55) (width 0.2) (layer "F.Cu") (net 37) (tstamp 406956e8-49de-4237-8754-53408b826c1c))
  (segment (start 115.85 -90.55) (end 117.4 -92.1) (width 0.2) (layer "F.Cu") (net 37) (tstamp 59845a05-5a0a-4e41-ada6-38f88df52505))
  (via (at 117.4 -92.1) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 37) (tstamp a7e3f801-b254-48e4-94cc-c47f0e9aa322))
  (segment (start 117.4 -92.1) (end 117.449501 -92.149501) (width 0.2) (layer "B.Cu") (net 37) (tstamp 1cbc4c6c-8611-4ec3-a25d-982cc792947a))
  (segment (start 115.35 -90.85) (end 117.311041 -92.811041) (width 0.2) (layer "F.Cu") (net 38) (tstamp 09a89bec-6ec2-43e7-9825-2404e6dc0548))
  (segment (start 117.311041 -92.811041) (end 117.637014 -92.811041) (width 0.2) (layer "F.Cu") (net 38) (tstamp b70ce0d2-230c-4b93-8735-747e4483e781))
  (segment (start 115.35 -89.2625) (end 115.35 -90.85) (width 0.2) (layer "F.Cu") (net 38) (tstamp f5c8cd32-a35c-47d8-b106-06783c17840f))
  (via (at 117.637014 -92.811041) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 38) (tstamp 5d076d64-d551-4c37-9b35-e9867cf8dc56))
  (segment (start 117.748055 -92.7) (end 117.637014 -92.811041) (width 0.2) (layer "B.Cu") (net 38) (tstamp 37c33c07-e877-4285-b6c6-6fcfa7407a9b))
  (segment (start 114.35 -89.2625) (end 114.35 -90) (width 0.2) (layer "F.Cu") (net 40) (tstamp 269f9c9f-9eb1-4b4c-a309-66bd4d06beb1))
  (segment (start 113.85 -89.2625) (end 113.85 -91.596123) (width 0.2) (layer "F.Cu") (net 41) (tstamp 2a221f03-e175-4502-8a60-2a8fdc2b61d8))
  (segment (start 113.85 -91.596123) (end 113.746623 -91.6995) (width 0.2) (layer "F.Cu") (net 41) (tstamp c49a402b-8eab-40de-8cb3-dfaefdda2f41))
  (via (at 113.746623 -91.6995) (size 0.6) (drill 0.2) (layers "F.Cu" "B.Cu") (net 41) (tstamp 5771defb-7327-4f6a-bf33-b18ce08c1582))
  (segment (start 113.597123 -91.55) (end 113.746623 -91.6995) (width 0.2) (layer "B.Cu") (net 41) (tstamp 0eba5db0-811a-4c95-9e75-e1dba2df2f46))
  (segment (start 116.35 -80.9375) (end 116.85 -80.9375) (width 0.2) (layer "F.Cu") (net 42) (tstamp a12e3bc0-22a2-434c-8ba7-f59e24c78c21))

)
