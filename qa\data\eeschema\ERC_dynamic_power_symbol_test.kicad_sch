(kicad_sch (version 20230819) (generator eeschema)

  (uuid 00325d8e-72cb-4ca3-8445-d6b5e061276d)

  (paper "A5")

  (lib_symbols
    (symbol "PCM_4ms_Power-symbol:PWR_FLAG" (power) (pin_numbers hide) (pin_names (offset 0) hide) (exclude_from_sim no) (in_bom yes) (on_board yes)
      (property "Reference" "#FLG" (at 0 1.905 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "PWR_FLAG" (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Description" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "PWR_FLAG_0_0"
        (pin power_out line (at 0 0 90) (length 0)
          (name "pwr" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "PWR_FLAG_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
            (xy -1.016 1.905)
            (xy 0 2.54)
            (xy 1.016 1.905)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
    )
    (symbol "power:GND" (power) (pin_names (offset 0)) (exclude_from_sim no) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (at 0 -6.35 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Description" "Power symbol creates a global label with name \"GND\" , ground" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "global power" (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 97.155 73.025) (diameter 0) (color 0 0 0 0)
    (uuid 318873ba-93d0-4611-adbd-ab20b3bf8da2)
  )
  (junction (at 97.155 88.265) (diameter 0) (color 0 0 0 0)
    (uuid 7dd8ed2a-e1f6-4007-9d1d-92b533495aee)
  )
  (junction (at 97.155 69.85) (diameter 0) (color 0 0 0 0)
    (uuid 83871bdf-6b08-4ac2-bad8-9ef65b3c79df)
  )
  (junction (at 81.915 54.61) (diameter 0) (color 0 0 0 0)
    (uuid bfb8ab1c-d3b9-4129-9f01-9f99e284c17f)
  )
  (junction (at 97.155 54.61) (diameter 0) (color 0 0 0 0)
    (uuid c95d470c-e721-4ed3-b686-41e6d3e42fc1)
  )

  (wire (pts (xy 97.155 54.61) (xy 103.505 54.61))
    (stroke (width 0) (type default))
    (uuid 25c8d790-f49a-4d2a-bf36-5092484d0993)
  )
  (wire (pts (xy 103.505 88.265) (xy 97.155 88.265))
    (stroke (width 0) (type default))
    (uuid 3b568d7a-4e3e-4cb7-a2a5-d4a40caec794)
  )
  (wire (pts (xy 97.155 73.025) (xy 97.155 69.85))
    (stroke (width 0) (type default))
    (uuid 548ce8a3-928e-4470-b180-e1983c5f2e59)
  )
  (wire (pts (xy 97.155 73.025) (xy 103.505 73.025))
    (stroke (width 0) (type default))
    (uuid 5e5d7738-6a11-402a-a270-14233883574e)
  )
  (wire (pts (xy 97.155 69.85) (xy 97.155 54.61))
    (stroke (width 0) (type default))
    (uuid 82e0575b-5426-48ca-9a77-2a998559130a)
  )
  (wire (pts (xy 81.915 54.61) (xy 81.915 59.055))
    (stroke (width 0) (type default))
    (uuid 98fb714b-ecfc-498d-b5ba-2470d43f6f35)
  )
  (wire (pts (xy 81.915 54.61) (xy 97.155 54.61))
    (stroke (width 0) (type default))
    (uuid a93a2ae6-bcee-4b61-942d-9cec89db1cb1)
  )
  (wire (pts (xy 103.505 69.85) (xy 97.155 69.85))
    (stroke (width 0) (type default))
    (uuid c156654b-4712-42d1-8613-602e2d88fff9)
  )
  (wire (pts (xy 97.155 88.265) (xy 97.155 73.025))
    (stroke (width 0) (type default))
    (uuid ca52f235-4198-4e5f-9e48-80670d79ce88)
  )
  (wire (pts (xy 81.915 52.705) (xy 81.915 54.61))
    (stroke (width 0) (type default))
    (uuid d70f84e2-4886-4606-ba5f-b24ec036f382)
  )
  (wire (pts (xy 97.155 88.265) (xy 97.155 91.44))
    (stroke (width 0) (type default))
    (uuid e69ef9e1-0e20-467f-884c-e2e0101ae07a)
  )
  (wire (pts (xy 97.155 91.44) (xy 103.505 91.44))
    (stroke (width 0) (type default))
    (uuid ea0f7891-d2c5-425b-8e3a-f5dde385e419)
  )

  (symbol (lib_id "PCM_4ms_Power-symbol:PWR_FLAG") (at 81.915 52.705 0) (unit 1)
    (exclude_from_sim no) (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 528bd027-e74e-400a-8ca2-cf0652e9fae7)
    (property "Reference" "#FLG0101" (at 81.915 50.8 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "PWR_FLAG" (at 81.915 48.5719 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 81.915 52.705 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 81.915 52.705 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "" (at 81.915 52.705 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid e08d6487-ccd7-4390-8e85-2bec8cc362df))
    (instances
      (project "ERC_dynamic_power_symbol_test"
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d"
          (reference "#FLG0101") (unit 1)
        )
      )
    )
  )

  (symbol (lib_id "power:GND") (at 81.915 59.055 0) (unit 1)
    (exclude_from_sim no) (in_bom yes) (on_board yes) (dnp no) (fields_autoplaced)
    (uuid 78a241b3-2465-484a-9234-d60d4e95876e)
    (property "Reference" "#PWR0101" (at 81.915 65.405 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (at 81.915 63.1881 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (at 81.915 59.055 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (at 81.915 59.055 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Description" "Power symbol creates a global label with name \"GND\" , ground" (at 81.915 59.055 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid f207bbaa-5dcb-41ec-b174-ceddb9408aaa))
    (instances
      (project "ERC_dynamic_power_symbol_test"
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d"
          (reference "#PWR0101") (unit 1)
        )
      )
    )
  )

  (sheet (at 103.505 66.04) (size 24.765 10.795) (fields_autoplaced)
    (stroke (width 0.1524) (type solid))
    (fill (color 0 0 0 0.0000))
    (uuid 088a9fe0-ec9a-43a7-9e7e-edfb4cee57b2)
    (property "Sheetname" "ERC_dynamic_power_symbol_subsheet1" (at 103.505 65.3284 0)
      (effects (font (size 1.27 1.27)) (justify left bottom))
    )
    (property "Sheetfile" "erc_test_dynamic_power_symbol_subsheet.kicad_sch" (at 103.505 77.4196 0)
      (effects (font (size 1.27 1.27)) (justify left top))
    )
    (property "param" "param_subsheet_1" (at 103.505 66.04 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "REF_NODE" input (at 103.505 69.85 180)
      (effects (font (size 1.27 1.27)) (justify left))
      (uuid 0a11936d-6619-423b-b15f-0eb82d418c39)
    )
    (pin "${param}" input (at 103.505 73.025 180)
      (effects (font (size 1.27 1.27)) (justify left))
      (uuid d55efedc-5409-4a66-ad09-ef4373b04f6c)
    )
    (instances
      (project "ERC_dynamic_power_symbol_test"
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d" (page "3"))
      )
    )
  )

  (sheet (at 103.505 84.455) (size 24.765 10.795) (fields_autoplaced)
    (stroke (width 0.1524) (type solid))
    (fill (color 0 0 0 0.0000))
    (uuid 356ec2aa-e896-4e5b-96d5-f383f5c93ff2)
    (property "Sheetname" "ERC_dynamic_power_symbol_subsheet2" (at 103.505 83.7434 0)
      (effects (font (size 1.27 1.27)) (justify left bottom))
    )
    (property "Sheetfile" "erc_test_dynamic_power_symbol_subsheet.kicad_sch" (at 103.505 95.8346 0)
      (effects (font (size 1.27 1.27)) (justify left top))
    )
    (property "param" "param_subsheet_2" (at 103.505 84.455 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "REF_NODE" input (at 103.505 88.265 180)
      (effects (font (size 1.27 1.27)) (justify left))
      (uuid 4065714c-1c4a-41a6-b87d-db2c45a3ac64)
    )
    (pin "${param}" input (at 103.505 91.44 180)
      (effects (font (size 1.27 1.27)) (justify left))
      (uuid 6db95d1f-4031-4232-a08d-617be8a00d5a)
    )
    (instances
      (project "ERC_dynamic_power_symbol_test"
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d" (page "4"))
      )
    )
  )

  (sheet (at 103.505 50.8) (size 15.24 7.62) (fields_autoplaced)
    (stroke (width 0.1524) (type solid))
    (fill (color 0 0 0 0.0000))
    (uuid c7b6fd2e-05cd-4dd4-8428-8dccf678354a)
    (property "Sheetname" "ERC_dynamic_power_symbol_subsheet" (at 103.505 50.0884 0)
      (effects (font (size 1.27 1.27)) (justify left bottom))
    )
    (property "Sheetfile" "erc_test_dynamic_power_symbol_subsheet.kicad_sch" (at 103.505 59.0046 0)
      (effects (font (size 1.27 1.27)) (justify left top))
    )
    (property "param" "param_subsheet" (at 103.505 50.8 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "REF_NODE" input (at 103.505 54.61 180)
      (effects (font (size 1.27 1.27)) (justify left))
      (uuid 769a5a79-6118-41b6-93fe-cf2c04ffe283)
    )
    (instances
      (project "ERC_dynamic_power_symbol_test"
        (path "/00325d8e-72cb-4ca3-8445-d6b5e061276d" (page "2"))
      )
    )
  )

  (sheet_instances
    (path "/" (page "1"))
  )
)
