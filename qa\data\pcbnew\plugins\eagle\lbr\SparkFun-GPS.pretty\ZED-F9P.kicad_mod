(footprint "ZED-F9P" (version 20231007) (generator pcbnew)
  (layer "F.Cu")
  (property "Reference" "REF**" (at -11 -9 0 unlocked) (layer "F.SilkS") (tstamp fa71ada2-f00b-4b4a-bde3-45325cd4fe7f)
    (effects (font (size 0.46736 0.46736) (thickness 0.04064)) (justify left bottom))
  )
  (property "Value" "ZED-F9P" (at -11 9.5 0 unlocked) (layer "F.Fab") (tstamp c8f04c79-25b5-4203-8bd9-0a15faa6b8be)
    (effects (font (size 0.46736 0.46736) (thickness 0.04064)) (justify left bottom))
  )
  (property "Footprint" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp 5e4a548f-8207-4f24-b8e7-4b94d5863c94)
    (effects (font (size 1.27 1.27)))
  )
  (property "Datasheet" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp f1ff5eee-441c-4d54-b64c-ef0420b997c4)
    (effects (font (size 1.27 1.27)))
  )
  (property "Description" "" (at 0 0 0 unlocked) (layer "F.Fab") hide (tstamp a506d6c6-8206-4f8c-99ca-4a1077ca3240)
    (effects (font (size 1.27 1.27)))
  )
  (fp_poly
    (pts
      (xy -7.8 -4.7)
      (xy -6.9 -4.7)
      (xy -6.9 -5.6)
      (xy -7.8 -5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 0c3e256a-a261-4ef3-9a2d-a7f1a50b2941))
  (fp_poly
    (pts
      (xy -7.8 -2.65)
      (xy -6.9 -2.65)
      (xy -6.9 -3.55)
      (xy -7.8 -3.55)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 688b471e-c404-4cff-afac-064006fbb630))
  (fp_poly
    (pts
      (xy -7.8 -0.6)
      (xy -6.9 -0.6)
      (xy -6.9 -1.5)
      (xy -7.8 -1.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp e83d0e40-6b9a-4883-8abd-61789da66097))
  (fp_poly
    (pts
      (xy -7.8 1.5)
      (xy -6.9 1.5)
      (xy -6.9 0.6)
      (xy -7.8 0.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 6f592232-567d-4e3b-881c-83d5d0903bc9))
  (fp_poly
    (pts
      (xy -7.8 3.55)
      (xy -6.9 3.55)
      (xy -6.9 2.65)
      (xy -7.8 2.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp adf56830-c281-43da-8c0c-fabf33fda266))
  (fp_poly
    (pts
      (xy -7.8 5.6)
      (xy -6.9 5.6)
      (xy -6.9 4.7)
      (xy -7.8 4.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7c04332e-d444-4f12-b9d1-05360927c357))
  (fp_poly
    (pts
      (xy -5.7 -4.7)
      (xy -4.8 -4.7)
      (xy -4.8 -5.6)
      (xy -5.7 -5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 3fb38838-997e-4c96-90cc-7efbf9a7cfb8))
  (fp_poly
    (pts
      (xy -5.7 -2.65)
      (xy -4.8 -2.65)
      (xy -4.8 -3.55)
      (xy -5.7 -3.55)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp c2aa0b0a-67a0-4d7d-ae8d-bd983c8015d8))
  (fp_poly
    (pts
      (xy -5.7 -0.6)
      (xy -4.8 -0.6)
      (xy -4.8 -1.5)
      (xy -5.7 -1.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7636bb30-f4fb-4424-ac29-405ff9c3155d))
  (fp_poly
    (pts
      (xy -5.7 1.5)
      (xy -4.8 1.5)
      (xy -4.8 0.6)
      (xy -5.7 0.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 07b8cfe3-2d51-4081-9bf7-6c2e94fef35e))
  (fp_poly
    (pts
      (xy -5.7 3.55)
      (xy -4.8 3.55)
      (xy -4.8 2.65)
      (xy -5.7 2.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 65940cd8-3339-47a8-a858-89a804119826))
  (fp_poly
    (pts
      (xy -5.7 5.6)
      (xy -4.8 5.6)
      (xy -4.8 4.7)
      (xy -5.7 4.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 36e4297f-96de-4daf-a895-b03db43518d7))
  (fp_poly
    (pts
      (xy -3.6 -4.7)
      (xy -2.7 -4.7)
      (xy -2.7 -5.6)
      (xy -3.6 -5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 429dcf3b-20b1-4c19-837b-e78d0f1a2f32))
  (fp_poly
    (pts
      (xy -3.6 -2.65)
      (xy -2.7 -2.65)
      (xy -2.7 -3.55)
      (xy -3.6 -3.55)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp c0043591-51a2-43f1-a479-3689244358dd))
  (fp_poly
    (pts
      (xy -3.6 -0.6)
      (xy -2.7 -0.6)
      (xy -2.7 -1.5)
      (xy -3.6 -1.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp ef2f9673-ca82-4fc5-bd57-03780649fc5e))
  (fp_poly
    (pts
      (xy -3.6 1.5)
      (xy -2.7 1.5)
      (xy -2.7 0.6)
      (xy -3.6 0.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7e608c1d-b5e0-40f0-91a2-ccb53f2d0677))
  (fp_poly
    (pts
      (xy -3.6 3.55)
      (xy -2.7 3.55)
      (xy -2.7 2.65)
      (xy -3.6 2.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 9ee5870d-f8ba-4c0e-9316-a8596a104517))
  (fp_poly
    (pts
      (xy -3.6 5.6)
      (xy -2.7 5.6)
      (xy -2.7 4.7)
      (xy -3.6 4.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7e2a61fe-15bb-4680-8748-3378f4f541b3))
  (fp_poly
    (pts
      (xy -1.5 -4.7)
      (xy -0.6 -4.7)
      (xy -0.6 -5.6)
      (xy -1.5 -5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 90021260-50d9-4514-858d-8ce9b13e2f48))
  (fp_poly
    (pts
      (xy -1.5 -2.65)
      (xy -0.6 -2.65)
      (xy -0.6 -3.55)
      (xy -1.5 -3.55)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 00725606-e3a4-408e-b078-10a7419d501e))
  (fp_poly
    (pts
      (xy -1.5 -0.6)
      (xy -0.6 -0.6)
      (xy -0.6 -1.5)
      (xy -1.5 -1.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7ee7688d-a27f-423a-8031-8e10d7be7681))
  (fp_poly
    (pts
      (xy -1.5 1.5)
      (xy -0.6 1.5)
      (xy -0.6 0.6)
      (xy -1.5 0.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 9017ec1b-176f-41c2-a75e-b979d6a6161c))
  (fp_poly
    (pts
      (xy -1.5 3.55)
      (xy -0.6 3.55)
      (xy -0.6 2.65)
      (xy -1.5 2.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 253973cc-1c22-482e-8abc-687d5a63eee4))
  (fp_poly
    (pts
      (xy -1.5 5.6)
      (xy -0.6 5.6)
      (xy -0.6 4.7)
      (xy -1.5 4.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 48a894bc-3c10-4b52-9a94-716a96d631cd))
  (fp_poly
    (pts
      (xy 0.6 -4.7)
      (xy 1.5 -4.7)
      (xy 1.5 -5.6)
      (xy 0.6 -5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 721e343b-9317-419b-b033-89dd0b604d8f))
  (fp_poly
    (pts
      (xy 0.6 -2.65)
      (xy 1.5 -2.65)
      (xy 1.5 -3.55)
      (xy 0.6 -3.55)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp d18c7925-b0a6-4817-b973-b4198df5ed98))
  (fp_poly
    (pts
      (xy 0.6 -0.6)
      (xy 1.5 -0.6)
      (xy 1.5 -1.5)
      (xy 0.6 -1.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp bbd6c39f-4b2a-40ba-b3f5-2017721bc020))
  (fp_poly
    (pts
      (xy 0.6 1.5)
      (xy 1.5 1.5)
      (xy 1.5 0.6)
      (xy 0.6 0.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 811cd263-b4da-4be2-bc56-aece6b4134b5))
  (fp_poly
    (pts
      (xy 0.6 3.55)
      (xy 1.5 3.55)
      (xy 1.5 2.65)
      (xy 0.6 2.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 49d2c6a2-237f-43bb-aeca-11bb22ff69f4))
  (fp_poly
    (pts
      (xy 0.6 5.6)
      (xy 1.5 5.6)
      (xy 1.5 4.7)
      (xy 0.6 4.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 57c3eb70-1047-42ff-9dfc-bf55b093333b))
  (fp_poly
    (pts
      (xy 2.7 -4.7)
      (xy 3.6 -4.7)
      (xy 3.6 -5.6)
      (xy 2.7 -5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 86ae8476-9323-4741-a326-41f2a704799f))
  (fp_poly
    (pts
      (xy 2.7 -2.65)
      (xy 3.6 -2.65)
      (xy 3.6 -3.55)
      (xy 2.7 -3.55)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 960230c5-8d71-49e5-a6e1-3d6a6afd0223))
  (fp_poly
    (pts
      (xy 2.7 -0.6)
      (xy 3.6 -0.6)
      (xy 3.6 -1.5)
      (xy 2.7 -1.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 98ade4d4-9bf3-4a9e-a631-1317401698cc))
  (fp_poly
    (pts
      (xy 2.7 1.5)
      (xy 3.6 1.5)
      (xy 3.6 0.6)
      (xy 2.7 0.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 05f74752-d714-405d-a15b-1b34ea79024b))
  (fp_poly
    (pts
      (xy 2.7 3.55)
      (xy 3.6 3.55)
      (xy 3.6 2.65)
      (xy 2.7 2.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp fb23711a-f3e7-419f-8c2b-40ffda0efdbe))
  (fp_poly
    (pts
      (xy 2.7 5.6)
      (xy 3.6 5.6)
      (xy 3.6 4.7)
      (xy 2.7 4.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp f090ca10-4ca8-4162-a224-b5bd51e0fbf4))
  (fp_poly
    (pts
      (xy 4.8 -4.7)
      (xy 5.7 -4.7)
      (xy 5.7 -5.6)
      (xy 4.8 -5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp e8b2b427-9f7a-4153-ac9d-4d1b6d0063de))
  (fp_poly
    (pts
      (xy 4.8 -2.65)
      (xy 5.7 -2.65)
      (xy 5.7 -3.55)
      (xy 4.8 -3.55)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp c71986a3-4c9d-493b-bb7d-dbced996fbcc))
  (fp_poly
    (pts
      (xy 4.8 -0.6)
      (xy 5.7 -0.6)
      (xy 5.7 -1.5)
      (xy 4.8 -1.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 124c50e0-7d5a-4bd5-af7d-8f4e4ae89118))
  (fp_poly
    (pts
      (xy 4.8 1.5)
      (xy 5.7 1.5)
      (xy 5.7 0.6)
      (xy 4.8 0.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp e6326a93-0f63-49ef-aed6-d5bca3727d18))
  (fp_poly
    (pts
      (xy 4.8 3.55)
      (xy 5.7 3.55)
      (xy 5.7 2.65)
      (xy 4.8 2.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 7eab0805-afec-4c21-8713-dba2b4075bbb))
  (fp_poly
    (pts
      (xy 4.8 5.6)
      (xy 5.7 5.6)
      (xy 5.7 4.7)
      (xy 4.8 4.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 3f393d5b-1f99-4e9b-8768-ecaf64cc4908))
  (fp_poly
    (pts
      (xy 6.9 -4.7)
      (xy 7.8 -4.7)
      (xy 7.8 -5.6)
      (xy 6.9 -5.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 5ff55816-ab73-44a4-8b15-4ec4e926c683))
  (fp_poly
    (pts
      (xy 6.9 -2.65)
      (xy 7.8 -2.65)
      (xy 7.8 -3.55)
      (xy 6.9 -3.55)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 06eab183-fb52-41a1-aee1-5288c36df3cb))
  (fp_poly
    (pts
      (xy 6.9 -0.6)
      (xy 7.8 -0.6)
      (xy 7.8 -1.5)
      (xy 6.9 -1.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp dad14002-4afd-4ad3-94b5-ca76b2097e88))
  (fp_poly
    (pts
      (xy 6.9 1.5)
      (xy 7.8 1.5)
      (xy 7.8 0.6)
      (xy 6.9 0.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp d170e744-8745-40a5-8d07-0586853413a2))
  (fp_poly
    (pts
      (xy 6.9 3.55)
      (xy 7.8 3.55)
      (xy 7.8 2.65)
      (xy 6.9 2.65)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 31496734-6a39-4c36-9261-6544899203e2))
  (fp_poly
    (pts
      (xy 6.9 5.6)
      (xy 7.8 5.6)
      (xy 7.8 4.7)
      (xy 6.9 4.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Paste") (tstamp 2657cc33-2a79-4a1d-93b1-235f0bad2084))
  (fp_line (start -11.25 -8.75) (end -8.8 -8.75)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 85928952-fce5-4270-8cb1-56ccd0fddbd3))
  (fp_line (start -11.25 -6.8) (end -11.25 -8.75)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 75c1e69e-dbdc-4ae0-980c-f9b1e7922f71))
  (fp_line (start -11.25 8.75) (end -11.25 6.8)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp f348623c-348d-44df-967b-317968666aa8))
  (fp_line (start -8.8 8.75) (end -11.25 8.75)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 8de77420-c2c2-4e86-abd0-cac86607f9d7))
  (fp_line (start 8.8 -8.75) (end 11.25 -8.75)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 00a5d8dd-4cb5-409f-b60f-6a9e34fa2a81))
  (fp_line (start 11.25 -8.75) (end 11.25 -6.8)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp 99e0ec70-9ae0-4297-afce-e99e626ecc31))
  (fp_line (start 11.25 6.8) (end 11.25 8.75)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp d1bef72a-6bf0-429a-950a-8352ffce4bae))
  (fp_line (start 11.25 8.75) (end 8.8 8.75)
    (stroke (width 0.1778) (type solid)) (layer "F.SilkS") (tstamp d3c91fc9-e001-40f1-b1c7-3e2aa395c9f7))
  (fp_circle (center -11.6 8.5) (end -11.529291 8.5)
    (stroke (width 0.141418) (type solid)) (fill solid) (layer "F.SilkS") (tstamp 6439b9da-ce2d-4513-af48-1a84cf66c5c8))
  (fp_line (start -11 -8.5) (end 11 -8.5)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 1f387c9a-f017-4177-97cf-96328e3f3433))
  (fp_line (start -11 8.5) (end -11 -8.5)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 9f515f3a-533d-4043-bd49-7833092de499))
  (fp_line (start 11 -8.5) (end 11 8.5)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp dceaa173-aa1d-4a05-9944-44a311170102))
  (fp_line (start 11 8.5) (end -11 8.5)
    (stroke (width 0.127) (type solid)) (layer "F.Fab") (tstamp 6d91d8b6-8e0b-4fa1-9f9c-9feac3dcc0ba))
  (fp_poly
    (pts
      (xy -10.7 -6.2)
      (xy -9.2 -6.2)
      (xy -9.2 -7)
      (xy -10.7 -7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp b79a35c1-c1ee-4f67-a0df-b9b039f98ab9))
  (fp_poly
    (pts
      (xy -10.7 -5.1)
      (xy -9.2 -5.1)
      (xy -9.2 -5.9)
      (xy -10.7 -5.9)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp f6304a59-91e6-4ed7-8a47-2253ec4cad33))
  (fp_poly
    (pts
      (xy -10.7 -4)
      (xy -9.2 -4)
      (xy -9.2 -4.8)
      (xy -10.7 -4.8)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp eb7b70cc-daa2-4e01-885b-54ec572c6981))
  (fp_poly
    (pts
      (xy -10.7 -2.9)
      (xy -9.2 -2.9)
      (xy -9.2 -3.7)
      (xy -10.7 -3.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp b69fbfe0-f5ed-48d9-928a-4907a0d83f0b))
  (fp_poly
    (pts
      (xy -10.7 -1.8)
      (xy -9.2 -1.8)
      (xy -9.2 -2.6)
      (xy -10.7 -2.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 69a2c4fd-5921-4138-975c-766a485fe35f))
  (fp_poly
    (pts
      (xy -10.7 -0.7)
      (xy -9.2 -0.7)
      (xy -9.2 -1.5)
      (xy -10.7 -1.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 10fa2cd2-576e-470c-a42f-61529b33b3d7))
  (fp_poly
    (pts
      (xy -10.7 0.4)
      (xy -9.2 0.4)
      (xy -9.2 -0.4)
      (xy -10.7 -0.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp cdeef713-f40a-4f7b-8f1d-5d1d8d34596a))
  (fp_poly
    (pts
      (xy -10.7 1.5)
      (xy -9.2 1.5)
      (xy -9.2 0.7)
      (xy -10.7 0.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 70f17dd4-fe95-47f7-bdd5-dd358f2f2a8d))
  (fp_poly
    (pts
      (xy -10.7 2.6)
      (xy -9.2 2.6)
      (xy -9.2 1.8)
      (xy -10.7 1.8)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 387c05b6-f2fd-4769-beea-5684305292a2))
  (fp_poly
    (pts
      (xy -10.7 3.7)
      (xy -9.2 3.7)
      (xy -9.2 2.9)
      (xy -10.7 2.9)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp c470e2b8-ae48-4a03-b6ee-3548c0fc34b6))
  (fp_poly
    (pts
      (xy -10.7 4.8)
      (xy -9.2 4.8)
      (xy -9.2 4)
      (xy -10.7 4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 69a01c54-8db4-4b95-bb7e-f875b724be49))
  (fp_poly
    (pts
      (xy -10.7 5.9)
      (xy -9.2 5.9)
      (xy -9.2 5.1)
      (xy -10.7 5.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp de5398f3-7c96-415e-84f9-ffb93827bc61))
  (fp_poly
    (pts
      (xy -10.7 7)
      (xy -9.2 7)
      (xy -9.2 6.2)
      (xy -10.7 6.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 400fcbae-4649-45d3-bcbb-cb053a64e9c0))
  (fp_poly
    (pts
      (xy -7.55 -6.7)
      (xy -6.75 -6.7)
      (xy -6.75 -8.2)
      (xy -7.55 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 94c6cdc6-3b79-4a78-9d62-aeb071660945))
  (fp_poly
    (pts
      (xy -7.55 8.2)
      (xy -6.75 8.2)
      (xy -6.75 6.7)
      (xy -7.55 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp a95d2b9a-4621-4f5e-8a09-462d0b35b9e6))
  (fp_poly
    (pts
      (xy -6.45 -6.7)
      (xy -5.65 -6.7)
      (xy -5.65 -8.2)
      (xy -6.45 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 4b6133ec-17aa-4c21-bfab-22773ce2fe3a))
  (fp_poly
    (pts
      (xy -6.45 8.2)
      (xy -5.65 8.2)
      (xy -5.65 6.7)
      (xy -6.45 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 89a77ec0-8c50-4857-b7c8-ac1ef4b4622b))
  (fp_poly
    (pts
      (xy -5.35 -6.7)
      (xy -4.55 -6.7)
      (xy -4.55 -8.2)
      (xy -5.35 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp da66fa2e-5f5d-4569-9711-c65d64f84722))
  (fp_poly
    (pts
      (xy -5.35 8.2)
      (xy -4.55 8.2)
      (xy -4.55 6.7)
      (xy -5.35 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 282c59a3-097c-41b1-a89e-a39ba1c7fbf3))
  (fp_poly
    (pts
      (xy -4.25 -6.7)
      (xy -3.45 -6.7)
      (xy -3.45 -8.2)
      (xy -4.25 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp c07f1032-f378-41d1-ab43-79cde474778e))
  (fp_poly
    (pts
      (xy -4.25 8.2)
      (xy -3.45 8.2)
      (xy -3.45 6.7)
      (xy -4.25 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 34fb971e-d771-4cf8-b4d9-db03f046236d))
  (fp_poly
    (pts
      (xy -3.15 -6.7)
      (xy -2.35 -6.7)
      (xy -2.35 -8.2)
      (xy -3.15 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 638cc697-026d-4d4b-83c0-336df57cd50a))
  (fp_poly
    (pts
      (xy -3.15 8.2)
      (xy -2.35 8.2)
      (xy -2.35 6.7)
      (xy -3.15 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp afa08a82-c7af-46db-bf0a-083510bd5a62))
  (fp_poly
    (pts
      (xy -2.05 -6.7)
      (xy -1.25 -6.7)
      (xy -1.25 -8.2)
      (xy -2.05 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 84e811c3-72d6-43b0-84e7-634b14b7453b))
  (fp_poly
    (pts
      (xy -2.05 8.2)
      (xy -1.25 8.2)
      (xy -1.25 6.7)
      (xy -2.05 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp ef851afa-76a0-4a6e-9f82-83541dc154e2))
  (fp_poly
    (pts
      (xy -0.95 -6.7)
      (xy -0.15 -6.7)
      (xy -0.15 -8.2)
      (xy -0.95 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 993d3f3b-1bcd-45e7-863b-6695c894e37a))
  (fp_poly
    (pts
      (xy -0.95 8.2)
      (xy -0.15 8.2)
      (xy -0.15 6.7)
      (xy -0.95 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 736519ba-0451-4f2c-bde2-01517509e633))
  (fp_poly
    (pts
      (xy 0.15 -6.7)
      (xy 0.95 -6.7)
      (xy 0.95 -8.2)
      (xy 0.15 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 3aae34d1-68ca-4ef7-9bcb-ceb28dadb3bf))
  (fp_poly
    (pts
      (xy 0.15 8.2)
      (xy 0.95 8.2)
      (xy 0.95 6.7)
      (xy 0.15 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 437534dc-4b0e-478b-944b-2493a9a98a48))
  (fp_poly
    (pts
      (xy 1.25 -6.7)
      (xy 2.05 -6.7)
      (xy 2.05 -8.2)
      (xy 1.25 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 1e6076f8-1bc8-4873-8b2d-0182e1b848f3))
  (fp_poly
    (pts
      (xy 1.25 8.2)
      (xy 2.05 8.2)
      (xy 2.05 6.7)
      (xy 1.25 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 11ea84b4-c09e-448b-9dd3-f7ea11640683))
  (fp_poly
    (pts
      (xy 2.35 -6.7)
      (xy 3.15 -6.7)
      (xy 3.15 -8.2)
      (xy 2.35 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 395fb8a3-27b7-4d47-a9df-53b86903c033))
  (fp_poly
    (pts
      (xy 2.35 8.2)
      (xy 3.15 8.2)
      (xy 3.15 6.7)
      (xy 2.35 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 290d06de-86c8-4e48-8b38-70343d02bcaa))
  (fp_poly
    (pts
      (xy 3.45 -6.7)
      (xy 4.25 -6.7)
      (xy 4.25 -8.2)
      (xy 3.45 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 952d45c2-5d01-4ae5-87d8-9d599a5c8c95))
  (fp_poly
    (pts
      (xy 3.45 8.2)
      (xy 4.25 8.2)
      (xy 4.25 6.7)
      (xy 3.45 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp c4807ac0-6d52-4ca3-825c-2164ecdf8804))
  (fp_poly
    (pts
      (xy 4.55 -6.7)
      (xy 5.35 -6.7)
      (xy 5.35 -8.2)
      (xy 4.55 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 79013f9a-0989-4cb3-a324-ba34ae4fcd7e))
  (fp_poly
    (pts
      (xy 4.55 8.2)
      (xy 5.35 8.2)
      (xy 5.35 6.7)
      (xy 4.55 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 18c3a836-ca33-4cf8-b4d0-86f0c6f8674d))
  (fp_poly
    (pts
      (xy 5.65 -6.7)
      (xy 6.45 -6.7)
      (xy 6.45 -8.2)
      (xy 5.65 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp cf3aedfa-bf12-48ab-b9b0-3794f310f3b9))
  (fp_poly
    (pts
      (xy 5.65 8.2)
      (xy 6.45 8.2)
      (xy 6.45 6.7)
      (xy 5.65 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 7f224dc3-f55b-4b39-b8de-ac144c5aa2a0))
  (fp_poly
    (pts
      (xy 6.75 -6.7)
      (xy 7.55 -6.7)
      (xy 7.55 -8.2)
      (xy 6.75 -8.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 9e778052-4f30-4fc3-b2b2-ef850b0441cd))
  (fp_poly
    (pts
      (xy 6.75 8.2)
      (xy 7.55 8.2)
      (xy 7.55 6.7)
      (xy 6.75 6.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 402ba746-e516-403a-8bad-e9dd8fe3f5cc))
  (fp_poly
    (pts
      (xy 9.2 -6.2)
      (xy 10.7 -6.2)
      (xy 10.7 -7)
      (xy 9.2 -7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp bdaad9ef-50b6-4ffa-9275-550cc0a1880d))
  (fp_poly
    (pts
      (xy 9.2 -5.1)
      (xy 10.7 -5.1)
      (xy 10.7 -5.9)
      (xy 9.2 -5.9)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 223d6c62-aa77-4317-9da9-4e0afa3abf29))
  (fp_poly
    (pts
      (xy 9.2 -4)
      (xy 10.7 -4)
      (xy 10.7 -4.8)
      (xy 9.2 -4.8)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 5be47fa8-edcf-4e2f-8289-5f1bb17aef10))
  (fp_poly
    (pts
      (xy 9.2 -2.9)
      (xy 10.7 -2.9)
      (xy 10.7 -3.7)
      (xy 9.2 -3.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp dbe4581f-ace1-4150-ba42-b89b1ec6c771))
  (fp_poly
    (pts
      (xy 9.2 -1.8)
      (xy 10.7 -1.8)
      (xy 10.7 -2.6)
      (xy 9.2 -2.6)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 50bf394a-68cc-419f-aebf-ba718fbcef6a))
  (fp_poly
    (pts
      (xy 9.2 -0.7)
      (xy 10.7 -0.7)
      (xy 10.7 -1.5)
      (xy 9.2 -1.5)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp a3758322-124b-4c28-8925-53de71b101c9))
  (fp_poly
    (pts
      (xy 9.2 0.4)
      (xy 10.7 0.4)
      (xy 10.7 -0.4)
      (xy 9.2 -0.4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 02967506-0c22-454c-ab4a-cf8cc1d09f65))
  (fp_poly
    (pts
      (xy 9.2 1.5)
      (xy 10.7 1.5)
      (xy 10.7 0.7)
      (xy 9.2 0.7)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp a2b703d3-edb5-4c6f-9d75-8edeed595fea))
  (fp_poly
    (pts
      (xy 9.2 2.6)
      (xy 10.7 2.6)
      (xy 10.7 1.8)
      (xy 9.2 1.8)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 48ca32a8-e0ce-4695-8f71-ef3def55d274))
  (fp_poly
    (pts
      (xy 9.2 3.7)
      (xy 10.7 3.7)
      (xy 10.7 2.9)
      (xy 9.2 2.9)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 6b09c350-5d05-4d08-9cde-875b5d39f861))
  (fp_poly
    (pts
      (xy 9.2 4.8)
      (xy 10.7 4.8)
      (xy 10.7 4)
      (xy 9.2 4)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp b133e7c4-632b-4382-97d7-d8daf15825f6))
  (fp_poly
    (pts
      (xy 9.2 5.9)
      (xy 10.7 5.9)
      (xy 10.7 5.1)
      (xy 9.2 5.1)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 73081e76-68ad-44a9-96fa-d8f738da4b37))
  (fp_poly
    (pts
      (xy 9.2 7)
      (xy 10.7 7)
      (xy 10.7 6.2)
      (xy 9.2 6.2)
    )
    (stroke (width 0) (type default)) (fill solid) (layer "F.Fab") (tstamp 37e7813d-9cbe-4f74-ac77-2755180e4fac))
  (pad "1" smd rect (at -7.15 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp e18bee2f-2905-49cd-8ad9-e518520f3c47)
  )
  (pad "2" smd rect (at -6.05 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp cb0e7eae-820f-48d6-b2ed-7109726cd1e9)
  )
  (pad "3" smd rect (at -4.95 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp e8e953c1-8cdc-458d-8835-526a2a746f16)
  )
  (pad "4" smd rect (at -3.85 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp efc0eac2-1905-436b-86f4-80edbfab228b)
  )
  (pad "5" smd rect (at -2.75 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 2a627576-6362-4a5b-85ea-043e4006a6a2)
  )
  (pad "6" smd rect (at -1.65 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 067f6ef7-2cc6-4a6b-82b6-8fdca72fe08c)
  )
  (pad "7" smd rect (at -0.55 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp c96c4c80-f645-4f89-82ed-04cbe38814bd)
  )
  (pad "8" smd rect (at 0.55 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 0497b16f-fb72-4c42-aec8-874a9ec6d2f2)
  )
  (pad "9" smd rect (at 1.65 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp ef938a8e-fab4-4df1-971f-5c2d67b92452)
  )
  (pad "10" smd rect (at 2.75 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 80ac36a9-c649-4e19-9c6c-c2b4a8bd77fb)
  )
  (pad "11" smd rect (at 3.85 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 5a09f6b8-2d76-4677-816c-409bb1607607)
  )
  (pad "12" smd rect (at 4.95 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 50ab85ab-ff82-4eb5-972d-d6fceb1ebc94)
  )
  (pad "13" smd rect (at 6.05 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp e54b6b1f-4aeb-41aa-8b9c-7f0732b05184)
  )
  (pad "14" smd rect (at 7.15 7.45 90) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 958c294d-c83d-4f30-b412-2f5dad8b94b9)
  )
  (pad "15" smd rect (at 9.95 6.6) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 3f0793c7-72f0-42bc-9754-93b311dfbd07)
  )
  (pad "16" smd rect (at 9.95 5.5) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 48b8535a-ac70-45eb-bd5e-505d4d779a1a)
  )
  (pad "17" smd rect (at 9.95 4.4) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b3b5bdd5-5b4e-4320-8360-dc56465c6491)
  )
  (pad "18" smd rect (at 9.95 3.3) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp e33dc1d5-86b6-4cc6-a78c-430b407a24b2)
  )
  (pad "19" smd rect (at 9.95 2.2) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 6e3b5a66-b954-4094-ad17-012c8179a478)
  )
  (pad "20" smd rect (at 9.95 1.1) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b420a8af-07d5-46ac-9593-9569c2348cb2)
  )
  (pad "21" smd rect (at 9.95 0) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp fbaf6e26-f2ec-426b-8317-a72a25b90e9a)
  )
  (pad "22" smd rect (at 9.95 -1.1) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 042bf702-3dbc-4a34-86e3-414edf4781e2)
  )
  (pad "23" smd rect (at 9.95 -2.2) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 17c9e513-cdda-4714-9344-40186cc14ad7)
  )
  (pad "24" smd rect (at 9.95 -3.3) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b9838be3-54f8-4c13-843a-924328bb9246)
  )
  (pad "25" smd rect (at 9.95 -4.4) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 52a76ebc-07bb-40a2-80dc-8c61ce962858)
  )
  (pad "26" smd rect (at 9.95 -5.5) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 0806bbdf-da76-428e-811e-8db46e1e94c8)
  )
  (pad "27" smd rect (at 9.95 -6.6) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 8ed35089-9f4d-4cb1-8514-aa2c2569ace8)
  )
  (pad "28" smd rect (at 7.15 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 9020ca77-a985-4741-ab0e-b5d2a13ad03d)
  )
  (pad "29" smd rect (at 6.05 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 5ff7084e-67fe-47ca-a38c-19c3bf9228cd)
  )
  (pad "30" smd rect (at 4.95 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 6d3633f7-835e-48ca-991c-617ea4d1b6fd)
  )
  (pad "31" smd rect (at 3.85 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 82e437c0-6b6a-4ce2-8fa3-2126c8cdab44)
  )
  (pad "32" smd rect (at 2.75 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp a738adc4-29bd-4d83-9db8-4564bebc8322)
  )
  (pad "33" smd rect (at 1.65 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b47c7247-086f-4917-a9cb-49d2c935c75d)
  )
  (pad "34" smd rect (at 0.55 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp c3103cd8-4039-438e-8872-217e325cff8b)
  )
  (pad "35" smd rect (at -0.55 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 30aa539d-479d-4a0d-8e67-7573ed10b22f)
  )
  (pad "36" smd rect (at -1.65 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 21171935-f87c-4e63-80de-cbf68e4f649b)
  )
  (pad "37" smd rect (at -2.75 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 71835b14-960b-4fcb-acf8-9982f41c276b)
  )
  (pad "38" smd rect (at -3.85 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp dd17ebd7-b5fd-4729-8139-9d74c1a863eb)
  )
  (pad "39" smd rect (at -4.95 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 9ac94279-94b1-43cd-8b8e-4d147e1a453c)
  )
  (pad "40" smd rect (at -6.05 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 133dc72f-6377-4acf-b591-8360d0546d96)
  )
  (pad "41" smd rect (at -7.15 -7.45 270) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp e0f4b4a2-30a5-4a75-8460-b1cf5f0e25f6)
  )
  (pad "42" smd rect (at -9.95 -6.6 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp ec7be4ea-787c-4870-a079-239df5cea68a)
  )
  (pad "43" smd rect (at -9.95 -5.5 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp f911eeda-57ff-4666-b155-937d0a8726c0)
  )
  (pad "44" smd rect (at -9.95 -4.4 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 2224491e-dd23-4257-9cab-887136cd31e2)
  )
  (pad "45" smd rect (at -9.95 -3.3 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp f1ecf8e1-585c-43e7-bad9-68205ade27b2)
  )
  (pad "46" smd rect (at -9.95 -2.2 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 7c997ad1-3570-42d7-b684-7af4ce865890)
  )
  (pad "47" smd rect (at -9.95 -1.1 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp efd8fe5e-8baf-4674-bc7b-45c4a48f1bc3)
  )
  (pad "48" smd rect (at -9.95 0 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 8f48b3b8-98de-4c18-b021-4e0c2fc23122)
  )
  (pad "49" smd rect (at -9.95 1.1 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp b78e203d-d676-425a-bbf8-1fa691166137)
  )
  (pad "50" smd rect (at -9.95 2.2 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 8f757137-e9da-4bb4-a7c0-61b8875d1ecf)
  )
  (pad "51" smd rect (at -9.95 3.3 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 37229542-9fd9-4a16-a19e-9315f7c45849)
  )
  (pad "52" smd rect (at -9.95 4.4 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 6511d233-8a39-4497-8769-e4184efcbf6b)
  )
  (pad "53" smd rect (at -9.95 5.5 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp a5c8f753-0c40-4a4f-ae1e-d01dac0f072e)
  )
  (pad "54" smd rect (at -9.95 6.6 180) (size 1.52 0.85) (layers "F.Cu" "F.Paste" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp c139ecb4-ee25-4c3b-a4c9-315e9eaa529b)
  )
  (pad "55" smd rect (at -1.05 1.05) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp e717ec43-813d-4573-bc04-4bf153f6f433)
  )
  (pad "56" smd rect (at -3.15 1.05) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 6abab209-4a23-4b00-afdf-4a334b766055)
  )
  (pad "57" smd rect (at -5.25 1.05) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 63286f02-9268-47bb-8889-c2188de2a123)
  )
  (pad "58" smd rect (at -7.35 1.05) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 25dca416-05bd-484f-b570-10ab7f5226dc)
  )
  (pad "59" smd rect (at -1.05 3.1) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 39702521-0d5c-4343-8fa6-f8d79007b85c)
  )
  (pad "60" smd rect (at -3.15 3.1) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 5d01b09f-0e81-41d2-9d12-2dae617ca92b)
  )
  (pad "61" smd rect (at -5.25 3.1) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 4859f4ce-8315-4614-91de-87a05fa8b140)
  )
  (pad "62" smd rect (at -7.35 3.1) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 72aba147-0c11-4ba1-92b1-c7f4cda0c675)
  )
  (pad "63" smd rect (at -1.05 5.15) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 69f3536d-e8b0-425a-adb7-a13ba0d7b468)
  )
  (pad "64" smd rect (at -3.15 5.15) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 17e10d4b-3674-4c2a-8f6f-643249ba4f49)
  )
  (pad "65" smd rect (at -5.25 5.15) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp cb0ae53c-fc9f-46ca-83bd-eddd913d7c33)
  )
  (pad "66" smd rect (at -7.35 5.15) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 26bf5bd0-8855-43ab-b6b2-b155c3117c24)
  )
  (pad "67" smd rect (at -1.05 -5.15) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 98aa3cd4-b0f0-4a38-ae96-637d53b056ae)
  )
  (pad "68" smd rect (at -3.15 -5.15) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 893ff9c3-9edc-4005-9faa-5a5a58e4a85f)
  )
  (pad "69" smd rect (at -5.25 -5.15) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp dfb89fa1-f4ef-4d7a-a9f1-5a1c7df24997)
  )
  (pad "70" smd rect (at -7.35 -5.15) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 04e0ae5c-01f0-44fe-93f4-ba3fdfb020bd)
  )
  (pad "71" smd rect (at -1.05 -3.1) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 601c21f5-ab8b-4f49-b035-0e771a26be5b)
  )
  (pad "72" smd rect (at -3.15 -3.1) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp ea282cac-7b5e-48cc-ac6d-85af3020fbeb)
  )
  (pad "73" smd rect (at -5.25 -3.1) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp bca187a5-ff48-4b2d-b0d1-95e0e2dad831)
  )
  (pad "74" smd rect (at -7.35 -3.1) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp e3a1550b-a66a-4574-87b1-e5928aff99ea)
  )
  (pad "75" smd rect (at -1.05 -1.05) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 2c760126-8db7-4adb-afa1-d062196e1c62)
  )
  (pad "76" smd rect (at -3.15 -1.05) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp a5856445-4537-4278-8b7f-f8f404fddd77)
  )
  (pad "77" smd rect (at -5.25 -1.05) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp d8c4604a-ffd3-451d-b628-d2264fed4c76)
  )
  (pad "78" smd rect (at -7.35 -1.05) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp fd5dadea-9942-4031-afa8-c02d0f29e5ed)
  )
  (pad "79" smd rect (at 1.05 -1.05 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 5171b7a3-87b5-41ac-8182-282d5133f967)
  )
  (pad "80" smd rect (at 3.15 -1.05 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 52bc0e91-08f8-4b9e-942b-73a46f2d785c)
  )
  (pad "81" smd rect (at 5.25 -1.05 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 6ef15610-d146-4c29-afc4-24c639e2aa1d)
  )
  (pad "82" smd rect (at 7.35 -1.05 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 1085a5b6-605d-4562-9715-7e2537668bab)
  )
  (pad "83" smd rect (at 1.05 -3.1 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 054ac840-f37b-4d5e-90a2-474e3d1be252)
  )
  (pad "84" smd rect (at 3.15 -3.1 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 8a03a987-56c9-4543-ac12-8c64bcee5c84)
  )
  (pad "85" smd rect (at 5.25 -3.1 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 26873654-7e10-4674-9601-f473f83e4d77)
  )
  (pad "86" smd rect (at 7.35 -3.1 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp d651fcbd-0940-4188-a036-1ff219fb457e)
  )
  (pad "87" smd rect (at 1.05 -5.15 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 468ba65f-7ece-41ed-b762-c10561ca1e13)
  )
  (pad "88" smd rect (at 3.15 -5.15 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp ad839f07-5974-4bff-b362-ca04ca5472a6)
  )
  (pad "89" smd rect (at 5.25 -5.15 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 72996512-1da2-4525-a106-95a8a3a1c3a5)
  )
  (pad "90" smd rect (at 7.35 -5.15 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 3921ba54-3421-4baf-a6ea-faacd787c292)
  )
  (pad "91" smd rect (at 1.05 5.15 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp e61d3977-0ea0-406d-9e3f-e3862c47616f)
  )
  (pad "92" smd rect (at 3.15 5.15 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 3f565e14-4dde-472e-98e5-2053526fa98a)
  )
  (pad "93" smd rect (at 5.25 5.15 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp f7a8267a-ea8c-4d7d-a080-11078e2d1ece)
  )
  (pad "94" smd rect (at 7.35 5.15 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 2ab64786-f47a-457b-bdcd-3161708f1e47)
  )
  (pad "95" smd rect (at 1.05 3.1 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp a21cd91d-5fd9-423e-9f5f-c5afc773220b)
  )
  (pad "96" smd rect (at 3.15 3.1 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 2f508d6d-ae4f-4884-a514-0c3b1bff5822)
  )
  (pad "97" smd rect (at 5.25 3.1 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp bcd231d1-0939-4e32-911a-8ad9f3737390)
  )
  (pad "98" smd rect (at 7.35 3.1 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 8b68b90d-c1a6-4b2d-90f6-3aff9ecd892d)
  )
  (pad "99" smd rect (at 1.05 1.05 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp e2cfa65f-4fc3-49a1-a2b9-02feff45cda2)
  )
  (pad "100" smd rect (at 3.15 1.05 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 87ea733f-3da7-4909-ad4c-e79d5163b18d)
  )
  (pad "101" smd rect (at 5.25 1.05 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 818637e2-5eed-43bc-b4cf-1a66736f1a3a)
  )
  (pad "102" smd rect (at 7.35 1.05 180) (size 1.1 1.1) (layers "F.Cu" "F.Mask")
    (solder_mask_margin 0.1016) (thermal_bridge_angle 0)
    (tstamp 9302cccb-9e1a-4baf-9630-24314e603ff2)
  )
)
