EESchema-LIBRARY Version  15/4/2008-12:52:19
#
#
# +12V
#
DEF +12V #PWR 0 0 N Y 1 F P
F0 "#PWR" 0 -50 20 H I C C
F1 "+12V" 0 100 30 H V C C
DRAW
P 3 0 1 0  0 0  0 40  0 40 N
C 0 60 20 0 1 0 N
X +12V 1 0 0 0 U 20 30 0 0 W N
ENDDRAW
ENDDEF
#
# -12V
#
DEF -12V #PWR 0 0 Y Y 1 F P
F0 "#PWR" 0 130 20 H I C C
F1 "-12V" 0 100 30 H V C C
DRAW
P 7 0 1 0  0 80  30 50  -20 50  -30 50  0 80  0 80  0 80 F
P 3 0 1 0  0 0  0 50  0 50 N
X -12V 1 0 0 0 U 20 20 0 0 W N
ENDDRAW
ENDDEF
#
# 74HCT04
#
DEF 74HCT04 U 0 30 Y Y 6 F N
F0 "U" 150 100 40 H V C C
F1 "74HCT04" 200 -100 40 H V C C
ALIAS 74HC14 74HC04 74LS14
DRAW
P 4 0 0 0  -150 150  -150 -150  150 0  -150 150 N
X VCC 14 -50 100 0 D 30 20 0 0 W N
X ~ 4 450 0 300 L 60 60 2 1 O I
X ~ 8 450 0 300 L 60 60 4 2 O
X ~ 10 450 0 300 L 60 60 5 2 O
X ~ 6 450 0 300 L 60 60 3 1 O I
X ~ 8 450 0 300 L 60 60 4 1 O I
X ~ 6 450 0 300 L 60 60 3 2 O
X ~ 10 450 0 300 L 60 60 5 1 O I
X ~ 4 450 0 300 L 60 60 2 2 O
X ~ 12 450 0 300 L 60 60 6 1 O I
X ~ 2 450 0 300 L 60 60 1 2 O
X ~ 12 450 0 300 L 60 60 6 2 O
X ~ 2 450 0 300 L 60 60 1 1 O I
X ~ 13 -450 0 300 R 60 60 6 1 I
X ~ 3 -450 0 300 R 60 60 2 2 I I
X ~ 11 -450 0 300 R 60 60 5 1 I
X ~ 5 -450 0 300 R 60 60 3 2 I I
X ~ 9 -450 0 300 R 60 60 4 1 I
X ~ 9 -450 0 300 R 60 60 4 2 I I
X ~ 5 -450 0 300 R 60 60 3 1 I
X ~ 11 -450 0 300 R 60 60 5 2 I I
X ~ 1 -450 0 300 R 60 60 1 1 I
X ~ 1 -450 0 300 R 60 60 1 2 I I
X ~ 3 -450 0 300 R 60 60 2 1 I
X ~ 13 -450 0 300 R 60 60 6 2 I I
X GND 7 -50 -100 0 U 30 20 0 0 W N
ENDDRAW
ENDDEF
#
# 7805
#
DEF 7805 U 0 30 N Y 1 F N
F0 "U" 150 -196 60 H V C C
F1 "7805" 0 200 60 H V C C
ALIAS LM7805 LM7812 78L05
DRAW
S -200 -150 200 150 0 1 0 N
X VO VO 400 50 200 L 40 40 1 1 w
X VI VI -400 50 200 R 40 40 1 1 I
X GND GND 0 -250 100 U 40 40 1 1 I
ENDDRAW
ENDDEF
#
# C
#
DEF C C 0 10 N Y 1 F N
F0 "C" 50 100 50 H V L C
F1 "C" 50 -100 50 H V L C
$FPLIST
 SM*
 C?
 C1-1
$ENDFPLIST
DRAW
P 2 0 1 8  -100 -30  100 -30 N
P 2 0 1 8  -100 30  100 30 N
X ~ 1 0 200 170 D 40 40 1 1 P
X ~ 2 0 -200 170 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# CONN_2
#
DEF CONN_2 P 0 40 Y N 1 F N
F0 "P" -50 0 40 V V C C
F1 "CONN_2" 50 0 40 V V C C
DRAW
S -100 150 100 -150 0 1 0 N
X PM 2 -350 -100 250 R 60 60 1 1 P I
X P1 1 -350 100 250 R 60 60 1 1 P I
ENDDRAW
ENDDEF
#
# CONN_2X2
#
DEF CONN_2X2 P 0 40 Y N 1 F N
F0 "P" 0 150 50 H V C C
F1 "CONN_2X2" 10 -130 40 H V C C
DRAW
S -100 100 100 -100 0 1 0 N
X 4 4 400 -50 300 L 60 60 1 1 P I
X 2 2 400 50 300 L 60 60 1 1 P I
X 3 3 -400 -50 300 R 60 60 1 1 P I
X 1 1 -400 50 300 R 60 60 1 1 P I
ENDDRAW
ENDDEF
#
# CONN_3
#
DEF CONN_3 K 0 40 Y N 1 F N
F0 "K" -50 0 50 V V C C
F1 "CONN_3" 50 0 40 V V C C
DRAW
S -100 150 100 -150 0 1 0 N
X P3 3 -350 -100 250 R 60 60 1 1 P I
X PM 2 -350 0 250 R 60 60 1 1 P I
X P1 1 -350 100 250 R 60 60 1 1 P I
ENDDRAW
ENDDEF
#
# CP
#
DEF CP C 0 10 N N 1 F N
F0 "C" 50 100 50 H V L C
F1 "CP" 50 -100 50 H V L C
ALIAS CAPAPOL
$FPLIST
 CP*
 SM*
$ENDFPLIST
DRAW
P 4 0 1 0  -50 50  -50 -20  50 -20  50 50 F
P 4 0 1 8  -100 50  -100 -50  100 -50  100 50 N
X ~ 1 0 200 150 D 40 40 1 1 P
X ~ 2 0 -200 150 U 40 40 1 1 P
ENDDRAW
ENDDEF
#
# CRYSTAL
#
DEF CRYSTAL X 0 40 N N 0 F N
F0 "X" 0 150 60 H V C C
F1 "CRYSTAL" 0 -150 60 H V C C
DRAW
P 5 0 1 12  -50 50  50 50  50 -50  -50 -50  -50 50 f
P 2 0 1 16  100 100  100 -100 N
P 2 0 1 16  -100 100  -100 -100 N
X 2 2 300 0 200 L 40 40 1 1 P
X 1 1 -300 0 200 R 40 40 1 1 P
ENDDRAW
ENDDEF
#
# DB25
#
DEF DB25 J 0 40 Y N 1 F N
F0 "J" 50 1350 70 H V C C
F1 "DB25" -50 -1350 70 H V C C
DRAW
P 2 0 1 0  -150 -500  20 -500 N
P 2 0 1 0  150 -1170  150 1170 N
C -70 800 30 0 1 0 N
P 2 0 1 0  -150 0  -100 0 N
P 2 0 1 0  -150 -300  20 -300 N
A 116 -1169 34 -657 -15 0 1 0 N 130 -1200 150 -1170
P 2 0 1 0  -150 200  -100 200 N
P 2 0 1 0  -150 300  20 300 N
P 2 0 1 0  -150 -100  20 -100 N
A -109 1270 41 1799 774 0 1 0 N -150 1270 -100 1310
P 2 0 1 0  -150 500  20 500 N
C -70 -200 30 0 1 0 N
P 2 0 1 0  -150 -1000  -100 -1000 N
C 50 300 30 0 1 0 N
C -70 0 30 0 1 0 N
P 2 0 1 0  130 1200  -100 1310 N
C 50 -500 30 0 1 0 N
C -70 -400 30 0 1 0 N
P 2 0 1 0  -150 -200  -100 -200 N
A -108 -1259 42 -1787 -788 0 1 0 N -150 -1260 -100 -1300
P 2 0 1 0  -150 -1260  -150 1270 N
P 2 0 1 0  -150 -1200  -100 -1200 N
P 2 0 1 0  -150 -800  -100 -800 N
P 2 0 1 0  -150 -400  -100 -400 N
C -70 -600 30 0 1 0 N
P 2 0 1 0  -150 -600  -100 -600 N
P 2 0 1 0  -150 900  20 900 N
C -70 600 30 0 1 0 N
P 2 0 1 0  -150 1000  -100 1000 N
C -70 1200 30 0 1 0 N
P 2 0 1 0  -150 1100  20 1100 N
C -70 1000 30 0 1 0 N
P 2 0 1 0  -150 -1100  20 -1100 N
C 50 -1100 30 0 1 0 N
P 2 0 1 0  -150 800  -100 800 N
P 2 0 1 0  -100 -1300  130 -1200 N
C -70 400 30 0 1 0 N
C 50 -900 30 0 1 0 N
P 2 0 1 0  -150 -700  20 -700 N
P 2 0 1 0  -150 700  20 700 N
C 50 -700 30 0 1 0 N
C 50 -300 30 0 1 0 N
P 2 0 1 0  -150 1200  -100 1200 N
C -70 -1200 30 0 1 0 N
P 2 0 1 0  -150 100  20 100 N
P 2 0 1 0  -150 -900  20 -900 N
P 2 0 1 0  -150 400  -100 400 N
C -70 -1000 30 0 1 0 N
P 2 0 1 0  -150 600  -100 600 N
A 117 1170 32 664 1 0 1 0 N 130 1199 149 1170
C 50 -100 30 0 1 0 N
C 50 100 30 0 1 0 N
C -70 -800 30 0 1 0 N
C 50 500 30 0 1 0 N
C 50 700 30 0 1 0 N
C 50 1100 30 0 1 0 N
C 50 900 30 0 1 0 N
C -70 200 30 0 1 0 N
X 1 1 -450 -1200 300 R 60 60 1 1 P
X P14 14 -450 -1100 300 R 60 60 1 1 P
X 2 2 -450 -1000 300 R 60 60 1 1 P
X P15 15 -450 -900 300 R 60 60 1 1 P
X 3 3 -450 -800 300 R 60 60 1 1 P
X P16 16 -450 -700 300 R 60 60 1 1 P
X 4 4 -450 -600 300 R 60 60 1 1 P
X P17 17 -450 -500 300 R 60 60 1 1 P
X 5 5 -450 -400 300 R 60 60 1 1 P
X P18 18 -450 -300 300 R 60 60 1 1 P
X 6 6 -450 -200 300 R 60 60 1 1 P
X P19 19 -450 -100 300 R 60 60 1 1 P
X 7 7 -450 0 300 R 60 60 1 1 P
X P20 20 -450 100 300 R 60 60 1 1 P
X 8 8 -450 200 300 R 60 60 1 1 P
X P21 21 -450 300 300 R 60 60 1 1 P
X 9 9 -450 400 300 R 60 60 1 1 P
X P22 22 -450 500 300 R 60 60 1 1 P
X 10 10 -450 600 300 R 60 60 1 1 P
X P23 23 -450 700 300 R 60 60 1 1 P
X 11 11 -450 800 300 R 60 60 1 1 P
X P24 24 -450 900 300 R 60 60 1 1 P
X 12 12 -450 1000 300 R 60 60 1 1 P
X P25 25 -450 1100 300 R 60 60 1 1 P
X 13 13 -450 1200 300 R 60 60 1 1 P
ENDDRAW
ENDDEF
#
# DB9
#
DEF DB9 J 0 40 Y N 1 F N
F0 "J" 0 550 70 H V C C
F1 "DB9" 0 -550 70 H V C C
DRAW
P 2 0 1 0  -150 300  20 300 N
P 2 0 1 0  -150 -460  -150 460 N
P 2 0 1 0  150 370  150 -390 N
P 2 0 1 0  -150 -400  -100 -400 N
P 2 0 1 0  -150 -300  20 -300 N
C 50 -100 30 0 1 0 N
P 2 0 1 0  -140 -470  -110 -490 N
C -70 0 30 0 1 0 N
P 2 0 1 0  -150 -459  -140 -470 N
C -70 200 30 0 1 0 N
P 2 0 1 0  -140 470  -100 490 N
C -70 400 30 0 1 0 N
P 2 0 1 0  150 -390  140 -409 N
C -70 -200 30 0 1 0 N
P 2 0 1 0  -110 -490  -50 -490 N
P 2 0 1 0  -150 -100  20 -100 N
P 2 0 1 0  -140 470  -150 460 N
P 2 0 1 0  -150 100  20 100 N
P 2 0 1 0  -150 400  -100 400 N
P 2 0 1 0  129 390  150 370 N
P 2 0 1 0  -100 490  -70 490 N
P 2 0 1 0  129 390  -70 490 N
C 50 100 30 0 1 0 N
C 50 300 30 0 1 0 N
C 50 -300 30 0 1 0 N
P 2 0 1 0  -150 -200  -100 -200 N
P 2 0 1 0  -150 0  -100 0 N
P 2 0 1 0  -150 200  -100 200 N
C -70 -400 30 0 1 0 N
P 2 0 1 0  140 -409  -50 -490 N
X 1 1 -450 -400 300 R 60 60 1 1 P
X P6 6 -450 -300 300 R 60 60 1 1 P
X 2 2 -450 -200 300 R 60 60 1 1 P
X P7 7 -450 -100 300 R 60 60 1 1 P
X 3 3 -450 0 300 R 60 60 1 1 P
X P8 8 -450 100 300 R 60 60 1 1 P
X 4 4 -450 200 300 R 60 60 1 1 P
X P9 9 -450 300 300 R 60 60 1 1 P
X 5 5 -450 400 300 R 60 60 1 1 P
ENDDRAW
ENDDEF
#
# DIODE
#
DEF DIODE D 0 40 N N 1 F N
F0 "D" 0 100 40 H V C C
F1 "DIODE" 0 -100 40 H V C C
$FPLIST
 D?
 S*
$ENDFPLIST
DRAW
P 3 0 1 0  -50 50  50 0  -50 -50 F
P 2 0 1 6  50 50  50 -50 N
X K 2 200 0 150 L 40 40 1 1 P
X A 1 -200 0 150 R 40 40 1 1 P
ENDDRAW
ENDDEF
#
# GND
#
DEF ~GND #PWR 0 0 Y Y 1 F P
F0 "#PWR" 0 0 30 H I C C
F1 "GND" 0 -70 30 H I C C
DRAW
P 4 0 1 4  -50 0  0 -50  50 0  -50 0 N
X GND 1 0 0 0 U 30 30 1 1 W N
ENDDRAW
ENDDEF
#
# LED
#
DEF LED D 0 40 Y N 1 F N
F0 "D" 0 100 50 H V C C
F1 "LED" 0 -100 50 H V C C
DRAW
P 3 0 1 0  -50 50  50 0  -50 -50 F
P 3 0 1 0  65 -40  110 -80  105 -55 N
P 3 0 1 0  80 -25  125 -65  120 -40 N
P 2 0 1 0  50 50  50 -50 N
X K 2 200 0 150 L 40 40 1 1 P
X A 1 -200 0 150 R 40 40 1 1 P
ENDDRAW
ENDDEF
#
# LM318N
#
DEF LM318N U 0 30 Y Y 1 F N
F0 "U" 100 300 60 H V C C
F1 "LM318N" 210 -250 60 H V C C
DRAW
P 2 0 1 0  200 200  0 100 N
P 2 0 1 0  200 100  100 50 N
P 2 0 1 0  200 -100  100 -50 N
P 2 0 1 0  -200 -200  -200 200 N
P 2 0 1 0  200 0  -200 -200 N
P 2 0 1 0  -200 200  200 0 N
X V+ 7 -100 400 250 D 60 30 1 1 W
X FOUT 5 500 -100 300 L 60 30 1 1 P
X OUT 6 500 0 300 L 60 30 1 1 O
X FIN1 8 500 100 300 L 60 30 1 1 P
X FIN2 1 500 200 300 L 60 30 1 1 P
X - 2 -500 -100 300 R 60 60 1 1 I
X + 3 -500 100 300 R 60 60 1 1 I
X V- 4 -100 -400 250 U 60 30 1 1 W
ENDDRAW
ENDDEF
#
# PWR_FLAG
#
DEF PWR_FLAG #FLG 0 0 N N 1 F P
F0 "#FLG" 0 270 30 H I C C
F1 "PWR_FLAG" 0 230 30 H V C C
DRAW
P 3 0 1 0  0 0  0 100  0 100 N
P 5 0 1 0  0 100  -100 150  0 200  100 150  0 100 N
X pwr 1 0 0 0 U 20 20 0 0 w
ENDDRAW
ENDDEF
#
# R
#
DEF R R 0 0 N Y 1 F N
F0 "R" 80 0 50 V V C C
F1 "R" 0 0 50 V V C C
$FPLIST
 R?
 SM0603
 SM0805
$ENDFPLIST
DRAW
S -40 150 40 -150 0 1 8 N
X ~ 1 0 250 100 D 60 60 1 1 P
X ~ 2 0 -250 100 U 60 60 1 1 P
ENDDRAW
ENDDEF
#
# RAM_32KO
#
DEF RAM_32KO U 0 40 Y Y 1 F N
F0 "U" 150 900 70 H V C C
F1 "RAM_32KO" 300 -1100 60 H V C C
DRAW
S -400 850 400 -1050 0 1 0 N
X VCC 28 0 850 0 D 60 60 0 0 W N
X D7 19 700 100 300 L 60 60 1 1 T
X D6 18 700 200 300 L 60 60 1 1 T
X D5 17 700 300 300 L 60 60 1 1 T
X D4 16 700 400 300 L 60 60 1 1 T
X D3 15 700 500 300 L 60 60 1 1 T
X D2 13 700 600 300 L 60 60 1 1 T
X D1 12 700 700 300 L 60 60 1 1 T
X D0 11 700 800 300 L 60 60 1 1 T
X WE 27 -700 -1000 300 R 60 60 1 1 I I
X OE 22 -700 -900 300 R 60 60 1 1 I I
X CS 20 -700 -800 300 R 60 60 1 1 I I
X A14 1 -700 -600 300 R 60 60 1 1 I
X A13 26 -700 -500 300 R 60 60 1 1 I
X A12 2 -700 -400 300 R 60 60 1 1 I
X A11 23 -700 -300 300 R 60 60 1 1 I
X A10 21 -700 -200 300 R 60 60 1 1 I
X A9 24 -700 -100 300 R 60 60 1 1 I
X A8 25 -700 0 300 R 60 60 1 1 I
X A7 3 -700 100 300 R 60 60 1 1 I
X A6 4 -700 200 300 R 60 60 1 1 I
X A5 5 -700 300 300 R 60 60 1 1 I
X A4 6 -700 400 300 R 60 60 1 1 I
X A3 7 -700 500 300 R 60 60 1 1 I
X A2 8 -700 600 300 R 60 60 1 1 I
X A1 9 -700 700 300 R 60 60 1 1 I
X A0 10 -700 800 300 R 60 60 1 1 I
X GND 14 0 -1050 0 U 60 60 0 0 W N
ENDDRAW
ENDDEF
#
# SW_PUSH
#
DEF SW_PUSH SW 0 40 N N 1 F N
F0 "SW" 150 110 50 H V C C
F1 "SW_PUSH" 0 -80 50 H V C C
DRAW
P 4 0 1 0  -40 60  -30 90  30 90  40 60 N
S -170 50 170 60 0 1 0 N
X 2 2 300 0 200 L 60 60 0 1 P I
X 1 1 -300 0 200 R 60 60 0 1 P I
ENDDRAW
ENDDEF
#
# TDA8702
#
DEF TDA8702 U 0 40 Y Y 1 F N
F0 "U" 200 800 60 H V L C
F1 "TDA8702" 200 -800 60 H V L C
DRAW
S -500 700 500 -700 0 1 0 N
X VCCD 13 -100 900 200 D 50 50 1 1 W
X VCCD 13 -100 900 300 D 50 50 1 2 W
X VCCA 16 100 900 300 D 50 50 1 2 W
X VCCA 16 100 900 200 D 50 50 1 1 W
X VOUT 14 800 -150 300 L 50 50 1 2 O
X VOUT 14 800 -150 300 L 50 50 1 1 O
X VOUT 15 800 200 300 L 50 50 1 2 O
X VOUT 15 800 200 300 L 50 50 1 1 O I
X Clock 5 -800 -600 300 R 50 50 1 1 I C
X Clock 5 -800 -600 300 R 50 50 1 2 I C
X VREF 1 -800 -400 300 R 50 50 1 1 B
X VREF 1 -800 -400 300 R 50 50 1 2 B
X DB7 7 -800 -100 300 R 50 50 1 2 I
X DB7 7 -800 -100 300 R 50 50 1 1 I
X DB6 8 -800 0 300 R 50 50 1 2 I
X DB6 8 -800 0 300 R 50 50 1 1 I
X DB6 8 -800 0 300 R 50 50 1 2 I
X DB6 8 -800 0 300 R 50 50 1 1 I
X DB5 9 -800 100 300 R 50 50 1 1 I
X DB5 9 -800 100 300 R 50 50 1 2 I
X DB4 10 -800 200 300 R 50 50 1 1 I
X DB4 10 -800 200 300 R 50 50 1 2 I
X DB3 4 -800 300 300 R 50 50 1 2 I
X DB3 4 -800 300 300 R 50 50 1 1 I
X DB2 3 -800 400 300 R 50 50 1 2 I
X DB2 3 -800 400 300 R 50 50 1 1 I
X DB1 11 -800 500 300 R 50 50 1 2 I
X DB1 11 -800 500 300 R 50 50 1 1 I
X DB0 12 -800 600 300 R 50 50 1 1 I
X DB0 12 -800 600 300 R 50 50 1 2 I
X DGND 6 -100 -900 300 U 50 50 1 2 W
X DGND 6 -100 -900 200 U 50 50 1 1 W
X AGND 2 100 -900 200 U 50 50 1 1 W
X AGND 2 100 -900 300 U 50 50 1 2 W
ENDDRAW
ENDDEF
#
# VCC
#
DEF VCC #PWR 0 0 Y Y 1 F P
F0 "#PWR" 0 100 30 H I C C
F1 "VCC" 0 100 30 H V C C
DRAW
P 3 0 1 4  0 0  0 30  0 30 N
C 0 50 20 0 1 4 N
X VCC 1 0 0 0 U 20 20 0 0 W N
ENDDRAW
ENDDEF
#
# XC95108PC84
#
DEF XC95108PC84 U 0 30 Y Y 1 F N
F0 "U" 550 2550 60 H V L C
F1 "XC95108PC84" 400 -1950 60 H V L C
DRAW
S -850 2450 850 -1850 0 1 0 N
X VCC 38 -300 2750 300 D 60 60 1 1 W
X VCC 73 -200 2750 300 D 60 60 1 1 W
X VCC 78 -100 2750 300 D 60 60 1 1 W
X VCCIO 22 100 2750 300 D 60 60 1 1 W
X VCCIO 64 200 2750 300 D 60 60 1 1 W
X P45 45 1150 -1750 300 L 60 60 1 1 B
X P46 46 1150 -1650 300 L 60 60 1 1 B
X P47 47 1150 -1550 300 L 60 60 1 1 B
X P48 48 1150 -1450 300 L 60 60 1 1 B
X P50 50 1150 -1350 300 L 60 60 1 1 B
X P51 51 1150 -1250 300 L 60 60 1 1 B
X P52 52 1150 -1150 300 L 60 60 1 1 B
X P53 53 1150 -1050 300 L 60 60 1 1 B
X P54 54 1150 -950 300 L 60 60 1 1 B
X P55 55 1150 -850 300 L 60 60 1 1 B
X P56 56 1150 -750 300 L 60 60 1 1 B
X P57 57 1150 -450 300 L 60 60 1 1 B
X P58 58 1150 -350 300 L 60 60 1 1 B
X P61 61 1150 -250 300 L 60 60 1 1 B
X P62 62 1150 -150 300 L 60 60 1 1 B
X P63 63 1150 -50 300 L 60 60 1 1 B
X P65 65 1150 50 300 L 60 60 1 1 B
X P66 66 1150 150 300 L 60 60 1 1 B
X P67 67 1150 250 300 L 60 60 1 1 B
X P68 68 1150 350 300 L 60 60 1 1 B
X P69 69 1150 450 300 L 60 60 1 1 B
X P70 70 1150 550 300 L 60 60 1 1 B
X P71 71 1150 750 300 L 60 60 1 1 B
X P72 72 1150 850 300 L 60 60 1 1 B
X I/O/GSR 74 1150 950 300 L 60 60 1 1 B
X P75 75 1150 1050 300 L 60 60 1 1 B
X I/O/GTS1 76 1150 1150 300 L 60 60 1 1 B
X I/O/GTS2 77 1150 1250 300 L 60 60 1 1 B
X P79 79 1150 1350 300 L 60 60 1 1 B
X P80 80 1150 1450 300 L 60 60 1 1 B
X P81 81 1150 1550 300 L 60 60 1 1 B
X P82 82 1150 1650 300 L 60 60 1 1 B
X P83 83 1150 1750 300 L 60 60 1 1 B
X P84 84 1150 1850 300 L 60 60 1 1 B
X P44 44 -1150 -1750 300 R 60 60 1 1 B
X P43 43 -1150 -1650 300 R 60 60 1 1 B
X P41 41 -1150 -1550 300 R 60 60 1 1 B
X P40 40 -1150 -1450 300 R 60 60 1 1 B
X P39 39 -1150 -1350 300 R 60 60 1 1 B
X P37 37 -1150 -1250 300 R 60 60 1 1 B
X P36 36 -1150 -1150 300 R 60 60 1 1 B
X P35 35 -1150 -1050 300 R 60 60 1 1 B
X P34 34 -1150 -950 300 R 60 60 1 1 B
X P33 33 -1150 -850 300 R 60 60 1 1 B
X P32 32 -1150 -750 300 R 60 60 1 1 B
X P31 31 -1150 -550 300 R 60 60 1 1 B
X P26 26 -1150 -450 300 R 60 60 1 1 B
X P25 25 -1150 -350 300 R 60 60 1 1 B
X P24 24 -1150 -250 300 R 60 60 1 1 B
X P23 23 -1150 -150 300 R 60 60 1 1 B
X P21 21 -1150 -50 300 R 60 60 1 1 B
X P20 20 -1150 50 300 R 60 60 1 1 B
X P19 19 -1150 150 300 R 60 60 1 1 B
X P18 18 -1150 250 300 R 60 60 1 1 B
X P17 17 -1150 350 300 R 60 60 1 1 B
X P15 15 -1150 450 300 R 60 60 1 1 B
X P14 14 -1150 550 300 R 60 60 1 1 B
X P13 13 -1150 750 300 R 60 60 1 1 B
X I/O/GCK3 12 -1150 850 300 R 60 60 1 1 B
X P11 11 -1150 950 300 R 60 60 1 1 B
X I/O/GCK2 10 -1150 1050 300 R 60 60 1 1 B
X I/O/GCK1 9 -1150 1150 300 R 60 60 1 1 B
X P7 7 -1150 1250 300 R 60 60 1 1 B
X P6 6 -1150 1350 300 R 60 60 1 1 B
X P5 5 -1150 1450 300 R 60 60 1 1 B
X P4 4 -1150 1550 300 R 60 60 1 1 B
X P3 3 -1150 1650 300 R 60 60 1 1 B
X P2 2 -1150 1750 300 R 60 60 1 1 B
X P1 1 -1150 1850 300 R 60 60 1 1 B
X TDO 59 -1150 2050 300 R 60 60 1 1 O
X TCK 30 -1150 2150 300 R 60 60 1 1 I
X TMS 29 -1150 2250 300 R 60 60 1 1 I
X TDI 28 -1150 2350 300 R 60 60 1 1 I
X GND 8 -250 -2150 300 U 60 60 1 1 W
X GND 16 -150 -2150 300 U 60 60 1 1 W
X GND 27 -50 -2150 300 U 60 60 1 1 W
X GND 42 50 -2150 300 U 60 60 1 1 W
X GND 49 150 -2150 300 U 60 60 1 1 W
X GND 60 250 -2150 300 U 60 60 1 1 W
ENDDRAW
ENDDEF
#
#EndLibrary
