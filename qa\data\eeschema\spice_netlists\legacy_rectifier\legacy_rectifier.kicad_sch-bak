(kicad_sch (version 20220104) (generator eeschema)

  (uuid 2382c413-3719-4909-9dfc-b83bba71274f)

  (paper "A4")

  (lib_symbols
    (symbol "power:PWR_FLAG" (power) (pin_numbers hide) (pin_names (offset 0) hide) (in_bom yes) (on_board yes)
      (property "Reference" "#FLG" (id 0) (at 0 1.905 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "PWR_FLAG" (id 1) (at 0 3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "~" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "power-flag" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Special symbol for telling ERC where power comes from" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "PWR_FLAG_0_0"
        (pin power_out line (at 0 0 90) (length 0)
          (name "pwr" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
      )
      (symbol "PWR_FLAG_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 1.27)
            (xy -1.016 1.905)
            (xy 0 2.54)
            (xy 1.016 1.905)
            (xy 0 1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
    )
    (symbol "rectifier_schlib:C" (pin_numbers hide) (pin_names (offset 0.254)) (in_bom yes) (on_board yes)
      (property "Reference" "C" (id 0) (at 0.635 2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Value" "C" (id 1) (at 0.635 -2.54 0)
        (effects (font (size 1.27 1.27)) (justify left))
      )
      (property "Footprint" "" (id 2) (at 0.9652 -3.81 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_fp_filters" "C? C_????_* C_???? SMD*_c Capacitor* Capacitors_ThroughHole:C_Radial_D10_L13_P5 Capacitors_SMD:C_0805 Capacitors_SMD:C_1206" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "C_0_1"
        (polyline
          (pts
            (xy -2.032 -0.762)
            (xy 2.032 -0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy -2.032 0.762)
            (xy 2.032 0.762)
          )
          (stroke (width 0.508) (type default))
          (fill (type none))
        )
      )
      (symbol "C_1_1"
        (pin passive line (at 0 3.81 270) (length 2.794)
          (name "~" (effects (font (size 1.016 1.016))))
          (number "1" (effects (font (size 1.016 1.016))))
        )
        (pin passive line (at 0 -3.81 90) (length 2.794)
          (name "~" (effects (font (size 1.016 1.016))))
          (number "2" (effects (font (size 1.016 1.016))))
        )
      )
    )
    (symbol "rectifier_schlib:D" (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "D" (id 0) (at 0 2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "D" (id 1) (at 0 -2.54 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_fp_filters" "Diode_* D-Pak_TO252AA *SingleDiode *_Diode_* *SingleDiode*" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "D_0_1"
        (polyline
          (pts
            (xy -1.27 1.27)
            (xy -1.27 -1.27)
          )
          (stroke (width 0.1524) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 1.27 1.27)
            (xy -1.27 0)
            (xy 1.27 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
      )
      (symbol "D_1_1"
        (pin passive line (at -3.81 0 0) (length 2.54)
          (name "K" (effects (font (size 0.508 0.508))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
        (pin passive line (at 3.81 0 180) (length 2.54)
          (name "A" (effects (font (size 0.508 0.508))))
          (number "2" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "rectifier_schlib:GND" (power) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "#PWR" (id 0) (at 0 -3.81 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Value" "GND" (id 1) (at 0 -3.1242 0)
        (effects (font (size 0.762 0.762)))
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.524 1.524)))
      )
      (symbol "GND_0_1"
        (polyline
          (pts
            (xy 0 0)
            (xy 0 -1.27)
            (xy 1.27 -1.27)
            (xy 0 -2.54)
            (xy -1.27 -1.27)
            (xy 0 -1.27)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "GND_1_1"
        (pin power_in line (at 0 0 270) (length 0) hide
          (name "GND" (effects (font (size 0.762 0.762))))
          (number "1" (effects (font (size 0.508 0.508))))
        )
      )
    )
    (symbol "rectifier_schlib:R" (pin_numbers hide) (pin_names (offset 0)) (in_bom yes) (on_board yes)
      (property "Reference" "R" (id 0) (at 2.032 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "R" (id 1) (at 0 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "" (id 2) (at -1.778 0 90)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "ki_fp_filters" "R_* Resistor_*" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "R_0_1"
        (rectangle (start -1.016 -2.54) (end 1.016 2.54)
          (stroke (width 0.254) (type default))
          (fill (type none))
        )
      )
      (symbol "R_1_1"
        (pin passive line (at 0 3.81 270) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -3.81 90) (length 1.27)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
    (symbol "rectifier_schlib:VSOURCE" (pin_names (offset 1.016)) (in_bom yes) (on_board yes)
      (property "Reference" "V" (id 0) (at 5.08 5.08 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "VSOURCE" (id 1) (at 6.35 2.54 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Footprint" "" (id 2) (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Datasheet" "" (id 3) (at 0 0 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Fieldname" "Value" (id 4) (at 0 0 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (property "Spice_Primitive" "V" (id 5) (at 0 0 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (property "Spice_Node_Sequence" "1 2" (id 6) (at -7.62 5.08 0)
        (effects (font (size 1.524 1.524)) hide)
      )
      (symbol "VSOURCE_0_1"
        (polyline
          (pts
            (xy 0 -1.905)
            (xy 0 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type none))
        )
        (polyline
          (pts
            (xy 0 1.905)
            (xy -0.635 0.635)
            (xy 0.635 0.635)
            (xy 0 1.905)
          )
          (stroke (width 0) (type default))
          (fill (type outline))
        )
        (circle (center 0 0) (radius 2.54)
          (stroke (width 0) (type default))
          (fill (type none))
        )
      )
      (symbol "VSOURCE_1_1"
        (pin input line (at 0 5.08 270) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin input line (at 0 -5.08 90) (length 2.54)
          (name "~" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )

  (junction (at 137.16 93.98) (diameter 1.016) (color 0 0 0 0)
    (uuid 1e1b062d-fad0-427c-a622-c5b8a80b5268)
  )
  (junction (at 111.76 109.22) (diameter 1.016) (color 0 0 0 0)
    (uuid 3b838d52-596d-4e4d-a6ac-e4c8e7621137)
  )
  (junction (at 123.19 109.22) (diameter 1.016) (color 0 0 0 0)
    (uuid cbdcaa78-3bbc-413f-91bf-2709119373ce)
  )
  (junction (at 137.16 109.22) (diameter 1.016) (color 0 0 0 0)
    (uuid d8603679-3e7b-4337-8dbc-1827f5f54d8a)
  )

  (wire (pts (xy 133.35 93.98) (xy 137.16 93.98))
    (stroke (width 0) (type solid))
    (uuid 07ab9493-5de4-4ff4-90d8-8ba5a553168e)
  )
  (wire (pts (xy 146.05 93.98) (xy 146.05 97.79))
    (stroke (width 0) (type solid))
    (uuid 20848edf-3aaa-41cb-8373-cf3e968c8757)
  )
  (wire (pts (xy 146.05 109.22) (xy 146.05 105.41))
    (stroke (width 0) (type solid))
    (uuid 2207d2b7-bc12-4982-8cfb-7b37bb776130)
  )
  (wire (pts (xy 111.76 109.22) (xy 123.19 109.22))
    (stroke (width 0) (type solid))
    (uuid 268ad5c7-a8e0-4402-a4c8-7931e9c0ece4)
  )
  (wire (pts (xy 123.19 109.22) (xy 137.16 109.22))
    (stroke (width 0) (type solid))
    (uuid 34f3d2e2-8462-4fdb-a2ac-7c0b1c2ca19d)
  )
  (wire (pts (xy 137.16 109.22) (xy 146.05 109.22))
    (stroke (width 0) (type solid))
    (uuid 56b1d650-b6db-40fc-89ac-235e374651ff)
  )
  (wire (pts (xy 111.76 109.22) (xy 111.76 107.95))
    (stroke (width 0) (type solid))
    (uuid 61ad572b-bbce-46de-8b95-6d669a3972a4)
  )
  (wire (pts (xy 137.16 93.98) (xy 146.05 93.98))
    (stroke (width 0) (type solid))
    (uuid 83ef7177-a51c-4eb1-8fb6-2f8f3cbe438f)
  )
  (wire (pts (xy 137.16 97.79) (xy 137.16 93.98))
    (stroke (width 0) (type solid))
    (uuid 89891bdd-bef2-43f1-a6a1-c994dbe853f7)
  )
  (wire (pts (xy 111.76 110.49) (xy 111.76 109.22))
    (stroke (width 0) (type solid))
    (uuid 8b873279-d1d9-4581-a487-d093a5f25f9d)
  )
  (wire (pts (xy 137.16 109.22) (xy 137.16 105.41))
    (stroke (width 0) (type solid))
    (uuid c67361f2-c274-4eeb-a601-a8a87b98198f)
  )
  (wire (pts (xy 123.19 109.22) (xy 123.19 110.49))
    (stroke (width 0) (type solid))
    (uuid e33b77ef-ccda-42f4-8363-f91dfba35f2d)
  )
  (wire (pts (xy 111.76 97.79) (xy 111.76 93.98))
    (stroke (width 0) (type solid))
    (uuid e7bec0fd-e2ed-4647-bfd3-db53dfb47684)
  )
  (wire (pts (xy 121.92 93.98) (xy 125.73 93.98))
    (stroke (width 0) (type solid))
    (uuid f72d8a2b-f8a5-4d0b-b1b9-22d72e34cff0)
  )
  (wire (pts (xy 111.76 93.98) (xy 114.3 93.98))
    (stroke (width 0) (type solid))
    (uuid fff0c783-b4be-416f-b7af-fc7db21b4306)
  )

  (text "*.ac dec 10 1 1Meg\n" (at 109.22 127 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid 31be42c9-a83f-44a4-aa95-efe30553d4bb)
  )
  (text ".tran 1u 10m\n" (at 109.22 124.46 0)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid 725dced4-bed6-43ad-ba3b-46e6299827e1)
  )

  (label "rect_out" (at 146.05 93.98 0) (fields_autoplaced)
    (effects (font (size 1.524 1.524)) (justify left bottom))
    (uuid 6e768832-e739-40a3-b1cf-e04b6064ba05)
  )
  (label "signal_in" (at 111.76 93.98 0) (fields_autoplaced)
    (effects (font (size 1.524 1.524)) (justify right bottom))
    (uuid 9fe48f3c-97dc-4bd8-9dda-1f57ffd91008)
  )

  (symbol (lib_id "rectifier_schlib:VSOURCE") (at 111.76 102.87 180) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-000057336052)
    (property "Reference" "V1" (id 0) (at 108.5088 104.0384 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "SINE(0 1.5 1k 0 0 0 0)" (id 1) (at 108.5088 101.727 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 111.76 102.87 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 111.76 102.87 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (id 4) (at 111.76 102.87 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "V" (id 5) (at 111.76 102.87 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Node_Sequence" "1 2" (id 6) (at 119.38 107.95 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid aceef1a1-5447-491e-8c51-e4c2fefde62d))
    (pin "2" (uuid 295a3a80-e3ec-4047-94bb-44838c8835f0))
  )

  (symbol (lib_id "rectifier_schlib:GND") (at 111.76 110.49 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000573360d3)
    (property "Reference" "#PWR01" (id 0) (at 111.76 116.84 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "GND" (id 1) (at 111.887 114.8842 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 111.76 110.49 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 111.76 110.49 0)
      (effects (font (size 1.27 1.27)))
    )
    (pin "1" (uuid e661509b-2354-4ad2-8b1b-69de4a26a842))
  )

  (symbol (lib_id "rectifier_schlib:R") (at 118.11 93.98 270) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000573360f5)
    (property "Reference" "R1" (id 0) (at 118.11 88.7222 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "1k" (id 1) (at 118.11 91.0336 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 118.11 92.202 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 118.11 93.98 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (id 4) (at 118.11 93.98 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Node_Sequence" "1 2" (id 5) (at 118.11 93.98 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "R" (id 6) (at 118.11 93.98 90)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid ffba358e-71d4-44b9-b9db-81db4067656e))
    (pin "2" (uuid d87a6c4f-baef-46a7-9b5d-c27db04b6a1c))
  )

  (symbol (lib_id "rectifier_schlib:D") (at 129.54 93.98 180) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000573361b8)
    (property "Reference" "D1" (id 0) (at 129.54 88.519 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Value" "1N4148" (id 1) (at 129.54 90.8304 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 129.54 93.98 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 129.54 93.98 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (id 4) (at 129.54 93.98 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "D" (id 5) (at 129.54 93.98 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Node_Sequence" "2 1" (id 6) (at 129.54 93.98 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Model" "1N4148" (id 7) (at 129.54 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Netlist_Enabled" "Y" (id 8) (at 129.54 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Spice_Lib_File" "diode.mod" (id 9) (at 129.54 93.98 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 3282902d-f9c3-4e48-96be-8748796bdce2))
    (pin "2" (uuid aeebb790-4cf0-490d-8f08-cd28027978ed))
  )

  (symbol (lib_id "rectifier_schlib:C") (at 137.16 101.6 0) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-00005733628f)
    (property "Reference" "C1" (id 0) (at 140.081 100.4316 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "100n" (id 1) (at 140.081 102.743 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "" (id 2) (at 138.1252 105.41 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 137.16 101.6 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (id 4) (at 137.16 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "C" (id 5) (at 137.16 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Node_Sequence" "1 2" (id 6) (at 137.16 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 8927c4f6-bfd9-437a-8ad5-25802d51ce9d))
    (pin "2" (uuid 92fca1af-ccee-47d4-8c4d-392ffd77d66b))
  )

  (symbol (lib_id "rectifier_schlib:R") (at 146.05 101.6 180) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 00000000-0000-0000-0000-0000573362f7)
    (property "Reference" "R2" (id 0) (at 147.828 100.4316 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Value" "100k" (id 1) (at 147.828 102.743 0)
      (effects (font (size 1.27 1.27)) (justify right))
    )
    (property "Footprint" "" (id 2) (at 147.828 101.6 90)
      (effects (font (size 1.27 1.27)))
    )
    (property "Datasheet" "" (id 3) (at 146.05 101.6 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Fieldname" "Value" (id 4) (at 146.05 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Node_Sequence" "1 2" (id 5) (at 146.05 101.6 0)
      (effects (font (size 1.524 1.524)) hide)
    )
    (property "Spice_Primitive" "R" (id 6) (at 146.05 101.6 90)
      (effects (font (size 1.524 1.524)) hide)
    )
    (pin "1" (uuid 135c45f3-f802-4e57-9f37-b45cf0926449))
    (pin "2" (uuid fec36f3a-ec93-4b2c-84a0-fb7b5ab96f03))
  )

  (symbol (lib_id "power:PWR_FLAG") (at 123.19 110.49 180) (unit 1)
    (in_bom yes) (on_board yes)
    (uuid 3cf2b621-b5e0-4e3b-ad75-1de14d8fb5dc)
    (property "Reference" "#FLG0101" (id 0) (at 123.19 112.395 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Value" "PWR_FLAG" (id 1) (at 123.19 114.8144 0)
      (effects (font (size 1.27 1.27)))
    )
    (property "Footprint" "" (id 2) (at 123.19 110.49 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "~" (id 3) (at 123.19 110.49 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (pin "1" (uuid 87870f0c-d5ce-4211-ab10-80d3de7d5711))
  )

  (sheet_instances
    (path "/" (page "1"))
  )

  (symbol_instances
    (path "/3cf2b621-b5e0-4e3b-ad75-1de14d8fb5dc"
      (reference "#FLG0101") (unit 1) (value "PWR_FLAG") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000573360d3"
      (reference "#PWR01") (unit 1) (value "GND") (footprint "")
    )
    (path "/00000000-0000-0000-0000-00005733628f"
      (reference "C1") (unit 1) (value "100n") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000573361b8"
      (reference "D1") (unit 1) (value "1N4148") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000573360f5"
      (reference "R1") (unit 1) (value "1k") (footprint "")
    )
    (path "/00000000-0000-0000-0000-0000573362f7"
      (reference "R2") (unit 1) (value "100k") (footprint "")
    )
    (path "/00000000-0000-0000-0000-000057336052"
      (reference "V1") (unit 1) (value "SINE(0 1.5 1k 0 0 0 0)") (footprint "")
    )
  )
)
