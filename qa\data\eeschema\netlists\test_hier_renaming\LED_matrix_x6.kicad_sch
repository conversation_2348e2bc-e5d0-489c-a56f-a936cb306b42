(kicad_sch (version 20201015) (generator eeschema)

  (paper "A3")

  (lib_symbols
    (symbol "Display_Graphic:788xHG" (in_bom yes) (on_board yes)
      (property "Reference" "DS" (id 0) (at -13.97 16.51 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Value" "788xHG" (id 1) (at -13.97 -19.05 0)
        (effects (font (size 1.27 1.27)))
      )
      (property "Footprint" "Display:788xHG" (id 2) (at 50.8 -10.16 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "Datasheet" "" (id 3) (at 50.8 -10.16 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_keywords" "led matrix bi-color red green" (id 4) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (property "ki_description" "Bi-color red and green 8x8 LED matrix 20x20 mm" (id 5) (at 0 0 0)
        (effects (font (size 1.27 1.27)) hide)
      )
      (symbol "788xHG_0_1"
        (circle (center -7.62 5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -7.62 7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -5.08 5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -5.08 7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -2.54 5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -2.54 7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 0 5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 0 7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 2.54 5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 2.54 7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 5.08 5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 5.08 7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 7.62 5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 7.62 7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 10.16 5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 10.16 7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (rectangle (start -15.24 15.24) (end 15.24 -17.78)
          (stroke (width 0.254)) (fill (type background))
        )
      )
      (symbol "788xHG_1_1"
        (circle (center -7.62 -10.16) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -7.62 -7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -7.62 -5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -7.62 -2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -7.62 0) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -7.62 2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -5.08 -10.16) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -5.08 -7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -5.08 -5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -5.08 -2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -5.08 0) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -5.08 2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -2.54 -10.16) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -2.54 -7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -2.54 -5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -2.54 -2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -2.54 0) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center -2.54 2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 0 -10.16) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 0 -7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 0 -5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 0 -2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 0 0) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 0 2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 2.54 -10.16) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 2.54 -7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 2.54 -5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 2.54 -2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 2.54 0) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 2.54 2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 5.08 -10.16) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 5.08 -7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 5.08 -5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 5.08 -2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 5.08 0) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 5.08 2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 7.62 -10.16) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 7.62 -7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 7.62 -5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 7.62 -2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 7.62 0) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 7.62 2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 10.16 -10.16) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 10.16 -7.62) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 10.16 -5.08) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 10.16 -2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 10.16 0) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (circle (center 10.16 2.54) (radius 0.635) (stroke (width 0)) (fill (type outline)))
        (pin passive line (at -7.62 -20.32 90) (length 2.54)
          (name "COL1G" (effects (font (size 1.27 1.27))))
          (number "1" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -17.78 -7.62 0) (length 2.54)
          (name "ROW7" (effects (font (size 1.27 1.27))))
          (number "10" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -17.78 -5.08 0) (length 2.54)
          (name "ROW6" (effects (font (size 1.27 1.27))))
          (number "11" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -17.78 -2.54 0) (length 2.54)
          (name "ROW5" (effects (font (size 1.27 1.27))))
          (number "12" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -17.78 0 0) (length 2.54)
          (name "ROW4" (effects (font (size 1.27 1.27))))
          (number "13" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -17.78 2.54 0) (length 2.54)
          (name "ROW3" (effects (font (size 1.27 1.27))))
          (number "14" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -17.78 5.08 0) (length 2.54)
          (name "ROW2" (effects (font (size 1.27 1.27))))
          (number "15" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -17.78 7.62 0) (length 2.54)
          (name "ROW1" (effects (font (size 1.27 1.27))))
          (number "16" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -7.62 17.78 270) (length 2.54)
          (name "COL1R" (effects (font (size 1.27 1.27))))
          (number "17" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 17.78 270) (length 2.54)
          (name "COL2R" (effects (font (size 1.27 1.27))))
          (number "18" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -2.54 17.78 270) (length 2.54)
          (name "COL3R" (effects (font (size 1.27 1.27))))
          (number "19" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -5.08 -20.32 90) (length 2.54)
          (name "COL2G" (effects (font (size 1.27 1.27))))
          (number "2" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 17.78 270) (length 2.54)
          (name "COL4R" (effects (font (size 1.27 1.27))))
          (number "20" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 2.54 17.78 270) (length 2.54)
          (name "COL5R" (effects (font (size 1.27 1.27))))
          (number "21" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 17.78 270) (length 2.54)
          (name "COL6R" (effects (font (size 1.27 1.27))))
          (number "22" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 7.62 17.78 270) (length 2.54)
          (name "COL7R" (effects (font (size 1.27 1.27))))
          (number "23" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 10.16 17.78 270) (length 2.54)
          (name "COL8R" (effects (font (size 1.27 1.27))))
          (number "24" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -2.54 -20.32 90) (length 2.54)
          (name "COL3G" (effects (font (size 1.27 1.27))))
          (number "3" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 0 -20.32 90) (length 2.54)
          (name "COL4G" (effects (font (size 1.27 1.27))))
          (number "4" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 2.54 -20.32 90) (length 2.54)
          (name "COL5G" (effects (font (size 1.27 1.27))))
          (number "5" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 5.08 -20.32 90) (length 2.54)
          (name "COL6G" (effects (font (size 1.27 1.27))))
          (number "6" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 7.62 -20.32 90) (length 2.54)
          (name "COL7G" (effects (font (size 1.27 1.27))))
          (number "7" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at 10.16 -20.32 90) (length 2.54)
          (name "COL8G" (effects (font (size 1.27 1.27))))
          (number "8" (effects (font (size 1.27 1.27))))
        )
        (pin passive line (at -17.78 -10.16 0) (length 2.54)
          (name "ROW8" (effects (font (size 1.27 1.27))))
          (number "9" (effects (font (size 1.27 1.27))))
        )
      )
    )
  )


  (bus_entry (at 175.26 55.88) (size 2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 175.26 58.42) (size 2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 175.26 60.96) (size 2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 175.26 63.5) (size 2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 175.26 66.04) (size 2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 175.26 68.58) (size 2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 175.26 71.12) (size 2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 175.26 73.66) (size 2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 370.84 146.05) (size -2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 370.84 148.59) (size -2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 370.84 151.13) (size -2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 370.84 153.67) (size -2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 370.84 156.21) (size -2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 370.84 158.75) (size -2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 370.84 161.29) (size -2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )
  (bus_entry (at 370.84 163.83) (size -2.54 -2.54)
    (stroke (width 0.1524) (type solid) (color 0 0 0 0))
  )

  (wire (pts (xy 185.42 53.34) (xy 177.8 53.34))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 185.42 55.88) (xy 177.8 55.88))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 185.42 58.42) (xy 177.8 58.42))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 185.42 60.96) (xy 177.8 60.96))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 185.42 63.5) (xy 177.8 63.5))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 185.42 66.04) (xy 177.8 66.04))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 185.42 68.58) (xy 177.8 68.58))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 185.42 71.12) (xy 177.8 71.12))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 370.84 146.05) (xy 382.27 146.05))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 370.84 148.59) (xy 382.27 148.59))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 370.84 151.13) (xy 382.27 151.13))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 370.84 153.67) (xy 382.27 153.67))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 370.84 156.21) (xy 382.27 156.21))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 370.84 158.75) (xy 382.27 158.75))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 370.84 161.29) (xy 382.27 161.29))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (wire (pts (xy 370.84 163.83) (xy 382.27 163.83))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 175.26 55.88) (xy 175.26 58.42))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 175.26 58.42) (xy 175.26 60.96))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 175.26 60.96) (xy 175.26 63.5))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 175.26 63.5) (xy 175.26 66.04))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 175.26 66.04) (xy 175.26 68.58))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 175.26 68.58) (xy 175.26 71.12))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 175.26 71.12) (xy 175.26 73.66))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 175.26 73.66) (xy 175.26 142.24))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 175.26 142.24) (xy 368.3 142.24))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 368.3 142.24) (xy 368.3 143.51))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 368.3 143.51) (xy 368.3 146.05))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 368.3 146.05) (xy 368.3 148.59))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 368.3 148.59) (xy 368.3 151.13))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 368.3 151.13) (xy 368.3 153.67))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 368.3 153.67) (xy 368.3 156.21))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 368.3 156.21) (xy 368.3 158.75))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )
  (bus (pts (xy 368.3 158.75) (xy 368.3 161.29))
    (stroke (width 0) (type solid) (color 0 0 0 0))
  )

  (label "ROW0" (at 179.07 53.34 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "ROW1" (at 179.07 55.88 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "ROW2" (at 179.07 58.42 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "ROW3" (at 179.07 60.96 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "ROW4" (at 179.07 63.5 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "ROW5" (at 179.07 66.04 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "ROW6" (at 179.07 68.58 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "ROW7" (at 179.07 71.12 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "ROW[7..0]" (at 193.04 142.24 0)
    (effects (font (size 1.27 1.27)) (justify left bottom))
  )
  (label "ROW0" (at 381 146.05 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
  )
  (label "ROW1" (at 381 148.59 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
  )
  (label "ROW2" (at 381 151.13 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
  )
  (label "ROW3" (at 381 153.67 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
  )
  (label "ROW4" (at 381 156.21 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
  )
  (label "ROW5" (at 381 158.75 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
  )
  (label "ROW6" (at 381 161.29 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
  )
  (label "ROW7" (at 381 163.83 180)
    (effects (font (size 1.27 1.27)) (justify right bottom))
  )

  (hierarchical_label "ROW0" (shape passive) (at 382.27 146.05 0)
    (effects (font (size 1.27 1.27)) (justify left))
  )
  (hierarchical_label "ROW1" (shape passive) (at 382.27 148.59 0)
    (effects (font (size 1.27 1.27)) (justify left))
  )
  (hierarchical_label "ROW2" (shape passive) (at 382.27 151.13 0)
    (effects (font (size 1.27 1.27)) (justify left))
  )
  (hierarchical_label "ROW3" (shape passive) (at 382.27 153.67 0)
    (effects (font (size 1.27 1.27)) (justify left))
  )
  (hierarchical_label "ROW4" (shape passive) (at 382.27 156.21 0)
    (effects (font (size 1.27 1.27)) (justify left))
  )
  (hierarchical_label "ROW5" (shape passive) (at 382.27 158.75 0)
    (effects (font (size 1.27 1.27)) (justify left))
  )
  (hierarchical_label "ROW6" (shape passive) (at 382.27 161.29 0)
    (effects (font (size 1.27 1.27)) (justify left))
  )
  (hierarchical_label "ROW7" (shape passive) (at 382.27 163.83 0)
    (effects (font (size 1.27 1.27)) (justify left))
  )

  (symbol (lib_id "Display_Graphic:788xHG") (at 203.2 60.96 0)
    (in_bom yes) (on_board yes)
    (uuid "e63356eb-7345-434d-8464-3fa9dc47789a")
    (property "Reference" "DS2" (id 0) (at 187.96 44.45 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Value" "788xHG" (id 1) (at 185.42 80.01 0)
      (effects (font (size 1.27 1.27)) (justify left))
    )
    (property "Footprint" "Display:788xHG" (id 2) (at 254 71.12 0)
      (effects (font (size 1.27 1.27)) hide)
    )
    (property "Datasheet" "" (id 3) (at 254 71.12 0)
      (effects (font (size 1.27 1.27)) hide)
    )
  )
)
