(kicad_pcb (version 20221018) (generator pcbnew)

  (general
    (thickness 1.55)
  )

  (paper "A4")
  (layers
    (0 "F.Cu" signal)
    (1 "In1.Cu" power)
    (2 "In2.Cu" power)
    (3 "In3.Cu" signal)
    (4 "In4.Cu" signal)
    (31 "B.Cu" signal)
    (32 "B<PERSON>" user "B.Adhesive")
    (33 "<PERSON><PERSON>" user "F.Adhesive")
    (34 "B.Paste" user)
    (35 "F.Paste" user)
    (36 "B.SilkS" user "B.Silkscreen")
    (37 "F.SilkS" user "F.Silkscreen")
    (38 "B.Mask" user)
    (39 "F.Mask" user)
    (40 "Dwgs.User" user "User.Drawings")
    (41 "Cmts.User" user "User.Comments")
    (42 "Eco1.User" user "User.Eco1")
    (43 "Eco2.User" user "User.Eco2")
    (44 "Edge.Cuts" user)
    (45 "Margin" user)
    (46 "B.CrtYd" user "B.Courtyard")
    (47 "F.CrtYd" user "F.Courtyard")
    (48 "B.Fab" user)
    (49 "F.Fab" user)
    (50 "User.1" user)
    (51 "User.2" user)
    (52 "User.3" user)
    (53 "User.4" user)
    (54 "User.5" user)
    (55 "User.6" user)
    (56 "User.7" user)
    (57 "User.8" user)
    (58 "User.9" user)
  )

  (setup
    (stackup
      (layer "F.SilkS" (type "Top Silk Screen"))
      (layer "F.Paste" (type "Top Solder Paste"))
      (layer "F.Mask" (type "Top Solder Mask") (thickness 0.01))
      (layer "F.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 1" (type "prepreg") (thickness 0.264) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In1.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 2" (type "prepreg") (thickness 0.264) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In2.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 3" (type "core") (thickness 0.264) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In3.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 4" (type "prepreg") (thickness 0.264) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "In4.Cu" (type "copper") (thickness 0.035))
      (layer "dielectric 5" (type "core") (thickness 0.264) (material "FR4") (epsilon_r 4.5) (loss_tangent 0.02))
      (layer "B.Cu" (type "copper") (thickness 0.035))
      (layer "B.Mask" (type "Bottom Solder Mask") (thickness 0.01))
      (layer "B.Paste" (type "Bottom Solder Paste"))
      (layer "B.SilkS" (type "Bottom Silk Screen"))
      (copper_finish "HAL lead-free")
      (dielectric_constraints no)
    )
    (pad_to_mask_clearance 0)
    (pcbplotparams
      (layerselection 0x0030000_7fffffe0)
      (plot_on_all_layers_selection 0x0001000_00000000)
      (disableapertmacros false)
      (usegerberextensions false)
      (usegerberattributes true)
      (usegerberadvancedattributes true)
      (creategerberjobfile true)
      (dashed_line_dash_ratio 12.000000)
      (dashed_line_gap_ratio 3.000000)
      (svgprecision 6)
      (plotframeref false)
      (viasonmask false)
      (mode 1)
      (useauxorigin false)
      (hpglpennumber 1)
      (hpglpenspeed 20)
      (hpglpendiameter 15.000000)
      (dxfpolygonmode true)
      (dxfimperialunits false)
      (dxfusepcbnewfont true)
      (psnegative false)
      (psa4output false)
      (plotreference true)
      (plotvalue false)
      (plotinvisibletext false)
      (sketchpadsonfab false)
      (subtractmaskfromsilk false)
      (outputformat 3)
      (mirror false)
      (drillshape 0)
      (scaleselection 1)
      (outputdirectory "CAM/")
    )
  )

  (net 0 "")
  (net 1 "GND")
  (net 2 "+3V3")
  (net 3 "D1G")
  (net 4 "D1R")
  (net 5 "D1B")
  (net 6 "D2G")
  (net 7 "D2R")
  (net 8 "D2B")
  (net 9 "AUTO_UP")
  (net 10 "AUTO_DN")
  (net 11 "SCL")
  (net 12 "SDA")
  (net 13 "unconnected-(U2-~{INT}-Pad1)")
  (net 14 "unconnected-(U2-NC-Pad3)")
  (net 15 "unconnected-(U2-NC-Pad8)")
  (net 16 "unconnected-(U2-NC-Pad13)")
  (net 17 "unconnected-(U2-NC-Pad18)")

  (footprint "Library:SSOP20" (layer "F.Cu")
    (tstamp 629751aa-e679-4604-b0e4-bd6a4140ed98)
    (at -9.2 -32 -90)
    (property "Sheetfile" "mcu_2_6.kicad_sch")
    (property "Sheetname" "")
    (property "ki_description" "8 Bit Port/Expander to I2C Bus, SSOP-20")
    (property "ki_keywords" "I2C Expander")
    (path "/024cef38-5927-47cb-8157-ae104f3635b5")
    (attr smd)
    (fp_text reference "U2" (at -3 4.3 90) (layer "F.SilkS") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp d0aed730-771f-4de2-8c72-2f4c4cfb6a7e)
    )
    (fp_text value "PCF8574ATS" (at 0 1.27 90) (layer "F.Fab") hide
        (effects (font (size 1 1) (thickness 0.15)))
      (tstamp 62d474a1-9cbb-493e-a045-6eb3a6354079)
    )
    (fp_line (start -3.8 -3.48) (end -3.75 -3.53)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 3630b78a-7bbe-454c-82f7-79baeb0bd727))
    (fp_line (start -3.8 3.48) (end -3.8 -3.48)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp b14f87a5-3b17-4120-b1a7-d7485d49d322))
    (fp_line (start -3.75 -3.53) (end -2.09 -3.53)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 8f132992-d844-4378-babd-616b6c533c10))
    (fp_line (start -3.75 3.53) (end -3.8 3.48)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 05f6fbdd-1a57-416e-a27a-daf947b4b3e2))
    (fp_line (start -2.09 -3.53) (end -2.04 -3.45)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp adcf7fcd-854d-4345-9bf0-d3f08f55c93e))
    (fp_line (start -2.09 3.53) (end -3.75 3.53)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp a89524fe-019b-4dbe-9ca6-0c525029a4a1))
    (fp_line (start -2.04 -3.45) (end 2.04 -3.45)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 128ab91d-db33-498b-8cd8-656f5644090d))
    (fp_line (start -2.04 3.45) (end -2.09 3.53)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 04534eae-107b-45df-80df-463ce96d637a))
    (fp_line (start -1.8 -3) (end -1.1 -3)
      (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp 8b489367-52e3-4fe1-aa2b-0de8ed002165))
    (fp_line (start -1.8 -2.3) (end -1.8 -3)
      (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp f85eb427-4053-4606-b437-5736fa3584cb))
    (fp_line (start -1.1 -3) (end -1.8 -2.3)
      (stroke (width 0.12) (type default)) (layer "F.SilkS") (tstamp b1f2d255-30bf-41a8-b2d3-031a171fbace))
    (fp_line (start 2.04 -3.45) (end 2.09 -3.53)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 2d322f25-6603-4da8-af1a-433e28b09186))
    (fp_line (start 2.04 3.45) (end -2.04 3.45)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp f1f6e51e-69cd-4c8d-9c18-f20772b58de7))
    (fp_line (start 2.04 3.48) (end 2.04 3.45)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp c11218a3-2d6d-47a4-9deb-2afab9c09197))
    (fp_line (start 2.09 -3.53) (end 3.75 -3.53)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 47b43cef-b71f-4036-80ae-674e181e5daa))
    (fp_line (start 2.09 3.53) (end 2.04 3.48)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 3e510644-7fd1-46bc-a164-7655363aabdf))
    (fp_line (start 3.75 -3.53) (end 3.8 -3.48)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 20ffe115-b8c3-423d-882b-094cd8607f86))
    (fp_line (start 3.75 3.53) (end 2.09 3.53)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 78998b65-ad07-4212-b4c8-891737c86073))
    (fp_line (start 3.8 -3.48) (end 3.8 3.48)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp b8911145-47f8-463a-9674-22b18d163af3))
    (fp_line (start 3.8 3.48) (end 3.75 3.53)
      (stroke (width 0.1) (type solid)) (layer "F.SilkS") (tstamp 209bb2bc-68ae-4a07-8cb5-4b70beee5568))
    (fp_line (start -3.2 -3.035) (end -3.2 -2.815)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7c6ef453-b559-45db-9cc1-6d1e817fecd3))
    (fp_line (start -3.2 -2.815) (end -2.2 -2.815)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9f111420-e9c3-48dd-a0de-8972d9298914))
    (fp_line (start -3.2 -2.385) (end -3.2 -2.165)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0135cd7e-4424-4bf1-aef2-dd274ecc37e4))
    (fp_line (start -3.2 -2.165) (end -2.2 -2.165)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 59562a13-a36e-46d6-9c33-6a45982da0a5))
    (fp_line (start -3.2 -1.735) (end -3.2 -1.515)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 37c95501-ec54-47a3-a96c-89d75241438d))
    (fp_line (start -3.2 -1.515) (end -2.2 -1.515)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d7fb3f33-51bf-46e9-979c-2c4eb8ee9a60))
    (fp_line (start -3.2 -1.085) (end -3.2 -0.865)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f8d6c0a4-7ef6-42b5-8bde-e161ceaccfbe))
    (fp_line (start -3.2 -0.865) (end -2.2 -0.865)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 312d623d-9d95-4e5c-9b5d-96196e931cf2))
    (fp_line (start -3.2 -0.435) (end -3.2 -0.215)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp fdb8c26d-67c1-4d9b-9f66-cb15cd640160))
    (fp_line (start -3.2 -0.215) (end -2.2 -0.215)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e213a98b-8c7c-4827-9dd1-7151750aa79d))
    (fp_line (start -3.2 0.215) (end -3.2 0.435)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b8ed6774-832f-461b-b26c-ac4696594b9c))
    (fp_line (start -3.2 0.435) (end -2.2 0.435)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 88af7e59-1fbe-484b-93f0-291ce5b9aa17))
    (fp_line (start -3.2 0.865) (end -3.2 1.085)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 728bb1f4-ef85-450a-bfb0-df297d1b46bd))
    (fp_line (start -3.2 1.085) (end -2.2 1.085)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7b3b3512-fba7-451e-b037-b256ad8cd664))
    (fp_line (start -3.2 1.515) (end -3.2 1.735)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 198392fd-5bfc-4165-8f62-a0bc31f0198c))
    (fp_line (start -3.2 1.735) (end -2.2 1.735)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 38278c33-1015-4e50-9c44-af4706810c1c))
    (fp_line (start -3.2 2.165) (end -3.2 2.385)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 04c20110-9202-4b85-a480-b29f3217f694))
    (fp_line (start -3.2 2.385) (end -2.2 2.385)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0cd42465-d9c9-4619-8be8-fc73ff69dfae))
    (fp_line (start -3.2 2.815) (end -3.2 3.035)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7753bd8d-7b21-409b-8f51-0bdcc261d24f))
    (fp_line (start -3.2 3.035) (end -2.2 3.035)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e81f1976-bd13-42c8-933c-368852873483))
    (fp_line (start -2.65 -2.815) (end -2.65 -3.035)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp fd74c31e-7dcc-454a-971d-708e13ee4820))
    (fp_line (start -2.65 -2.165) (end -2.65 -2.385)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ac9e26ec-54c3-4d35-be23-d1dc981cce76))
    (fp_line (start -2.65 -1.515) (end -2.65 -1.735)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4e570d56-de90-41aa-99b2-c24a945aab98))
    (fp_line (start -2.65 -0.865) (end -2.65 -1.085)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ea922115-bc41-4cdf-bfe0-d4ef2fadcfa9))
    (fp_line (start -2.65 -0.215) (end -2.65 -0.435)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0e5e24bf-0e1f-4b45-8417-ed02d7933ea8))
    (fp_line (start -2.65 0.435) (end -2.65 0.215)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a8433381-584f-49d7-92ed-06347cb2dcaf))
    (fp_line (start -2.65 1.085) (end -2.65 0.865)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 352e3adb-bff2-4a74-9bd6-fb4e07d721e9))
    (fp_line (start -2.65 1.735) (end -2.65 1.515)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 62eb7afd-33f6-4338-aa9c-aabd5d4583ac))
    (fp_line (start -2.65 2.385) (end -2.65 2.165)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b6ebf711-a138-4445-b384-57588fdac512))
    (fp_line (start -2.65 3.035) (end -2.65 2.815)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 52630362-3e2b-477a-9be5-adfbae3a479b))
    (fp_line (start -2.2 -3.11) (end -2.2 -3.035)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5f505b1a-e7cc-4eea-bdc2-853259dc326b))
    (fp_line (start -2.2 -3.035) (end -3.2 -3.035)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a93c7770-76a0-4d68-be62-6b7c4be62e97))
    (fp_line (start -2.2 -2.815) (end -2.2 -2.385)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp eb6e8366-fe9d-452d-8afe-643375ca4d8b))
    (fp_line (start -2.2 -2.385) (end -3.2 -2.385)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f3f5fd4e-d038-4fae-9ae3-c98897855ec4))
    (fp_line (start -2.2 -2.165) (end -2.2 -1.735)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ba55df83-5308-4fc9-8305-bbd72bea4fd3))
    (fp_line (start -2.2 -1.735) (end -3.2 -1.735)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f9ac8146-e5e6-410b-a0dd-2e35ba0339a9))
    (fp_line (start -2.2 -1.515) (end -2.2 -1.085)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ad21d703-72c5-4bc2-b9f8-77f9cb360fa2))
    (fp_line (start -2.2 -1.085) (end -3.2 -1.085)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp db5b00f0-2d18-4b1e-8317-f95b9b075997))
    (fp_line (start -2.2 -0.865) (end -2.2 -0.435)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c4f94a19-9b58-4d90-9938-0917b057c6ca))
    (fp_line (start -2.2 -0.435) (end -3.2 -0.435)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp afd603c7-2b35-4f0d-889d-dcd1d927e496))
    (fp_line (start -2.2 -0.215) (end -2.2 0.215)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b755f3f3-f3dd-4434-bd6a-e5c0240c0f00))
    (fp_line (start -2.2 0.215) (end -3.2 0.215)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b2ae2e56-be54-45a2-bd92-41cf31ef1254))
    (fp_line (start -2.2 0.435) (end -2.2 0.865)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8b3cba88-812f-454b-ad95-8bcbe90c51b6))
    (fp_line (start -2.2 0.865) (end -3.2 0.865)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1f6629c6-b2fa-44a2-bf89-bc60d048e0d1))
    (fp_line (start -2.2 1.085) (end -2.2 1.515)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c4bb9d9f-0485-412e-a9c1-7184c531b3e3))
    (fp_line (start -2.2 1.515) (end -3.2 1.515)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp abb4ee05-4927-4d9a-89cb-4b08cebc58f8))
    (fp_line (start -2.2 1.735) (end -2.2 2.165)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a6fa8224-8565-4d9d-9793-de4951b53034))
    (fp_line (start -2.2 2.165) (end -3.2 2.165)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2952b4eb-0d19-4fc1-9d7a-88b6f510729d))
    (fp_line (start -2.2 2.385) (end -2.2 2.815)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 92e64edf-b10a-4893-b930-f14ad658d780))
    (fp_line (start -2.2 2.815) (end -3.2 2.815)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp efe88418-ffae-467f-aa6e-be1b0688a425))
    (fp_line (start -2.2 3.035) (end -2.2 3.11)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 96800ff3-05cd-4cac-8575-cc42eb088162))
    (fp_line (start -2.2 3.11) (end -2.06 3.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b8aac15f-571c-40dc-a020-ba6e22b51c92))
    (fp_line (start -2.125 -3.0725) (end -2.125 3.0725)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a3ad8480-74d8-4cc7-92ff-b9a346d3e4e7))
    (fp_line (start -2.125 3.0725) (end -2.00278 3.01139)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ab31e89a-06f7-4467-aac4-529f6bcdfce2))
    (fp_line (start -2.06 -3.25) (end -2.2 -3.11)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 4d345623-3531-4012-8762-fb4f844efeb4))
    (fp_line (start -2.06 3.25) (end 2.06 3.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 455b50df-5e59-46de-9512-016347443514))
    (fp_line (start -2.0225 -3.175) (end -2.125 -3.0725)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 66c5d988-6726-4fca-a25d-06a343d1998e))
    (fp_line (start -2.0225 3.175) (end -2.125 3.0725)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp cc44df5e-f0e9-4bea-b12e-6cd9fdf1aa71))
    (fp_line (start -2.0225 3.175) (end 2.0225 3.175)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 52554934-384f-4620-8515-24d66bbd7c14))
    (fp_line (start -2.00278 -3.01139) (end -2.125 -3.0725)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b36b9b49-f8e0-4be3-948e-cafa26be98ae))
    (fp_line (start -2.00278 -3.01139) (end -1.96139 -3.05278)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 39dc8fef-397a-4442-ab71-65c1587b79fc))
    (fp_line (start -2.00278 3.01139) (end -2.00278 -3.01139)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a06beb16-d320-4254-ab73-68cc453d8478))
    (fp_line (start -2.00278 3.01139) (end -1.96139 3.05278)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp efc56961-e355-4f67-a269-ba034b6c4f58))
    (fp_line (start -1.96139 -3.05278) (end -2.0225 -3.175)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp eeda8d7d-ebbb-43e0-a419-9db9a4610d63))
    (fp_line (start -1.96139 3.05278) (end -2.0225 3.175)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f5c77f03-a956-453b-8c95-476c16417010))
    (fp_line (start 1.96139 -3.05278) (end -1.96139 -3.05278)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e09ea527-a2f9-447e-a66a-bd585e9dc668))
    (fp_line (start 1.96139 -3.05278) (end 2.0225 -3.175)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp ce433771-034d-4f83-8b6c-fa1dc0d84210))
    (fp_line (start 1.96139 3.05278) (end -1.96139 3.05278)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 091908a9-33b0-4182-909f-b0e0f5fa1bcb))
    (fp_line (start 2.00278 -3.01139) (end 1.96139 -3.05278)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp df509102-9c01-4e89-810b-0c58a7e20986))
    (fp_line (start 2.00278 -3.01139) (end 2.00278 3.01139)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a152fe7b-f4b1-48cd-a6c6-8be50b924707))
    (fp_line (start 2.00278 3.01139) (end 1.96139 3.05278)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0a98eec4-321d-4d3b-af38-8ee1728191fd))
    (fp_line (start 2.00278 3.01139) (end 2.125 3.0725)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6eecb21a-51dd-42c6-a7f7-0817d4b9b962))
    (fp_line (start 2.0225 -3.175) (end -2.0225 -3.175)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp d77b5bd9-c149-41a5-bf61-4f0b45ac1dd3))
    (fp_line (start 2.0225 3.175) (end 1.96139 3.05278)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a36fb3fc-b574-4a71-a26a-e17f20709057))
    (fp_line (start 2.0225 3.175) (end 2.125 3.0725)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b018bf33-054b-46bf-86b3-019ae6870cd4))
    (fp_line (start 2.06 -3.25) (end -2.06 -3.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp aead49c9-28cd-4fd6-8059-41c16a65391d))
    (fp_line (start 2.06 3.25) (end 2.2 3.11)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a6246012-e3b2-4332-98a9-4fd55e519fdc))
    (fp_line (start 2.125 -3.0725) (end 2.00278 -3.01139)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c2ad9161-f519-4253-a976-f4ce2e90e8e0))
    (fp_line (start 2.125 -3.0725) (end 2.0225 -3.175)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 43d795c4-d2cb-4c5d-bba1-e7743bbe2315))
    (fp_line (start 2.125 3.0725) (end 2.125 -3.0725)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 678d7105-4278-40da-8452-ab65a5c9646c))
    (fp_line (start 2.2 -3.11) (end 2.06 -3.25)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3c345a01-9f92-4586-8615-fb4129b43b73))
    (fp_line (start 2.2 -3.035) (end 2.2 -3.11)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a4f9ed1a-ea2c-4ff5-9768-e6d187dce97c))
    (fp_line (start 2.2 -2.815) (end 3.2 -2.815)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2fddba98-47d5-43c3-935c-768e4c946565))
    (fp_line (start 2.2 -2.385) (end 2.2 -2.815)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6f0b1789-dd99-4e13-8de3-e551a9caadcc))
    (fp_line (start 2.2 -2.165) (end 3.2 -2.165)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 58cb567f-665a-4342-9310-bfca80387417))
    (fp_line (start 2.2 -1.735) (end 2.2 -2.165)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6edbf722-4e9b-4725-b9a5-1596a7c2a726))
    (fp_line (start 2.2 -1.515) (end 3.2 -1.515)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0a512637-eea5-4983-af6c-5e432e0dfc50))
    (fp_line (start 2.2 -1.085) (end 2.2 -1.515)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 3c893150-c676-4ff2-a7f6-c4e849650ad0))
    (fp_line (start 2.2 -0.865) (end 3.2 -0.865)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 342faf73-5803-48c6-8cc5-b68c2e142912))
    (fp_line (start 2.2 -0.435) (end 2.2 -0.865)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c3d2c84c-2594-48a9-bb8b-c5dc57807fd2))
    (fp_line (start 2.2 -0.215) (end 3.2 -0.215)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 78a93c97-28b8-475c-8429-f86badd4ce04))
    (fp_line (start 2.2 0.215) (end 2.2 -0.215)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 18a62029-ae00-47be-8123-e192308143d7))
    (fp_line (start 2.2 0.435) (end 3.2 0.435)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2f2df161-fd04-4e6e-9eb4-70f70233bb54))
    (fp_line (start 2.2 0.865) (end 2.2 0.435)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 646d68e1-2511-4f1e-a558-950be69fcb7d))
    (fp_line (start 2.2 1.085) (end 3.2 1.085)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 342fa22f-1497-4905-8302-bc6379215f85))
    (fp_line (start 2.2 1.515) (end 2.2 1.085)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f966b896-ae66-4a0b-8dec-93e9373042f7))
    (fp_line (start 2.2 1.735) (end 3.2 1.735)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 44f8046d-194e-493a-9925-2117859f766d))
    (fp_line (start 2.2 2.165) (end 2.2 1.735)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c5b0e8c2-4be8-4913-aa86-b1895a3a0d4b))
    (fp_line (start 2.2 2.385) (end 3.2 2.385)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a1bdfb3b-fdc4-4e58-ad0f-f2598249e93a))
    (fp_line (start 2.2 2.815) (end 2.2 2.385)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 73898ab1-3f02-4fdb-be88-5b7abcd47751))
    (fp_line (start 2.2 3.035) (end 3.2 3.035)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 9db76ba0-2040-4868-ae2b-2908a7120f78))
    (fp_line (start 2.2 3.11) (end 2.2 3.035)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 42a879d8-90d3-458e-a6ee-5406deb09e33))
    (fp_line (start 2.65 -3.035) (end 2.65 -2.815)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 043bd35f-0c3d-4b60-a367-ef00cd6b242b))
    (fp_line (start 2.65 -2.385) (end 2.65 -2.165)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp bb2df844-f73c-45f9-a17d-2e213a8d8b6a))
    (fp_line (start 2.65 -1.735) (end 2.65 -1.515)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 09669b68-abd8-4d29-a7c2-fd8832ede59c))
    (fp_line (start 2.65 -1.085) (end 2.65 -0.865)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a8889011-4ae3-444a-b6a2-cbfbc0572cce))
    (fp_line (start 2.65 -0.435) (end 2.65 -0.215)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp adb66547-b454-4070-8547-b21a2529b9b5))
    (fp_line (start 2.65 0.215) (end 2.65 0.435)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 0cfba110-074e-4820-b9a1-1ac4d2811cc4))
    (fp_line (start 2.65 0.865) (end 2.65 1.085)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8f404b39-8186-48b8-841c-8752c97dbe8e))
    (fp_line (start 2.65 1.515) (end 2.65 1.735)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f720fbeb-29b3-48c3-b7e2-8cbe34ef77a3))
    (fp_line (start 2.65 2.165) (end 2.65 2.385)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 87d78d89-e2a8-401c-8d70-2f15bf50f7e6))
    (fp_line (start 2.65 2.815) (end 2.65 3.035)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 5befd621-c5ae-4778-8601-aa4589340064))
    (fp_line (start 3.2 -3.035) (end 2.2 -3.035)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 2db026db-69c6-4065-9d39-5cef59eaf622))
    (fp_line (start 3.2 -2.815) (end 3.2 -3.035)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp e262b01c-58ed-4b4b-bea0-97b0676a8eaa))
    (fp_line (start 3.2 -2.385) (end 2.2 -2.385)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp db384151-2247-489f-bc99-a1fb1dd5c8e2))
    (fp_line (start 3.2 -2.165) (end 3.2 -2.385)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 764a16b7-6b4a-46a2-bcc1-a41697cffd9c))
    (fp_line (start 3.2 -1.735) (end 2.2 -1.735)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f7d51387-9334-43fc-9abf-383c726dff21))
    (fp_line (start 3.2 -1.515) (end 3.2 -1.735)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 7bebb09b-d7ef-40e6-a8ba-e736baeb6e05))
    (fp_line (start 3.2 -1.085) (end 2.2 -1.085)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 74e8c5be-97c6-434b-8626-fe1373d19d42))
    (fp_line (start 3.2 -0.865) (end 3.2 -1.085)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp b6d20187-3638-447a-b396-f430a58cecaa))
    (fp_line (start 3.2 -0.435) (end 2.2 -0.435)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 979b89b3-7dc0-44a7-8e6f-a45df0225db4))
    (fp_line (start 3.2 -0.215) (end 3.2 -0.435)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 43076c15-9d4e-4c0a-8576-895bab1bb47b))
    (fp_line (start 3.2 0.215) (end 2.2 0.215)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 6e72e8d1-d9c6-4b92-9883-5608889645ae))
    (fp_line (start 3.2 0.435) (end 3.2 0.215)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp c8bff34e-cbbe-4218-86c6-973cdb4db832))
    (fp_line (start 3.2 0.865) (end 2.2 0.865)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 27343f5f-51e3-471e-b2b6-f93e7ed0a40f))
    (fp_line (start 3.2 1.085) (end 3.2 0.865)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 47e6d5fa-9d70-4bd0-b5e9-209e47125ee7))
    (fp_line (start 3.2 1.515) (end 2.2 1.515)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 41273d71-4071-4627-9637-dde82e61c9dc))
    (fp_line (start 3.2 1.735) (end 3.2 1.515)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp f19735b4-c057-4868-bfd6-4dcc1f9fe944))
    (fp_line (start 3.2 2.165) (end 2.2 2.165)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 8986affe-5749-4d9e-954a-c8c3e5405ffd))
    (fp_line (start 3.2 2.385) (end 3.2 2.165)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 1b6ef1a9-b447-471a-8c6c-c6976afc83e7))
    (fp_line (start 3.2 2.815) (end 2.2 2.815)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp 36ad49e1-d622-479f-890a-ec1bc41ff733))
    (fp_line (start 3.2 3.035) (end 3.2 2.815)
      (stroke (width 0.1) (type solid)) (layer "F.Fab") (tstamp a4942d72-fd89-4b30-b3a7-96619e841566))
    (fp_circle (center -1.30278 -2.35278) (end -1.05278 -2.35278)
      (stroke (width 0.1) (type solid)) (fill none) (layer "F.Fab") (tstamp 81c37099-d208-4666-8009-0492a250a754))
    (pad "1" smd roundrect (at -2.925 -3.025 270) (size 1.35 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.166667)
      (net 13 "unconnected-(U2-~{INT}-Pad1)") (pinfunction "~{INT}") (pintype "open_collector+no_connect") (clearance 0.2) (tstamp 9a6e5e35-fab3-45ce-8c35-a60b966554f1))
    (pad "2" smd roundrect (at -2.925 -2.275 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 11 "SCL") (pinfunction "SCL") (pintype "input") (clearance 0.2) (tstamp ffec11de-b6e0-44a4-8af8-88d1e080246f))
    (pad "3" smd roundrect (at -2.925 -1.625 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 14 "unconnected-(U2-NC-Pad3)") (pinfunction "NC") (pintype "no_connect") (clearance 0.2) (tstamp 4932d41d-44b3-4455-9b8b-03b09925df40))
    (pad "4" smd roundrect (at -2.925 -0.975 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 12 "SDA") (pinfunction "SDA") (pintype "bidirectional") (clearance 0.2) (tstamp 0672c103-7c95-4ac2-8144-3a158e2e9f5c))
    (pad "5" smd roundrect (at -2.925 -0.325 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 2 "+3V3") (pinfunction "VDD") (pintype "power_in") (clearance 0.2) (tstamp 36b59b3d-5d4b-4c51-9a33-161a5236b21b))
    (pad "6" smd roundrect (at -2.925 0.325 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "A0") (pintype "input") (clearance 0.2) (tstamp aed52006-b2ec-4efd-81de-59a0af3ef57e))
    (pad "7" smd roundrect (at -2.925 0.975 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "A1") (pintype "input") (clearance 0.2) (tstamp bde899c2-ad9d-4948-96d7-65446324ad96))
    (pad "8" smd roundrect (at -2.925 1.625 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 15 "unconnected-(U2-NC-Pad8)") (pinfunction "NC") (pintype "no_connect") (clearance 0.2) (tstamp 8bb61ae8-e0ef-4b32-bc9e-07e4625dd142))
    (pad "9" smd roundrect (at -2.925 2.275 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "A2") (pintype "input") (clearance 0.2) (tstamp 4662cbf5-043e-4557-bce9-c89b0248dbcf))
    (pad "10" smd roundrect (at -2.925 3.025 270) (size 1.35 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.166667)
      (net 9 "AUTO_UP") (pinfunction "P0") (pintype "bidirectional") (clearance 0.2) (tstamp eefd4e8c-5786-436c-95d8-12daf5a42af2))
    (pad "11" smd roundrect (at 2.925 3.025 270) (size 1.35 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.166667)
      (net 10 "AUTO_DN") (pinfunction "P1") (pintype "bidirectional") (clearance 0.2) (tstamp 10f22775-cd69-4b98-a389-4921c8fdad77))
    (pad "12" smd roundrect (at 2.925 2.275 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 3 "D1G") (pinfunction "P2") (pintype "bidirectional") (clearance 0.2) (tstamp 3d70cd58-a7c9-4b29-aeed-53ea4ce5ef88))
    (pad "13" smd roundrect (at 2.925 1.625 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 16 "unconnected-(U2-NC-Pad13)") (pinfunction "NC") (pintype "no_connect") (clearance 0.2) (tstamp b26fa7e1-2fd5-41db-88c6-d69b5df0a6f2))
    (pad "14" smd roundrect (at 2.925 0.975 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 4 "D1R") (pinfunction "P3") (pintype "bidirectional") (clearance 0.2) (tstamp 74eea79e-baa8-4b4f-8042-af29df63d8bf))
    (pad "15" smd roundrect (at 2.925 0.325 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 1 "GND") (pinfunction "VSS") (pintype "power_in") (clearance 0.2) (tstamp 33931d9f-56ce-4158-ab4c-a0a38b069ef3))
    (pad "16" smd roundrect (at 2.925 -0.325 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 5 "D1B") (pinfunction "P4") (pintype "bidirectional") (clearance 0.2) (tstamp b692f5e4-0786-4a14-866c-5017255e6d71))
    (pad "17" smd roundrect (at 2.925 -0.975 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 6 "D2G") (pinfunction "P5") (pintype "bidirectional") (clearance 0.2) (tstamp ac4486a3-ed95-4d9c-8792-940e617055ee))
    (pad "18" smd roundrect (at 2.925 -1.625 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 17 "unconnected-(U2-NC-Pad18)") (pinfunction "NC") (pintype "no_connect") (clearance 0.2) (tstamp 905a7af1-7c38-4f8f-98ad-7c7c8b6349ff))
    (pad "19" smd roundrect (at 2.925 -2.275 270) (size 1.35 0.4) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.25)
      (net 7 "D2R") (pinfunction "P6") (pintype "bidirectional") (clearance 0.2) (tstamp ceb9dd82-30a4-42dd-b499-ab924cee8de0))
    (pad "20" smd roundrect (at 2.925 -3.025 270) (size 1.35 0.6) (layers "F.Cu" "F.Paste" "F.Mask") (roundrect_rratio 0.166667)
      (net 8 "D2B") (pinfunction "P7") (pintype "bidirectional") (clearance 0.2) (tstamp 6392418e-5faf-44d3-8b5f-921550eb7480))
    (model "${KIPRJMOD}/3D-models/SSOP20.step"
      (offset (xyz 0 0 0))
      (scale (xyz 1 1 1))
      (rotate (xyz 0 0 0))
    )
  )

  (gr_rect (start -15.24 -38.9636) (end -3.2512 -26.6192)
    (stroke (width 0.05) (type default)) (fill none) (layer "Edge.Cuts") (tstamp 08db0f19-d29d-4606-ae1a-3f53552dd769))

  (zone (net 1) (net_name "GND") (layer "F.Cu") (tstamp a18a5ff0-8934-4b72-be34-76c1649d5477) (name "GND") (hatch edge 0.508)
    (connect_pads (clearance 0.25))
    (min_thickness 0.200001) (filled_areas_thickness no)
    (fill yes (thermal_gap 0.25) (thermal_bridge_width 0.3) (island_removal_mode 1) (island_area_min 5))
    (polygon
      (pts
        (xy 50.2 149.7)
        (xy -50.1 149.6)
        (xy -50 -40)
        (xy 50 -40)
      )
    )
    (filled_polygon
      (layer "F.Cu")
      (pts
        (xy -3.4012 -38.849837)
        (xy -3.364963 -38.8136)
        (xy -3.3517 -38.7641)
        (xy -3.3517 -26.8187)
        (xy -3.364963 -26.7692)
        (xy -3.4012 -26.732963)
        (xy -3.4507 -26.7197)
        (xy -15.0405 -26.7197)
        (xy -15.09 -26.732963)
        (xy -15.126237 -26.7692)
        (xy -15.1395 -26.8187)
        (xy -15.1395 -29.694865)
        (xy -12.7255 -29.694865)
        (xy -12.725499 -28.455136)
        (xy -12.722585 -28.430009)
        (xy -12.677206 -28.327235)
        (xy -12.597764 -28.247793)
        (xy -12.494989 -28.202414)
        (xy -12.469866 -28.1995)
        (xy -12.469865 -28.1995)
        (xy -11.980137 -28.1995)
        (xy -11.955007 -28.202415)
        (xy -11.839986 -28.253201)
        (xy -11.799999 -28.261636)
        (xy -11.760011 -28.253201)
        (xy -11.644989 -28.202414)
        (xy -11.619866 -28.1995)
        (xy -11.619865 -28.1995)
        (xy -11.330137 -28.1995)
        (xy -11.305007 -28.202415)
        (xy -11.189986 -28.253201)
        (xy -11.149999 -28.261636)
        (xy -11.110011 -28.253201)
        (xy -10.994989 -28.202414)
        (xy -10.969866 -28.1995)
        (xy -10.969865 -28.1995)
        (xy -10.680137 -28.1995)
        (xy -10.655007 -28.202415)
        (xy -10.539986 -28.253201)
        (xy -10.499999 -28.261636)
        (xy -10.460011 -28.253201)
        (xy -10.344989 -28.202414)
        (xy -10.319866 -28.1995)
        (xy -10.319865 -28.1995)
        (xy -10.030137 -28.1995)
        (xy -10.005007 -28.202415)
        (xy -9.940362 -28.230958)
        (xy -9.882183 -28.237707)
        (xy -9.841287 -28.21615)
        (xy -9.726249 -28.159912)
        (xy -9.675 -28.152446)
        (xy -9.675 -29.997554)
        (xy -9.375 -29.997554)
        (xy -9.375 -28.152445)
        (xy -9.323752 -28.159911)
        (xy -9.208704 -28.216155)
        (xy -9.167814 -28.237708)
        (xy -9.109637 -28.230958)
        (xy -9.044991 -28.202414)
        (xy -9.019866 -28.1995)
        (xy -9.019865 -28.1995)
        (xy -8.730137 -28.1995)
        (xy -8.705007 -28.202415)
        (xy -8.589986 -28.253201)
        (xy -8.549999 -28.261636)
        (xy -8.510011 -28.253201)
        (xy -8.394989 -28.202414)
        (xy -8.369866 -28.1995)
        (xy -8.369865 -28.1995)
        (xy -8.080137 -28.1995)
        (xy -8.055007 -28.202415)
        (xy -7.939986 -28.253201)
        (xy -7.899999 -28.261636)
        (xy -7.860011 -28.253201)
        (xy -7.744989 -28.202414)
        (xy -7.719866 -28.1995)
        (xy -7.719865 -28.1995)
        (xy -7.430137 -28.1995)
        (xy -7.405007 -28.202415)
        (xy -7.289986 -28.253201)
        (xy -7.249999 -28.261636)
        (xy -7.210011 -28.253201)
        (xy -7.094989 -28.202414)
        (xy -7.069866 -28.1995)
        (xy -7.069865 -28.1995)
        (xy -6.780137 -28.1995)
        (xy -6.755007 -28.202415)
        (xy -6.639986 -28.253201)
        (xy -6.599999 -28.261636)
        (xy -6.560011 -28.253201)
        (xy -6.444989 -28.202414)
        (xy -6.419866 -28.1995)
        (xy -6.419865 -28.1995)
        (xy -5.930137 -28.1995)
        (xy -5.905009 -28.202415)
        (xy -5.802235 -28.247793)
        (xy -5.722793 -28.327235)
        (xy -5.677414 -28.43001)
        (xy -5.6745 -28.455132)
        (xy -5.6745 -29.694862)
        (xy -5.677415 -29.71999)
        (xy -5.722793 -29.822764)
        (xy -5.802235 -29.902206)
        (xy -5.873864 -29.933833)
        (xy -5.905009 -29.947585)
        (xy -5.930135 -29.9505)
        (xy -6.419864 -29.950499)
        (xy -6.444991 -29.947585)
        (xy -6.547765 -29.902206)
        (xy -6.547765 -29.902205)
        (xy -6.560012 -29.896798)
        (xy -6.6 -29.888363)
        (xy -6.639988 -29.896798)
        (xy -6.652234 -29.902205)
        (xy -6.652235 -29.902206)
        (xy -6.755009 -29.947585)
        (xy -6.780135 -29.9505)
        (xy -7.069864 -29.950499)
        (xy -7.094991 -29.947585)
        (xy -7.197765 -29.902206)
        (xy -7.197765 -29.902205)
        (xy -7.210012 -29.896798)
        (xy -7.25 -29.888363)
        (xy -7.289988 -29.896798)
        (xy -7.302234 -29.902205)
        (xy -7.302235 -29.902206)
        (xy -7.405009 -29.947585)
        (xy -7.430135 -29.9505)
        (xy -7.719864 -29.950499)
        (xy -7.744991 -29.947585)
        (xy -7.847765 -29.902206)
        (xy -7.847765 -29.902205)
        (xy -7.860012 -29.896798)
        (xy -7.9 -29.888363)
        (xy -7.939988 -29.896798)
        (xy -7.952234 -29.902205)
        (xy -7.952235 -29.902206)
        (xy -8.055009 -29.947585)
        (xy -8.080135 -29.9505)
        (xy -8.369864 -29.950499)
        (xy -8.394991 -29.947585)
        (xy -8.497765 -29.902206)
        (xy -8.497765 -29.902205)
        (xy -8.510012 -29.896798)
        (xy -8.55 -29.888363)
        (xy -8.589988 -29.896798)
        (xy -8.602234 -29.902205)
        (xy -8.602235 -29.902206)
        (xy -8.705009 -29.947585)
        (xy -8.730135 -29.9505)
        (xy -9.019864 -29.950499)
        (xy -9.044991 -29.947585)
        (xy -9.109637 -29.91904)
        (xy -9.167815 -29.912292)
        (xy -9.208722 -29.933853)
        (xy -9.32375 -29.990087)
        (xy -9.375 -29.997554)
        (xy -9.675 -29.997554)
        (xy -9.726247 -29.990088)
        (xy -9.841317 -29.933833)
        (xy -9.882181 -29.912292)
        (xy -9.940363 -29.919041)
        (xy -9.973864 -29.933833)
        (xy -10.005009 -29.947585)
        (xy -10.030135 -29.9505)
        (xy -10.319864 -29.950499)
        (xy -10.344991 -29.947585)
        (xy -10.447765 -29.902206)
        (xy -10.447765 -29.902205)
        (xy -10.460012 -29.896798)
        (xy -10.5 -29.888363)
        (xy -10.539988 -29.896798)
        (xy -10.552234 -29.902205)
        (xy -10.552235 -29.902206)
        (xy -10.655009 -29.947585)
        (xy -10.680135 -29.9505)
        (xy -10.969864 -29.950499)
        (xy -10.994991 -29.947585)
        (xy -11.097765 -29.902206)
        (xy -11.097765 -29.902205)
        (xy -11.110012 -29.896798)
        (xy -11.15 -29.888363)
        (xy -11.189988 -29.896798)
        (xy -11.202234 -29.902205)
        (xy -11.202235 -29.902206)
        (xy -11.305009 -29.947585)
        (xy -11.330135 -29.9505)
        (xy -11.619864 -29.950499)
        (xy -11.644991 -29.947585)
        (xy -11.747765 -29.902206)
        (xy -11.747765 -29.902205)
        (xy -11.760012 -29.896798)
        (xy -11.8 -29.888363)
        (xy -11.839988 -29.896798)
        (xy -11.852234 -29.902205)
        (xy -11.852235 -29.902206)
        (xy -11.955009 -29.947585)
        (xy -11.980135 -29.9505)
        (xy -12.469864 -29.950499)
        (xy -12.494991 -29.947585)
        (xy -12.597765 -29.902206)
        (xy -12.677206 -29.822765)
        (xy -12.722585 -29.719991)
        (xy -12.7255 -29.694865)
        (xy -15.1395 -29.694865)
        (xy -15.1395 -34.002445)
        (xy -11.325 -34.002445)
        (xy -11.273752 -34.009911)
        (xy -11.158704 -34.066155)
        (xy -11.117814 -34.087708)
        (xy -11.059637 -34.080958)
        (xy -10.994991 -34.052414)
        (xy -10.969866 -34.0495)
        (xy -10.969865 -34.0495)
        (xy -10.680137 -34.0495)
        (xy -10.655007 -34.052415)
        (xy -10.590362 -34.080958)
        (xy -10.532183 -34.087707)
        (xy -10.491287 -34.06615)
        (xy -10.376249 -34.009912)
        (xy -10.325 -34.002446)
        (xy -10.325 -35.075001)
        (xy -10.025 -35.075001)
        (xy -10.024999 -35.075)
        (xy -9.675001 -35.075)
        (xy -9.675 -35.075001)
        (xy -9.675 -35.847554)
        (xy -9.375 -35.847554)
        (xy -9.375 -34.002445)
        (xy -9.323752 -34.009911)
        (xy -9.208704 -34.066155)
        (xy -9.167814 -34.087708)
        (xy -9.109637 -34.080958)
        (xy -9.044991 -34.052414)
        (xy -9.019866 -34.0495)
        (xy -9.019865 -34.0495)
        (xy -8.730137 -34.0495)
        (xy -8.705007 -34.052415)
        (xy -8.589986 -34.103201)
        (xy -8.549999 -34.111636)
        (xy -8.510011 -34.103201)
        (xy -8.394989 -34.052414)
        (xy -8.369866 -34.0495)
        (xy -8.369865 -34.0495)
        (xy -8.080137 -34.0495)
        (xy -8.055007 -34.052415)
        (xy -7.939986 -34.103201)
        (xy -7.899999 -34.111636)
        (xy -7.860011 -34.103201)
        (xy -7.744989 -34.052414)
        (xy -7.719866 -34.0495)
        (xy -7.719865 -34.0495)
        (xy -7.430137 -34.0495)
        (xy -7.405007 -34.052415)
        (xy -7.289986 -34.103201)
        (xy -7.249999 -34.111636)
        (xy -7.210011 -34.103201)
        (xy -7.094989 -34.052414)
        (xy -7.069866 -34.0495)
        (xy -7.069865 -34.0495)
        (xy -6.780137 -34.0495)
        (xy -6.755007 -34.052415)
        (xy -6.639986 -34.103201)
        (xy -6.599999 -34.111636)
        (xy -6.560011 -34.103201)
        (xy -6.444989 -34.052414)
        (xy -6.419866 -34.0495)
        (xy -6.419865 -34.0495)
        (xy -5.930137 -34.0495)
        (xy -5.905009 -34.052415)
        (xy -5.802235 -34.097793)
        (xy -5.722793 -34.177235)
        (xy -5.677414 -34.28001)
        (xy -5.6745 -34.305132)
        (xy -5.6745 -35.544862)
        (xy -5.677415 -35.56999)
        (xy -5.722793 -35.672764)
        (xy -5.802235 -35.752206)
        (xy -5.873864 -35.783833)
        (xy -5.905009 -35.797585)
        (xy -5.930135 -35.8005)
        (xy -6.419864 -35.800499)
        (xy -6.444991 -35.797585)
        (xy -6.547765 -35.752206)
        (xy -6.547765 -35.752205)
        (xy -6.560012 -35.746798)
        (xy -6.6 -35.738363)
        (xy -6.639988 -35.746798)
        (xy -6.652234 -35.752205)
        (xy -6.652235 -35.752206)
        (xy -6.755009 -35.797585)
        (xy -6.780135 -35.8005)
        (xy -7.069864 -35.800499)
        (xy -7.094991 -35.797585)
        (xy -7.197765 -35.752206)
        (xy -7.197765 -35.752205)
        (xy -7.210012 -35.746798)
        (xy -7.25 -35.738363)
        (xy -7.289988 -35.746798)
        (xy -7.302234 -35.752205)
        (xy -7.302235 -35.752206)
        (xy -7.405009 -35.797585)
        (xy -7.430135 -35.8005)
        (xy -7.719864 -35.800499)
        (xy -7.744991 -35.797585)
        (xy -7.847765 -35.752206)
        (xy -7.847765 -35.752205)
        (xy -7.860012 -35.746798)
        (xy -7.9 -35.738363)
        (xy -7.939988 -35.746798)
        (xy -7.952234 -35.752205)
        (xy -7.952235 -35.752206)
        (xy -8.055009 -35.797585)
        (xy -8.080135 -35.8005)
        (xy -8.369864 -35.800499)
        (xy -8.394991 -35.797585)
        (xy -8.497765 -35.752206)
        (xy -8.497765 -35.752205)
        (xy -8.510012 -35.746798)
        (xy -8.55 -35.738363)
        (xy -8.589988 -35.746798)
        (xy -8.602234 -35.752205)
        (xy -8.602235 -35.752206)
        (xy -8.705009 -35.797585)
        (xy -8.730135 -35.8005)
        (xy -9.019864 -35.800499)
        (xy -9.044991 -35.797585)
        (xy -9.109637 -35.76904)
        (xy -9.167815 -35.762292)
        (xy -9.208722 -35.783853)
        (xy -9.32375 -35.840087)
        (xy -9.375 -35.847554)
        (xy -9.675 -35.847554)
        (xy -9.726249 -35.840088)
        (xy -9.806519 -35.800846)
        (xy -9.85 -35.790786)
        (xy -9.893481 -35.800846)
        (xy -9.973749 -35.840087)
        (xy -10.025 -35.847554)
        (xy -10.025 -35.075001)
        (xy -10.325 -35.075001)
        (xy -10.325 -35.847554)
        (xy -10.376247 -35.840088)
        (xy -10.491317 -35.783833)
        (xy -10.532181 -35.762292)
        (xy -10.590363 -35.769041)
        (xy -10.623864 -35.783833)
        (xy -10.655009 -35.797585)
        (xy -10.680135 -35.8005)
        (xy -10.969864 -35.800499)
        (xy -10.994991 -35.797585)
        (xy -11.059637 -35.76904)
        (xy -11.117815 -35.762292)
        (xy -11.158722 -35.783853)
        (xy -11.27375 -35.840087)
        (xy -11.325 -35.847554)
        (xy -11.325 -34.002445)
        (xy -10.025 -34.002445)
        (xy -9.97375 -34.009911)
        (xy -9.893479 -34.049154)
        (xy -9.849999 -34.059213)
        (xy -9.806518 -34.049154)
        (xy -9.726248 -34.009912)
        (xy -9.675 -34.002446)
        (xy -9.675 -34.774999)
        (xy -9.675001 -34.775)
        (xy -10.024999 -34.775)
        (xy -10.025 -34.774999)
        (xy -10.025 -34.002445)
        (xy -11.325 -34.002445)
        (xy -15.1395 -34.002445)
        (xy -15.1395 -35.544865)
        (xy -12.7255 -35.544865)
        (xy -12.725499 -34.305136)
        (xy -12.722585 -34.280009)
        (xy -12.677206 -34.177235)
        (xy -12.597764 -34.097793)
        (xy -12.494989 -34.052414)
        (xy -12.469866 -34.0495)
        (xy -12.469865 -34.0495)
        (xy -11.980137 -34.0495)
        (xy -11.955007 -34.052415)
        (xy -11.890362 -34.080958)
        (xy -11.832183 -34.087707)
        (xy -11.791287 -34.06615)
        (xy -11.676249 -34.009912)
        (xy -11.625 -34.002446)
        (xy -11.625 -35.847554)
        (xy -11.676247 -35.840088)
        (xy -11.791317 -35.783833)
        (xy -11.832181 -35.762292)
        (xy -11.890363 -35.769041)
        (xy -11.923864 -35.783833)
        (xy -11.955009 -35.797585)
        (xy -11.980135 -35.8005)
        (xy -12.469864 -35.800499)
        (xy -12.494991 -35.797585)
        (xy -12.597765 -35.752206)
        (xy -12.677206 -35.672765)
        (xy -12.722585 -35.569991)
        (xy -12.7255 -35.544865)
        (xy -15.1395 -35.544865)
        (xy -15.1395 -38.7641)
        (xy -15.126237 -38.8136)
        (xy -15.09 -38.849837)
        (xy -15.0405 -38.8631)
        (xy -3.4507 -38.8631)
      )
    )
  )
)
